"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monogrid+gainmap-js@3.1.0_three@0.167.1";
exports.ids = ["vendor-chunks/@monogrid+gainmap-js@3.1.0_three@0.167.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Q: () => (/* binding */ QuadRenderer)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\n\n\nconst getBufferForType = (type, width, height) => {\n    let out;\n    switch (type) {\n        case three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType:\n            out = new Uint8ClampedArray(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n            out = new Uint16Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType:\n            out = new Uint32Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.ByteType:\n            out = new Int8Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.ShortType:\n            out = new Int16Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.IntType:\n            out = new Int32Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n            out = new Float32Array(width * height * 4);\n            break;\n        default:\n            throw new Error('Unsupported data type');\n    }\n    return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n    if (_canReadPixelsResult !== undefined)\n        return _canReadPixelsResult;\n    const testRT = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(1, 1, renderTargetOptions);\n    renderer.setRenderTarget(testRT);\n    const mesh = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh(new three__WEBPACK_IMPORTED_MODULE_0__.PlaneGeometry(), new three__WEBPACK_IMPORTED_MODULE_0__.MeshBasicMaterial({ color: 0xffffff }));\n    renderer.render(mesh, camera);\n    renderer.setRenderTarget(null);\n    const out = getBufferForType(type, testRT.width, testRT.height);\n    renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n    testRT.dispose();\n    mesh.geometry.dispose();\n    mesh.material.dispose();\n    _canReadPixelsResult = out[0] !== 0;\n    return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n    /**\n     * Constructs a new QuadRenderer\n     *\n     * @param options Parameters for this QuadRenderer\n     */\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n        this._rendererIsDisposable = false;\n        this._supportsReadPixels = true;\n        /**\n         * Renders the input texture using the specified material\n         */\n        this.render = () => {\n            this._renderer.setRenderTarget(this._renderTarget);\n            try {\n                this._renderer.render(this._scene, this._camera);\n            }\n            catch (e) {\n                this._renderer.setRenderTarget(null);\n                throw e;\n            }\n            this._renderer.setRenderTarget(null);\n        };\n        this._width = options.width;\n        this._height = options.height;\n        this._type = options.type;\n        this._colorSpace = options.colorSpace;\n        const rtOptions = {\n            // fixed options\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n            depthBuffer: false,\n            stencilBuffer: false,\n            // user options\n            type: this._type, // set in class property\n            colorSpace: this._colorSpace, // set in class property\n            anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n            generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n            magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n            wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping,\n            wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping\n        };\n        this._material = options.material;\n        if (options.renderer) {\n            this._renderer = options.renderer;\n        }\n        else {\n            this._renderer = QuadRenderer.instantiateRenderer();\n            this._rendererIsDisposable = true;\n        }\n        this._scene = new three__WEBPACK_IMPORTED_MODULE_0__.Scene();\n        this._camera = new three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera();\n        this._camera.position.set(0, 0, 10);\n        this._camera.left = -0.5;\n        this._camera.right = 0.5;\n        this._camera.top = 0.5;\n        this._camera.bottom = -0.5;\n        this._camera.updateProjectionMatrix();\n        if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n            let alternativeType;\n            switch (this._type) {\n                case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n                    alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? three__WEBPACK_IMPORTED_MODULE_0__.FloatType : undefined;\n                    break;\n            }\n            if (alternativeType !== undefined) {\n                console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${three__WEBPACK_IMPORTED_MODULE_0__.FloatType}`);\n                this._type = alternativeType;\n            }\n            else {\n                this._supportsReadPixels = false;\n                console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n            }\n        }\n        this._quad = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh(new three__WEBPACK_IMPORTED_MODULE_0__.PlaneGeometry(), this._material);\n        this._quad.geometry.computeBoundingBox();\n        this._scene.add(this._quad);\n        this._renderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, rtOptions);\n        this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : three__WEBPACK_IMPORTED_MODULE_0__.UVMapping;\n    }\n    /**\n     * Instantiates a temporary renderer\n     *\n     * @returns\n     */\n    static instantiateRenderer() {\n        const renderer = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderer();\n        renderer.setSize(128, 128);\n        // renderer.outputColorSpace = SRGBColorSpace\n        // renderer.toneMapping = LinearToneMapping\n        // renderer.debug.checkShaderErrors = false\n        // this._rendererIsDisposable = true\n        return renderer;\n    }\n    /**\n     * Obtains a Buffer containing the rendered texture.\n     *\n     * @throws Error if the browser cannot read pixels from this RenderTarget type.\n     * @returns a TypedArray containing RGBA values from this renderer\n     */\n    toArray() {\n        if (!this._supportsReadPixels)\n            throw new Error('Can\\'t read pixels in this browser');\n        const out = getBufferForType(this._type, this._width, this._height);\n        this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n        return out;\n    }\n    /**\n     * Performs a readPixel operation in the renderTarget\n     * and returns a DataTexture containing the read data\n     *\n     * @param options options\n     * @returns\n     */\n    toDataTexture(options) {\n        const returnValue = new three__WEBPACK_IMPORTED_MODULE_0__.DataTexture(\n        // fixed values\n        this.toArray(), this.width, this.height, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, this._type, \n        // user values\n        (options === null || options === void 0 ? void 0 : options.mapping) || three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1, \n        // fixed value\n        three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace);\n        // set this afterwards, we can't set it in constructor\n        returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n        return returnValue;\n    }\n    /**\n     * If using a disposable renderer, it will dispose it.\n     */\n    disposeOnDemandRenderer() {\n        this._renderer.setRenderTarget(null);\n        if (this._rendererIsDisposable) {\n            this._renderer.dispose();\n            this._renderer.forceContextLoss();\n        }\n    }\n    /**\n     * Will dispose of **all** assets used by this renderer.\n     *\n     *\n     * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n     * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n     * or are otherwise done with it.\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n     * // DO NOT dispose the renderTarget here,\n     * // it is used directly in the material\n     * result.dispose()\n     * ```\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const pmremGenerator = new PMREMGenerator( renderer );\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n     * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n     * // renderTarget can be disposed here\n     * // because it was used to generate a PMREM texture\n     * result.dispose(true)\n     * ```\n     */\n    dispose(disposeRenderTarget) {\n        this.disposeOnDemandRenderer();\n        if (disposeRenderTarget) {\n            this.renderTarget.dispose();\n        }\n        // dispose shader material texture uniforms\n        if (this.material instanceof three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial) {\n            Object.values(this.material.uniforms).forEach(v => {\n                if (v.value instanceof three__WEBPACK_IMPORTED_MODULE_0__.Texture)\n                    v.value.dispose();\n            });\n        }\n        // dispose other material properties\n        Object.values(this.material).forEach(value => {\n            if (value instanceof three__WEBPACK_IMPORTED_MODULE_0__.Texture)\n                value.dispose();\n        });\n        this.material.dispose();\n        this._quad.geometry.dispose();\n    }\n    /**\n     * Width of the texture\n     */\n    get width() { return this._width; }\n    set width(value) {\n        this._width = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * Height of the texture\n     */\n    get height() { return this._height; }\n    set height(value) {\n        this._height = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * The renderer used\n     */\n    get renderer() { return this._renderer; }\n    /**\n     * The `WebGLRenderTarget` used.\n     */\n    get renderTarget() { return this._renderTarget; }\n    set renderTarget(value) {\n        this._renderTarget = value;\n        this._width = value.width;\n        this._height = value.height;\n        // this._type = value.texture.type\n    }\n    /**\n     * The `Material` used.\n     */\n    get material() { return this._material; }\n    /**\n     *\n     */\n    get type() { return this._type; }\n    get colorSpace() { return this._colorSpace; }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/decode.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/decode.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GainMapDecoderMaterial: () => (/* binding */ GainMapDecoderMaterial),\n/* harmony export */   GainMapLoader: () => (/* binding */ GainMapLoader),\n/* harmony export */   HDRJPGLoader: () => (/* binding */ HDRJPGLoader),\n/* harmony export */   JPEGRLoader: () => (/* binding */ HDRJPGLoader),\n/* harmony export */   MPFExtractor: () => (/* binding */ MPFExtractor),\n/* harmony export */   QuadRenderer: () => (/* reexport safe */ _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   extractGainmapFromJPEG: () => (/* binding */ extractGainmapFromJPEG),\n/* harmony export */   extractXMP: () => (/* binding */ extractXMP)\n/* harmony export */ });\n/* harmony import */ var _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QuadRenderer-DuOPRGA4.js */ \"(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\n\n\n\nconst vertexShader = /* glsl */ `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */ `\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial {\n    /**\n     *\n     * @param params\n     */\n    constructor({ gamma, offsetHdr, offsetSdr, gainMapMin, gainMapMax, maxDisplayBoost, hdrCapacityMin, hdrCapacityMax, sdr, gainMap }) {\n        super({\n            name: 'GainMapDecoderMaterial',\n            vertexShader,\n            fragmentShader,\n            uniforms: {\n                sdr: { value: sdr },\n                gainMap: { value: gainMap },\n                gamma: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2]) },\n                offsetHdr: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(offsetHdr) },\n                offsetSdr: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(offsetSdr) },\n                gainMapMin: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(gainMapMin) },\n                gainMapMax: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(gainMapMax) },\n                weightFactor: {\n                    value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n                }\n            },\n            blending: three__WEBPACK_IMPORTED_MODULE_0__.NoBlending,\n            depthTest: false,\n            depthWrite: false\n        });\n        this._maxDisplayBoost = maxDisplayBoost;\n        this._hdrCapacityMin = hdrCapacityMin;\n        this._hdrCapacityMax = hdrCapacityMax;\n        this.needsUpdate = true;\n        this.uniformsNeedUpdate = true;\n    }\n    get sdr() { return this.uniforms.sdr.value; }\n    set sdr(value) { this.uniforms.sdr.value = value; }\n    get gainMap() { return this.uniforms.gainMap.value; }\n    set gainMap(value) { this.uniforms.gainMap.value = value; }\n    /**\n     * @see {@link GainMapMetadata.offsetHdr}\n     */\n    get offsetHdr() { return this.uniforms.offsetHdr.value.toArray(); }\n    set offsetHdr(value) { this.uniforms.offsetHdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.offsetSdr}\n     */\n    get offsetSdr() { return this.uniforms.offsetSdr.value.toArray(); }\n    set offsetSdr(value) { this.uniforms.offsetSdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMin}\n     */\n    get gainMapMin() { return this.uniforms.gainMapMin.value.toArray(); }\n    set gainMapMin(value) { this.uniforms.gainMapMin.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMax}\n     */\n    get gainMapMax() { return this.uniforms.gainMapMax.value.toArray(); }\n    set gainMapMax(value) { this.uniforms.gainMapMax.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gamma}\n     */\n    get gamma() {\n        const g = this.uniforms.gamma.value;\n        return [1 / g.x, 1 / g.y, 1 / g.z];\n    }\n    set gamma(value) {\n        const g = this.uniforms.gamma.value;\n        g.x = 1.0 / value[0];\n        g.y = 1.0 / value[1];\n        g.z = 1.0 / value[2];\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMin() { return this._hdrCapacityMin; }\n    set hdrCapacityMin(value) {\n        this._hdrCapacityMin = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMax() { return this._hdrCapacityMax; }\n    set hdrCapacityMax(value) {\n        this._hdrCapacityMax = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n     * @remarks Non Logarithmic space\n     */\n    get maxDisplayBoost() { return this._maxDisplayBoost; }\n    set maxDisplayBoost(value) {\n        this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n        this.calculateWeight();\n    }\n    calculateWeight() {\n        const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n        this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n    }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = (params) => {\n    const { sdr, gainMap, renderer } = params;\n    if (sdr.colorSpace !== three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace) {\n        console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n        sdr.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace;\n    }\n    sdr.needsUpdate = true;\n    if (gainMap.colorSpace !== three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace) {\n        console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n        gainMap.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace;\n    }\n    gainMap.needsUpdate = true;\n    const material = new GainMapDecoderMaterial({\n        ...params,\n        sdr,\n        gainMap\n    });\n    const quadRenderer = new _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q({\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        width: sdr.image.width,\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        height: sdr.image.height,\n        type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n        colorSpace: three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace,\n        material,\n        renderer,\n        renderTargetOptions: params.renderTargetOptions\n    });\n    try {\n        quadRenderer.render();\n    }\n    catch (e) {\n        quadRenderer.disposeOnDemandRenderer();\n        throw e;\n    }\n    return quadRenderer;\n};\n\nclass GainMapNotFoundError extends Error {\n}\n\nclass XMPMetadataNotFoundError extends Error {\n}\n\nconst getXMLValue = (xml, tag, defaultValue) => {\n    // Check for attribute format first: tag=\"value\"\n    const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n    if (attributeMatch)\n        return attributeMatch[1];\n    // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n    const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n    if (tagMatch) {\n        // Check if it contains rdf:li elements\n        const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n        if (liValues && liValues.length === 3) {\n            return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n        }\n        return tagMatch[1].trim();\n    }\n    if (defaultValue !== undefined)\n        return defaultValue;\n    throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = (input) => {\n    let str;\n    // support node test environment\n    if (typeof TextDecoder !== 'undefined')\n        str = new TextDecoder().decode(input);\n    else\n        str = input.toString();\n    let start = str.indexOf('<x:xmpmeta');\n    while (start !== -1) {\n        const end = str.indexOf('x:xmpmeta>', start);\n        const xmpBlock = str.slice(start, end + 10);\n        try {\n            const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n            const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n            const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n            const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n            const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n            // These are always attributes, so we can use a simpler regex\n            const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n            const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n            const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n            if (!hdrCapacityMaxMatch)\n                throw new Error('Incomplete gainmap metadata');\n            const hdrCapacityMax = hdrCapacityMaxMatch[1];\n            return {\n                gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n                gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n                gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n                offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n                offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n                hdrCapacityMin: parseFloat(hdrCapacityMin),\n                hdrCapacityMax: parseFloat(hdrCapacityMax)\n            };\n        }\n        catch (e) {\n            // Continue searching for another xmpmeta block if this one fails\n        }\n        start = str.indexOf('<x:xmpmeta', end);\n    }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n    constructor(options) {\n        this.options = {\n            debug: options && options.debug !== undefined ? options.debug : false,\n            extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n            extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n        };\n    }\n    extract(imageArrayBuffer) {\n        return new Promise((resolve, reject) => {\n            const debug = this.options.debug;\n            const dataView = new DataView(imageArrayBuffer.buffer);\n            // If you're executing this line on a big endian machine, it'll be reversed.\n            // bigEnd further down though, refers to the endianness of the image itself.\n            if (dataView.getUint16(0) !== 0xffd8) {\n                reject(new Error('Not a valid jpeg'));\n                return;\n            }\n            const length = dataView.byteLength;\n            let offset = 2;\n            let loops = 0;\n            let marker; // APP# marker\n            while (offset < length) {\n                if (++loops > 250) {\n                    reject(new Error(`Found no marker after ${loops} loops 😵`));\n                    return;\n                }\n                if (dataView.getUint8(offset) !== 0xff) {\n                    reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n                    return;\n                }\n                marker = dataView.getUint8(offset + 1);\n                if (debug)\n                    console.log(`Marker: ${marker.toString(16)}`);\n                if (marker === 0xe2) {\n                    if (debug)\n                        console.log('Found APP2 marker (0xffe2)');\n                    // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n                    // Great way to visualize image information in html is using Exiftool. E.g.:\n                    // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n                    const formatPt = offset + 4;\n                    /*\n                     *  Structure of the MP Format Identifier\n                     *\n                     *  Offset Addr.  | Code (Hex)  | Description\n                     *  +00             ff            Marker Prefix      <-- offset\n                     *  +01             e2            APP2\n                     *  +02             #n            APP2 Field Length\n                     *  +03             #n            APP2 Field Length\n                     *  +04             4d            'M'                <-- formatPt\n                     *  +05             50            'P'\n                     *  +06             46            'F'\n                     *  +07             00            NULL\n                     *                                                   <-- tiffOffset\n                     */\n                    if (dataView.getUint32(formatPt) === 0x4d504600) {\n                        // Found MPF tag, so we start dig out sub images\n                        const tiffOffset = formatPt + 4;\n                        let bigEnd; // Endianness from TIFF header\n                        // Test for TIFF validity and endianness\n                        // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n                        if (dataView.getUint16(tiffOffset) === 0x4949) {\n                            bigEnd = false;\n                        }\n                        else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n                            bigEnd = true;\n                        }\n                        else {\n                            reject(new Error('No valid endianness marker found in TIFF header'));\n                            return;\n                        }\n                        if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n                            reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n                            return;\n                        }\n                        // 32 bit number stating the offset from the start of the 8 Byte MP Header\n                        // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n                        const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n                        if (firstIFDOffset < 0x00000008) {\n                            reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n                            return;\n                        }\n                        // Move ahead to MP Index IFD\n                        // Assume we're at the first IFD, so firstIFDOffset points to\n                        // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n                        // we fail silently here due to this assumption)\n                        // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n                        const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n                        const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n                        // Extract info from MPEntries (starting after Count)\n                        const entriesStart = dirStart + 2;\n                        let numberOfImages = 0;\n                        for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n                            // Each entry is 12 Bytes long\n                            // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n                            if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                                // stored in Last 4 bytes of its 12 Byte entry.\n                                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n                            }\n                        }\n                        const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n                        const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n                        const images = [];\n                        for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n                            const image = {\n                                MPType: dataView.getUint32(i, !bigEnd),\n                                size: dataView.getUint32(i + 4, !bigEnd),\n                                // This offset is specified relative to the address of the MP Endian\n                                // field in the MP Header, unless the image is a First Individual Image,\n                                // in which case the value of the offset shall be NULL (0x00000000).\n                                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                                start: -1,\n                                end: -1,\n                                isFII: false\n                            };\n                            if (!image.dataOffset) {\n                                // dataOffset is 0x00000000 for First Individual Image\n                                image.start = 0;\n                                image.isFII = true;\n                            }\n                            else {\n                                image.start = tiffOffset + image.dataOffset;\n                                image.isFII = false;\n                            }\n                            image.end = image.start + image.size;\n                            images.push(image);\n                        }\n                        if (this.options.extractNonFII && images.length) {\n                            const bufferBlob = new Blob([dataView]);\n                            const imgs = [];\n                            for (const image of images) {\n                                if (image.isFII && !this.options.extractFII) {\n                                    continue; // Skip FII\n                                }\n                                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                                // we don't need this\n                                // const imageUrl = URL.createObjectURL(imageBlob)\n                                // image.img = document.createElement('img')\n                                // image.img.src = imageUrl\n                                imgs.push(imageBlob);\n                            }\n                            resolve(imgs);\n                        }\n                    }\n                }\n                offset += 2 + dataView.getUint16(offset + 2);\n            }\n        });\n    }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async (jpegFile) => {\n    const metadata = extractXMP(jpegFile);\n    if (!metadata)\n        throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n    const mpfExtractor = new MPFExtractor({ extractFII: true, extractNonFII: true });\n    const images = await mpfExtractor.extract(jpegFile);\n    if (images.length !== 2)\n        throw new GainMapNotFoundError('Gain map recovery image not found');\n    return {\n        sdr: new Uint8Array(await images[0].arrayBuffer()),\n        gainMap: new Uint8Array(await images[1].arrayBuffer()),\n        metadata\n    };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = (blob) => {\n    return new Promise((resolve, reject) => {\n        const img = document.createElement('img');\n        img.onload = () => { resolve(img); };\n        img.onerror = (e) => { reject(e); };\n        img.src = URL.createObjectURL(blob);\n    });\n};\n\nclass LoaderBase extends three__WEBPACK_IMPORTED_MODULE_0__.Loader {\n    /**\n     *\n     * @param renderer\n     * @param manager\n     */\n    constructor(renderer, manager) {\n        super(manager);\n        if (renderer)\n            this._renderer = renderer;\n        this._internalLoadingManager = new three__WEBPACK_IMPORTED_MODULE_0__.LoadingManager();\n    }\n    /**\n     * Specify the renderer to use when rendering the gain map\n     *\n     * @param renderer\n     * @returns\n     */\n    setRenderer(renderer) {\n        this._renderer = renderer;\n        return this;\n    }\n    /**\n     * Specify the renderTarget options to use when rendering the gain map\n     *\n     * @param options\n     * @returns\n     */\n    setRenderTargetOptions(options) {\n        this._renderTargetOptions = options;\n        return this;\n    }\n    /**\n     * @private\n     * @returns\n     */\n    prepareQuadRenderer() {\n        if (!this._renderer)\n            console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n        // temporary values\n        const material = new GainMapDecoderMaterial({\n            gainMapMax: [1, 1, 1],\n            gainMapMin: [0, 0, 0],\n            gamma: [1, 1, 1],\n            offsetHdr: [1, 1, 1],\n            offsetSdr: [1, 1, 1],\n            hdrCapacityMax: 1,\n            hdrCapacityMin: 0,\n            maxDisplayBoost: 1,\n            gainMap: new three__WEBPACK_IMPORTED_MODULE_0__.Texture(),\n            sdr: new three__WEBPACK_IMPORTED_MODULE_0__.Texture()\n        });\n        return new _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q({\n            width: 16,\n            height: 16,\n            type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n            colorSpace: three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace,\n            material,\n            renderer: this._renderer,\n            renderTargetOptions: this._renderTargetOptions\n        });\n    }\n    /**\n   * @private\n   * @param quadRenderer\n   * @param metadata\n   * @param sdrBuffer\n   * @param gainMapBuffer\n   */\n    async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n        // this is optional, will render a black gain-map if not present\n        const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], { type: 'image/jpeg' }) : undefined;\n        const sdrBlob = new Blob([sdrBuffer], { type: 'image/jpeg' });\n        let sdrImage;\n        let gainMapImage;\n        let needsFlip = false;\n        if (typeof createImageBitmap === 'undefined') {\n            const res = await Promise.all([\n                gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined),\n                getHTMLImageFromBlob(sdrBlob)\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n            needsFlip = true;\n        }\n        else {\n            const res = await Promise.all([\n                gainMapBlob ? createImageBitmap(gainMapBlob, { imageOrientation: 'flipY' }) : Promise.resolve(undefined),\n                createImageBitmap(sdrBlob, { imageOrientation: 'flipY' })\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n        }\n        const gainMap = new three__WEBPACK_IMPORTED_MODULE_0__.Texture(gainMapImage || new ImageData(2, 2), three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.LinearMipMapLinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType, 1, three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace);\n        gainMap.flipY = needsFlip;\n        gainMap.needsUpdate = true;\n        const sdr = new three__WEBPACK_IMPORTED_MODULE_0__.Texture(sdrImage, three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.LinearMipMapLinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType, 1, three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace);\n        sdr.flipY = needsFlip;\n        sdr.needsUpdate = true;\n        quadRenderer.width = sdrImage.width;\n        quadRenderer.height = sdrImage.height;\n        quadRenderer.material.gainMap = gainMap;\n        quadRenderer.material.sdr = sdr;\n        quadRenderer.material.gainMapMin = metadata.gainMapMin;\n        quadRenderer.material.gainMapMax = metadata.gainMapMax;\n        quadRenderer.material.offsetHdr = metadata.offsetHdr;\n        quadRenderer.material.offsetSdr = metadata.offsetSdr;\n        quadRenderer.material.gamma = metadata.gamma;\n        quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n        quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n        quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n        quadRenderer.material.needsUpdate = true;\n        quadRenderer.render();\n    }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n    /**\n     * Loads a gainmap using separate data\n     * * sdr image\n     * * gain map image\n     * * metadata json\n     *\n     * useful for webp gain maps\n     *\n     * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        let sdr;\n        let gainMap;\n        let metadata;\n        const loadCheck = async () => {\n            if (sdr && gainMap && metadata) {\n                // solves #16\n                try {\n                    await this.render(quadRenderer, metadata, sdr, gainMap);\n                }\n                catch (error) {\n                    this.manager.itemError(sdrUrl);\n                    this.manager.itemError(gainMapUrl);\n                    this.manager.itemError(metadataUrl);\n                    if (typeof onError === 'function')\n                        onError(error);\n                    quadRenderer.disposeOnDemandRenderer();\n                    return;\n                }\n                if (typeof onLoad === 'function')\n                    onLoad(quadRenderer);\n                this.manager.itemEnd(sdrUrl);\n                this.manager.itemEnd(gainMapUrl);\n                this.manager.itemEnd(metadataUrl);\n                quadRenderer.disposeOnDemandRenderer();\n            }\n        };\n        let sdrLengthComputable = true;\n        let sdrTotal = 0;\n        let sdrLoaded = 0;\n        let gainMapLengthComputable = true;\n        let gainMapTotal = 0;\n        let gainMapLoaded = 0;\n        let metadataLengthComputable = true;\n        let metadataTotal = 0;\n        let metadataLoaded = 0;\n        const progressHandler = () => {\n            if (typeof onProgress === 'function') {\n                const total = sdrTotal + gainMapTotal + metadataTotal;\n                const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n                const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n                onProgress(new ProgressEvent('progress', { lengthComputable, loaded, total }));\n            }\n        };\n        this.manager.itemStart(sdrUrl);\n        this.manager.itemStart(gainMapUrl);\n        this.manager.itemStart(metadataUrl);\n        const sdrLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        sdrLoader.setResponseType('arraybuffer');\n        sdrLoader.setRequestHeader(this.requestHeader);\n        sdrLoader.setPath(this.path);\n        sdrLoader.setWithCredentials(this.withCredentials);\n        sdrLoader.load(sdrUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid sdr buffer');\n            sdr = buffer;\n            await loadCheck();\n        }, (e) => {\n            sdrLengthComputable = e.lengthComputable;\n            sdrLoaded = e.loaded;\n            sdrTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(sdrUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const gainMapLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        gainMapLoader.setResponseType('arraybuffer');\n        gainMapLoader.setRequestHeader(this.requestHeader);\n        gainMapLoader.setPath(this.path);\n        gainMapLoader.setWithCredentials(this.withCredentials);\n        gainMapLoader.load(gainMapUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid gainmap buffer');\n            gainMap = buffer;\n            await loadCheck();\n        }, (e) => {\n            gainMapLengthComputable = e.lengthComputable;\n            gainMapLoaded = e.loaded;\n            gainMapTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(gainMapUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const metadataLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        // metadataLoader.setResponseType('json')\n        metadataLoader.setRequestHeader(this.requestHeader);\n        metadataLoader.setPath(this.path);\n        metadataLoader.setWithCredentials(this.withCredentials);\n        metadataLoader.load(metadataUrl, async (json) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof json !== 'string')\n                throw new Error('Invalid metadata string');\n            // TODO: implement check on JSON file and remove this eslint disable\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            metadata = JSON.parse(json);\n            await loadCheck();\n        }, (e) => {\n            metadataLengthComputable = e.lengthComputable;\n            metadataLoaded = e.loaded;\n            metadataTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(metadataUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n    /**\n     * Loads a JPEG containing gain map metadata\n     * Renders a normal SDR image if gainmap data is not found\n     *\n     * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load(url, onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        const loader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        loader.setResponseType('arraybuffer');\n        loader.setRequestHeader(this.requestHeader);\n        loader.setPath(this.path);\n        loader.setWithCredentials(this.withCredentials);\n        this.manager.itemStart(url);\n        loader.load(url, async (jpeg) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof jpeg === 'string')\n                throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n            const jpegBuffer = new Uint8Array(jpeg);\n            let sdrJPEG;\n            let gainMapJPEG;\n            let metadata;\n            try {\n                const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n                // gain map is successfully reconstructed\n                sdrJPEG = extractionResult.sdr;\n                gainMapJPEG = extractionResult.gainMap;\n                metadata = extractionResult.metadata;\n            }\n            catch (e) {\n                // render the SDR version if this is not a gainmap\n                if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n                    console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n                    metadata = {\n                        gainMapMin: [0, 0, 0],\n                        gainMapMax: [1, 1, 1],\n                        gamma: [1, 1, 1],\n                        hdrCapacityMin: 0,\n                        hdrCapacityMax: 1,\n                        offsetHdr: [0, 0, 0],\n                        offsetSdr: [0, 0, 0]\n                    };\n                    sdrJPEG = jpegBuffer;\n                }\n                else {\n                    throw e;\n                }\n            }\n            // solves #16\n            try {\n                await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n            }\n            catch (error) {\n                this.manager.itemError(url);\n                if (typeof onError === 'function')\n                    onError(error);\n                quadRenderer.disposeOnDemandRenderer();\n                return;\n            }\n            if (typeof onLoad === 'function')\n                onLoad(quadRenderer);\n            this.manager.itemEnd(url);\n            quadRenderer.disposeOnDemandRenderer();\n        }, onProgress, (error) => {\n            this.manager.itemError(url);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/decode.js\n");

/***/ })

};
;