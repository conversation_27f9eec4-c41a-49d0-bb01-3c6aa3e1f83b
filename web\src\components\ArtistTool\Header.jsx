import {
  CloudUpload,
  Share,
  Store,
  Bell,
  RotateCcw,
  Camera,
  Upload,
  Maximize2,
  Minimize2,
  History,
  RotateCw,
  Undo,
  Redo,
  AlertCircle,
  RotateLeft,
} from "lucide-react";
import Image from "next/image";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { useState, useEffect } from "react";
import { SaveSceneModal } from "./SaveSceneModal";
import { LoadSceneModal } from "./LoadSceneModal";
import Link from "next/link";
import { useParams } from "next/navigation";
import VersionHistory from "../VersionHistory";
import { getVersions } from "@/lib/actions/version.actions";

export default function Header({
  onReset,
  onScreenshot,
  onSaveScene,
  onLoadScene,
  isFullscreen,
  onToggleFullscreen,
  user,
  project,
  onUndo,
  onRedo,
  hasUnsavedChanges,
  onRevertToSaved,
}) {
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  return (
    <>
      <header
        id="header"
        className="fixed w-full z-50 px-4 py-2 transition-all duration-300"
      >
        <div className="rounded-full px-6 py-1 flex items-center justify-between">
          <div
            className={`w-auto h-10 transition-all duration-300 ${
              isFullscreen ? "opacity-0" : "opacity-100"
            }`}
          >
            <Link href={`/projects/${project._id}`}>
              <div className="size-12">
                <Image
                  src="/images/CSS Logo.png"
                  alt="Logo"
                  width={1024}
                  height={780}
                  className="w-full h-full object-contain"
                />
              </div>
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <HeaderButton
                icon={isFullscreen ? Minimize2 : Maximize2}
                tooltip={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
                onClick={onToggleFullscreen}
              />
            </TooltipProvider>
            <div
              className={`h-6 w-px bg-white/20 mx-1 transition-all duration-300 ${
                isFullscreen ? "opacity-0" : "opacity-100"
              }`}
            />
            <div
              className={`flex items-center gap-2 transition-all duration-300 ${
                isFullscreen ? "opacity-0" : "opacity-100"
              }`}
            >
              <TooltipProvider>
                <HeaderButton
                  icon={Undo}
                  tooltip="Undo (Ctrl+Z)"
                  onClick={onUndo}
                />
              </TooltipProvider>
              <TooltipProvider>
                <HeaderButton
                  icon={Redo}
                  tooltip="Redo (Ctrl+Y)"
                  onClick={onRedo}
                />
              </TooltipProvider>
              {hasUnsavedChanges && (
                <>
                  <TooltipProvider>
                    <HeaderButton
                      icon={RotateLeft}
                      tooltip="Revert to Last Saved Version"
                      onClick={onRevertToSaved}
                    />
                  </TooltipProvider>
                  <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-orange-500/20 border border-orange-500/30">
                    <AlertCircle className="w-3 h-3 text-orange-400" />
                    <span className="text-xs text-orange-400 font-medium">
                      Unsaved Changes
                    </span>
                  </div>
                </>
              )}
              <div className="h-6 w-px bg-white/20 mx-1" />
              <TooltipProvider>
                <HeaderButton
                  icon={RotateCcw}
                  tooltip="Reset Scene"
                  onClick={onReset}
                />
              </TooltipProvider>
              <TooltipProvider>
                <HeaderButton
                  icon={Camera}
                  tooltip="Screenshot"
                  onClick={onScreenshot}
                />
              </TooltipProvider>
              <TooltipProvider>
                <HeaderButton
                  icon={History}
                  tooltip="Version History"
                  onClick={() => setShowVersionHistory(true)}
                />
              </TooltipProvider>
              <div className="h-6 w-px bg-white/20 mx-1" />
              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => setShowLoadModal(true)}
                        className="px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group"
                      >
                        <Upload className="w-4 h-4 text-white/80 group-hover:text-white" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Load Scene</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => setShowSaveModal(true)}
                        className="px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group"
                      >
                        <CloudUpload className="w-4 h-4 text-white/80 group-hover:text-white" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Save Scene</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
      </header>

      <SaveSceneModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        onSave={(sceneName, description, saveType) => {
          onSaveScene(sceneName, description, saveType);
          setShowSaveModal(false);
        }}
      />

      <LoadSceneModal
        isOpen={showLoadModal}
        onClose={() => setShowLoadModal(false)}
        onLoad={(config) => {
          onLoadScene(config);
          setShowLoadModal(false);
        }}
      />

      <VersionHistory
        isOpen={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        userId={user.id}
        workspaceId={project._id}
        onApplyVersion={(config) => {
          onLoadScene(config);
          setShowVersionHistory(false);
        }}
      />
    </>
  );
}

function HeaderButton({ icon: Icon, tooltip, primary = false, onClick }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          onClick={onClick}
          className={`p-1.5 rounded-full ${
            primary
              ? "bg-primary text-white hover:bg-primary-600"
              : "text-white hover:bg-white/20"
          }`}
        >
          <Icon className="w-4 h-4" />
        </button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  );
}
