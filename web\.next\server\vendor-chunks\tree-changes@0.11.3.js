"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tree-changes@0.11.3";
exports.ids = ["vendor-chunks/tree-changes@0.11.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/tree-changes@0.11.3/node_modules/tree-changes/dist/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/tree-changes@0.11.3/node_modules/tree-changes/dist/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ treeChanges)\n/* harmony export */ });\n/* harmony import */ var _gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @gilbarbara/deep-equal */ \"(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.3.1/node_modules/@gilbarbara/deep-equal/dist/index.mjs\");\n/* harmony import */ var is_lite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-lite */ \"(ssr)/./node_modules/.pnpm/is-lite@1.2.1/node_modules/is-lite/dist/index.mjs\");\n// src/index.ts\n\n\n\n// src/helpers.ts\n\n\nfunction canHaveLength(...arguments_) {\n  return arguments_.every((d) => is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(d));\n}\nfunction checkEquality(left, right, value) {\n  if (!isSameType(left, right)) {\n    return false;\n  }\n  if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array)) {\n    return !left.some(hasValue(value)) && right.some(hasValue(value));\n  }\n  if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n    return !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value));\n  }\n  return right === value;\n}\nfunction compareNumbers(previousData, data, options) {\n  const { actual, key, previous, type } = options;\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n  let changed = [left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number) && (type === \"increased\" ? left < right : left > right);\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].undefined(actual)) {\n    changed = changed && right === actual;\n  }\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].undefined(previous)) {\n    changed = changed && left === previous;\n  }\n  return changed;\n}\nfunction compareValues(previousData, data, options) {\n  const { key, type, value } = options;\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n  const primary = type === \"added\" ? left : right;\n  const secondary = type === \"added\" ? right : left;\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].nullOrUndefined(value)) {\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(primary)) {\n      if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(primary) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(primary)) {\n        return checkEquality(primary, secondary, value);\n      }\n    } else {\n      return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(secondary, value);\n    }\n    return false;\n  }\n  if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array)) {\n    return !secondary.every(isEqualPredicate(primary));\n  }\n  if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n    return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n  }\n  return ![left, right].every((d) => is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].primitive(d) && is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(d)) && (type === \"added\" ? !is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(left) && is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(right) : is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(left) && !is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(right));\n}\nfunction getIterables(previousData, data, { key } = {}) {\n  let left = nested(previousData, key);\n  let right = nested(data, key);\n  if (!isSameType(left, right)) {\n    throw new TypeError(\"Inputs have different types\");\n  }\n  if (!canHaveLength(left, right)) {\n    throw new TypeError(\"Inputs don't have length\");\n  }\n  if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n    left = Object.keys(left);\n    right = Object.keys(right);\n  }\n  return [left, right];\n}\nfunction hasEntry(input) {\n  return ([key, value]) => {\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(input)) {\n      return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value) || input.some((d) => (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(value) && isEqualPredicate(value)(d));\n    }\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(input) && input[key]) {\n      return !!input[key] && (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input[key], value);\n    }\n    return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value);\n  };\n}\nfunction hasExtraKeys(left, right) {\n  return right.some((d) => !left.includes(d));\n}\nfunction hasValue(input) {\n  return (value) => {\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(input)) {\n      return input.some((d) => (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(value) && isEqualPredicate(value)(d));\n    }\n    return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value);\n  };\n}\nfunction includesOrEqualsTo(previousValue, value) {\n  return is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(previousValue) ? previousValue.some((d) => (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value)) : (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(previousValue, value);\n}\nfunction isEqualPredicate(data) {\n  return (value) => data.some((d) => (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value));\n}\nfunction isSameType(...arguments_) {\n  return arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array) || arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number) || arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject) || arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string);\n}\nfunction nested(data, property) {\n  if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(data) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(data)) {\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string(property)) {\n      const props = property.split(\".\");\n      return props.reduce((acc, d) => acc && acc[d], data);\n    }\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number(property)) {\n      return data[property];\n    }\n    return data;\n  }\n  return data;\n}\n\n// src/index.ts\nfunction treeChanges(previousData, data) {\n  if ([previousData, data].some(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].nullOrUndefined)) {\n    throw new Error(\"Missing required parameters\");\n  }\n  if (![previousData, data].every((d) => is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(d))) {\n    throw new Error(\"Expected plain objects or array\");\n  }\n  const added = (key, value) => {\n    try {\n      return compareValues(previousData, data, { key, type: \"added\", value });\n    } catch {\n      return false;\n    }\n  };\n  const changed = (key, actual, previous) => {\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(actual);\n      const hasPrevious = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(previous);\n      if (hasActual || hasPrevious) {\n        const leftComparator = hasPrevious ? includesOrEqualsTo(previous, left) : !includesOrEqualsTo(actual, left);\n        const rightComparator = includesOrEqualsTo(actual, right);\n        return leftComparator && rightComparator;\n      }\n      if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array) || [left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n        return !(0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(left, right);\n      }\n      return left !== right;\n    } catch {\n      return false;\n    }\n  };\n  const changedFrom = (key, previous, actual) => {\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n      return false;\n    }\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(actual);\n      return includesOrEqualsTo(previous, left) && (hasActual ? includesOrEqualsTo(actual, right) : !hasActual);\n    } catch {\n      return false;\n    }\n  };\n  const decreased = (key, actual, previous) => {\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n      return false;\n    }\n    try {\n      return compareNumbers(previousData, data, { key, actual, previous, type: \"decreased\" });\n    } catch {\n      return false;\n    }\n  };\n  const emptied = (key) => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n      return !!left.length && !right.length;\n    } catch {\n      return false;\n    }\n  };\n  const filled = (key) => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n      return !left.length && !!right.length;\n    } catch {\n      return false;\n    }\n  };\n  const increased = (key, actual, previous) => {\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n      return false;\n    }\n    try {\n      return compareNumbers(previousData, data, { key, actual, previous, type: \"increased\" });\n    } catch {\n      return false;\n    }\n  };\n  const removed = (key, value) => {\n    try {\n      return compareValues(previousData, data, { key, type: \"removed\", value });\n    } catch {\n      return false;\n    }\n  };\n  return { added, changed, changedFrom, decreased, emptied, filled, increased, removed };\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tree-changes@0.11.3/node_modules/tree-changes/dist/index.mjs\n");

/***/ })

};
;