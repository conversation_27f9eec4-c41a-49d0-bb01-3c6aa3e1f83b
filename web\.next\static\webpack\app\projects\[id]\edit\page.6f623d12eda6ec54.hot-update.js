"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js ***!
  \***************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Bell; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.396.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Bell = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Bell\", [\n    [\n        \"path\",\n        {\n            d: \"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\",\n            key: \"1qo2s2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\",\n            key: \"qgo35s\"\n        }\n    ]\n]);\n //# sourceMappingURL=bell.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \***********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CircleAlert; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.396.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleAlert\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/share.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/share.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Share; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.396.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Share = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Share\", [\n    [\n        \"path\",\n        {\n            d: \"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\",\n            key: \"1b2hhj\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 6 12 2 8 6\",\n            key: \"m901s6\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"2\",\n            y2: \"15\",\n            key: \"1p0rca\"\n        }\n    ]\n]);\n //# sourceMappingURL=share.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/share.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/store.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/store.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Store; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.396.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Store = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Store\", [\n    [\n        \"path\",\n        {\n            d: \"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7\",\n            key: \"ztvudi\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\",\n            key: \"1b2hhj\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4\",\n            key: \"2ebpfo\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 7h20\",\n            key: \"1fcdvo\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7\",\n            key: \"6c3vgh\"\n        }\n    ]\n]);\n //# sourceMappingURL=store.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC4zOTYuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zdG9yZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLFFBQVFDLGdFQUFnQkEsQ0FBQyxTQUFTO0lBQ3RDO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQStEQyxLQUFLO1FBQUE7S0FBVTtJQUM1RjtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUE2Q0MsS0FBSztRQUFBO0tBQVU7SUFDMUU7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBOENDLEtBQUs7UUFBQTtLQUFVO0lBQzNFO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQVdDLEtBQUs7UUFBQTtLQUFVO0lBQ3hDO1FBQ0U7UUFDQTtZQUNFRCxHQUFHO1lBQ0hDLEtBQUs7UUFDUDtLQUNGO0NBQ0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9zdG9yZS50cz9iMTI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgU3RvcmVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1pQTNJRFF1TkRFdE5DNDBNVUV5SURJZ01DQXdJREVnTnk0NE15QXlhRGd1TXpSaE1pQXlJREFnTUNBeElERXVOREl1TlRsTU1qSWdOeUlnTHo0S0lDQThjR0YwYUNCa1BTSk5OQ0F4TW5ZNFlUSWdNaUF3SURBZ01DQXlJREpvTVRKaE1pQXlJREFnTUNBd0lESXRNbll0T0NJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRVZ01qSjJMVFJoTWlBeUlEQWdNQ0F3TFRJdE1tZ3RNbUV5SURJZ01DQXdJREF0TWlBeWRqUWlJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRUSWdOMmd5TUNJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTWpJZ04zWXpZVElnTWlBd0lEQWdNUzB5SURKaE1pNDNJREl1TnlBd0lEQWdNUzB4TGpVNUxTNDJNeTQzTGpjZ01DQXdJREF0TGpneUlEQkJNaTQzSURJdU55QXdJREFnTVNBeE5pQXhNbUV5TGpjZ01pNDNJREFnTUNBeExURXVOVGt0TGpZekxqY3VOeUF3SURBZ01DMHVPRElnTUVFeUxqY2dNaTQzSURBZ01DQXhJREV5SURFeVlUSXVOeUF5TGpjZ01DQXdJREV0TVM0MU9TMHVOak11Tnk0M0lEQWdNQ0F3TFM0NE1pQXdRVEl1TnlBeUxqY2dNQ0F3SURFZ09DQXhNbUV5TGpjZ01pNDNJREFnTUNBeExURXVOVGt0TGpZekxqY3VOeUF3SURBZ01DMHVPRElnTUVFeUxqY2dNaTQzSURBZ01DQXhJRFFnTVRKaE1pQXlJREFnTUNBeExUSXRNbFkzSWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvc3RvcmVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTdG9yZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ1N0b3JlJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdtMiA3IDQuNDEtNC40MUEyIDIgMCAwIDEgNy44MyAyaDguMzRhMiAyIDAgMCAxIDEuNDIuNTlMMjIgNycsIGtleTogJ3p0dnVkaScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ000IDEydjhhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0ydi04Jywga2V5OiAnMWIyaGhqJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE1IDIydi00YTIgMiAwIDAgMC0yLTJoLTJhMiAyIDAgMCAwLTIgMnY0Jywga2V5OiAnMmVicGZvJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTIgN2gyMCcsIGtleTogJzFmY2R2bycgfV0sXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00yMiA3djNhMiAyIDAgMCAxLTIgMmEyLjcgMi43IDAgMCAxLTEuNTktLjYzLjcuNyAwIDAgMC0uODIgMEEyLjcgMi43IDAgMCAxIDE2IDEyYTIuNyAyLjcgMCAwIDEtMS41OS0uNjMuNy43IDAgMCAwLS44MiAwQTIuNyAyLjcgMCAwIDEgMTIgMTJhMi43IDIuNyAwIDAgMS0xLjU5LS42My43LjcgMCAwIDAtLjgyIDBBMi43IDIuNyAwIDAgMSA4IDEyYTIuNyAyLjcgMCAwIDEtMS41OS0uNjMuNy43IDAgMCAwLS44MiAwQTIuNyAyLjcgMCAwIDEgNCAxMmEyIDIgMCAwIDEtMi0yVjcnLFxuICAgICAga2V5OiAnNmMzdmdoJyxcbiAgICB9LFxuICBdLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IFN0b3JlO1xuIl0sIm5hbWVzIjpbIlN0b3JlIiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/store.js\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AlertCircle: function() { return /* reexport safe */ _icons_circle_alert_js__WEBPACK_IMPORTED_MODULE_0__["default"]; },
/* harmony export */   Bell: function() { return /* reexport safe */ _icons_bell_js__WEBPACK_IMPORTED_MODULE_1__["default"]; },
/* harmony export */   Camera: function() { return /* reexport safe */ _icons_camera_js__WEBPACK_IMPORTED_MODULE_2__["default"]; },
/* harmony export */   CloudUpload: function() { return /* reexport safe */ _icons_cloud_upload_js__WEBPACK_IMPORTED_MODULE_3__["default"]; },
/* harmony export */   History: function() { return /* reexport safe */ _icons_history_js__WEBPACK_IMPORTED_MODULE_4__["default"]; },
/* harmony export */   Maximize2: function() { return /* reexport safe */ _icons_maximize_2_js__WEBPACK_IMPORTED_MODULE_5__["default"]; },
/* harmony export */   Minimize2: function() { return /* reexport safe */ _icons_minimize_2_js__WEBPACK_IMPORTED_MODULE_6__["default"]; },
/* harmony export */   Redo: function() { return /* reexport safe */ _icons_redo_js__WEBPACK_IMPORTED_MODULE_7__["default"]; },
/* harmony export */   RotateCcw: function() { return /* reexport safe */ _icons_rotate_ccw_js__WEBPACK_IMPORTED_MODULE_8__["default"]; },
/* harmony export */   RotateCw: function() { return /* reexport safe */ _icons_rotate_cw_js__WEBPACK_IMPORTED_MODULE_9__["default"]; },
/* harmony export */   Share: function() { return /* reexport safe */ _icons_share_js__WEBPACK_IMPORTED_MODULE_10__["default"]; },
/* harmony export */   Store: function() { return /* reexport safe */ _icons_store_js__WEBPACK_IMPORTED_MODULE_11__["default"]; },
/* harmony export */   Undo: function() { return /* reexport safe */ _icons_undo_js__WEBPACK_IMPORTED_MODULE_12__["default"]; },
/* harmony export */   Upload: function() { return /* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_13__["default"]; }
/* harmony export */ });
/* harmony import */ var _icons_circle_alert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/circle-alert.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js");
/* harmony import */ var _icons_bell_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/bell.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js");
/* harmony import */ var _icons_camera_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/camera.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js");
/* harmony import */ var _icons_cloud_upload_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/cloud-upload.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js");
/* harmony import */ var _icons_history_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/history.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js");
/* harmony import */ var _icons_maximize_2_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/maximize-2.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js");
/* harmony import */ var _icons_minimize_2_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/minimize-2.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js");
/* harmony import */ var _icons_redo_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/redo.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js");
/* harmony import */ var _icons_rotate_ccw_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/rotate-ccw.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js");
/* harmony import */ var _icons_rotate_cw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/rotate-cw.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-cw.js");
/* harmony import */ var _icons_share_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/share.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/share.js");
/* harmony import */ var _icons_store_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/store.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/store.js");
/* harmony import */ var _icons_undo_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/undo.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js");
/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./icons/upload.js */ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js");
















;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ }),

/***/ "(app-pages-browser)/./src/components/ArtistTool/Header.jsx":
/*!**********************************************!*\
  !*** ./src/components/ArtistTool/Header.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SaveSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/SaveSceneModal.jsx\");\n/* harmony import */ var _LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LoadSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/LoadSceneModal.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _VersionHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../VersionHistory */ \"(app-pages-browser)/./src/components/VersionHistory.tsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onReset, onScreenshot, onSaveScene, onLoadScene, isFullscreen, onToggleFullscreen, user, project, onUndo, onRedo, hasUnsavedChanges, onRevertToSaved } = param;\n    _s();\n    const [showSaveModal, setShowSaveModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showLoadModal, setShowLoadModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showVersionHistory, setShowVersionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                id: \"header\",\n                className: \"fixed w-full z-50 px-4 py-2 transition-all duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full px-6 py-1 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-auto h-10 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: \"/projects/\".concat(project._id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"size-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/CSS Logo.png\",\n                                        alt: \"Logo\",\n                                        width: 1024,\n                                        height: 780,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                        icon: isFullscreen ? _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        tooltip: isFullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\",\n                                        onClick: onToggleFullscreen\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-white/20 mx-1 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\")\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                tooltip: \"Undo (Ctrl+Z)\",\n                                                onClick: onUndo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                tooltip: \"Redo (Ctrl+Y)\",\n                                                onClick: onRedo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                        icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__.RotateLeft,\n                                                        tooltip: \"Revert to Last Saved Version\",\n                                                        onClick: onRevertToSaved\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 px-2 py-1 rounded-full bg-orange-500/20 border border-orange-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-3 h-3 text-orange-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-orange-400 font-medium\",\n                                                            children: \"Unsaved Changes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                tooltip: \"Reset Scene\",\n                                                onClick: onReset\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                tooltip: \"Screenshot\",\n                                                onClick: onScreenshot\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                tooltip: \"Version History\",\n                                                onClick: ()=>setShowVersionHistory(true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowLoadModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Load Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowSaveModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Save Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__.SaveSceneModal, {\n                isOpen: showSaveModal,\n                onClose: ()=>setShowSaveModal(false),\n                onSave: (sceneName, description, saveType)=>{\n                    onSaveScene(sceneName, description, saveType);\n                    setShowSaveModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__.LoadSceneModal, {\n                isOpen: showLoadModal,\n                onClose: ()=>setShowLoadModal(false),\n                onLoad: (config)=>{\n                    onLoadScene(config);\n                    setShowLoadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VersionHistory__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showVersionHistory,\n                onClose: ()=>setShowVersionHistory(false),\n                userId: user.id,\n                workspaceId: project._id,\n                onApplyVersion: (config)=>{\n                    onLoadScene(config);\n                    setShowVersionHistory(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"BpQ93XwKg8UzXTTc2Gl4xwXptgA=\");\n_c = Header;\nfunction HeaderButton(param) {\n    let { icon: Icon, tooltip, primary = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClick,\n                    className: \"p-1.5 rounded-full \".concat(primary ? \"bg-primary text-white hover:bg-primary-600\" : \"text-white hover:bg-white/20\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: tooltip\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeaderButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"HeaderButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Header.jsx\n"));

/***/ })

});