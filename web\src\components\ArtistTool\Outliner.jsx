"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import { ChevronDown, Play, Pause, ChevronRight } from "lucide-react";
import { predefinedAnimations } from "./animations/CameraAnimations";

// Add useMediaQuery hook
const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);
    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
};

// Function to categorize objects into logical layers
const categorizeObjects = (objects) => {
  const layers = {
    gems: [],
    metals: [],
    engravings: [],
    accessories: [],
    other: [],
  };

  objects.forEach((object) => {
    const name = (object.name || "").toLowerCase();
    const userData = object.userData || {};

    // Categorize based on name patterns and userData
    if (
      name.includes("diamond") ||
      name.includes("gem") ||
      name.includes("stone") ||
      name.includes("crystal") ||
      userData.type === "gem"
    ) {
      layers.gems.push(object);
    } else if (
      name.includes("ring") ||
      name.includes("band") ||
      name.includes("setting") ||
      name.includes("metal") ||
      name.includes("gold") ||
      name.includes("silver") ||
      name.includes("platinum") ||
      name.includes("prong") ||
      name.includes("bridge") ||
      userData.type === "metal"
    ) {
      layers.metals.push(object);
    } else if (
      name.includes("text") ||
      name.includes("engraving") ||
      name.includes("decal") ||
      name.includes("font") ||
      userData.type === "engraving"
    ) {
      layers.engravings.push(object);
    } else if (
      name.includes("decoration") ||
      name.includes("ornament") ||
      name.includes("detail") ||
      userData.type === "accessory"
    ) {
      layers.accessories.push(object);
    } else {
      layers.other.push(object);
    }
  });

  return layers;
};

export function Outliner({
  sceneObjects,
  selectedObjects,
  onSelectObject,
  onCameraView,
  onPlayAnimation,
  groundType,
  setGroundType,
  collapsed,
  setCollapsed,
}) {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [expandedItems, setExpandedItems] = useState({
    layers: true,
    gems: false,
    metals: true,
    engravings: false,
    accessories: false,
    other: false,
    cameras: false,
    animations: false,
    ground: true,
  });
  const [internalCollapsed, setInternalCollapsed] = useState(() => {
    // Initialize based on device type - closed on mobile, open on desktop
    if (typeof window !== "undefined") {
      return window.innerWidth < 768;
    }
    return false; // Default to open on server-side rendering
  });
  const [currentAnimation, setCurrentAnimation] = useState(null);

  // Use external collapsed state if provided, otherwise use internal state
  const isCollapsed = collapsed !== undefined ? collapsed : internalCollapsed;
  const setIsCollapsed = setCollapsed || setInternalCollapsed;

  // Update collapsed state when mobile state changes (only for internal state)
  useEffect(() => {
    if (collapsed === undefined) {
      // On mobile, always keep collapsed. On desktop, start expanded
      setInternalCollapsed(isMobile);
    }
  }, [isMobile, collapsed]);

  const toggleExpand = (section) => {
    setExpandedItems((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const cameraViews = [
    { id: "front", name: "Front" },
    { id: "top", name: "Top" },
    { id: "side", name: "Side" },
    { id: "perspective", name: "Perspective" },
  ];

  // Handle object selection with shift key for multi-select
  const handleObjectSelect = (object, event) => {
    if (event && event.shiftKey) {
      // If shift key is pressed, handle multi-selection
      // Ensure selectedObjects is always an array
      const objectsArray = Array.isArray(selectedObjects)
        ? selectedObjects
        : [selectedObjects].filter(Boolean);

      if (objectsArray.includes(object)) {
        // If already selected, remove it
        onSelectObject(objectsArray.filter((obj) => obj !== object));
      } else {
        // Otherwise add it to selection
        onSelectObject([...objectsArray, object]);
      }
    } else {
      // If no shift key, just select this object
      onSelectObject([object]);
    }
  };

  const handlePlayAnimation = (animationId) => {
    if (currentAnimation === animationId) {
      setCurrentAnimation(null);
      onPlayAnimation(null);
    } else {
      setCurrentAnimation(animationId);
      onPlayAnimation(animationId);
    }
  };

  // Handle layer selection (select all objects in a layer)
  const handleLayerSelect = (layerObjects, event) => {
    event.stopPropagation();

    if (event.shiftKey) {
      // Add layer objects to existing selection
      const objectsArray = Array.isArray(selectedObjects)
        ? selectedObjects
        : [selectedObjects].filter(Boolean);

      const newSelection = [...objectsArray];
      layerObjects.forEach((obj) => {
        if (!newSelection.includes(obj)) {
          newSelection.push(obj);
        }
      });
      onSelectObject(newSelection);
    } else {
      // Replace selection with layer objects
      onSelectObject(layerObjects);
    }
  };

  // Get categorized objects
  const layers = sceneObjects ? categorizeObjects(sceneObjects) : {};

  console.log("layers", sceneObjects);

  // Layer configuration with icons and colors
  const layerConfig = {
    gems: {
      name: "Gems & Diamonds",
      icon: "💎",
      color: "text-blue-300",
      bgColor: "bg-blue-500/20",
    },
    metals: {
      name: "Metal Components",
      icon: "⚡",
      color: "text-yellow-300",
      bgColor: "bg-yellow-500/20",
    },
    engravings: {
      name: "Engravings & Text",
      icon: "✏️",
      color: "text-green-300",
      bgColor: "bg-green-500/20",
    },
    accessories: {
      name: "Accessories",
      icon: "✨",
      color: "text-purple-300",
      bgColor: "bg-purple-500/20",
    },
    other: {
      name: "Other Objects",
      icon: "📦",
      color: "text-gray-300",
      bgColor: "bg-gray-500/20",
    },
  };

  return (
    <div
      className={`flex flex-col text-[#FDE9CE] transition-all duration-300 ease-in-out max-w-[260px] 
      ${isCollapsed ? "h-fit" : "h-[500px]"}
      ${isCollapsed ? "w-[100px]" : "w-[260px]"}`}
    >
      <div
        className={`flex items-center cursor-pointer ${
          isCollapsed ? "p-2 justify-center" : "p-3 justify-between "
        } 
          
          ${
            isCollapsed ? "rounded-lg" : "rounded-t-lg"
          } transition-all duration-300`}
        onClick={toggleCollapse}
      >
        {isCollapsed ? (
          <Image
            src="/icons/NewOutliner Icon.svg"
            alt="Outliner"
            width={32}
            height={32}
            className="text-center"
          />
        ) : (
          <h3 className="text-lg font-semibold text-[#FDE9CE] flex items-center gap-2">
            <ChevronDown size={16} className="text-[#FDE9CE]" />
            Scene Outliner
          </h3>
        )}
      </div>

      {/* Content container with transition */}
      <div
        className={`overflow-hidden scrollbar-none transition-all duration-300 ease-in-out flex-1
          ${isCollapsed ? "max-h-0 opacity-0" : "opacity-100"}`}
      >
        <div className="p-3 mt-[-.5rem] h-full overflow-y-auto touch-auto overscroll-contain scrollbar-none">
          <img
            src="/images/—Pngtree—elegant gold line border_8053691 (1) 2.png"
            alt="Outliner"
            className="w-full h-auto"
          />

          {/* Scene Layers Section */}
          {sceneObjects && sceneObjects.length > 0 && (
            <div className="mb-3">
              <div
                className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
                onClick={() => toggleExpand("layers")}
              >
                <span className="text-sm font-medium">Scene Layers</span>
                <div className="flex items-center gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectObject(sceneObjects);
                    }}
                    className="text-[9px] px-1 bg-blue-500/30 rounded hover:bg-blue-500 hover:text-white transition-colors"
                    title="Select All"
                  >
                    All
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectObject([]);
                    }}
                    className="text-[9px] px-1 bg-red-500/30 rounded hover:bg-red-500 hover:text-white transition-colors"
                    title="Clear Selection"
                  >
                    Clear
                  </button>
                  <span className="text-xs ml-1">
                    {expandedItems.layers ? (
                      <ChevronDown size={12} />
                    ) : (
                      <ChevronRight size={12} />
                    )}
                  </span>
                </div>
              </div>

              {expandedItems.layers && (
                <div className="flex flex-col gap-1 p-1 max-h-[200px] overflow-y-auto touch-auto overscroll-contain">
                  {Object.entries(layers).map(([layerKey, layerObjects]) => {
                    if (layerObjects.length === 0) return null;

                    const config = layerConfig[layerKey];
                    const selectedCount = layerObjects.filter((obj) =>
                      Array.isArray(selectedObjects)
                        ? selectedObjects.includes(obj)
                        : selectedObjects === obj
                    ).length;

                    return (
                      <div key={layerKey} className="mb-2">
                        {/* Layer Header */}
                        <div
                          className={`flex items-center justify-between p-1.5 rounded cursor-pointer ${config.bgColor} hover:opacity-80 transition-opacity`}
                          onClick={() => toggleExpand(layerKey)}
                        >
                          <div className="flex items-center gap-2">
                            <span className="text-xs">{config.icon}</span>
                            <span
                              className={`text-xs font-medium ${config.color}`}
                            >
                              {config.name}
                            </span>
                            <span className="text-[10px] text-gray-400">
                              ({layerObjects.length})
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            {selectedCount > 0 && (
                              <span className="text-[9px] bg-blue-600 text-white px-1 rounded">
                                {selectedCount}
                              </span>
                            )}
                            <button
                              onClick={(e) =>
                                handleLayerSelect(layerObjects, e)
                              }
                              className="text-[9px] px-1 bg-white/20 rounded hover:bg-white/40 transition-colors"
                              title={`Select all ${config.name.toLowerCase()}`}
                            >
                              All
                            </button>
                            <span className="text-[10px]">
                              {expandedItems[layerKey] ? (
                                <ChevronDown size={10} />
                              ) : (
                                <ChevronRight size={10} />
                              )}
                            </span>
                          </div>
                        </div>

                        {/* Layer Objects */}
                        {expandedItems[layerKey] && (
                          <div className="ml-3 mt-1 space-y-0.5">
                            {layerObjects.map((object, index) => (
                              <div
                                key={index}
                                className={`flex items-center gap-2 p-1 rounded cursor-pointer select-none transition-colors ${
                                  (
                                    Array.isArray(selectedObjects)
                                      ? selectedObjects.includes(object)
                                      : selectedObjects === object
                                  )
                                    ? "bg-blue-500/30"
                                    : "hover:bg-white/10"
                                }`}
                                onClick={(e) => handleObjectSelect(object, e)}
                              >
                                <span className="text-[10px] w-3">
                                  {(
                                    Array.isArray(selectedObjects)
                                      ? selectedObjects.includes(object)
                                      : selectedObjects === object
                                  )
                                    ? "●"
                                    : "○"}
                                </span>
                                <span className="text-[11px] truncate flex-1">
                                  {object.name ||
                                    `${config.name.split(" ")[0]} ${index + 1}`}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {/* Camera Views Section */}
          <div className="mb-3 select-none">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("cameras")}
            >
              <span className="text-sm font-medium">Cameras</span>
              <span className="text-xs">
                {expandedItems.cameras ? (
                  <ChevronDown size={12} />
                ) : (
                  <ChevronRight size={12} />
                )}
              </span>
            </div>

            {expandedItems.cameras && (
              <div className="flex flex-col gap-1 p-2">
                <div className="grid grid-cols-2 gap-1.5">
                  {cameraViews.map((view) => (
                    <button
                      key={view.id}
                      onClick={() => onCameraView && onCameraView(view.id)}
                      className="p-1.5 text-[11px] bg-transparent border border-[#FDE9CE]/30 rounded hover:bg-white/10 transition-colors"
                    >
                      {view.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Ground Section */}
          <div className="mb-3 select-none">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("ground")}
            >
              <span className="text-sm font-medium">Platform</span>
              <span className="text-xs">
                {expandedItems.ground ? (
                  <ChevronDown size={12} />
                ) : (
                  <ChevronRight size={12} />
                )}
              </span>
            </div>

            {expandedItems.ground && (
              <div className="flex flex-col gap-1 p-2">
                <select
                  value={groundType}
                  onChange={(e) => setGroundType(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded px-2 py-1 text-[11px] text-[#FDE9CE]"
                >
                  <option value="reflective">Reflective</option>
                  <option value="backplate">Backplate</option>
                  <option value="none">No Ground</option>
                  <option value="grid">Grid</option>
                  <option value="solid">Solid</option>
                </select>
              </div>
            )}
          </div>

          {/* Animations Section */}
          <div className="mb-3 select-none">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("animations")}
            >
              <span className="text-sm font-medium">Animations</span>
              <span className="text-xs">
                {expandedItems.animations ? (
                  <ChevronDown size={12} />
                ) : (
                  <ChevronRight size={12} />
                )}
              </span>
            </div>

            {expandedItems.animations && (
              <div className="flex flex-col gap-1 p-2">
                {predefinedAnimations.map((animation) => (
                  <div
                    key={animation.id}
                    className={`flex flex-col p-1.5 rounded cursor-pointer transition-colors ${
                      currentAnimation === animation.id
                        ? "bg-blue-500/30"
                        : "hover:bg-white/10"
                    }`}
                    onClick={() => handlePlayAnimation(animation.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-[11px] font-medium">
                        {animation.name}
                      </span>
                      <span className="flex items-center justify-center w-4 h-4 rounded-full">
                        {currentAnimation === animation.id ? (
                          <Pause size={12} className="text-[#FDE9CE]" />
                        ) : (
                          <Play size={12} className="text-[#FDE9CE]" />
                        )}
                      </span>
                    </div>
                    <span className="text-[9px] text-gray-400 mt-0.5">
                      {animation.description}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
