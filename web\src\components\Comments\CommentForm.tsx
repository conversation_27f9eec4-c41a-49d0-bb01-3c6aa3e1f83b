import { useEffect, useState } from "react";
import { Html } from "@react-three/drei";
import { CommentInputPosition } from "@/types";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface CommentFormProps {
  position: any; // Use THREE.Vector3 if available in this file's context
  inputPosition: CommentInputPosition | null;
  onSubmit: (
    text: string,
    importance: string,
    inputPosition: CommentInputPosition | null
  ) => void;
  onCancel: () => void;
  loading: boolean;
  className?: string;
}

export default function CommentForm({
  position,
  inputPosition,
  onSubmit,
  onCancel,
  loading,
  className,
}: CommentFormProps) {
  const [text, setText] = useState("");
  const [importance, setImportance] = useState<string>("low");
  const [isOpen, setIsOpen] = useState(true);

  // Prevent event propagation when clicking inside the form
  const handleFormClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onCancel();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    // Add click listener for outside clicks
    const handleOutsideClick = (e: MouseEvent) => {
      onCancel();
    };

    const canvas = document.querySelector("canvas");
    if (canvas) {
      canvas.addEventListener("click", handleOutsideClick);
    }

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      if (canvas) {
        canvas.removeEventListener("click", handleOutsideClick);
      }
    };
  }, [onCancel]);

  return (
    <Html position={position}>
      <Popover
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            onCancel();
          }
        }}
      >
        <PopoverTrigger asChild>
          <div className="w-4 h-4 bg-transparent cursor-pointer" />
        </PopoverTrigger>
        <PopoverContent
          className="bg-gradient-to-br from-[#47474B] via-[#635A4F] to-[#48484D] p-3 rounded-lg w-72 border border-[#E6D2BA] text-[#FDE9CE]"
          onClick={handleFormClick}
          side="top"
          align="center"
        >
          <div className="flex flex-col gap-4">
            <div className="relative">
              <textarea
                className="w-full bg-white/20 text-[#FDE9CE] rounded p-2 mb-2 placeholder:text-[#FDE9CE] focus:outline-0 focus:border-[#f3e1ca] focus:ring-[#f3e1ca]"
                placeholder="Add a comment..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    if (text.trim() && !loading) {
                      onSubmit(text, importance, inputPosition);
                      setText("");
                      setIsOpen(false);
                    }
                  }
                }}
                autoFocus
              />
              <button
                className="absolute bottom-4 right-2 p-2 rounded-full hover:bg-white/10 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  if (text.trim()) {
                    onSubmit(text, importance, inputPosition);
                    setText("");
                    setIsOpen(false);
                  }
                }}
                disabled={loading}
              >
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.0027 2.24693C13.3974 2.24693 14.7485 2.51724 16.0208 3.05246C17.2497 3.56876 18.3534 4.30941 19.3022 5.2528C20.251 6.19619 20.9959 7.29366 21.5152 8.51547C22.0535 9.78053 22.3253 11.124 22.3253 12.5107C22.3253 13.8974 22.0535 15.2408 21.5152 16.5059C20.9959 17.7277 20.251 18.8252 19.3022 19.7686C18.3534 20.712 17.2497 21.4526 16.0208 21.9689C14.7485 22.5041 13.3974 22.7744 12.0027 22.7744C10.6081 22.7744 9.25691 22.5041 7.98459 21.9689C6.75578 21.4526 5.65202 20.712 4.70322 19.7686C3.75442 18.8252 3.00952 17.7277 2.49026 16.5059C1.95197 15.2408 1.68011 13.8974 1.68011 12.5107C1.68011 11.124 1.95197 9.78053 2.49026 8.51547C3.00952 7.29366 3.75442 6.19619 4.70322 5.2528C5.65202 4.30941 6.75578 3.56876 7.98459 3.05246C9.25691 2.51724 10.6081 2.24693 12.0027 2.24693ZM12.0027 0.579102C5.37472 0.579102 0 5.92047 0 12.5107C0 19.1009 5.372 24.4423 12 24.4423C18.628 24.4423 24 19.1009 24 12.5107C24 5.92047 18.6307 0.579102 12.0027 0.579102Z"
                    fill="#030000"
                  />
                  <path
                    d="M12.0027 2.24693C13.3974 2.24693 14.7485 2.51724 16.0208 3.05246C17.2497 3.56876 18.3534 4.30941 19.3022 5.2528C20.251 6.19619 20.9959 7.29366 21.5152 8.51547C22.0535 9.78053 22.3253 11.124 22.3253 12.5107C22.3253 13.8974 22.0535 15.2408 21.5152 16.5059C20.9959 17.7277 20.251 18.8252 19.3022 19.7686C18.3534 20.712 17.2497 21.4526 16.0208 21.9689C14.7485 22.5041 13.3974 22.7744 12.0027 22.7744C10.6081 22.7744 9.25691 22.5041 7.98459 21.9689C6.75578 21.4526 5.65202 20.712 4.70322 19.7686C3.75442 18.8252 3.00952 17.7277 2.49026 16.5059C1.95197 15.2408 1.68011 13.8974 1.68011 12.5107C1.68011 11.124 1.95197 9.78053 2.49026 8.51547C3.00952 7.29366 3.75442 6.19619 4.70322 5.2528C5.65202 4.30941 6.75578 3.56876 7.98459 3.05246C9.25691 2.51724 10.6081 2.24693 12.0027 2.24693ZM12.0027 0.579102C5.37472 0.579102 0 5.92047 0 12.5107C0 19.1009 5.372 24.4423 12 24.4423C18.628 24.4423 24 19.1009 24 12.5107C24 5.92047 18.6307 0.579102 12.0027 0.579102Z"
                    fill="url(#paint0_linear_288_3552)"
                  />
                  <path
                    d="M13.5312 19.2594C13.2321 19.2594 12.9548 19.0999 12.8026 18.8404L10.3341 14.556L5.97338 12.8071C5.66618 12.6827 5.45957 12.3908 5.44869 12.061C5.43782 11.7312 5.6254 11.4258 5.92445 11.2825L17.3454 5.84383C17.6499 5.69786 18.0142 5.74922 18.267 5.97628C18.5198 6.20064 18.6095 6.55475 18.4981 6.87372L14.3223 18.7026C14.2135 19.0107 13.9335 19.227 13.6073 19.2567C13.5801 19.2594 13.5556 19.2594 13.5312 19.2594ZM8.36577 11.9691L11.2312 13.1179C11.4052 13.1882 11.552 13.3153 11.6444 13.4774L13.3463 16.432L16.2471 8.21988L8.36577 11.9691Z"
                    fill="#030000"
                  />
                  <path
                    d="M13.5312 19.2594C13.2321 19.2594 12.9548 19.0999 12.8026 18.8404L10.3341 14.556L5.97338 12.8071C5.66618 12.6827 5.45957 12.3908 5.44869 12.061C5.43782 11.7312 5.6254 11.4258 5.92445 11.2825L17.3454 5.84383C17.6499 5.69786 18.0142 5.74922 18.267 5.97628C18.5198 6.20064 18.6095 6.55475 18.4981 6.87372L14.3223 18.7026C14.2135 19.0107 13.9335 19.227 13.6073 19.2567C13.5801 19.2594 13.5556 19.2594 13.5312 19.2594ZM8.36577 11.9691L11.2312 13.1179C11.4052 13.1882 11.552 13.3153 11.6444 13.4774L13.3463 16.432L16.2471 8.21988L8.36577 11.9691Z"
                    fill="url(#paint1_linear_288_3552)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_288_3552"
                      x1="5.04"
                      y1="0.579102"
                      x2="23.8667"
                      y2="24.547"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#FDE9CE" />
                      <stop offset="1" stopColor="#978B7B" />
                    </linearGradient>
                    <linearGradient
                      id="paint1_linear_288_3552"
                      x1="8.19861"
                      y1="5.76172"
                      x2="18.9318"
                      y2="18.9447"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#FDE9CE" />
                      <stop offset="1" stopColor="#978B7B" />
                    </linearGradient>
                  </defs>
                </svg>
              </button>
            </div>
          </div>

          <div className="mb-3 flex items-center">
            <label className="text-[#DABD99] text-sm mb-1 block">
              Importance
            </label>
            <div className="flex">
              <button
                type="button"
                className="px-2 py-1 rounded text-xs flex items-center gap-2 text-[#DABD99]"
                onClick={(e) => {
                  e.stopPropagation();
                  setImportance("high");
                }}
              >
                <div
                  className={`w-4 h-4 rounded-full bg-[#FF5757] ${
                    importance === "high" ? "border-2 border-black" : ""
                  }`}
                ></div>
                High
              </button>
              <button
                type="button"
                className="px-2 py-1 rounded text-xs flex items-center gap-2 text-[#DABD99]"
                onClick={(e) => {
                  e.stopPropagation();
                  setImportance("medium");
                }}
              >
                <div
                  className={`w-4 h-4 rounded-full bg-[#FF9F45] ${
                    importance === "medium" ? "border-2 border-black" : ""
                  }`}
                ></div>
                Mid
              </button>
              <button
                type="button"
                className="px-2 py-1 rounded text-xs flex items-center gap-2 text-[#DABD99]"
                onClick={(e) => {
                  e.stopPropagation();
                  setImportance("low");
                }}
              >
                <div
                  className={`w-4 h-4 rounded-full bg-[#F5C754] ${
                    importance === "low" ? "border-2 border-black" : ""
                  }`}
                ></div>
                Low
              </button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </Html>
  );
}
