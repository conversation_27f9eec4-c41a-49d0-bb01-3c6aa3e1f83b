"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+signature-v4-multi-region@3.299.0";
exports.ids = ["vendor-chunks/@aws-sdk+signature-v4-multi-region@3.299.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4MultiRegion: () => (/* binding */ SignatureV4MultiRegion)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_signature_v4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/signature-v4 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js\");\n\nclass SignatureV4MultiRegion {\n    constructor(options) {\n        this.sigv4Signer = new _aws_sdk_signature_v4__WEBPACK_IMPORTED_MODULE_0__.SignatureV4(options);\n        this.signerOptions = options;\n    }\n    async sign(requestToSign, options = {}) {\n        if (options.signingRegion === \"*\") {\n            if (this.signerOptions.runtime !== \"node\")\n                throw new Error(\"This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js\");\n            return this.getSigv4aSigner().sign(requestToSign, options);\n        }\n        return this.sigv4Signer.sign(requestToSign, options);\n    }\n    async presign(originalRequest, options = {}) {\n        if (options.signingRegion === \"*\") {\n            if (this.signerOptions.runtime !== \"node\")\n                throw new Error(\"This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js\");\n            return this.getSigv4aSigner().presign(originalRequest, options);\n        }\n        return this.sigv4Signer.presign(originalRequest, options);\n    }\n    getSigv4aSigner() {\n        if (!this.sigv4aSigner) {\n            let CrtSignerV4;\n            try {\n                CrtSignerV4 =  true && (__webpack_require__(/*! @aws-sdk/signature-v4-crt */ \"?3419\").CrtSignerV4);\n                if (typeof CrtSignerV4 !== \"function\")\n                    throw new Error();\n            }\n            catch (e) {\n                e.message =\n                    `${e.message}\\nPlease check if you have installed \"@aws-sdk/signature-v4-crt\" package explicitly. \\n` +\n                        \"For more information please go to \" +\n                        \"https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt\";\n                throw e;\n            }\n            this.sigv4aSigner = new CrtSignerV4({\n                ...this.signerOptions,\n                signingAlgorithm: 1,\n            });\n        }\n        return this.sigv4aSigner;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4MultiRegion: () => (/* binding */ SignatureV4MultiRegion)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_signature_v4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/signature-v4 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js\");\n\nclass SignatureV4MultiRegion {\n    constructor(options) {\n        this.sigv4Signer = new _aws_sdk_signature_v4__WEBPACK_IMPORTED_MODULE_0__.SignatureV4(options);\n        this.signerOptions = options;\n    }\n    async sign(requestToSign, options = {}) {\n        if (options.signingRegion === \"*\") {\n            if (this.signerOptions.runtime !== \"node\")\n                throw new Error(\"This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js\");\n            return this.getSigv4aSigner().sign(requestToSign, options);\n        }\n        return this.sigv4Signer.sign(requestToSign, options);\n    }\n    async presign(originalRequest, options = {}) {\n        if (options.signingRegion === \"*\") {\n            if (this.signerOptions.runtime !== \"node\")\n                throw new Error(\"This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js\");\n            return this.getSigv4aSigner().presign(originalRequest, options);\n        }\n        return this.sigv4Signer.presign(originalRequest, options);\n    }\n    getSigv4aSigner() {\n        if (!this.sigv4aSigner) {\n            let CrtSignerV4;\n            try {\n                CrtSignerV4 =  true && (__webpack_require__(/*! @aws-sdk/signature-v4-crt */ \"?3419\").CrtSignerV4);\n                if (typeof CrtSignerV4 !== \"function\")\n                    throw new Error();\n            }\n            catch (e) {\n                e.message =\n                    `${e.message}\\nPlease check if you have installed \"@aws-sdk/signature-v4-crt\" package explicitly. \\n` +\n                        \"For more information please go to \" +\n                        \"https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt\";\n                throw e;\n            }\n            this.sigv4aSigner = new CrtSignerV4({\n                ...this.signerOptions,\n                signingAlgorithm: 1,\n            });\n        }\n        return this.sigv4aSigner;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4MultiRegion */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjQtbXVsdGktcmVnaW9uQDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC1tdWx0aS1yZWdpb24vZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjQtbXVsdGktcmVnaW9uQDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC1tdWx0aS1yZWdpb24vZGlzdC1lcy9pbmRleC5qcz9lZWU2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL1NpZ25hdHVyZVY0TXVsdGlSZWdpb25cIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4MultiRegion: () => (/* reexport safe */ _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__.SignatureV4MultiRegion)\n/* harmony export */ });\n/* harmony import */ var _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4MultiRegion */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0LW11bHRpLXJlZ2lvbkAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQtbXVsdGktcmVnaW9uL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0LW11bHRpLXJlZ2lvbkAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQtbXVsdGktcmVnaW9uL2Rpc3QtZXMvaW5kZXguanM/ZGNiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9TaWduYXR1cmVWNE11bHRpUmVnaW9uXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\n");

/***/ })

};
;