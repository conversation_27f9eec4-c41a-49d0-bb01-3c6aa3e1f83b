import { getVersions } from "@/lib/actions/version.actions";
import { MoreVertical } from "lucide-react";
import moment from "moment";
import Image from "next/image";
import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle } from "./ui/sheet";
import { useVersionState } from "@/lib/states";
import { updateVersion } from "@/lib/actions/version.actions";
import { toast } from "sonner";
import { deleteVersion } from "@/lib/actions/version.actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";

export interface Version {
  name: string;
  id: number;
  timestamp: Date | string;
  versionNumber: string;
  author: {
    name: string;
    image: string;
  };
  description: string;
  config: any;
}

export interface VersionHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  workspaceId: string;
  onApplyVersion: (config: any) => void;
}

const VersionHistory = ({
  isOpen,
  onClose,
  userId,
  workspaceId,
  onApplyVersion,
}: VersionHistoryProps) => {
  const {
    versionsByWorkspace,
    refreshVersions,
    startPeriodicRefresh,
    updateVersion: updateVersionState,
    deleteVersion: deleteVersionState,
  } = useVersionState();
  const [loading, setLoading] = useState(false);
  const [renamingId, setRenamingId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");
  const [renameLoading, setRenameLoading] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteVersionName, setDeleteVersionName] = useState<string>("");

  const versions = versionsByWorkspace[workspaceId] || [];

  console.log("versions", versions);

  useEffect(() => {
    if (!isOpen) return;
    if (!versions || versions.length === 0) {
      setLoading(true);
      refreshVersions(userId, workspaceId).finally(() => setLoading(false));
    }
    startPeriodicRefresh(userId, workspaceId);
  }, [isOpen, userId, workspaceId]);

  const handleRename = async (versionId: string) => {
    setRenameLoading(true);
    try {
      const updated = await updateVersion({
        userId,
        workspaceId,
        versionId,
        name: renameValue,
      });
      if (updated) {
        updateVersionState(workspaceId, updated);
      }
      toast.success("Version renamed!");
      setRenamingId(null);
      setRenameValue("");
    } catch (err: any) {
      toast.error(err?.message || "Failed to rename version");
    } finally {
      setRenameLoading(false);
    }
  };

  const handleDelete = async (versionId: string) => {
    setDeleteLoading(true);
    try {
      await deleteVersion({ userId, workspaceId, versionId });
      deleteVersionState(workspaceId, versionId);
      toast.success("Version deleted!");
      setDeleteId(null);
      setDeleteVersionName("");
    } catch (err: any) {
      toast.error(err?.message || "Failed to delete version");
    } finally {
      setDeleteLoading(false);
    }
  };

  if (loading) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent
          side="right"
          className="w-[400px] sm:w-[540px] z-[1000] bg-gradient-to-tr from-[#18191E] to-[#2E3038] text-[#FDE9CE] overflow-y-auto"
        >
          <SheetHeader>
            <SheetTitle
              className={`font-sansitaSwashed text-2xl font-medium text-[#FDE9CE]`}
            >
              Version History
            </SheetTitle>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <p>Loading...</p>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (!versions || versions.length === 0) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent
          side="right"
          className="w-[400px] sm:w-[540px] z-[1000] bg-gradient-to-tr from-[#18191E] to-[#2E3038] text-[#FDE9CE] overflow-y-auto"
        >
          <SheetHeader>
            <SheetTitle
              className={`font-sansitaSwashed text-2xl font-medium text-[#FDE9CE]`}
            >
              Version History
            </SheetTitle>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <p>No versions found.</p>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  const currentVersion = versions[0];
  const olderVersions = versions.slice(1);

  const VersionItem = ({
    version,
    isCurrent = false,
  }: {
    version: Version;
    isCurrent?: boolean;
  }) => (
    <div
      key={version.id}
      className="p-4 border border-slate-700 z-[1000] rounded-md cursor-pointer group relative mb-5 text-sm"
    >
      <div className="flex items-center space-x-3 mb-2">
        <Avatar>
          <AvatarImage
            src={version.author.image}
            alt={`${version.author.name}'s avatar`}
          />
          <AvatarFallback className="bg-[#293038] border border-[#F5C754]">
            {version.author.name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          {renamingId === version.id.toString() ? (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleRename(version.id.toString());
              }}
              className="flex flex-col gap-2"
            >
              <input
                className="bg-zinc-800 border border-zinc-600 rounded px-2 py-1 text-white"
                value={renameValue}
                onChange={(e) => setRenameValue(e.target.value)}
                disabled={renameLoading}
                autoFocus
              />
              <div className="flex items-center gap-2">
                <button
                  type="submit"
                  className="text-xs px-2 py-1 rounded bg-primary text-white disabled:opacity-50"
                  disabled={renameLoading || !renameValue.trim()}
                >
                  {renameLoading ? "Saving..." : "Save"}
                </button>
                <button
                  type="button"
                  className="text-xs px-2 py-1 rounded bg-zinc-700 text-white"
                  onClick={() => {
                    setRenamingId(null);
                    setRenameValue("");
                  }}
                  disabled={renameLoading}
                >
                  Cancel
                </button>
              </div>
            </form>
          ) : (
            <p className="font-semibold text-sm">{version.name}</p>
          )}
          <p className="text-xs text-gray-400">{version.author.name}</p>
        </div>
      </div>
      <p>{version.description}</p>
      <p className=" mb-1">{moment(version.timestamp).format("MMMM D")}</p>
      {!isCurrent && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger>
              <MoreVertical className="h-6 w-6 text-white z-[1000] hover:text-zinc-500 cursor-pointer" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-zinc-900 z-[1000] text-white">
              <DropdownMenuItem
                onClick={() => {
                  setRenamingId(version.id.toString());
                  setRenameValue(version.name);
                }}
                className="hover:bg-zinc-800"
              >
                Rename Version
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onApplyVersion(version.config)}
                className="hover:bg-zinc-800"
              >
                Apply Version
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setDeleteId(version.id.toString());
                  setDeleteVersionName(version.name);
                }}
                className="hover:bg-red-700 text-red-400"
              >
                Delete Version
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        side="right"
        className="w-[400px] sm:w-[540px] z-[1000] bg-gradient-to-tr from-[#18191E] to-[#2E3038] text-[#FDE9CE] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle
            className={`font-sansitaSwashed text-2xl font-medium text-[#FDE9CE]`}
          >
            Version History
          </SheetTitle>
        </SheetHeader>
        <div className="px-4 py-2 h-8 w-full">
          <Image
            src="/images/—Pngtree—elegant gold line border_8053691 (1) 2.png"
            alt=""
            width={500}
            height={16}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex flex-col space-y-4 mt-4">
          <div className="mb-4">
            <h3 className="text-base uppercase font-semibold mb-2">
              Current Version
            </h3>
            <VersionItem version={currentVersion} isCurrent={true} />
          </div>
          <div>
            <h3 className="text-base uppercase font-semibold mb-2">
              Older Versions
            </h3>
            {olderVersions.map((version) => (
              <VersionItem key={version.id} version={version} />
            ))}
          </div>
        </div>
        <AlertDialog
          open={!!deleteId}
          onOpenChange={(open) => {
            if (!open) {
              setDeleteId(null);
              setDeleteVersionName("");
            }
          }}
        >
          <AlertDialogContent className="bg-gradient-to-tl from-[#32343D] to-[#14192D] border border-[#FDE9CE]/20">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-[#FDE9CE]">
                Delete Version?
              </AlertDialogTitle>
              <AlertDialogDescription className="text-[#FDE9CE]/80">
                Are you sure you want to delete version{" "}
                <b className="text-[#FDE9CE]">{deleteVersionName}</b>? This
                action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => {
                  setDeleteId(null);
                  setDeleteVersionName("");
                }}
                disabled={deleteLoading}
                className="bg-[#1A1D2E] text-[#FDE9CE] border border-[#FDE9CE]/20 hover:bg-white/10"
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => deleteId && handleDelete(deleteId)}
                disabled={deleteLoading}
                className="bg-white/10 text-[#FDE9CE] hover:bg-white/20"
              >
                {deleteLoading ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </SheetContent>
    </Sheet>
  );
};

export default VersionHistory;
