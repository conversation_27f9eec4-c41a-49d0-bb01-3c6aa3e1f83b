"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Update the config state for unsaved changes tracking\n            setDbSyncedConfig(config);\n            setWorkingConfig(config);\n            // Reset unsaved changes state since we just loaded a saved version\n            setHasUnsavedChanges(false);\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // State to track unsaved changes\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if there are unsaved changes\n    const checkUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Update hasUnsavedChanges when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsavedChanges = checkUnsavedChanges();\n        setHasUnsavedChanges(unsavedChanges);\n    }, [\n        checkUnsavedChanges\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig,\n        handleLoadScene\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n        // Reset unsaved changes state since we're resetting everything\n        setHasUnsavedChanges(false);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                    // Update the synced config to reflect the new saved state\n                    setDbSyncedConfig(sceneConfig);\n                    // Reset unsaved changes state since we just saved\n                    setHasUnsavedChanges(false);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    // Update working config when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentConfig = getComparisonConfig();\n        setWorkingConfig(currentConfig);\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri,\n        setWorkingConfig,\n        getComparisonConfig\n    ]);\n    // Add beforeunload event listener to warn about unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (event)=>{\n            if (hasUnsavedChanges) {\n                const message = \"You have unsaved changes. Are you sure you want to leave?\";\n                event.preventDefault();\n                event.returnValue = message; // For Chrome\n                return message; // For other browsers\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        hasUnsavedChanges\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                    // Set this as the baseline for unsaved changes tracking\n                    setDbSyncedConfig(currentVersion.config);\n                    // Reset unsaved changes state since we just loaded the saved version\n                    setHasUnsavedChanges(false);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1932,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction,\n                hasUnsavedChanges: hasUnsavedChanges,\n                onRevertToSaved: revertToSaved\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1933,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1948,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1956,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1951,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1980,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1992,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2009,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2018,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2039,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2097,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1975,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2125,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2170,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2169,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2208,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2232,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2244,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2270,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2279,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2273,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2288,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2318,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2312,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2330,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2167,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2141,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2346,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"FHdA2hf0sgaUOw2xWJA+o2ZJPR4=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});