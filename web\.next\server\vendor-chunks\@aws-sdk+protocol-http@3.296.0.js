"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+protocol-http@3.296.0";
exports.ids = ["vendor-chunks/@aws-sdk+protocol-http@3.296.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field)\n/* harmony export */ });\n/* harmony import */ var _FieldPosition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FieldPosition */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\");\n\nclass Field {\n    constructor({ name, kind = _FieldPosition__WEBPACK_IMPORTED_MODULE_0__.FieldPosition.HEADER, values = [] }) {\n        this.name = name;\n        this.kind = kind;\n        this.values = values;\n    }\n    add(value) {\n        this.values.push(value);\n    }\n    set(values) {\n        this.values = values;\n    }\n    remove(value) {\n        this.values = this.values.filter((v) => v !== value);\n    }\n    toString() {\n        return this.values.map((v) => (v.includes(\",\") || v.includes(\" \") ? `\"${v}\"` : v)).join(\", \");\n    }\n    get() {\n        return this.values;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUN6QztBQUNQLGtCQUFrQixhQUFhLHlEQUFhLHNCQUFzQjtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0ZBQWdGLEVBQUU7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZC5qcz83NThjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZpZWxkUG9zaXRpb24gfSBmcm9tIFwiLi9GaWVsZFBvc2l0aW9uXCI7XG5leHBvcnQgY2xhc3MgRmllbGQge1xuICAgIGNvbnN0cnVjdG9yKHsgbmFtZSwga2luZCA9IEZpZWxkUG9zaXRpb24uSEVBREVSLCB2YWx1ZXMgPSBbXSB9KSB7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMua2luZCA9IGtpbmQ7XG4gICAgICAgIHRoaXMudmFsdWVzID0gdmFsdWVzO1xuICAgIH1cbiAgICBhZGQodmFsdWUpIHtcbiAgICAgICAgdGhpcy52YWx1ZXMucHVzaCh2YWx1ZSk7XG4gICAgfVxuICAgIHNldCh2YWx1ZXMpIHtcbiAgICAgICAgdGhpcy52YWx1ZXMgPSB2YWx1ZXM7XG4gICAgfVxuICAgIHJlbW92ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlcyA9IHRoaXMudmFsdWVzLmZpbHRlcigodikgPT4gdiAhPT0gdmFsdWUpO1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVzLm1hcCgodikgPT4gKHYuaW5jbHVkZXMoXCIsXCIpIHx8IHYuaW5jbHVkZXMoXCIgXCIpID8gYFwiJHt2fVwiYCA6IHYpKS5qb2luKFwiLCBcIik7XG4gICAgfVxuICAgIGdldCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVzO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field)\n/* harmony export */ });\n/* harmony import */ var _FieldPosition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FieldPosition */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\");\n\nclass Field {\n    constructor({ name, kind = _FieldPosition__WEBPACK_IMPORTED_MODULE_0__.FieldPosition.HEADER, values = [] }) {\n        this.name = name;\n        this.kind = kind;\n        this.values = values;\n    }\n    add(value) {\n        this.values.push(value);\n    }\n    set(values) {\n        this.values = values;\n    }\n    remove(value) {\n        this.values = this.values.filter((v) => v !== value);\n    }\n    toString() {\n        return this.values.map((v) => (v.includes(\",\") || v.includes(\" \") ? `\"${v}\"` : v)).join(\", \");\n    }\n    get() {\n        return this.values;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvRmllbGQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDekM7QUFDUCxrQkFBa0IsYUFBYSx5REFBYSxzQkFBc0I7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdGQUFnRixFQUFFO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvRmllbGQuanM/MjQwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGaWVsZFBvc2l0aW9uIH0gZnJvbSBcIi4vRmllbGRQb3NpdGlvblwiO1xuZXhwb3J0IGNsYXNzIEZpZWxkIHtcbiAgICBjb25zdHJ1Y3Rvcih7IG5hbWUsIGtpbmQgPSBGaWVsZFBvc2l0aW9uLkhFQURFUiwgdmFsdWVzID0gW10gfSkge1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLmtpbmQgPSBraW5kO1xuICAgICAgICB0aGlzLnZhbHVlcyA9IHZhbHVlcztcbiAgICB9XG4gICAgYWRkKHZhbHVlKSB7XG4gICAgICAgIHRoaXMudmFsdWVzLnB1c2godmFsdWUpO1xuICAgIH1cbiAgICBzZXQodmFsdWVzKSB7XG4gICAgICAgIHRoaXMudmFsdWVzID0gdmFsdWVzO1xuICAgIH1cbiAgICByZW1vdmUodmFsdWUpIHtcbiAgICAgICAgdGhpcy52YWx1ZXMgPSB0aGlzLnZhbHVlcy5maWx0ZXIoKHYpID0+IHYgIT09IHZhbHVlKTtcbiAgICB9XG4gICAgdG9TdHJpbmcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZhbHVlcy5tYXAoKHYpID0+ICh2LmluY2x1ZGVzKFwiLFwiKSB8fCB2LmluY2x1ZGVzKFwiIFwiKSA/IGBcIiR7dn1cImAgOiB2KSkuam9pbihcIiwgXCIpO1xuICAgIH1cbiAgICBnZXQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZhbHVlcztcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FieldPosition: () => (/* binding */ FieldPosition)\n/* harmony export */ });\nvar FieldPosition;\n(function (FieldPosition) {\n    FieldPosition[FieldPosition[\"HEADER\"] = 0] = \"HEADER\";\n    FieldPosition[FieldPosition[\"TRAILER\"] = 1] = \"TRAILER\";\n})(FieldPosition || (FieldPosition = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZFBvc2l0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsc0NBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Byb3RvY29sLWh0dHBAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL0ZpZWxkUG9zaXRpb24uanM/NDE1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIEZpZWxkUG9zaXRpb247XG4oZnVuY3Rpb24gKEZpZWxkUG9zaXRpb24pIHtcbiAgICBGaWVsZFBvc2l0aW9uW0ZpZWxkUG9zaXRpb25bXCJIRUFERVJcIl0gPSAwXSA9IFwiSEVBREVSXCI7XG4gICAgRmllbGRQb3NpdGlvbltGaWVsZFBvc2l0aW9uW1wiVFJBSUxFUlwiXSA9IDFdID0gXCJUUkFJTEVSXCI7XG59KShGaWVsZFBvc2l0aW9uIHx8IChGaWVsZFBvc2l0aW9uID0ge30pKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FieldPosition: () => (/* binding */ FieldPosition)\n/* harmony export */ });\nvar FieldPosition;\n(function (FieldPosition) {\n    FieldPosition[FieldPosition[\"HEADER\"] = 0] = \"HEADER\";\n    FieldPosition[FieldPosition[\"TRAILER\"] = 1] = \"TRAILER\";\n})(FieldPosition || (FieldPosition = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvRmllbGRQb3NpdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZFBvc2l0aW9uLmpzPzZmM2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBGaWVsZFBvc2l0aW9uO1xuKGZ1bmN0aW9uIChGaWVsZFBvc2l0aW9uKSB7XG4gICAgRmllbGRQb3NpdGlvbltGaWVsZFBvc2l0aW9uW1wiSEVBREVSXCJdID0gMF0gPSBcIkhFQURFUlwiO1xuICAgIEZpZWxkUG9zaXRpb25bRmllbGRQb3NpdGlvbltcIlRSQUlMRVJcIl0gPSAxXSA9IFwiVFJBSUxFUlwiO1xufSkoRmllbGRQb3NpdGlvbiB8fCAoRmllbGRQb3NpdGlvbiA9IHt9KSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fields: () => (/* binding */ Fields)\n/* harmony export */ });\nclass Fields {\n    constructor({ fields = [], encoding = \"utf-8\" }) {\n        this.entries = {};\n        fields.forEach(this.setField.bind(this));\n        this.encoding = encoding;\n    }\n    setField(field) {\n        this.entries[field.name.toLowerCase()] = field;\n    }\n    getField(name) {\n        return this.entries[name.toLowerCase()];\n    }\n    removeField(name) {\n        delete this.entries[name.toLowerCase()];\n    }\n    getByType(kind) {\n        return Object.values(this.entries).filter((field) => field.kind === kind);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1Asa0JBQWtCLGlDQUFpQztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Byb3RvY29sLWh0dHBAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL0ZpZWxkcy5qcz9hMjMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBGaWVsZHMge1xuICAgIGNvbnN0cnVjdG9yKHsgZmllbGRzID0gW10sIGVuY29kaW5nID0gXCJ1dGYtOFwiIH0pIHtcbiAgICAgICAgdGhpcy5lbnRyaWVzID0ge307XG4gICAgICAgIGZpZWxkcy5mb3JFYWNoKHRoaXMuc2V0RmllbGQuYmluZCh0aGlzKSk7XG4gICAgICAgIHRoaXMuZW5jb2RpbmcgPSBlbmNvZGluZztcbiAgICB9XG4gICAgc2V0RmllbGQoZmllbGQpIHtcbiAgICAgICAgdGhpcy5lbnRyaWVzW2ZpZWxkLm5hbWUudG9Mb3dlckNhc2UoKV0gPSBmaWVsZDtcbiAgICB9XG4gICAgZ2V0RmllbGQobmFtZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5lbnRyaWVzW25hbWUudG9Mb3dlckNhc2UoKV07XG4gICAgfVxuICAgIHJlbW92ZUZpZWxkKG5hbWUpIHtcbiAgICAgICAgZGVsZXRlIHRoaXMuZW50cmllc1tuYW1lLnRvTG93ZXJDYXNlKCldO1xuICAgIH1cbiAgICBnZXRCeVR5cGUoa2luZCkge1xuICAgICAgICByZXR1cm4gT2JqZWN0LnZhbHVlcyh0aGlzLmVudHJpZXMpLmZpbHRlcigoZmllbGQpID0+IGZpZWxkLmtpbmQgPT09IGtpbmQpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fields: () => (/* binding */ Fields)\n/* harmony export */ });\nclass Fields {\n    constructor({ fields = [], encoding = \"utf-8\" }) {\n        this.entries = {};\n        fields.forEach(this.setField.bind(this));\n        this.encoding = encoding;\n    }\n    setField(field) {\n        this.entries[field.name.toLowerCase()] = field;\n    }\n    getField(name) {\n        return this.entries[name.toLowerCase()];\n    }\n    removeField(name) {\n        delete this.entries[name.toLowerCase()];\n    }\n    getByType(kind) {\n        return Object.values(this.entries).filter((field) => field.kind === kind);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvRmllbGRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLGtCQUFrQixpQ0FBaUM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZHMuanM/MDljNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRmllbGRzIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGZpZWxkcyA9IFtdLCBlbmNvZGluZyA9IFwidXRmLThcIiB9KSB7XG4gICAgICAgIHRoaXMuZW50cmllcyA9IHt9O1xuICAgICAgICBmaWVsZHMuZm9yRWFjaCh0aGlzLnNldEZpZWxkLmJpbmQodGhpcykpO1xuICAgICAgICB0aGlzLmVuY29kaW5nID0gZW5jb2Rpbmc7XG4gICAgfVxuICAgIHNldEZpZWxkKGZpZWxkKSB7XG4gICAgICAgIHRoaXMuZW50cmllc1tmaWVsZC5uYW1lLnRvTG93ZXJDYXNlKCldID0gZmllbGQ7XG4gICAgfVxuICAgIGdldEZpZWxkKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZW50cmllc1tuYW1lLnRvTG93ZXJDYXNlKCldO1xuICAgIH1cbiAgICByZW1vdmVGaWVsZChuYW1lKSB7XG4gICAgICAgIGRlbGV0ZSB0aGlzLmVudHJpZXNbbmFtZS50b0xvd2VyQ2FzZSgpXTtcbiAgICB9XG4gICAgZ2V0QnlUeXBlKGtpbmQpIHtcbiAgICAgICAgcmV0dXJuIE9iamVjdC52YWx1ZXModGhpcy5lbnRyaWVzKS5maWx0ZXIoKGZpZWxkKSA9PiBmaWVsZC5raW5kID09PSBraW5kKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9odHRwSGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaHR0cEhhbmRsZXIuanM/NWIzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaHR0cEhhbmRsZXIuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Byb3RvY29sLWh0dHBAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2h0dHBIYW5kbGVyLmpzP2M5NTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpRequest: () => (/* binding */ HttpRequest)\n/* harmony export */ });\nclass HttpRequest {\n    constructor(options) {\n        this.method = options.method || \"GET\";\n        this.hostname = options.hostname || \"localhost\";\n        this.port = options.port;\n        this.query = options.query || {};\n        this.headers = options.headers || {};\n        this.body = options.body;\n        this.protocol = options.protocol\n            ? options.protocol.slice(-1) !== \":\"\n                ? `${options.protocol}:`\n                : options.protocol\n            : \"https:\";\n        this.path = options.path ? (options.path.charAt(0) !== \"/\" ? `/${options.path}` : options.path) : \"/\";\n    }\n    static isInstance(request) {\n        if (!request)\n            return false;\n        const req = request;\n        return (\"method\" in req &&\n            \"protocol\" in req &&\n            \"hostname\" in req &&\n            \"path\" in req &&\n            typeof req[\"query\"] === \"object\" &&\n            typeof req[\"headers\"] === \"object\");\n    }\n    clone() {\n        const cloned = new HttpRequest({\n            ...this,\n            headers: { ...this.headers },\n        });\n        if (cloned.query)\n            cloned.query = cloneQuery(cloned.query);\n        return cloned;\n    }\n}\nfunction cloneQuery(query) {\n    return Object.keys(query).reduce((carry, paramName) => {\n        const param = query[paramName];\n        return {\n            ...carry,\n            [paramName]: Array.isArray(param) ? [...param] : param,\n        };\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpRequest: () => (/* binding */ HttpRequest)\n/* harmony export */ });\nclass HttpRequest {\n    constructor(options) {\n        this.method = options.method || \"GET\";\n        this.hostname = options.hostname || \"localhost\";\n        this.port = options.port;\n        this.query = options.query || {};\n        this.headers = options.headers || {};\n        this.body = options.body;\n        this.protocol = options.protocol\n            ? options.protocol.slice(-1) !== \":\"\n                ? `${options.protocol}:`\n                : options.protocol\n            : \"https:\";\n        this.path = options.path ? (options.path.charAt(0) !== \"/\" ? `/${options.path}` : options.path) : \"/\";\n    }\n    static isInstance(request) {\n        if (!request)\n            return false;\n        const req = request;\n        return (\"method\" in req &&\n            \"protocol\" in req &&\n            \"hostname\" in req &&\n            \"path\" in req &&\n            typeof req[\"query\"] === \"object\" &&\n            typeof req[\"headers\"] === \"object\");\n    }\n    clone() {\n        const cloned = new HttpRequest({\n            ...this,\n            headers: { ...this.headers },\n        });\n        if (cloned.query)\n            cloned.query = cloneQuery(cloned.query);\n        return cloned;\n    }\n}\nfunction cloneQuery(query) {\n    return Object.keys(query).reduce((carry, paramName) => {\n        const param = query[paramName];\n        return {\n            ...carry,\n            [paramName]: Array.isArray(param) ? [...param] : param,\n        };\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpResponse: () => (/* binding */ HttpResponse)\n/* harmony export */ });\nclass HttpResponse {\n    constructor(options) {\n        this.statusCode = options.statusCode;\n        this.headers = options.headers || {};\n        this.body = options.body;\n    }\n    static isInstance(response) {\n        if (!response)\n            return false;\n        const resp = response;\n        return typeof resp.statusCode === \"number\" && typeof resp.headers === \"object\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9odHRwUmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Byb3RvY29sLWh0dHBAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2h0dHBSZXNwb25zZS5qcz80YjIzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBIdHRwUmVzcG9uc2Uge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5zdGF0dXNDb2RlID0gb3B0aW9ucy5zdGF0dXNDb2RlO1xuICAgICAgICB0aGlzLmhlYWRlcnMgPSBvcHRpb25zLmhlYWRlcnMgfHwge307XG4gICAgICAgIHRoaXMuYm9keSA9IG9wdGlvbnMuYm9keTtcbiAgICB9XG4gICAgc3RhdGljIGlzSW5zdGFuY2UocmVzcG9uc2UpIHtcbiAgICAgICAgaWYgKCFyZXNwb25zZSlcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgY29uc3QgcmVzcCA9IHJlc3BvbnNlO1xuICAgICAgICByZXR1cm4gdHlwZW9mIHJlc3Auc3RhdHVzQ29kZSA9PT0gXCJudW1iZXJcIiAmJiB0eXBlb2YgcmVzcC5oZWFkZXJzID09PSBcIm9iamVjdFwiO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpResponse: () => (/* binding */ HttpResponse)\n/* harmony export */ });\nclass HttpResponse {\n    constructor(options) {\n        this.statusCode = options.statusCode;\n        this.headers = options.headers || {};\n        this.body = options.body;\n    }\n    static isInstance(response) {\n        if (!response)\n            return false;\n        const resp = response;\n        return typeof resp.statusCode === \"number\" && typeof resp.headers === \"object\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaHR0cFJlc3BvbnNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9odHRwUmVzcG9uc2UuanM/MGU1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgSHR0cFJlc3BvbnNlIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHRoaXMuc3RhdHVzQ29kZSA9IG9wdGlvbnMuc3RhdHVzQ29kZTtcbiAgICAgICAgdGhpcy5oZWFkZXJzID0gb3B0aW9ucy5oZWFkZXJzIHx8IHt9O1xuICAgICAgICB0aGlzLmJvZHkgPSBvcHRpb25zLmJvZHk7XG4gICAgfVxuICAgIHN0YXRpYyBpc0luc3RhbmNlKHJlc3BvbnNlKSB7XG4gICAgICAgIGlmICghcmVzcG9uc2UpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHJlc3AgPSByZXNwb25zZTtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiByZXNwLnN0YXR1c0NvZGUgPT09IFwibnVtYmVyXCIgJiYgdHlwZW9mIHJlc3AuaGVhZGVycyA9PT0gXCJvYmplY3RcIjtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Field */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Field__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Field__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _FieldPosition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FieldPosition */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _FieldPosition__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _FieldPosition__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _Fields__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Fields */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Fields__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Fields__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _httpHandler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./httpHandler */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _httpHandler__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _httpHandler__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _httpRequest__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./httpRequest */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _httpRequest__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _httpRequest__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _httpResponse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./httpResponse */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _httpResponse__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _httpResponse__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _isValidHostname__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isValidHostname */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _isValidHostname__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _isValidHostname__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QjtBQUNRO0FBQ1A7QUFDSztBQUNBO0FBQ0M7QUFDRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pbmRleC5qcz8xMmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0ZpZWxkXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9GaWVsZFBvc2l0aW9uXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9GaWVsZHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2h0dHBIYW5kbGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9odHRwUmVxdWVzdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vaHR0cFJlc3BvbnNlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pc1ZhbGlkSG9zdG5hbWVcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* reexport safe */ _Field__WEBPACK_IMPORTED_MODULE_0__.Field),\n/* harmony export */   FieldPosition: () => (/* reexport safe */ _FieldPosition__WEBPACK_IMPORTED_MODULE_1__.FieldPosition),\n/* harmony export */   Fields: () => (/* reexport safe */ _Fields__WEBPACK_IMPORTED_MODULE_2__.Fields),\n/* harmony export */   HttpRequest: () => (/* reexport safe */ _httpRequest__WEBPACK_IMPORTED_MODULE_4__.HttpRequest),\n/* harmony export */   HttpResponse: () => (/* reexport safe */ _httpResponse__WEBPACK_IMPORTED_MODULE_5__.HttpResponse),\n/* harmony export */   isValidHostname: () => (/* reexport safe */ _isValidHostname__WEBPACK_IMPORTED_MODULE_6__.isValidHostname)\n/* harmony export */ });\n/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Field */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Field.js\");\n/* harmony import */ var _FieldPosition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FieldPosition */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/FieldPosition.js\");\n/* harmony import */ var _Fields__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Fields */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/Fields.js\");\n/* harmony import */ var _httpHandler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./httpHandler */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpHandler.js\");\n/* harmony import */ var _httpRequest__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./httpRequest */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpRequest.js\");\n/* harmony import */ var _httpResponse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./httpResponse */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/httpResponse.js\");\n/* harmony import */ var _isValidHostname__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isValidHostname */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QjtBQUNRO0FBQ1A7QUFDSztBQUNBO0FBQ0M7QUFDRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pbmRleC5qcz80MmE3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0ZpZWxkXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9GaWVsZFBvc2l0aW9uXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9GaWVsZHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2h0dHBIYW5kbGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9odHRwUmVxdWVzdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vaHR0cFJlc3BvbnNlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pc1ZhbGlkSG9zdG5hbWVcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidHostname: () => (/* binding */ isValidHostname)\n/* harmony export */ });\nfunction isValidHostname(hostname) {\n    const hostPattern = /^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$/;\n    return hostPattern.test(hostname);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pc1ZhbGlkSG9zdG5hbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Byb3RvY29sLWh0dHBAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2lzVmFsaWRIb3N0bmFtZS5qcz9hNzAxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkSG9zdG5hbWUoaG9zdG5hbWUpIHtcbiAgICBjb25zdCBob3N0UGF0dGVybiA9IC9eW2EtejAtOV1bYS16MC05XFwuXFwtXSpbYS16MC05XSQvO1xuICAgIHJldHVybiBob3N0UGF0dGVybi50ZXN0KGhvc3RuYW1lKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidHostname: () => (/* binding */ isValidHostname)\n/* harmony export */ });\nfunction isValidHostname(hostname) {\n    const hostPattern = /^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$/;\n    return hostPattern.test(hostname);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcHJvdG9jb2wtaHR0cEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaXNWYWxpZEhvc3RuYW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytwcm90b2NvbC1odHRwQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pc1ZhbGlkSG9zdG5hbWUuanM/NDQ0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNWYWxpZEhvc3RuYW1lKGhvc3RuYW1lKSB7XG4gICAgY29uc3QgaG9zdFBhdHRlcm4gPSAvXlthLXowLTldW2EtejAtOVxcLlxcLV0qW2EtejAtOV0kLztcbiAgICByZXR1cm4gaG9zdFBhdHRlcm4udGVzdChob3N0bmFtZSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/isValidHostname.js\n");

/***/ })

};
;