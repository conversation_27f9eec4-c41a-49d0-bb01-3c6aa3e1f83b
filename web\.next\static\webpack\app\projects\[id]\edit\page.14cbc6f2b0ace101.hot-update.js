"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Header.jsx":
/*!**********************************************!*\
  !*** ./src/components/ArtistTool/Header.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SaveSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/SaveSceneModal.jsx\");\n/* harmony import */ var _LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LoadSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/LoadSceneModal.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _VersionHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../VersionHistory */ \"(app-pages-browser)/./src/components/VersionHistory.tsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onReset, onScreenshot, onSaveScene, onLoadScene, isFullscreen, onToggleFullscreen, user, project, onUndo, onRedo, hasUnsavedChanges, onRevertToSaved } = param;\n    _s();\n    const [showSaveModal, setShowSaveModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showLoadModal, setShowLoadModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showVersionHistory, setShowVersionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                id: \"header\",\n                className: \"fixed w-full z-50 px-4 py-2 transition-all duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full px-6 py-1 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-auto h-10 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: \"/projects/\".concat(project._id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"size-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/CSS Logo.png\",\n                                        alt: \"Logo\",\n                                        width: 1024,\n                                        height: 780,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                        icon: isFullscreen ? _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        tooltip: isFullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\",\n                                        onClick: onToggleFullscreen\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-white/20 mx-1 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\")\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                tooltip: \"Undo (Ctrl+Z)\",\n                                                onClick: onUndo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                tooltip: \"Redo (Ctrl+Y)\",\n                                                onClick: onRedo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                tooltip: \"Reset Scene\",\n                                                onClick: onReset\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                tooltip: \"Screenshot\",\n                                                onClick: onScreenshot\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                tooltip: \"Version History\",\n                                                onClick: ()=>setShowVersionHistory(true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowLoadModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Load Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowSaveModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 154,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Save Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__.SaveSceneModal, {\n                isOpen: showSaveModal,\n                onClose: ()=>setShowSaveModal(false),\n                onSave: (sceneName, description, saveType)=>{\n                    onSaveScene(sceneName, description, saveType);\n                    setShowSaveModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__.LoadSceneModal, {\n                isOpen: showLoadModal,\n                onClose: ()=>setShowLoadModal(false),\n                onLoad: (config)=>{\n                    onLoadScene(config);\n                    setShowLoadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VersionHistory__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showVersionHistory,\n                onClose: ()=>setShowVersionHistory(false),\n                userId: user.id,\n                workspaceId: project._id,\n                onApplyVersion: (config)=>{\n                    onLoadScene(config);\n                    setShowVersionHistory(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"BpQ93XwKg8UzXTTc2Gl4xwXptgA=\");\n_c = Header;\nfunction HeaderButton(param) {\n    let { icon: Icon, tooltip, primary = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClick,\n                    className: \"p-1.5 rounded-full \".concat(primary ? \"bg-primary text-white hover:bg-primary-600\" : \"text-white hover:bg-white/20\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: tooltip\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeaderButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"HeaderButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvSGVhZGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFnQnNCO0FBQ1M7QUFNUjtBQUM0QztBQUN2QjtBQUNNO0FBQ0E7QUFDckI7QUFDZTtBQUNHO0FBQ2E7QUFFN0MsU0FBUytCLE9BQU8sS0FhOUI7UUFiOEIsRUFDN0JDLE9BQU8sRUFDUEMsWUFBWSxFQUNaQyxXQUFXLEVBQ1hDLFdBQVcsRUFDWEMsWUFBWSxFQUNaQyxrQkFBa0IsRUFDbEJDLElBQUksRUFDSkMsT0FBTyxFQUNQQyxNQUFNLEVBQ05DLE1BQU0sRUFDTkMsaUJBQWlCLEVBQ2pCQyxlQUFlLEVBQ2hCLEdBYjhCOztJQWM3QixNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHdEIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDdUIsZUFBZUMsaUJBQWlCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN5QixvQkFBb0JDLHNCQUFzQixHQUFHMUIsK0NBQVFBLENBQUM7SUFFN0QscUJBQ0U7OzBCQUNFLDhEQUFDMkI7Z0JBQ0NDLElBQUc7Z0JBQ0hDLFdBQVU7MEJBRVYsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQ0NELFdBQVcsMkNBRVYsT0FEQ2hCLGVBQWUsY0FBYztzQ0FHL0IsNEVBQUNULGlEQUFJQTtnQ0FBQzJCLE1BQU0sYUFBeUIsT0FBWmYsUUFBUWdCLEdBQUc7MENBQ2xDLDRFQUFDRjtvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ3JDLGtEQUFLQTt3Q0FDSnlDLEtBQUk7d0NBQ0pDLEtBQUk7d0NBQ0pDLE9BQU87d0NBQ1BDLFFBQVE7d0NBQ1JQLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLbEIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2xDLHdEQUFlQTs4Q0FDZCw0RUFBQzBDO3dDQUNDQyxNQUFNekIsZUFBZTVCLHVNQUFTQSxHQUFHRCx1TUFBU0E7d0NBQzFDdUQsU0FBUzFCLGVBQWUsb0JBQW9CO3dDQUM1QzJCLFNBQVMxQjs7Ozs7Ozs7Ozs7OENBR2IsOERBQUNnQjtvQ0FDQ0QsV0FBVyx5REFFVixPQURDaEIsZUFBZSxjQUFjOzs7Ozs7OENBR2pDLDhEQUFDaUI7b0NBQ0NELFdBQVcsdURBRVYsT0FEQ2hCLGVBQWUsY0FBYzs7c0RBRy9CLDhEQUFDbEIsd0RBQWVBO3NEQUNkLDRFQUFDMEM7Z0RBQ0NDLE1BQU1sRCx1TUFBSUE7Z0RBQ1ZtRCxTQUFRO2dEQUNSQyxTQUFTdkI7Ozs7Ozs7Ozs7O3NEQUdiLDhEQUFDdEIsd0RBQWVBO3NEQUNkLDRFQUFDMEM7Z0RBQ0NDLE1BQU1qRCx1TUFBSUE7Z0RBQ1ZrRCxTQUFRO2dEQUNSQyxTQUFTdEI7Ozs7Ozs7Ozs7O3NEQUdiLDhEQUFDWTs0Q0FBSUQsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDbEMsd0RBQWVBO3NEQUNkLDRFQUFDMEM7Z0RBQ0NDLE1BQU16RCx1TUFBU0E7Z0RBQ2YwRCxTQUFRO2dEQUNSQyxTQUFTL0I7Ozs7Ozs7Ozs7O3NEQUdiLDhEQUFDZCx3REFBZUE7c0RBQ2QsNEVBQUMwQztnREFDQ0MsTUFBTXhELHVNQUFNQTtnREFDWnlELFNBQVE7Z0RBQ1JDLFNBQVM5Qjs7Ozs7Ozs7Ozs7c0RBR2IsOERBQUNmLHdEQUFlQTtzREFDZCw0RUFBQzBDO2dEQUNDQyxNQUFNcEQsdU1BQU9BO2dEQUNicUQsU0FBUTtnREFDUkMsU0FBUyxJQUFNZCxzQkFBc0I7Ozs7Ozs7Ozs7O3NEQUd6Qyw4REFBQ0k7NENBQUlELFdBQVU7Ozs7OztzREFDZiw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDbEMsd0RBQWVBOzhEQUNkLDRFQUFDRixnREFBT0E7OzBFQUNOLDhEQUFDRyx1REFBY0E7Z0VBQUM2QyxPQUFPOzBFQUNyQiw0RUFBQ0M7b0VBQ0NGLFNBQVMsSUFBTWhCLGlCQUFpQjtvRUFDaENLLFdBQVU7OEVBRVYsNEVBQUM5Qyx1TUFBTUE7d0VBQUM4QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBFQUd0Qiw4REFBQ25DLHVEQUFjQTswRUFDYiw0RUFBQ2lEOzhFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUlULDhEQUFDaEQsd0RBQWVBOzhEQUNkLDRFQUFDRixnREFBT0E7OzBFQUNOLDhEQUFDRyx1REFBY0E7Z0VBQUM2QyxPQUFPOzBFQUNyQiw0RUFBQ0M7b0VBQ0NGLFNBQVMsSUFBTWxCLGlCQUFpQjtvRUFDaENPLFdBQVU7OEVBRVYsNEVBQUNwRCx1TUFBV0E7d0VBQUNvRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBFQUczQiw4REFBQ25DLHVEQUFjQTswRUFDYiw0RUFBQ2lEOzhFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVW5CLDhEQUFDekMsMkRBQWNBO2dCQUNiMEMsUUFBUXZCO2dCQUNSd0IsU0FBUyxJQUFNdkIsaUJBQWlCO2dCQUNoQ3dCLFFBQVEsQ0FBQ0MsV0FBV0MsYUFBYUM7b0JBQy9CdEMsWUFBWW9DLFdBQVdDLGFBQWFDO29CQUNwQzNCLGlCQUFpQjtnQkFDbkI7Ozs7OzswQkFHRiw4REFBQ25CLDJEQUFjQTtnQkFDYnlDLFFBQVFyQjtnQkFDUnNCLFNBQVMsSUFBTXJCLGlCQUFpQjtnQkFDaEMwQixRQUFRLENBQUNDO29CQUNQdkMsWUFBWXVDO29CQUNaM0IsaUJBQWlCO2dCQUNuQjs7Ozs7OzBCQUdGLDhEQUFDbEIsdURBQWNBO2dCQUNic0MsUUFBUW5CO2dCQUNSb0IsU0FBUyxJQUFNbkIsc0JBQXNCO2dCQUNyQzBCLFFBQVFyQyxLQUFLYSxFQUFFO2dCQUNmeUIsYUFBYXJDLFFBQVFnQixHQUFHO2dCQUN4QnNCLGdCQUFnQixDQUFDSDtvQkFDZnZDLFlBQVl1QztvQkFDWnpCLHNCQUFzQjtnQkFDeEI7Ozs7Ozs7O0FBSVI7R0FwS3dCbEI7S0FBQUE7QUFzS3hCLFNBQVM2QixhQUFhLEtBQWlEO1FBQWpELEVBQUVDLE1BQU1pQixJQUFJLEVBQUVoQixPQUFPLEVBQUVpQixVQUFVLEtBQUssRUFBRWhCLE9BQU8sRUFBRSxHQUFqRDtJQUNwQixxQkFDRSw4REFBQy9DLGdEQUFPQTs7MEJBQ04sOERBQUNHLHVEQUFjQTtnQkFBQzZDLE9BQU87MEJBQ3JCLDRFQUFDQztvQkFDQ0YsU0FBU0E7b0JBQ1RYLFdBQVcsc0JBSVYsT0FIQzJCLFVBQ0ksK0NBQ0E7OEJBR04sNEVBQUNEO3dCQUFLMUIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHcEIsOERBQUNuQyx1REFBY0E7MEJBQ2IsNEVBQUNpRDs4QkFBR0o7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSVo7TUFwQlNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvSGVhZGVyLmpzeD85NDFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgQ2xvdWRVcGxvYWQsXHJcbiAgU2hhcmUsXHJcbiAgU3RvcmUsXHJcbiAgQmVsbCxcclxuICBSb3RhdGVDY3csXHJcbiAgQ2FtZXJhLFxyXG4gIFVwbG9hZCxcclxuICBNYXhpbWl6ZTIsXHJcbiAgTWluaW1pemUyLFxyXG4gIEhpc3RvcnksXHJcbiAgUm90YXRlQ3csXHJcbiAgVW5kbyxcclxuICBSZWRvLFxyXG4gIEFsZXJ0Q2lyY2xlLFxyXG4gIFJvdGF0ZUxlZnQsXHJcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuaW1wb3J0IHtcclxuICBUb29sdGlwLFxyXG4gIFRvb2x0aXBDb250ZW50LFxyXG4gIFRvb2x0aXBQcm92aWRlcixcclxuICBUb29sdGlwVHJpZ2dlcixcclxufSBmcm9tIFwiLi4vdWkvdG9vbHRpcFwiO1xyXG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckltYWdlLCBBdmF0YXJGYWxsYmFjayB9IGZyb20gXCIuLi91aS9hdmF0YXJcIjtcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBTYXZlU2NlbmVNb2RhbCB9IGZyb20gXCIuL1NhdmVTY2VuZU1vZGFsXCI7XHJcbmltcG9ydCB7IExvYWRTY2VuZU1vZGFsIH0gZnJvbSBcIi4vTG9hZFNjZW5lTW9kYWxcIjtcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCBWZXJzaW9uSGlzdG9yeSBmcm9tIFwiLi4vVmVyc2lvbkhpc3RvcnlcIjtcclxuaW1wb3J0IHsgZ2V0VmVyc2lvbnMgfSBmcm9tIFwiQC9saWIvYWN0aW9ucy92ZXJzaW9uLmFjdGlvbnNcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWRlcih7XHJcbiAgb25SZXNldCxcclxuICBvblNjcmVlbnNob3QsXHJcbiAgb25TYXZlU2NlbmUsXHJcbiAgb25Mb2FkU2NlbmUsXHJcbiAgaXNGdWxsc2NyZWVuLFxyXG4gIG9uVG9nZ2xlRnVsbHNjcmVlbixcclxuICB1c2VyLFxyXG4gIHByb2plY3QsXHJcbiAgb25VbmRvLFxyXG4gIG9uUmVkbyxcclxuICBoYXNVbnNhdmVkQ2hhbmdlcyxcclxuICBvblJldmVydFRvU2F2ZWQsXHJcbn0pIHtcclxuICBjb25zdCBbc2hvd1NhdmVNb2RhbCwgc2V0U2hvd1NhdmVNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dMb2FkTW9kYWwsIHNldFNob3dMb2FkTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzaG93VmVyc2lvbkhpc3RvcnksIHNldFNob3dWZXJzaW9uSGlzdG9yeV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8aGVhZGVyXHJcbiAgICAgICAgaWQ9XCJoZWFkZXJcIlxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIHctZnVsbCB6LTUwIHB4LTQgcHktMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgcHgtNiBweS0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWF1dG8gaC0xMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcclxuICAgICAgICAgICAgICBpc0Z1bGxzY3JlZW4gPyBcIm9wYWNpdHktMFwiIDogXCJvcGFjaXR5LTEwMFwiXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8TGluayBocmVmPXtgL3Byb2plY3RzLyR7cHJvamVjdC5faWR9YH0+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzaXplLTEyXCI+XHJcbiAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9DU1MgTG9nby5wbmdcIlxyXG4gICAgICAgICAgICAgICAgICBhbHQ9XCJMb2dvXCJcclxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezEwMjR9XHJcbiAgICAgICAgICAgICAgICAgIGhlaWdodD17NzgwfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb250YWluXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8VG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIDxIZWFkZXJCdXR0b25cclxuICAgICAgICAgICAgICAgIGljb249e2lzRnVsbHNjcmVlbiA/IE1pbmltaXplMiA6IE1heGltaXplMn1cclxuICAgICAgICAgICAgICAgIHRvb2x0aXA9e2lzRnVsbHNjcmVlbiA/IFwiRXhpdCBGdWxsc2NyZWVuXCIgOiBcIkVudGVyIEZ1bGxzY3JlZW5cIn1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlRnVsbHNjcmVlbn1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L1Rvb2x0aXBQcm92aWRlcj5cclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtNiB3LXB4IGJnLXdoaXRlLzIwIG14LTEgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgICAgICAgICBpc0Z1bGxzY3JlZW4gPyBcIm9wYWNpdHktMFwiIDogXCJvcGFjaXR5LTEwMFwiXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcclxuICAgICAgICAgICAgICAgIGlzRnVsbHNjcmVlbiA/IFwib3BhY2l0eS0wXCIgOiBcIm9wYWNpdHktMTAwXCJcclxuICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxUb29sdGlwUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgICA8SGVhZGVyQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGljb249e1VuZG99XHJcbiAgICAgICAgICAgICAgICAgIHRvb2x0aXA9XCJVbmRvIChDdHJsK1opXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25VbmRvfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L1Rvb2x0aXBQcm92aWRlcj5cclxuICAgICAgICAgICAgICA8VG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgICAgPEhlYWRlckJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBpY29uPXtSZWRvfVxyXG4gICAgICAgICAgICAgICAgICB0b29sdGlwPVwiUmVkbyAoQ3RybCtZKVwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVkb31cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9Ub29sdGlwUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgdy1weCBiZy13aGl0ZS8yMCBteC0xXCIgLz5cclxuICAgICAgICAgICAgICA8VG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgICAgPEhlYWRlckJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBpY29uPXtSb3RhdGVDY3d9XHJcbiAgICAgICAgICAgICAgICAgIHRvb2x0aXA9XCJSZXNldCBTY2VuZVwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVzZXR9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvVG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIDxUb29sdGlwUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgICA8SGVhZGVyQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGljb249e0NhbWVyYX1cclxuICAgICAgICAgICAgICAgICAgdG9vbHRpcD1cIlNjcmVlbnNob3RcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvblNjcmVlbnNob3R9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvVG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIDxUb29sdGlwUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgICA8SGVhZGVyQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGljb249e0hpc3Rvcnl9XHJcbiAgICAgICAgICAgICAgICAgIHRvb2x0aXA9XCJWZXJzaW9uIEhpc3RvcnlcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VmVyc2lvbkhpc3RvcnkodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvVG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IHctcHggYmctd2hpdGUvMjAgbXgtMVwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgPFRvb2x0aXBQcm92aWRlcj5cclxuICAgICAgICAgICAgICAgICAgPFRvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dMb2FkTW9kYWwodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMS41IHJvdW5kZWQtZnVsbCBiYWNrZHJvcC1ibHVyLXNtIGJnLXdoaXRlLzEwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgaG92ZXI6Ymctd2hpdGUvMjAgaG92ZXI6Ym9yZGVyLXdoaXRlLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMS41IGdyb3VwXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGUvODAgZ3JvdXAtaG92ZXI6dGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwPkxvYWQgU2NlbmU8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgPC9Ub29sdGlwUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAgICA8VG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgICAgICA8VG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NhdmVNb2RhbCh0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBweS0xLjUgcm91bmRlZC1mdWxsIGJhY2tkcm9wLWJsdXItc20gYmctd2hpdGUvMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBob3ZlcjpiZy13aGl0ZS8yMCBob3Zlcjpib3JkZXItd2hpdGUvMzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xLjUgZ3JvdXBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2xvdWRVcGxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlLzgwIGdyb3VwLWhvdmVyOnRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cD5TYXZlIFNjZW5lPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgIDwvVG9vbHRpcFByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2hlYWRlcj5cclxuXHJcbiAgICAgIDxTYXZlU2NlbmVNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17c2hvd1NhdmVNb2RhbH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93U2F2ZU1vZGFsKGZhbHNlKX1cclxuICAgICAgICBvblNhdmU9eyhzY2VuZU5hbWUsIGRlc2NyaXB0aW9uLCBzYXZlVHlwZSkgPT4ge1xyXG4gICAgICAgICAgb25TYXZlU2NlbmUoc2NlbmVOYW1lLCBkZXNjcmlwdGlvbiwgc2F2ZVR5cGUpO1xyXG4gICAgICAgICAgc2V0U2hvd1NhdmVNb2RhbChmYWxzZSk7XHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIDxMb2FkU2NlbmVNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17c2hvd0xvYWRNb2RhbH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93TG9hZE1vZGFsKGZhbHNlKX1cclxuICAgICAgICBvbkxvYWQ9eyhjb25maWcpID0+IHtcclxuICAgICAgICAgIG9uTG9hZFNjZW5lKGNvbmZpZyk7XHJcbiAgICAgICAgICBzZXRTaG93TG9hZE1vZGFsKGZhbHNlKTtcclxuICAgICAgICB9fVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgPFZlcnNpb25IaXN0b3J5XHJcbiAgICAgICAgaXNPcGVuPXtzaG93VmVyc2lvbkhpc3Rvcnl9XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1ZlcnNpb25IaXN0b3J5KGZhbHNlKX1cclxuICAgICAgICB1c2VySWQ9e3VzZXIuaWR9XHJcbiAgICAgICAgd29ya3NwYWNlSWQ9e3Byb2plY3QuX2lkfVxyXG4gICAgICAgIG9uQXBwbHlWZXJzaW9uPXsoY29uZmlnKSA9PiB7XHJcbiAgICAgICAgICBvbkxvYWRTY2VuZShjb25maWcpO1xyXG4gICAgICAgICAgc2V0U2hvd1ZlcnNpb25IaXN0b3J5KGZhbHNlKTtcclxuICAgICAgICB9fVxyXG4gICAgICAvPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG5cclxuZnVuY3Rpb24gSGVhZGVyQnV0dG9uKHsgaWNvbjogSWNvbiwgdG9vbHRpcCwgcHJpbWFyeSA9IGZhbHNlLCBvbkNsaWNrIH0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFRvb2x0aXA+XHJcbiAgICAgIDxUb29sdGlwVHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgIDxidXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BwLTEuNSByb3VuZGVkLWZ1bGwgJHtcclxuICAgICAgICAgICAgcHJpbWFyeVxyXG4gICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5IHRleHQtd2hpdGUgaG92ZXI6YmctcHJpbWFyeS02MDBcIlxyXG4gICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzIwXCJcclxuICAgICAgICAgIH1gfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICA8VG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgPHA+e3Rvb2x0aXB9PC9wPlxyXG4gICAgICA8L1Rvb2x0aXBDb250ZW50PlxyXG4gICAgPC9Ub29sdGlwPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkNsb3VkVXBsb2FkIiwiU2hhcmUiLCJTdG9yZSIsIkJlbGwiLCJSb3RhdGVDY3ciLCJDYW1lcmEiLCJVcGxvYWQiLCJNYXhpbWl6ZTIiLCJNaW5pbWl6ZTIiLCJIaXN0b3J5IiwiUm90YXRlQ3ciLCJVbmRvIiwiUmVkbyIsIkFsZXJ0Q2lyY2xlIiwiUm90YXRlTGVmdCIsIkltYWdlIiwiVG9vbHRpcCIsIlRvb2x0aXBDb250ZW50IiwiVG9vbHRpcFByb3ZpZGVyIiwiVG9vbHRpcFRyaWdnZXIiLCJBdmF0YXIiLCJBdmF0YXJJbWFnZSIsIkF2YXRhckZhbGxiYWNrIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTYXZlU2NlbmVNb2RhbCIsIkxvYWRTY2VuZU1vZGFsIiwiTGluayIsInVzZVBhcmFtcyIsIlZlcnNpb25IaXN0b3J5IiwiZ2V0VmVyc2lvbnMiLCJIZWFkZXIiLCJvblJlc2V0Iiwib25TY3JlZW5zaG90Iiwib25TYXZlU2NlbmUiLCJvbkxvYWRTY2VuZSIsImlzRnVsbHNjcmVlbiIsIm9uVG9nZ2xlRnVsbHNjcmVlbiIsInVzZXIiLCJwcm9qZWN0Iiwib25VbmRvIiwib25SZWRvIiwiaGFzVW5zYXZlZENoYW5nZXMiLCJvblJldmVydFRvU2F2ZWQiLCJzaG93U2F2ZU1vZGFsIiwic2V0U2hvd1NhdmVNb2RhbCIsInNob3dMb2FkTW9kYWwiLCJzZXRTaG93TG9hZE1vZGFsIiwic2hvd1ZlcnNpb25IaXN0b3J5Iiwic2V0U2hvd1ZlcnNpb25IaXN0b3J5IiwiaGVhZGVyIiwiaWQiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwiX2lkIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJIZWFkZXJCdXR0b24iLCJpY29uIiwidG9vbHRpcCIsIm9uQ2xpY2siLCJhc0NoaWxkIiwiYnV0dG9uIiwicCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblNhdmUiLCJzY2VuZU5hbWUiLCJkZXNjcmlwdGlvbiIsInNhdmVUeXBlIiwib25Mb2FkIiwiY29uZmlnIiwidXNlcklkIiwid29ya3NwYWNlSWQiLCJvbkFwcGx5VmVyc2lvbiIsIkljb24iLCJwcmltYXJ5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Header.jsx\n"));

/***/ })

});