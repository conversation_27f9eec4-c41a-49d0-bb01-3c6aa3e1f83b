"use client";
import InviteModal from "@/components/InviteModal";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Comment } from "@/types";
import { Gltf, Html, OrbitControls, Stage } from "@react-three/drei";
import { Canvas, ThreeEvent } from "@react-three/fiber";
import { Edit, MessageCircle, Share2, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { Suspense, useCallback, useEffect, useState } from "react";
import * as THREE from "three";
import { useOnboarding } from "./Onboarding";
import CommentForm from "@/components/Comments/CommentForm";
import CommentReplyCard from "@/components/Comments/CommentReplyCard";

interface ModelViewerProps {
  isProjectPage?: boolean;
  modelPath: string;
  userId: string;
  user?: any;
  workspaceId?: string;
  isCommentsMode?: boolean;
  onCommentSubmit?: (
    text: string,
    importance: string,
    inputPosition: { x: number; y: number } | null,
    position: THREE.Vector3 | null
  ) => void;
  onReplySubmit?: (text: string, commentId: string) => void;
  comments?: Comment[];
  projectId?: string;
  isPreviewMode?: boolean;
  commentLoading?: boolean;
  onEditReply?: (
    commentId: string,
    replyId: string,
    newReply: string
  ) => Promise<void>;
  onEditComment?: (commentId: string, newText: string) => Promise<void>;
}

function CommentMarkers({
  comments,
  isCommentsMode,
  user,
  onReply,
  onEditReply,
  onEditComment,
}: {
  comments: Comment[];
  isCommentsMode?: boolean;
  user: any;
  onReply: (text: string, commentId: string) => void;
  onEditReply?: (
    commentId: string,
    replyId: string,
    newReply: string
  ) => Promise<void>;
  onEditComment?: (commentId: string, newText: string) => Promise<void>;
}) {
  const [openCommentId, setOpenCommentId] = useState<string | null>(null);

  // Function to determine color based on importance
  const getCommentStyle = (importance: string = "low") => {
    switch (importance) {
      case "high":
        return "#FF5757";
      case "medium":
        return "#FF9F45";
      case "low":
      default:
        return "#F5C754";
    }
  };

  if (!isCommentsMode) return null;

  return (
    <>
      {comments.map((comment) => {
        const color = getCommentStyle(comment.importance);
        const firstLetter = comment.author.name.charAt(0).toUpperCase();

        return (
          <Html
            key={comment.id}
            position={[
              comment.position?.x || 0,
              comment.position?.y || 0,
              comment.position?.z || 0,
            ]}
            style={{ zIndex: 10 }}
            zIndexRange={[10, 0]}
          >
            <Popover
              open={openCommentId === comment.id}
              onOpenChange={(open) =>
                setOpenCommentId(open ? comment.id : null)
              }
            >
              <PopoverTrigger asChild>
                <div
                  className="w-8 h-8 rounded-full flex items-center justify-center transform -translate-x-1/2 -translate-y-1/2 cursor-pointer hover:scale-110 transition-transform"
                  style={{ backgroundColor: color }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <span className="text-white font-semibold text-sm">
                    {firstLetter}
                  </span>
                </div>
              </PopoverTrigger>
              <CommentReplyCard
                comment={comment}
                user={user}
                onReply={onReply}
                onEditReply={onEditReply}
                onEditComment={onEditComment}
                isOpen={openCommentId === comment.id}
                onOpenChange={(open) =>
                  setOpenCommentId(open ? comment.id : null)
                }
              />
            </Popover>
          </Html>
        );
      })}
    </>
  );
}

export default function ModelViewer({
  modelPath,
  isProjectPage,
  userId,
  user,
  workspaceId,
  isCommentsMode = false,
  onCommentSubmit,
  onReplySubmit,
  comments = [],
  projectId,
  isPreviewMode = false,
  commentLoading,
  onEditReply,
  onEditComment,
}: ModelViewerProps) {
  const router = useRouter();
  const { startOnboarding } = useOnboarding();
  const [contextMenu, setContextMenu] = useState({
    show: false,
    x: 0,
    y: 0,
  });

  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [commentPosition, setCommentPosition] = useState<THREE.Vector3 | null>(
    null
  );
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [localCommentsMode, setLocalCommentsMode] = useState(isCommentsMode);
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null);
  const [showCommentDetails, setShowCommentDetails] = useState(false);
  const [isOutlinerOpen, setIsOutlinerOpen] = useState(false);
  const [commentReplyPosition, setCommentReplyPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [commentInputPosition, setCommentInputPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // Update local state when prop changes
  useEffect(() => {
    setLocalCommentsMode(isCommentsMode);
  }, [isCommentsMode]);

  // Notify parent component when local state changes
  useEffect(() => {
    // If there's a way to notify the parent component about the local comments mode change
    console.log("Comments mode changed:", localCommentsMode);
  }, [localCommentsMode]);

  // Start model viewer onboarding when comments mode is first enabled
  useEffect(() => {
    if (localCommentsMode && isProjectPage) {
      const hasCompletedModelViewerOnboarding = localStorage.getItem(
        "modelviewer-onboarding-completed"
      );
      if (!hasCompletedModelViewerOnboarding) {
        setTimeout(() => {
          startOnboarding("modelViewer");
        }, 1000);
      }
    }
  }, [localCommentsMode, isProjectPage, startOnboarding]);

  // Mock version history data
  const mockVersions = [
    {
      id: 1,
      timestamp: new Date(),
      versionNumber: "1.0.0",
      author: "John Doe",
      changes: "Initial design",
      description: "Created the initial design of the diamond ring",
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 86400000),
      versionNumber: "0.9.0",
      author: "Jane Smith",
      changes: "Prototype design",
      description: "Prototype design with basic structure",
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 172800000),
      versionNumber: "0.8.0",
      author: "Mike Johnson",
      changes: "Concept design",
      description: "Initial concept sketches and basic 3D model",
    },
  ];

  const handleContextMenu = useCallback(
    (event: React.MouseEvent) => {
      if (isPreviewMode) {
        event.preventDefault();
        return;
      }

      event.preventDefault();
      setContextMenu({
        show: true,
        x: event.clientX,
        y: event.clientY,
      });
    },
    [isPreviewMode]
  );

  const handleClick = useCallback(
    (event: any) => {
      if (!localCommentsMode) return;
      if (event.object) {
        setCommentPosition(event.point);
        setCommentInputPosition({ x: event.clientX, y: event.clientY });
        setShowCommentDetails(false);
        setSelectedComment(null);
        setCommentReplyPosition(null);
      }
    },
    [localCommentsMode]
  );

  const handleApplyVersion = (versionId: string) => {
    console.log(`Applying version ${versionId}`);
    // Implementation for applying a specific version
  };

  const handleVersionCompare = (versionIds: string[]) => {
    // Logic to display multiple versions side by side
  };

  // Add a new function to handle replies
  const handleReplySubmit = (text: string, commentId: string) => {
    if (onReplySubmit) {
      onReplySubmit(text, commentId);
    }
    // Don't close the comment details after submitting a reply
    // This allows users to add multiple replies
  };

  const handleCommentClick = (
    comment: Comment,
    event: ThreeEvent<MouseEvent>
  ) => {
    console.log("Comment clicked:", comment);
    // Hide the comment input if it's open
    setCommentPosition(null);

    // Use the actual mouse position from the event
    const mouseX = event.clientX;
    const mouseY = event.clientY;

    setCommentReplyPosition({ x: mouseX, y: mouseY });

    // Set the selected comment and show the details - do this AFTER position is set
    setSelectedComment(comment);
    setShowCommentDetails(true);

    // Add more debugging
    console.log("Selected comment set to:", comment.id);
    console.log("showCommentDetails set to true");
    console.log("Comment reply position set to:", { x: mouseX, y: mouseY });
  };

  // Add debugging for render
  useEffect(() => {
    console.log("Rendering with selectedComment:", selectedComment?.id);
    console.log("showCommentDetails:", showCommentDetails);
  }, [selectedComment, showCommentDetails]);

  const RadialMenu = () => {
    if (!contextMenu.show) return null;

    const menuItems = [
      {
        icon: MessageCircle,
        label: "Comments",
        action: () => {
          console.log("Toggling comments mode");
          setLocalCommentsMode(!localCommentsMode);
        },
      },
      { icon: Share2, label: "Share", action: () => setIsShareModalOpen(true) },
      {
        icon: Edit,
        label: "Edit",
        action: () => {
          if (projectId) {
            router.push(`/projects/${projectId}/edit`);
          }
        },
      },
    ];

    return (
      <div
        className="fixed z-50"
        style={{
          left: `${contextMenu.x}px`,
          top: `${contextMenu.y}px`,
        }}
      >
        <TooltipProvider>
          <div className="relative">
            {menuItems.map((item, index) => {
              const totalAngle = Math.PI;
              const startAngle = Math.PI / 2;
              const angleStep = totalAngle / (menuItems.length - 1);
              const angle = startAngle + index * angleStep;
              const radius = 80;
              const x = Math.cos(angle) * radius;
              const y = Math.sin(angle) * radius;

              return (
                <Tooltip key={item.label}>
                  <TooltipTrigger asChild>
                    <div
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 gold_icon_gradient !p-0 w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
                      style={{
                        left: `${x}px`,
                        top: `${y}px`,
                      }}
                      onClick={() => {
                        console.log(`Clicked ${item.label}`);
                        item.action();
                        setContextMenu({ show: false, x: 0, y: 0 });
                      }}
                    >
                      <item.icon className="size-5 text-[#18191E]" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{item.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        </TooltipProvider>
      </div>
    );
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenu.show) {
        const isMenuClick = (event.target as Element).closest(
          ".gold_icon_gradient"
        );
        if (!isMenuClick) {
          setContextMenu({ show: false, x: 0, y: 0 });
        }
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [contextMenu.show]);

  // Export Modal Component
  const ExportModal = () => (
    <Dialog open={isExportModalOpen} onOpenChange={setIsExportModalOpen}>
      <DialogContent className="sm:max-w-[625px] bg-gradient-to-br from-[#47474B] via-[#635A4F] to-[#48484D] border-[#FDE9CE]/30 text-[#FDE9CE]">
        <div className="p-6">
          <h2 className="text-2xl font-sansitaSwashed mb-6">Export Options</h2>

          <div className="space-y-6">
            <div className="p-4 border border-[#FDE9CE]/30 rounded-lg hover:bg-[#FDE9CE]/10 cursor-pointer transition">
              <h3 className="text-xl mb-2">Save to Data Center</h3>
              <p className="text-[#FDE9CE]/70">
                Store your model securely in our cloud data center for easy
                access and sharing.
              </p>
            </div>

            <div className="p-4 border border-[#FDE9CE]/30 rounded-lg hover:bg-[#FDE9CE]/10 cursor-pointer transition">
              <h3 className="text-xl mb-2">Include CAD Files</h3>
              <p className="text-[#FDE9CE]/70">
                Export with CAD files for manufacturing and detailed
                engineering.
              </p>
            </div>

            <div className="p-4 border border-[#FDE9CE]/30 rounded-lg hover:bg-[#FDE9CE]/10 cursor-pointer transition">
              <h3 className="text-xl mb-2">Download as GLB/GLTF</h3>
              <p className="text-[#FDE9CE]/70">
                Download in standard 3D format compatible with most 3D software.
              </p>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <Button
              className="bg-[#F5C754] text-[#18191E] hover:bg-[#F5C754]/90"
              onClick={() => setIsExportModalOpen(false)}
            >
              Export
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  if (isPreviewMode) {
    return (
      <>
        {localCommentsMode && (
          <div className="fixed top-6 right-20 z-10 flex items-center gap-2">
            <div className="bg-[#F5C754] text-[#18191E] px-3 py-1 rounded-full flex items-center gap-2">
              <MessageCircle size={16} />
              <span className="text-sm font-medium">Comments Mode</span>
            </div>
            <Button
              size="sm"
              className="bg-[#47474B] text-[#FDE9CE] hover:bg-[#47474B]/80"
              onClick={() => setLocalCommentsMode(false)}
            >
              <X size={16} />
            </Button>
          </div>
        )}

        <Canvas
          className="canvas"
          camera={{ position: [0, 0, 5], fov: 45 }}
          style={{
            width: "100%",
            height: "100%",
            background: "linear-gradient(to bottom right, #363643, #5C5C6E)",
          }}
          onContextMenu={handleContextMenu}
          onClick={isPreviewMode ? undefined : handleClick}
          onPointerMissed={() => {
            if (!isPreviewMode) {
              console.log("Pointer missed");
              if (commentPosition) setCommentPosition(null);
            }
          }}
        >
          <Suspense>
            <Stage intensity={0.5} adjustCamera={2}>
              <Gltf
                src={modelPath}
                onClick={(e) => {
                  if (localCommentsMode) {
                    e.stopPropagation();
                    console.log("Direct click on GLTF model", e);
                    setCommentPosition(e.point);
                    setShowCommentDetails(false);
                    setSelectedComment(null);
                    setCommentReplyPosition(null);
                  }
                }}
              />
            </Stage>

            <OrbitControls
              makeDefault
              enabled={
                !localCommentsMode || (!commentPosition && !showCommentDetails)
              }
            />

            {comments && comments.length > 0 && (
              <CommentMarkers
                comments={comments.filter((c) => !c.resolved)}
                isCommentsMode={localCommentsMode}
                user={user}
                onReply={handleReplySubmit}
                onEditReply={onEditReply}
                onEditComment={onEditComment}
              />
            )}

            {commentPosition && localCommentsMode && (
              <CommentForm
                className="comment-form"
                position={commentPosition}
                inputPosition={commentInputPosition}
                onSubmit={(text, importance, inputPosition) => {
                  if (onCommentSubmit) {
                    onCommentSubmit(
                      text,
                      importance,
                      inputPosition,
                      commentPosition
                    );
                  }
                  setCommentPosition(null);
                  setCommentInputPosition(null);
                }}
                onCancel={() => {
                  setCommentPosition(null);
                  setCommentInputPosition(null);
                }}
                loading={!!commentLoading}
              />
            )}
          </Suspense>
        </Canvas>

        {/* Render the comment reply card with absolute positioning */}
        {/* {selectedComment && showCommentDetails && commentReplyPosition && (
          <CommentReplyCard
            comment={selectedComment}
            position={commentReplyPosition}
            user={user}
            onClose={() => {
              setShowCommentDetails(false);
              setSelectedComment(null);
              setCommentReplyPosition(null);
            }}
            onReply={handleReplySubmit}
            onEditReply={onEditReply}
            onEditComment={onEditComment}
          />
        )} */}

        {!isPreviewMode && contextMenu.show && <RadialMenu />}

        {!isPreviewMode && (
          <>
            <InviteModal
              isOpen={isShareModalOpen}
              onClose={() => setIsShareModalOpen(false)}
              userId={userId}
            />

            <ExportModal />
          </>
        )}
      </>
    );
  }

  return (
    <>
      {localCommentsMode && (
        <div className="fixed top-6 right-20 z-10 flex items-center gap-2">
          <div className="bg-[#F5C754] text-[#18191E] px-3 py-1 rounded-full flex items-center gap-2">
            <MessageCircle size={16} />
            <span className="text-sm font-medium">Comments Mode</span>
          </div>
          <Button
            size="sm"
            className="bg-[#47474B] text-[#FDE9CE] hover:bg-[#47474B]/80"
            onClick={() => setLocalCommentsMode(false)}
          >
            <X size={16} />
          </Button>
        </div>
      )}

      <Canvas
        camera={{ position: [0, 0, 5], fov: 45 }}
        style={{
          width: "100%",
          height: "100%",
          background: "linear-gradient(to bottom right, #363643, #5C5C6E)",
        }}
        onContextMenu={handleContextMenu}
        onClick={isPreviewMode ? undefined : handleClick}
        onPointerMissed={() => {
          if (!isPreviewMode) {
            console.log("Pointer missed");
            if (commentPosition) setCommentPosition(null);
          }
        }}
      >
        <Suspense>
          <Stage intensity={0.5} adjustCamera={2}>
            <Gltf
              src={modelPath}
              onClick={(e) => {
                if (localCommentsMode) {
                  e.stopPropagation();
                  console.log("Direct click on GLTF model", e);
                  setCommentPosition(e.point);
                  setShowCommentDetails(false);
                  setSelectedComment(null);
                  setCommentReplyPosition(null);
                }
              }}
            />
          </Stage>

          <OrbitControls
            makeDefault
            enabled={
              !localCommentsMode || (!commentPosition && !showCommentDetails)
            }
          />

          {comments && comments.length > 0 && (
            <CommentMarkers
              comments={comments.filter((c) => !c.resolved)}
              isCommentsMode={localCommentsMode}
              user={user}
              onReply={handleReplySubmit}
              onEditReply={onEditReply}
              onEditComment={onEditComment}
            />
          )}

          {commentPosition && localCommentsMode && (
            <CommentForm
              position={commentPosition}
              inputPosition={commentInputPosition}
              onSubmit={(text, importance, inputPosition) => {
                if (onCommentSubmit) {
                  onCommentSubmit(
                    text,
                    importance,
                    inputPosition,
                    commentPosition
                  );
                }
                setCommentPosition(null);
                setCommentInputPosition(null);
              }}
              onCancel={() => {
                setCommentPosition(null);
                setCommentInputPosition(null);
              }}
              loading={!!commentLoading}
            />
          )}
        </Suspense>
      </Canvas>

      {/* Render the comment reply card with absolute positioning */}
      {/* {selectedComment && showCommentDetails && commentReplyPosition && (
        <CommentReplyCard
          comment={selectedComment}
          position={commentReplyPosition}
          user={user}
          onClose={() => {
            setShowCommentDetails(false);
            setSelectedComment(null);
            setCommentReplyPosition(null);
          }}
          onReply={handleReplySubmit}
          onEditReply={onEditReply}
          onEditComment={onEditComment}
        />
      )} */}

      {!isPreviewMode && contextMenu.show && <RadialMenu />}

      {!isPreviewMode && (
        <>
          <InviteModal
            isOpen={isShareModalOpen}
            onClose={() => setIsShareModalOpen(false)}
            userId={userId}
          />

          <ExportModal />
        </>
      )}
    </>
  );
}
