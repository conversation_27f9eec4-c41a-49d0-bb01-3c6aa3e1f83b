"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Update the config state for unsaved changes tracking\n            setDbSyncedConfig(config);\n            setWorkingConfig(config);\n            // Reset unsaved changes state since we just loaded a saved version\n            setHasUnsavedChanges(false);\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // State to track unsaved changes\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if there are unsaved changes\n    const checkUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Update hasUnsavedChanges when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsavedChanges = checkUnsavedChanges();\n        setHasUnsavedChanges(unsavedChanges);\n    }, [\n        checkUnsavedChanges\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig,\n        handleLoadScene\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                    // Update the synced config to reflect the new saved state\n                    setDbSyncedConfig(sceneConfig);\n                    // Reset unsaved changes state since we just saved\n                    setHasUnsavedChanges(false);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    // Update working config when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentConfig = getComparisonConfig();\n        setWorkingConfig(currentConfig);\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri,\n        setWorkingConfig,\n        getComparisonConfig\n    ]);\n    // Add beforeunload event listener to warn about unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (event)=>{\n            if (hasUnsavedChanges) {\n                const message = \"You have unsaved changes. Are you sure you want to leave?\";\n                event.preventDefault();\n                event.returnValue = message; // For Chrome\n                return message; // For other browsers\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        hasUnsavedChanges\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                    // Set this as the baseline for unsaved changes tracking\n                    setDbSyncedConfig(currentVersion.config);\n                    // Reset unsaved changes state since we just loaded the saved version\n                    setHasUnsavedChanges(false);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1929,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction,\n                hasUnsavedChanges: hasUnsavedChanges,\n                onRevertToSaved: revertToSaved\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1930,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1945,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1953,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1948,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1977,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1989,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2006,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2015,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2036,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2094,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1972,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2122,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2167,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2166,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2229,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2241,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2267,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2270,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2291,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2285,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2315,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2309,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2164,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2138,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2343,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"FHdA2hf0sgaUOw2xWJA+o2ZJPR4=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});