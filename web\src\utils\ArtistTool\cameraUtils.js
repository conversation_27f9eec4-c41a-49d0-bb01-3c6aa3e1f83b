import * as THREE from "three";

export const setCameraView = (orbitControls, viewType) => {
  if (!orbitControls) return;

  // Define camera positions and targets for each view
  const views = {
    front: {
      position: new THREE.Vector3(0, 2, 5),
      target: new THREE.Vector3(0, 1, 0),
    },
    top: {
      position: new THREE.Vector3(0, 5, 0),
      target: new THREE.Vector3(0, 0, 0),
    },
    side: {
      position: new THREE.Vector3(5, 2, 0),
      target: new THREE.Vector3(0, 1, 0),
    },
    perspective: {
      position: new THREE.Vector3(5, 3, 5),
      target: new THREE.Vector3(0, 2, 0),
    },
  };

  // Get the view configuration
  const viewConfig = views[viewType];
  if (!viewConfig) return;

  // Set the camera position and target directly
  orbitControls.object.position.copy(viewConfig.position);
  orbitControls.target.copy(viewConfig.target);
  orbitControls.update();
};

export const calculateTargetPosition = (selectedObjects) => {
  const target = new THREE.Vector3(0, 0, 0);
  if (selectedObjects.length > 0) {
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    if (objectsArray.length > 0) {
      const center = new THREE.Vector3();
      objectsArray.forEach((obj) => {
        center.add(obj.position);
      });
      center.divideScalar(objectsArray.length);
      target.copy(center);
    }
  }
  return target;
};

export const handleDecalPlacement = (
  event,
  setIsPlacingDecal,
  setDecalPosition,
  setDecalRotation
) => {
  if (!event || !event.point || !event.face || !event.object) {
    console.warn("Decal placement click missing event data", event);
    setIsPlacingDecal(false);
    return;
  }
  event.stopPropagation();

  const { point: worldPoint, face, object: clickedMesh } = event;

  const localPoint = clickedMesh.worldToLocal(worldPoint.clone());
  setDecalPosition([localPoint.x, localPoint.y, localPoint.z]);

  const tempObject = new THREE.Object3D();

  // Normal transformation: world normal to local normal for lookAt target
  const inverseWorldMatrix = clickedMesh.matrixWorld.clone().invert();
  const localNormalMatrix = new THREE.Matrix3().getNormalMatrix(
    inverseWorldMatrix
  );
  const localNormalForLookAt = face.normal
    .clone()
    .applyMatrix3(localNormalMatrix)
    .normalize();
  const localLookAtTarget = localPoint.clone().add(localNormalForLookAt);

  tempObject.lookAt(localLookAtTarget);

  const qSurface = tempObject.quaternion.clone();
  const qTextOnDecal = new THREE.Quaternion().setFromEuler(
    new THREE.Euler(Math.PI / 2, 0, 0, "XYZ")
  );

  const qFinal = qSurface.multiply(qTextOnDecal);
  const finalEuler = new THREE.Euler().setFromQuaternion(qFinal, "XYZ");

  setDecalRotation([finalEuler.x, finalEuler.y, finalEuler.z]);
  setIsPlacingDecal(false);
};
