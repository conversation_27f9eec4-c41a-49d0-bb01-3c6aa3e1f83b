import { useState } from "react";
import { PopoverContent } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageCircle, X } from "lucide-react";
import { Comment } from "@/types";

interface CommentReplyCardProps {
  comment: Comment;
  user: any;
  onReply: (text: string, commentId: string) => void;
  onEditReply?: (
    commentId: string,
    replyId: string,
    newReply: string
  ) => Promise<void>;
  onEditComment?: (commentId: string, newText: string) => Promise<void>;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function CommentReplyCard({
  comment,
  user,
  onReply,
  onEditReply,
  onEditComment,
  isOpen,
  onOpenChange,
}: CommentReplyCardProps) {
  const [replyText, setReplyText] = useState("");
  const [localReplies, setLocalReplies] = useState<
    {
      replyData: string;
      author: { name: string; image?: string };
      createdAt: string;
    }[]
  >([]);
  const [editingReplyId, setEditingReplyId] = useState<string | null>(null);
  const [editingReplyText, setEditingReplyText] = useState<string>("");
  const [editLoading, setEditLoading] = useState(false);
  const [editingComment, setEditingComment] = useState(false);
  const [editingCommentText, setEditingCommentText] = useState(comment.text);
  const [editCommentLoading, setEditCommentLoading] = useState(false);

  const handleLocalReply = (text: string, commentId: string) => {
    if (text.trim()) {
      const newReply = {
        replyData: text,
        author: {
          name: user.name,
          image: user.image,
        },
        createdAt: new Date().toISOString(),
      };

      // setLocalReplies((prev) => [...prev, newReply]);
      onReply(text, commentId);
      setReplyText("");
    }
  };

  return (
    <PopoverContent
      className="w-80 p-4 bg-gradient-to-br from-[#47474B] via-[#635A4F] to-[#48484D] text-[#FDE9CE] border border-[#E6D2BA]"
      side="top"
      align="center"
    >
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={comment.author.image} alt={comment.author.name} />
            <AvatarFallback className="bg-[#D2BBA0]">
              {comment.author.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <span className="font-medium">{comment.author.name}</span>
            <div className="text-xs text-[#FDE9CE]/60">
              {new Date(comment.createdAt).toLocaleString()}
            </div>
          </div>
          {user?.name === comment.author.name && (
            <button
              className="ml-2 text-xs text-[#F5C754] hover:underline"
              onClick={() => {
                setEditingComment((prev) => !prev);
                setEditingCommentText(comment.text);
              }}
              disabled={editCommentLoading}
            >
              {editingComment ? "" : "Edit"}
            </button>
          )}
        </div>
        <Button
          size="sm"
          onClick={() => onOpenChange(false)}
          className="bg-transparent hover:bg-transparent text-[#FDE9CE]"
        >
          <X size={16} />
        </Button>
      </div>

      {editingComment ? (
        <div className="flex flex-col gap-2 mb-4">
          <textarea
            className="w-full bg-white/20 text-[#FDE9CE] rounded p-1 text-sm border border-[#f3e1ca]"
            value={editingCommentText}
            onChange={(e) => setEditingCommentText(e.target.value)}
            onKeyDown={async (e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                if (editingCommentText.trim() && !editCommentLoading) {
                  setEditCommentLoading(true);
                  onOpenChange(false);
                  await onEditComment?.(comment.id, editingCommentText);
                  setEditCommentLoading(false);
                  setEditingComment(false);
                }
              }
            }}
            rows={2}
            disabled={editCommentLoading}
          />
          <div className="flex gap-2">
            <button
              className="px-2 py-1 rounded bg-[#F5C754] text-[#18191E] text-xs"
              disabled={editCommentLoading || !editingCommentText.trim()}
              onClick={async () => {
                setEditCommentLoading(true);
                onOpenChange(false);
                await onEditComment?.(comment.id, editingCommentText);
                setEditCommentLoading(false);
                setEditingComment(false);
              }}
            >
              Save
            </button>
            <button
              className="px-2 py-1 rounded bg-[#47474B] text-[#FDE9CE] text-xs"
              onClick={() => setEditingComment(false)}
              disabled={editCommentLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <p className="mb-4 text-[#FDE9CE]/90">{comment.text}</p>
      )}

      {(comment.replies?.length > 0 || localReplies.length > 0) && (
        <div className="mb-4 border-t border-[#FDE9CE]/20 pt-3">
          <h4 className="text-sm font-medium mb-2">Replies</h4>
          <div className="space-y-3 max-h-32 overflow-y-auto">
            {comment.replies &&
              comment.replies.map((reply, index) => (
                <div
                  key={`server-reply-${reply.id || index}`}
                  className="text-sm"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Avatar className="h-6 w-6">
                      <AvatarImage
                        src={reply.author.image}
                        alt={reply.author.name}
                      />
                      <AvatarFallback className="bg-[#D2BBA0] text-xs">
                        {reply.author.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <span className="font-medium text-xs">
                        {reply.author.name}
                      </span>
                      <div className="text-xs text-[#FDE9CE]/60">
                        {reply.createdAt &&
                          new Date(reply.createdAt).toLocaleString()}
                      </div>
                    </div>
                    {user?.name === reply.author.name && (
                      <button
                        className="ml-2 text-xs text-[#F5C754] hover:underline"
                        onClick={() => {
                          setEditingReplyId(reply.id || index.toString());
                          setEditingReplyText(reply.replyData);
                        }}
                        disabled={editLoading}
                      >
                        {editingReplyId === (reply.id || index.toString())
                          ? ""
                          : "Edit"}
                      </button>
                    )}
                  </div>
                  {editingReplyId === (reply.id || index.toString()) ? (
                    <div className="ml-7 flex flex-col gap-2">
                      <textarea
                        className="w-full bg-white/20 text-[#FDE9CE] rounded p-1 text-xs border border-[#f3e1ca]"
                        value={editingReplyText}
                        onChange={(e) => setEditingReplyText(e.target.value)}
                        onKeyDown={async (e) => {
                          if (e.key === "Enter" && !e.shiftKey) {
                            e.preventDefault();
                            if (editingReplyText.trim() && !editLoading) {
                              setEditLoading(true);
                              onOpenChange(false);
                              await onEditReply?.(
                                comment.id,
                                reply.id,
                                editingReplyText
                              );
                              setEditLoading(false);
                              setEditingReplyId(null);
                            }
                          }
                        }}
                        rows={2}
                        disabled={editLoading}
                      />
                      <div className="flex gap-2">
                        <button
                          className="px-2 py-1 rounded bg-[#F5C754] text-[#18191E] text-xs"
                          disabled={editLoading || !editingReplyText.trim()}
                          onClick={async () => {
                            setEditLoading(true);
                            onOpenChange(false);
                            await onEditReply?.(
                              comment.id,
                              reply.id,
                              editingReplyText
                            );
                            setEditLoading(false);
                            setEditingReplyId(null);
                          }}
                        >
                          Save
                        </button>
                        <button
                          className="px-2 py-1 rounded bg-[#47474B] text-[#FDE9CE] text-xs"
                          onClick={() => setEditingReplyId(null)}
                          disabled={editLoading}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="ml-7 text-[#FDE9CE]/80 text-xs">
                      {reply.replyData}
                    </p>
                  )}
                </div>
              ))}

            {localReplies.map((reply, index) => (
              <div key={`local-reply-${index}`} className="text-sm">
                <div className="flex items-center gap-2 mb-1">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={reply.author.image}
                      alt={reply.author.name}
                    />
                    <AvatarFallback className="bg-[#D2BBA0] text-xs">
                      {reply.author.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <span className="font-medium text-xs">
                      {reply.author.name}
                    </span>
                    <div className="text-xs text-[#FDE9CE]/60">
                      {new Date(reply.createdAt).toLocaleString()}
                    </div>
                  </div>
                </div>
                <p className="ml-7 text-[#FDE9CE]/80 text-xs">
                  {reply.replyData}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="border-t border-[#FDE9CE]/20 pt-3">
        <div className="relative">
          <textarea
            className="w-full bg-white/20 text-[#FDE9CE] rounded-xl p-2 mb-2 text-sm placeholder:text-[#FDE9CE]/60 border border-[#f3e1ca] focus:outline-0 focus:border-[#f3e1ca] focus:ring-[#f3e1ca]"
            placeholder="Add a reply..."
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                if (replyText.trim()) {
                  handleLocalReply(replyText, comment.id);
                }
              }
            }}
            rows={2}
          />
          <button
            className="absolute bottom-4 right-2 p-1 rounded-full hover:bg-white/10 transition-colors"
            onClick={() => handleLocalReply(replyText, comment.id)}
          >
            <MessageCircle size={16} className="text-[#F5C754]" />
          </button>
        </div>
      </div>
    </PopoverContent>
  );
}
