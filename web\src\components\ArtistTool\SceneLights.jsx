"use client";

import { useRef, useEffect } from "react";
import { useFrame } from "@react-three/fiber";
import { TransformControls } from "@react-three/drei";
import {
  DirectionalLightHelper,
  PointLightHelper,
  SpotLightHelper,
} from "three";

export default function SceneLights({
  lights = [],
  lightRefs = { current: {} },
  showHelpers = true,
  selectedLight,
  onLightSelect,
  onLightMoved,
  showLightSpheres = true,
  threePointLighting = false,
}) {
  const refs = useRef([]);
  const meshRefs = useRef([]);

  // Initialize refs safely whenever lights change
  useEffect(() => {
    // Ensure refs arrays match the length of lights
    if (refs.current.length !== lights.length) {
      refs.current = Array(lights.length)
        .fill()
        .map((_, i) => refs.current[i] || { current: null });
    }

    if (meshRefs.current.length !== lights.length) {
      meshRefs.current = Array(lights.length)
        .fill()
        .map((_, i) => meshRefs.current[i] || { current: null });
    }
  }, [lights]);

  // Update the mesh sphere position to match the actual position of the lights
  useFrame(() => {
    refs.current.forEach((lightRef, index) => {
      if (lightRef?.current && meshRefs.current[index]?.current) {
        meshRefs.current[index].current.position.copy(
          lightRef.current.position
        );
      }
    });
  });

  const lightsArray = lights || [];

  if (!Array.isArray(lightsArray)) {
    console.warn(
      "SceneLights: Expected lights prop to be an array, received:",
      lights
    );
    return null;
  }

  const handleTransformChange = (lightId) => {
    if (lightRefs.current[lightId]) {
      const position = lightRefs.current[lightId].position.clone();
      onLightMoved?.(lightId, position);
    }
  };

  return (
    <>
      {lightsArray.map((light, index) => {
        // Ensure the ref object exists for this index
        if (!refs.current[index]) {
          refs.current[index] = { current: null };
        }

        if (!meshRefs.current[index]) {
          meshRefs.current[index] = { current: null };
        }

        // Skip showing spheres for 3-point lighting system
        const shouldShowSphere =
          showLightSpheres &&
          (selectedLight === light.id || showHelpers) &&
          !(
            threePointLighting &&
            ["keyLight", "fillLight", "backLight"].includes(light.id)
          );

        switch (light.type) {
          case "ambient":
            return (
              <ambientLight
                key={light.id}
                ref={(ref) => {
                  if (refs.current[index]) {
                    refs.current[index].current = ref;
                  }
                  if (ref && lightRefs?.current) {
                    lightRefs.current[light.id] = ref;
                  }
                }}
                intensity={light.intensity || 1}
                color={light.color || "#ffffff"}
              />
            );
          case "directional":
            return (
              <group key={light.id}>
                <directionalLight
                  ref={(ref) => {
                    if (refs.current[index]) {
                      refs.current[index].current = ref;
                    }
                    if (ref && lightRefs?.current) {
                      lightRefs.current[light.id] = ref;
                    }
                  }}
                  position={light.position || [0, 0, 0]}
                  intensity={light.intensity || 1}
                  color={light.color || "#ffffff"}
                  castShadow={light.castShadow || false}
                  shadow-mapSize={[4096, 4096]}
                  shadow-camera-far={50}
                  shadow-camera-left={-10}
                  shadow-camera-right={10}
                  shadow-camera-top={10}
                  shadow-camera-bottom={-10}
                  shadow-bias={-0.0005}
                  shadow-radius={2}
                >
                  <orthographicCamera
                    attach="shadow-camera"
                    args={[-10, 10, 10, -10, 0.1, 50]}
                  />
                </directionalLight>
                {shouldShowSphere && light.visible !== false && (
                  <mesh
                    ref={(ref) => {
                      if (meshRefs.current[index]) {
                        meshRefs.current[index].current = ref;
                      }
                    }}
                    position={light.position || [0, 0, 0]}
                    onClick={(e) => {
                      e.stopPropagation();
                      onLightSelect(light.id);
                    }}
                  >
                    <sphereGeometry args={[0.2]} />
                    <meshBasicMaterial color={light.color || "yellow"} />
                  </mesh>
                )}
                {selectedLight === light.id && (
                  <TransformControls
                    object={refs.current[index].current}
                    mode="translate"
                    onObjectChange={() => handleTransformChange(light.id)}
                  />
                )}
                {showHelpers &&
                  light.helperVisible &&
                  light.visible !== false &&
                  refs.current[index].current && (
                    <primitive
                      object={
                        new DirectionalLightHelper(
                          refs.current[index].current,
                          0.5
                        )
                      }
                    />
                  )}
              </group>
            );
          case "point":
            return (
              <group key={light.id}>
                <pointLight
                  ref={(ref) => {
                    if (refs.current[index]) {
                      refs.current[index].current = ref;
                    }
                    if (ref && lightRefs?.current) {
                      lightRefs.current[light.id] = ref;
                    }
                  }}
                  position={light.position || [2, 2, 2]}
                  intensity={light.intensity || 1}
                  color={light.color || "#ffffff"}
                  distance={light.distance || 0}
                  decay={light.decay || 2}
                  castShadow={light.castShadow || false}
                  shadow-mapSize={[512, 512]}
                />
                {shouldShowSphere && (
                  <mesh
                    ref={(ref) => {
                      if (meshRefs.current[index]) {
                        meshRefs.current[index].current = ref;
                      }
                    }}
                    position={light.position || [2, 2, 2]}
                    onClick={(e) => {
                      e.stopPropagation();
                      onLightSelect(light.id);
                    }}
                  >
                    <sphereGeometry args={[0.2]} />
                    <meshBasicMaterial color={light.color || "orange"} />
                  </mesh>
                )}
                {selectedLight === light.id && (
                  <TransformControls
                    object={refs.current[index].current}
                    mode="translate"
                    onObjectChange={() => handleTransformChange(light.id)}
                  />
                )}
                {showHelpers &&
                  light.helperVisible &&
                  refs.current[index].current && (
                    <primitive
                      object={
                        new PointLightHelper(
                          refs.current[index].current,
                          light.helperSize || 0.5
                        )
                      }
                    />
                  )}
              </group>
            );
          case "spot":
            return (
              <group key={light.id}>
                <spotLight
                  ref={(ref) => {
                    if (refs.current[index]) {
                      refs.current[index].current = ref;
                    }
                    if (ref && lightRefs?.current) {
                      lightRefs.current[light.id] = ref;
                    }
                  }}
                  position={light.position || [0, 3, 0]}
                  intensity={light.intensity || 1}
                  distance={light.distance || 0}
                  angle={light.angle || Math.PI / 3}
                  penumbra={light.penumbra || 0.5}
                  decay={light.decay || 0}
                  castShadow={true}
                  shadow-mapSize={[2048, 2048]}
                  shadow-camera-far={50}
                  shadow-camera-near={0.1}
                >
                  <orthographicCamera
                    attach="shadow-camera"
                    args={[-10, 10, 10, -10, 0.1, 50]}
                  />
                </spotLight>
                {showLightSpheres &&
                  (selectedLight === light.id || showHelpers) && (
                    <mesh
                      ref={(ref) => {
                        if (meshRefs.current[index]) {
                          meshRefs.current[index].current = ref;
                        }
                      }}
                      position={light.position || [0, 3, 0]}
                      onClick={(e) => {
                        e.stopPropagation();
                        onLightSelect(light.id);
                      }}
                    >
                      <sphereGeometry args={[0.2]} />
                      <meshBasicMaterial color="white" />
                    </mesh>
                  )}
                {selectedLight === light.id && (
                  <TransformControls
                    object={refs.current[index].current}
                    mode="translate"
                    onObjectChange={() => handleTransformChange(light.id)}
                  />
                )}
                {showHelpers &&
                  light.helperVisible &&
                  refs.current[index].current && (
                    <primitive
                      object={new SpotLightHelper(refs.current[index].current)}
                    />
                  )}
              </group>
            );
          default:
            return null;
        }
      })}
    </>
  );
}
