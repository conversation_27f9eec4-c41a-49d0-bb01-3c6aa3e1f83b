"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey";
exports.ids = ["vendor-chunks/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/EffectComposer.js":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/EffectComposer.js ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EffectComposer: () => (/* binding */ EffectComposer),\n/* harmony export */   EffectComposerContext: () => (/* binding */ EffectComposerContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/misc/WebGL.js\");\n\n\n\n\n\n\nconst EffectComposerContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst isConvolution = (effect) => (effect.getAttributes() & postprocessing__WEBPACK_IMPORTED_MODULE_2__.EffectAttribute.CONVOLUTION) === postprocessing__WEBPACK_IMPORTED_MODULE_2__.EffectAttribute.CONVOLUTION;\nconst EffectComposer = react__WEBPACK_IMPORTED_MODULE_1__.memo(\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n    ({\n      children,\n      camera: _camera,\n      scene: _scene,\n      resolutionScale,\n      enabled = true,\n      renderPriority = 1,\n      autoClear = true,\n      depthBuffer,\n      enableNormalPass,\n      stencilBuffer,\n      multisampling = 8,\n      frameBufferType = three__WEBPACK_IMPORTED_MODULE_3__.HalfFloatType\n    }, ref) => {\n      const { gl, scene: defaultScene, camera: defaultCamera, size } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.D)();\n      const scene = _scene || defaultScene;\n      const camera = _camera || defaultCamera;\n      const [composer, normalPass, downSamplingPass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        const webGL2Available = (0,three_stdlib__WEBPACK_IMPORTED_MODULE_5__.isWebGL2Available)();\n        const effectComposer = new postprocessing__WEBPACK_IMPORTED_MODULE_2__.EffectComposer(gl, {\n          depthBuffer,\n          stencilBuffer,\n          multisampling: multisampling > 0 && webGL2Available ? multisampling : 0,\n          frameBufferType\n        });\n        effectComposer.addPass(new postprocessing__WEBPACK_IMPORTED_MODULE_2__.RenderPass(scene, camera));\n        let downSamplingPass2 = null;\n        let normalPass2 = null;\n        if (enableNormalPass) {\n          normalPass2 = new postprocessing__WEBPACK_IMPORTED_MODULE_2__.NormalPass(scene, camera);\n          normalPass2.enabled = false;\n          effectComposer.addPass(normalPass2);\n          if (resolutionScale !== void 0 && webGL2Available) {\n            downSamplingPass2 = new postprocessing__WEBPACK_IMPORTED_MODULE_2__.DepthDownsamplingPass({ normalBuffer: normalPass2.texture, resolutionScale });\n            downSamplingPass2.enabled = false;\n            effectComposer.addPass(downSamplingPass2);\n          }\n        }\n        return [effectComposer, normalPass2, downSamplingPass2];\n      }, [\n        camera,\n        gl,\n        depthBuffer,\n        stencilBuffer,\n        multisampling,\n        frameBufferType,\n        scene,\n        enableNormalPass,\n        resolutionScale\n      ]);\n      (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => composer == null ? void 0 : composer.setSize(size.width, size.height), [composer, size]);\n      (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F)(\n        (_, delta) => {\n          if (enabled) {\n            const currentAutoClear = gl.autoClear;\n            gl.autoClear = autoClear;\n            if (stencilBuffer && !autoClear)\n              gl.clearStencil();\n            composer.render(delta);\n            gl.autoClear = currentAutoClear;\n          }\n        },\n        enabled ? renderPriority : 0\n      );\n      const group = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n      (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        var _a;\n        const passes = [];\n        const groupInstance = (_a = group.current) == null ? void 0 : _a.__r3f;\n        if (groupInstance && composer) {\n          const children2 = groupInstance.objects;\n          for (let i = 0; i < children2.length; i++) {\n            const child = children2[i];\n            if (child instanceof postprocessing__WEBPACK_IMPORTED_MODULE_2__.Effect) {\n              const effects = [child];\n              if (!isConvolution(child)) {\n                let next = null;\n                while ((next = children2[i + 1]) instanceof postprocessing__WEBPACK_IMPORTED_MODULE_2__.Effect) {\n                  if (isConvolution(next))\n                    break;\n                  effects.push(next);\n                  i++;\n                }\n              }\n              const pass = new postprocessing__WEBPACK_IMPORTED_MODULE_2__.EffectPass(camera, ...effects);\n              passes.push(pass);\n            } else if (child instanceof postprocessing__WEBPACK_IMPORTED_MODULE_2__.Pass) {\n              passes.push(child);\n            }\n          }\n          for (const pass of passes)\n            composer == null ? void 0 : composer.addPass(pass);\n          if (normalPass)\n            normalPass.enabled = true;\n          if (downSamplingPass)\n            downSamplingPass.enabled = true;\n        }\n        return () => {\n          for (const pass of passes)\n            composer == null ? void 0 : composer.removePass(pass);\n          if (normalPass)\n            normalPass.enabled = false;\n          if (downSamplingPass)\n            downSamplingPass.enabled = false;\n        };\n      }, [composer, children, camera, normalPass, downSamplingPass]);\n      (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        const currentTonemapping = gl.toneMapping;\n        gl.toneMapping = three__WEBPACK_IMPORTED_MODULE_3__.NoToneMapping;\n        return () => {\n          gl.toneMapping = currentTonemapping;\n        };\n      }, [gl]);\n      const state = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n        () => ({ composer, normalPass, downSamplingPass, resolutionScale, camera, scene }),\n        [composer, normalPass, downSamplingPass, resolutionScale, camera, scene]\n      );\n      (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, () => composer, [composer]);\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(EffectComposerContext.Provider, { value: state, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"group\", { ref: group, children }) });\n    }\n  )\n);\n\n//# sourceMappingURL=EffectComposer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/EffectComposer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Autofocus.js":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Autofocus.js ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Autofocus: () => (/* binding */ Autofocus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var maath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! maath */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/maath.esm.js\");\n/* harmony import */ var _DepthOfField_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DepthOfField.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/DepthOfField.js\");\n/* harmony import */ var _EffectComposer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../EffectComposer.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/EffectComposer.js\");\n\n\n\n\n\n\n\n\nconst Autofocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n  ({ target = void 0, mouse: followMouse = false, debug = void 0, manual = false, smoothTime = 0.25, ...props }, fref) => {\n    const dofRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hitpointRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const targetRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.D)(({ scene: scene2 }) => scene2);\n    const pointer = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.D)(({ pointer: pointer2 }) => pointer2);\n    const { composer, camera } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_EffectComposer_js__WEBPACK_IMPORTED_MODULE_4__.EffectComposerContext);\n    const [depthPickingPass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => new postprocessing__WEBPACK_IMPORTED_MODULE_5__.DepthPickingPass());\n    const [copyPass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => new postprocessing__WEBPACK_IMPORTED_MODULE_5__.CopyPass());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n      composer.addPass(depthPickingPass);\n      composer.addPass(copyPass);\n      return () => {\n        composer.removePass(depthPickingPass);\n        composer.removePass(copyPass);\n      };\n    }, [composer, depthPickingPass, copyPass]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n      return () => {\n        depthPickingPass.dispose();\n        copyPass.dispose();\n      };\n    }, [depthPickingPass, copyPass]);\n    const [hitpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(0, 0, 0));\n    const [ndc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(0, 0, 0));\n    const getHit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(\n      async (x, y) => {\n        ndc.x = x;\n        ndc.y = y;\n        ndc.z = await depthPickingPass.readDepth(ndc);\n        ndc.z = ndc.z * 2 - 1;\n        const hit = 1 - ndc.z > 1e-7;\n        return hit ? ndc.unproject(camera) : false;\n      },\n      [ndc, depthPickingPass, camera]\n    );\n    const update = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(\n      async (delta, updateTarget = true) => {\n        var _a;\n        if (target) {\n          hitpoint.set(...target);\n        } else {\n          const { x, y } = followMouse ? pointer : { x: 0, y: 0 };\n          const hit = await getHit(x, y);\n          if (hit)\n            hitpoint.copy(hit);\n        }\n        if (updateTarget && ((_a = dofRef.current) == null ? void 0 : _a.target)) {\n          if (smoothTime > 0 && delta > 0) {\n            maath__WEBPACK_IMPORTED_MODULE_2__.easing.damp3(dofRef.current.target, hitpoint, smoothTime, delta);\n          } else {\n            dofRef.current.target.copy(hitpoint);\n          }\n        }\n      },\n      [target, hitpoint, followMouse, getHit, smoothTime, pointer]\n    );\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.F)(async (_, delta) => {\n      var _a;\n      if (!manual) {\n        update(delta);\n      }\n      if (hitpointRef.current) {\n        hitpointRef.current.position.copy(hitpoint);\n      }\n      if (targetRef.current && ((_a = dofRef.current) == null ? void 0 : _a.target)) {\n        targetRef.current.position.copy(dofRef.current.target);\n      }\n    });\n    const api = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n      () => ({\n        dofRef,\n        hitpoint,\n        update\n      }),\n      [hitpoint, update]\n    );\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(fref, () => api, [api]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [\n      debug ? (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.h)(\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"mesh\", { ref: hitpointRef, children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"sphereGeometry\", { args: [debug, 16, 16] }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meshBasicMaterial\", { color: \"#00ff00\", opacity: 1, transparent: true, depthWrite: false })\n          ] }),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"mesh\", { ref: targetRef, children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"sphereGeometry\", { args: [debug / 2, 16, 16] }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"meshBasicMaterial\", { color: \"#00ff00\", opacity: 0.5, transparent: true, depthWrite: false })\n          ] })\n        ] }),\n        scene\n      ) : null,\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DepthOfField_js__WEBPACK_IMPORTED_MODULE_7__.DepthOfField, { ref: dofRef, ...props, target: hitpoint })\n    ] });\n  }\n);\n\n//# sourceMappingURL=Autofocus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Autofocus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Bloom.js":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Bloom.js ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bloom: () => (/* binding */ Bloom)\n/* harmony export */ });\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js\");\n\n\nconst Bloom = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.wrapEffect)(postprocessing__WEBPACK_IMPORTED_MODULE_1__.BloomEffect, {\n  blendFunction: postprocessing__WEBPACK_IMPORTED_MODULE_1__.BlendFunction.ADD\n});\n\n//# sourceMappingURL=Bloom.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK3Bvc3Rwcm9jZXNzaW5nQDIuMTkuMV9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yX2VtdW9jMnZ5cmthZ3diNGJzY3hnZG4zc2V5L25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvcG9zdHByb2Nlc3NpbmcvZGlzdC9lZmZlY3RzL0Jsb29tLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0RDtBQUNwQjtBQUN4QyxjQUFjLG9EQUFVLENBQUMsdURBQVc7QUFDcEMsaUJBQWlCLHlEQUFhO0FBQzlCLENBQUM7QUFHQztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByZWFjdC10aHJlZStwb3N0cHJvY2Vzc2luZ0AyLjE5LjFfQHJlYWN0LXRocmVlK2ZpYmVyQDguMTguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcl9lbXVvYzJ2eXJrYWd3YjRic2N4Z2RuM3NleS9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL3Bvc3Rwcm9jZXNzaW5nL2Rpc3QvZWZmZWN0cy9CbG9vbS5qcz8xM2EyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJsZW5kRnVuY3Rpb24sIEJsb29tRWZmZWN0IH0gZnJvbSBcInBvc3Rwcm9jZXNzaW5nXCI7XG5pbXBvcnQgeyB3cmFwRWZmZWN0IH0gZnJvbSBcIi4uL3V0aWwuanNcIjtcbmNvbnN0IEJsb29tID0gd3JhcEVmZmVjdChCbG9vbUVmZmVjdCwge1xuICBibGVuZEZ1bmN0aW9uOiBCbGVuZEZ1bmN0aW9uLkFERFxufSk7XG5leHBvcnQge1xuICBCbG9vbVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUJsb29tLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Bloom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/DepthOfField.js":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/DepthOfField.js ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DepthOfField: () => (/* binding */ DepthOfField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _EffectComposer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../EffectComposer.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/EffectComposer.js\");\n\n\n\n\n\nconst DepthOfField = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function DepthOfField2({\n  blendFunction,\n  worldFocusDistance,\n  worldFocusRange,\n  focusDistance,\n  focusRange,\n  focalLength,\n  bokehScale,\n  resolutionScale,\n  resolutionX,\n  resolutionY,\n  width,\n  height,\n  target,\n  depthTexture,\n  ...props\n}, ref) {\n  const { camera } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_EffectComposer_js__WEBPACK_IMPORTED_MODULE_2__.EffectComposerContext);\n  const autoFocus = target != null;\n  const effect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    const effect2 = new postprocessing__WEBPACK_IMPORTED_MODULE_3__.DepthOfFieldEffect(camera, {\n      blendFunction,\n      worldFocusDistance,\n      worldFocusRange,\n      focusDistance,\n      focusRange,\n      focalLength,\n      bokehScale,\n      resolutionScale,\n      resolutionX,\n      resolutionY,\n      width,\n      height\n    });\n    if (autoFocus)\n      effect2.target = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n    if (depthTexture)\n      effect2.setDepthTexture(depthTexture.texture, depthTexture.packing);\n    const maskPass = effect2.maskPass;\n    maskPass.maskFunction = postprocessing__WEBPACK_IMPORTED_MODULE_3__.MaskFunction.MULTIPLY_RGB_SET_ALPHA;\n    return effect2;\n  }, [\n    camera,\n    blendFunction,\n    worldFocusDistance,\n    worldFocusRange,\n    focusDistance,\n    focusRange,\n    focalLength,\n    bokehScale,\n    resolutionScale,\n    resolutionX,\n    resolutionY,\n    width,\n    height,\n    autoFocus,\n    depthTexture\n  ]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    return () => {\n      effect.dispose();\n    };\n  }, [effect]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"primitive\", { ...props, ref, object: effect, target });\n});\n\n//# sourceMappingURL=DepthOfField.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/DepthOfField.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/ToneMapping.js":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/ToneMapping.js ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToneMapping: () => (/* binding */ ToneMapping)\n/* harmony export */ });\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js\");\n\n\nconst ToneMapping = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.wrapEffect)(postprocessing__WEBPACK_IMPORTED_MODULE_1__.ToneMappingEffect);\n\n//# sourceMappingURL=ToneMapping.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK3Bvc3Rwcm9jZXNzaW5nQDIuMTkuMV9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yX2VtdW9jMnZ5cmthZ3diNGJzY3hnZG4zc2V5L25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvcG9zdHByb2Nlc3NpbmcvZGlzdC9lZmZlY3RzL1RvbmVNYXBwaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtRDtBQUNYO0FBQ3hDLG9CQUFvQixvREFBVSxDQUFDLDZEQUFpQjtBQUc5QztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByZWFjdC10aHJlZStwb3N0cHJvY2Vzc2luZ0AyLjE5LjFfQHJlYWN0LXRocmVlK2ZpYmVyQDguMTguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcl9lbXVvYzJ2eXJrYWd3YjRic2N4Z2RuM3NleS9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL3Bvc3Rwcm9jZXNzaW5nL2Rpc3QvZWZmZWN0cy9Ub25lTWFwcGluZy5qcz9kMTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRvbmVNYXBwaW5nRWZmZWN0IH0gZnJvbSBcInBvc3Rwcm9jZXNzaW5nXCI7XG5pbXBvcnQgeyB3cmFwRWZmZWN0IH0gZnJvbSBcIi4uL3V0aWwuanNcIjtcbmNvbnN0IFRvbmVNYXBwaW5nID0gd3JhcEVmZmVjdChUb25lTWFwcGluZ0VmZmVjdCk7XG5leHBvcnQge1xuICBUb25lTWFwcGluZ1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVRvbmVNYXBwaW5nLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/ToneMapping.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Vignette.js":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Vignette.js ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Vignette: () => (/* binding */ Vignette)\n/* harmony export */ });\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/.pnpm/postprocessing@6.37.0_three@0.167.1/node_modules/postprocessing/build/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js\");\n\n\nconst Vignette = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.wrapEffect)(postprocessing__WEBPACK_IMPORTED_MODULE_1__.VignetteEffect);\n\n//# sourceMappingURL=Vignette.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK3Bvc3Rwcm9jZXNzaW5nQDIuMTkuMV9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yX2VtdW9jMnZ5cmthZ3diNGJzY3hnZG4zc2V5L25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvcG9zdHByb2Nlc3NpbmcvZGlzdC9lZmZlY3RzL1ZpZ25ldHRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNSO0FBQ3hDLGlCQUFpQixvREFBVSxDQUFDLDBEQUFjO0FBR3hDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK3Bvc3Rwcm9jZXNzaW5nQDIuMTkuMV9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yX2VtdW9jMnZ5cmthZ3diNGJzY3hnZG4zc2V5L25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvcG9zdHByb2Nlc3NpbmcvZGlzdC9lZmZlY3RzL1ZpZ25ldHRlLmpzP2M0NzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVmlnbmV0dGVFZmZlY3QgfSBmcm9tIFwicG9zdHByb2Nlc3NpbmdcIjtcbmltcG9ydCB7IHdyYXBFZmZlY3QgfSBmcm9tIFwiLi4vdXRpbC5qc1wiO1xuY29uc3QgVmlnbmV0dGUgPSB3cmFwRWZmZWN0KFZpZ25ldHRlRWZmZWN0KTtcbmV4cG9ydCB7XG4gIFZpZ25ldHRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9VmlnbmV0dGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/effects/Vignette.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRef: () => (/* binding */ resolveRef),\n/* harmony export */   useVector2: () => (/* binding */ useVector2),\n/* harmony export */   wrapEffect: () => (/* binding */ wrapEffect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\nconst resolveRef = (ref) => typeof ref === \"object\" && ref != null && \"current\" in ref ? ref.current : ref;\nlet i = 0;\nconst components = /* @__PURE__ */ new WeakMap();\nconst wrapEffect = (effect, defaults) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function Effect({ blendFunction = defaults == null ? void 0 : defaults.blendFunction, opacity = defaults == null ? void 0 : defaults.opacity, ...props }, ref) {\n  let Component = components.get(effect);\n  if (!Component) {\n    const key = `@react-three/postprocessing/${effect.name}-${i++}`;\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.e)({ [key]: effect });\n    components.set(effect, Component = key);\n  }\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)((state) => state.camera);\n  const args = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(\n    () => {\n      var _a, _b;\n      return [...(_a = defaults == null ? void 0 : defaults.args) != null ? _a : [], ...(_b = props.args) != null ? _b : [{ ...defaults, ...props }]];\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(props)]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    Component,\n    {\n      camera,\n      \"blendMode-blendFunction\": blendFunction,\n      \"blendMode-opacity-value\": opacity,\n      ...props,\n      ref,\n      args\n    }\n  );\n});\nconst useVector2 = (props, key) => {\n  const value = props[key];\n  return react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    if (typeof value === \"number\")\n      return new three__WEBPACK_IMPORTED_MODULE_3__.Vector2(value, value);\n    else if (value)\n      return new three__WEBPACK_IMPORTED_MODULE_3__.Vector2(...value);\n    else\n      return new three__WEBPACK_IMPORTED_MODULE_3__.Vector2();\n  }, [value]);\n};\n\n//# sourceMappingURL=util.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+postprocessing@2.19.1_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__r_emuoc2vyrkagwb4bscxgdn3sey/node_modules/@react-three/postprocessing/dist/util.js\n");

/***/ })

};
;