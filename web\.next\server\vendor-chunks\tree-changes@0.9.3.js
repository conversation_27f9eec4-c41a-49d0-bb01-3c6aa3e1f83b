"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tree-changes@0.9.3";
exports.ids = ["vendor-chunks/tree-changes@0.9.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/helpers.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/helpers.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canHaveLength: () => (/* binding */ canHaveLength),\n/* harmony export */   checkEquality: () => (/* binding */ checkEquality),\n/* harmony export */   compareNumbers: () => (/* binding */ compareNumbers),\n/* harmony export */   compareValues: () => (/* binding */ compareValues),\n/* harmony export */   getIterables: () => (/* binding */ getIterables),\n/* harmony export */   hasEntry: () => (/* binding */ hasEntry),\n/* harmony export */   hasExtraKeys: () => (/* binding */ hasExtraKeys),\n/* harmony export */   hasValue: () => (/* binding */ hasValue),\n/* harmony export */   includesOrEqualsTo: () => (/* binding */ includesOrEqualsTo),\n/* harmony export */   isEqualPredicate: () => (/* binding */ isEqualPredicate),\n/* harmony export */   isSameType: () => (/* binding */ isSameType),\n/* harmony export */   nested: () => (/* binding */ nested)\n/* harmony export */ });\n/* harmony import */ var _gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @gilbarbara/deep-equal */ \"(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/index.js\");\n/* harmony import */ var is_lite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-lite */ \"(ssr)/./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js\");\n\n\nfunction canHaveLength() {\n    var arguments_ = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        arguments_[_i] = arguments[_i];\n    }\n    return arguments_.every(function (d) { return is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(d); });\n}\nfunction checkEquality(left, right, value) {\n    if (!isSameType(left, right)) {\n        return false;\n    }\n    if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array)) {\n        return !left.some(hasValue(value)) && right.some(hasValue(value));\n    }\n    /* istanbul ignore else */\n    if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n        return (!Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value)));\n    }\n    return right === value;\n}\nfunction compareNumbers(previousData, data, options) {\n    var actual = options.actual, key = options.key, previous = options.previous, type = options.type;\n    var left = nested(previousData, key);\n    var right = nested(data, key);\n    var changed = [left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number) && (type === 'increased' ? left < right : left > right);\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].undefined(actual)) {\n        changed = changed && right === actual;\n    }\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].undefined(previous)) {\n        changed = changed && left === previous;\n    }\n    return changed;\n}\nfunction compareValues(previousData, data, options) {\n    var key = options.key, type = options.type, value = options.value;\n    var left = nested(previousData, key);\n    var right = nested(data, key);\n    var primary = type === 'added' ? left : right;\n    var secondary = type === 'added' ? right : left;\n    // console.log({ primary, secondary });\n    if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].nullOrUndefined(value)) {\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(primary)) {\n            // check if nested data matches\n            if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(primary) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(primary)) {\n                return checkEquality(primary, secondary, value);\n            }\n        }\n        else {\n            return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(secondary, value);\n        }\n        return false;\n    }\n    if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array)) {\n        return !secondary.every(isEqualPredicate(primary));\n    }\n    if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n        return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n    }\n    return (![left, right].every(function (d) { return is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].primitive(d) && is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(d); }) &&\n        (type === 'added'\n            ? !is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(left) && is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(right)\n            : is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(left) && !is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(right)));\n}\nfunction getIterables(previousData, data, _a) {\n    var _b = _a === void 0 ? {} : _a, key = _b.key;\n    var left = nested(previousData, key);\n    var right = nested(data, key);\n    if (!isSameType(left, right)) {\n        throw new TypeError('Inputs have different types');\n    }\n    if (!canHaveLength(left, right)) {\n        throw new TypeError(\"Inputs don't have length\");\n    }\n    if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n        left = Object.keys(left);\n        right = Object.keys(right);\n    }\n    return [left, right];\n}\nfunction hasEntry(input) {\n    return function (_a) {\n        var key = _a[0], value = _a[1];\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(input)) {\n            return ((0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value) ||\n                input.some(function (d) { return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value) || (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(value) && isEqualPredicate(value)(d)); }));\n        }\n        /* istanbul ignore else */\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(input) && input[key]) {\n            return !!input[key] && (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input[key], value);\n        }\n        return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value);\n    };\n}\nfunction hasExtraKeys(left, right) {\n    return right.some(function (d) { return !left.includes(d); });\n}\nfunction hasValue(input) {\n    return function (value) {\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(input)) {\n            return input.some(function (d) { return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value) || (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(value) && isEqualPredicate(value)(d)); });\n        }\n        return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, value);\n    };\n}\nfunction includesOrEqualsTo(previousValue, value) {\n    return is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(previousValue)\n        ? previousValue.some(function (d) { return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value); })\n        : (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(previousValue, value);\n}\nfunction isEqualPredicate(data) {\n    return function (value) { return data.some(function (d) { return (0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, value); }); };\n}\nfunction isSameType() {\n    var arguments_ = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        arguments_[_i] = arguments[_i];\n    }\n    return (arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array) ||\n        arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number) ||\n        arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject) ||\n        arguments_.every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string));\n}\nfunction nested(data, property) {\n    /* istanbul ignore else */\n    if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(data) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(data)) {\n        /* istanbul ignore else */\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].string(property)) {\n            var props = property.split('.');\n            return props.reduce(function (acc, d) { return acc && acc[d]; }, data);\n        }\n        /* istanbul ignore else */\n        if (is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].number(property)) {\n            return data[property];\n        }\n        return data;\n    }\n    return data;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdHJlZS1jaGFuZ2VzQDAuOS4zL25vZGVfbW9kdWxlcy90cmVlLWNoYW5nZXMvZXNtL2hlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFDbEI7QUFDbEI7QUFDUDtBQUNBLHFCQUFxQix1QkFBdUI7QUFDNUM7QUFDQTtBQUNBLDJDQUEyQyxPQUFPLCtDQUFFLGNBQWMsK0NBQUUsYUFBYSwrQ0FBRSxrQkFBa0I7QUFDckc7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QiwrQ0FBRTtBQUM5QjtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQUU7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQywrQ0FBRTtBQUN4QyxTQUFTLCtDQUFFO0FBQ1g7QUFDQTtBQUNBLFNBQVMsK0NBQUU7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixvQkFBb0I7QUFDekMsU0FBUywrQ0FBRTtBQUNYLFlBQVksK0NBQUU7QUFDZDtBQUNBLGdCQUFnQiwrQ0FBRSxtQkFBbUIsK0NBQUU7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa0VBQUs7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtDQUFFO0FBQzlCO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQUU7QUFDOUI7QUFDQTtBQUNBLGdEQUFnRCxPQUFPLCtDQUFFLGlCQUFpQiwrQ0FBRSxjQUFjO0FBQzFGO0FBQ0EsZUFBZSwrQ0FBRSxrQkFBa0IsK0NBQUU7QUFDckMsY0FBYywrQ0FBRSxtQkFBbUIsK0NBQUU7QUFDckM7QUFDTztBQUNQLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtDQUFFO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxZQUFZLCtDQUFFO0FBQ2Qsb0JBQW9CLGtFQUFLO0FBQ3pCLDBDQUEwQyxPQUFPLGtFQUFLLGVBQWUsK0NBQUUsK0NBQStDO0FBQ3RIO0FBQ0E7QUFDQSxZQUFZLCtDQUFFO0FBQ2QsbUNBQW1DLGtFQUFLO0FBQ3hDO0FBQ0EsZUFBZSxrRUFBSztBQUNwQjtBQUNBO0FBQ087QUFDUCxxQ0FBcUMsMkJBQTJCO0FBQ2hFO0FBQ087QUFDUDtBQUNBLFlBQVksK0NBQUU7QUFDZCw2Q0FBNkMsT0FBTyxrRUFBSyxlQUFlLCtDQUFFLCtDQUErQztBQUN6SDtBQUNBLGVBQWUsa0VBQUs7QUFDcEI7QUFDQTtBQUNPO0FBQ1AsV0FBVywrQ0FBRTtBQUNiLDRDQUE0QyxPQUFPLGtFQUFLLGFBQWE7QUFDckUsVUFBVSxrRUFBSztBQUNmO0FBQ087QUFDUCw4QkFBOEIsZ0NBQWdDLE9BQU8sa0VBQUssYUFBYTtBQUN2RjtBQUNPO0FBQ1A7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQSw2QkFBNkIsK0NBQUU7QUFDL0IseUJBQXlCLCtDQUFFO0FBQzNCLHlCQUF5QiwrQ0FBRTtBQUMzQix5QkFBeUIsK0NBQUU7QUFDM0I7QUFDTztBQUNQO0FBQ0EsUUFBUSwrQ0FBRSxzQkFBc0IsK0NBQUU7QUFDbEM7QUFDQSxZQUFZLCtDQUFFO0FBQ2Q7QUFDQSxvREFBb0QsdUJBQXVCO0FBQzNFO0FBQ0E7QUFDQSxZQUFZLCtDQUFFO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vdHJlZS1jaGFuZ2VzQDAuOS4zL25vZGVfbW9kdWxlcy90cmVlLWNoYW5nZXMvZXNtL2hlbHBlcnMuanM/NGQ2YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXF1YWwgZnJvbSAnQGdpbGJhcmJhcmEvZGVlcC1lcXVhbCc7XG5pbXBvcnQgaXMgZnJvbSAnaXMtbGl0ZSc7XG5leHBvcnQgZnVuY3Rpb24gY2FuSGF2ZUxlbmd0aCgpIHtcbiAgICB2YXIgYXJndW1lbnRzXyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIGFyZ3VtZW50c19bX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgcmV0dXJuIGFyZ3VtZW50c18uZXZlcnkoZnVuY3Rpb24gKGQpIHsgcmV0dXJuIGlzLnN0cmluZyhkKSB8fCBpcy5hcnJheShkKSB8fCBpcy5wbGFpbk9iamVjdChkKTsgfSk7XG59XG5leHBvcnQgZnVuY3Rpb24gY2hlY2tFcXVhbGl0eShsZWZ0LCByaWdodCwgdmFsdWUpIHtcbiAgICBpZiAoIWlzU2FtZVR5cGUobGVmdCwgcmlnaHQpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKFtsZWZ0LCByaWdodF0uZXZlcnkoaXMuYXJyYXkpKSB7XG4gICAgICAgIHJldHVybiAhbGVmdC5zb21lKGhhc1ZhbHVlKHZhbHVlKSkgJiYgcmlnaHQuc29tZShoYXNWYWx1ZSh2YWx1ZSkpO1xuICAgIH1cbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAqL1xuICAgIGlmIChbbGVmdCwgcmlnaHRdLmV2ZXJ5KGlzLnBsYWluT2JqZWN0KSkge1xuICAgICAgICByZXR1cm4gKCFPYmplY3QuZW50cmllcyhsZWZ0KS5zb21lKGhhc0VudHJ5KHZhbHVlKSkgJiYgT2JqZWN0LmVudHJpZXMocmlnaHQpLnNvbWUoaGFzRW50cnkodmFsdWUpKSk7XG4gICAgfVxuICAgIHJldHVybiByaWdodCA9PT0gdmFsdWU7XG59XG5leHBvcnQgZnVuY3Rpb24gY29tcGFyZU51bWJlcnMocHJldmlvdXNEYXRhLCBkYXRhLCBvcHRpb25zKSB7XG4gICAgdmFyIGFjdHVhbCA9IG9wdGlvbnMuYWN0dWFsLCBrZXkgPSBvcHRpb25zLmtleSwgcHJldmlvdXMgPSBvcHRpb25zLnByZXZpb3VzLCB0eXBlID0gb3B0aW9ucy50eXBlO1xuICAgIHZhciBsZWZ0ID0gbmVzdGVkKHByZXZpb3VzRGF0YSwga2V5KTtcbiAgICB2YXIgcmlnaHQgPSBuZXN0ZWQoZGF0YSwga2V5KTtcbiAgICB2YXIgY2hhbmdlZCA9IFtsZWZ0LCByaWdodF0uZXZlcnkoaXMubnVtYmVyKSAmJiAodHlwZSA9PT0gJ2luY3JlYXNlZCcgPyBsZWZ0IDwgcmlnaHQgOiBsZWZ0ID4gcmlnaHQpO1xuICAgIGlmICghaXMudW5kZWZpbmVkKGFjdHVhbCkpIHtcbiAgICAgICAgY2hhbmdlZCA9IGNoYW5nZWQgJiYgcmlnaHQgPT09IGFjdHVhbDtcbiAgICB9XG4gICAgaWYgKCFpcy51bmRlZmluZWQocHJldmlvdXMpKSB7XG4gICAgICAgIGNoYW5nZWQgPSBjaGFuZ2VkICYmIGxlZnQgPT09IHByZXZpb3VzO1xuICAgIH1cbiAgICByZXR1cm4gY2hhbmdlZDtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjb21wYXJlVmFsdWVzKHByZXZpb3VzRGF0YSwgZGF0YSwgb3B0aW9ucykge1xuICAgIHZhciBrZXkgPSBvcHRpb25zLmtleSwgdHlwZSA9IG9wdGlvbnMudHlwZSwgdmFsdWUgPSBvcHRpb25zLnZhbHVlO1xuICAgIHZhciBsZWZ0ID0gbmVzdGVkKHByZXZpb3VzRGF0YSwga2V5KTtcbiAgICB2YXIgcmlnaHQgPSBuZXN0ZWQoZGF0YSwga2V5KTtcbiAgICB2YXIgcHJpbWFyeSA9IHR5cGUgPT09ICdhZGRlZCcgPyBsZWZ0IDogcmlnaHQ7XG4gICAgdmFyIHNlY29uZGFyeSA9IHR5cGUgPT09ICdhZGRlZCcgPyByaWdodCA6IGxlZnQ7XG4gICAgLy8gY29uc29sZS5sb2coeyBwcmltYXJ5LCBzZWNvbmRhcnkgfSk7XG4gICAgaWYgKCFpcy5udWxsT3JVbmRlZmluZWQodmFsdWUpKSB7XG4gICAgICAgIGlmIChpcy5kZWZpbmVkKHByaW1hcnkpKSB7XG4gICAgICAgICAgICAvLyBjaGVjayBpZiBuZXN0ZWQgZGF0YSBtYXRjaGVzXG4gICAgICAgICAgICBpZiAoaXMuYXJyYXkocHJpbWFyeSkgfHwgaXMucGxhaW5PYmplY3QocHJpbWFyeSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hlY2tFcXVhbGl0eShwcmltYXJ5LCBzZWNvbmRhcnksIHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBlcXVhbChzZWNvbmRhcnksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChbbGVmdCwgcmlnaHRdLmV2ZXJ5KGlzLmFycmF5KSkge1xuICAgICAgICByZXR1cm4gIXNlY29uZGFyeS5ldmVyeShpc0VxdWFsUHJlZGljYXRlKHByaW1hcnkpKTtcbiAgICB9XG4gICAgaWYgKFtsZWZ0LCByaWdodF0uZXZlcnkoaXMucGxhaW5PYmplY3QpKSB7XG4gICAgICAgIHJldHVybiBoYXNFeHRyYUtleXMoT2JqZWN0LmtleXMocHJpbWFyeSksIE9iamVjdC5rZXlzKHNlY29uZGFyeSkpO1xuICAgIH1cbiAgICByZXR1cm4gKCFbbGVmdCwgcmlnaHRdLmV2ZXJ5KGZ1bmN0aW9uIChkKSB7IHJldHVybiBpcy5wcmltaXRpdmUoZCkgJiYgaXMuZGVmaW5lZChkKTsgfSkgJiZcbiAgICAgICAgKHR5cGUgPT09ICdhZGRlZCdcbiAgICAgICAgICAgID8gIWlzLmRlZmluZWQobGVmdCkgJiYgaXMuZGVmaW5lZChyaWdodClcbiAgICAgICAgICAgIDogaXMuZGVmaW5lZChsZWZ0KSAmJiAhaXMuZGVmaW5lZChyaWdodCkpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRJdGVyYWJsZXMocHJldmlvdXNEYXRhLCBkYXRhLCBfYSkge1xuICAgIHZhciBfYiA9IF9hID09PSB2b2lkIDAgPyB7fSA6IF9hLCBrZXkgPSBfYi5rZXk7XG4gICAgdmFyIGxlZnQgPSBuZXN0ZWQocHJldmlvdXNEYXRhLCBrZXkpO1xuICAgIHZhciByaWdodCA9IG5lc3RlZChkYXRhLCBrZXkpO1xuICAgIGlmICghaXNTYW1lVHlwZShsZWZ0LCByaWdodCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignSW5wdXRzIGhhdmUgZGlmZmVyZW50IHR5cGVzJyk7XG4gICAgfVxuICAgIGlmICghY2FuSGF2ZUxlbmd0aChsZWZ0LCByaWdodCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIklucHV0cyBkb24ndCBoYXZlIGxlbmd0aFwiKTtcbiAgICB9XG4gICAgaWYgKFtsZWZ0LCByaWdodF0uZXZlcnkoaXMucGxhaW5PYmplY3QpKSB7XG4gICAgICAgIGxlZnQgPSBPYmplY3Qua2V5cyhsZWZ0KTtcbiAgICAgICAgcmlnaHQgPSBPYmplY3Qua2V5cyhyaWdodCk7XG4gICAgfVxuICAgIHJldHVybiBbbGVmdCwgcmlnaHRdO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGhhc0VudHJ5KGlucHV0KSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIChfYSkge1xuICAgICAgICB2YXIga2V5ID0gX2FbMF0sIHZhbHVlID0gX2FbMV07XG4gICAgICAgIGlmIChpcy5hcnJheShpbnB1dCkpIHtcbiAgICAgICAgICAgIHJldHVybiAoZXF1YWwoaW5wdXQsIHZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIGlucHV0LnNvbWUoZnVuY3Rpb24gKGQpIHsgcmV0dXJuIGVxdWFsKGQsIHZhbHVlKSB8fCAoaXMuYXJyYXkodmFsdWUpICYmIGlzRXF1YWxQcmVkaWNhdGUodmFsdWUpKGQpKTsgfSkpO1xuICAgICAgICB9XG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBlbHNlICovXG4gICAgICAgIGlmIChpcy5wbGFpbk9iamVjdChpbnB1dCkgJiYgaW5wdXRba2V5XSkge1xuICAgICAgICAgICAgcmV0dXJuICEhaW5wdXRba2V5XSAmJiBlcXVhbChpbnB1dFtrZXldLCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGVxdWFsKGlucHV0LCB2YWx1ZSk7XG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBoYXNFeHRyYUtleXMobGVmdCwgcmlnaHQpIHtcbiAgICByZXR1cm4gcmlnaHQuc29tZShmdW5jdGlvbiAoZCkgeyByZXR1cm4gIWxlZnQuaW5jbHVkZXMoZCk7IH0pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGhhc1ZhbHVlKGlucHV0KSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICh2YWx1ZSkge1xuICAgICAgICBpZiAoaXMuYXJyYXkoaW5wdXQpKSB7XG4gICAgICAgICAgICByZXR1cm4gaW5wdXQuc29tZShmdW5jdGlvbiAoZCkgeyByZXR1cm4gZXF1YWwoZCwgdmFsdWUpIHx8IChpcy5hcnJheSh2YWx1ZSkgJiYgaXNFcXVhbFByZWRpY2F0ZSh2YWx1ZSkoZCkpOyB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZXF1YWwoaW5wdXQsIHZhbHVlKTtcbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGluY2x1ZGVzT3JFcXVhbHNUbyhwcmV2aW91c1ZhbHVlLCB2YWx1ZSkge1xuICAgIHJldHVybiBpcy5hcnJheShwcmV2aW91c1ZhbHVlKVxuICAgICAgICA/IHByZXZpb3VzVmFsdWUuc29tZShmdW5jdGlvbiAoZCkgeyByZXR1cm4gZXF1YWwoZCwgdmFsdWUpOyB9KVxuICAgICAgICA6IGVxdWFsKHByZXZpb3VzVmFsdWUsIHZhbHVlKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc0VxdWFsUHJlZGljYXRlKGRhdGEpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHZhbHVlKSB7IHJldHVybiBkYXRhLnNvbWUoZnVuY3Rpb24gKGQpIHsgcmV0dXJuIGVxdWFsKGQsIHZhbHVlKTsgfSk7IH07XG59XG5leHBvcnQgZnVuY3Rpb24gaXNTYW1lVHlwZSgpIHtcbiAgICB2YXIgYXJndW1lbnRzXyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIGFyZ3VtZW50c19bX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgcmV0dXJuIChhcmd1bWVudHNfLmV2ZXJ5KGlzLmFycmF5KSB8fFxuICAgICAgICBhcmd1bWVudHNfLmV2ZXJ5KGlzLm51bWJlcikgfHxcbiAgICAgICAgYXJndW1lbnRzXy5ldmVyeShpcy5wbGFpbk9iamVjdCkgfHxcbiAgICAgICAgYXJndW1lbnRzXy5ldmVyeShpcy5zdHJpbmcpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBuZXN0ZWQoZGF0YSwgcHJvcGVydHkpIHtcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAqL1xuICAgIGlmIChpcy5wbGFpbk9iamVjdChkYXRhKSB8fCBpcy5hcnJheShkYXRhKSkge1xuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAqL1xuICAgICAgICBpZiAoaXMuc3RyaW5nKHByb3BlcnR5KSkge1xuICAgICAgICAgICAgdmFyIHByb3BzID0gcHJvcGVydHkuc3BsaXQoJy4nKTtcbiAgICAgICAgICAgIHJldHVybiBwcm9wcy5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgZCkgeyByZXR1cm4gYWNjICYmIGFjY1tkXTsgfSwgZGF0YSk7XG4gICAgICAgIH1cbiAgICAgICAgLyogaXN0YW5idWwgaWdub3JlIGVsc2UgKi9cbiAgICAgICAgaWYgKGlzLm51bWJlcihwcm9wZXJ0eSkpIHtcbiAgICAgICAgICAgIHJldHVybiBkYXRhW3Byb3BlcnR5XTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZGF0YTtcbiAgICB9XG4gICAgcmV0dXJuIGRhdGE7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWxwZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ treeChanges)\n/* harmony export */ });\n/* harmony import */ var _gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @gilbarbara/deep-equal */ \"(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/index.js\");\n/* harmony import */ var is_lite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-lite */ \"(ssr)/./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/helpers.js\");\n\n\n\nfunction treeChanges(previousData, data) {\n    if ([previousData, data].some(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].nullOrUndefined)) {\n        throw new Error('Missing required parameters');\n    }\n    if (![previousData, data].every(function (d) { return is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject(d) || is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array(d); })) {\n        throw new Error('Expected plain objects or array');\n    }\n    var added = function (key, value) {\n        try {\n            return (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.compareValues)(previousData, data, { key: key, type: 'added', value: value });\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var changed = function (key, actual, previous) {\n        try {\n            var left = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.nested)(previousData, key);\n            var right = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.nested)(data, key);\n            var hasActual = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(actual);\n            var hasPrevious = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(previous);\n            if (hasActual || hasPrevious) {\n                var leftComparator = hasPrevious\n                    ? (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.includesOrEqualsTo)(previous, left)\n                    : !(0,_helpers__WEBPACK_IMPORTED_MODULE_1__.includesOrEqualsTo)(actual, left);\n                var rightComparator = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.includesOrEqualsTo)(actual, right);\n                return leftComparator && rightComparator;\n            }\n            if ([left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].array) || [left, right].every(is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].plainObject)) {\n                return !(0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(left, right);\n            }\n            return left !== right;\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var changedFrom = function (key, previous, actual) {\n        if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n            return false;\n        }\n        try {\n            var left = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.nested)(previousData, key);\n            var right = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.nested)(data, key);\n            var hasActual = is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(actual);\n            return ((0,_helpers__WEBPACK_IMPORTED_MODULE_1__.includesOrEqualsTo)(previous, left) &&\n                (hasActual ? (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.includesOrEqualsTo)(actual, right) : !hasActual));\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    /**\n     * @deprecated\n     * Use \"changed\" instead\n     */\n    var changedTo = function (key, actual) {\n        if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n            return false;\n        }\n        /* istanbul ignore next */\n        if (true) {\n            // eslint-disable-next-line no-console\n            console.warn('`changedTo` is deprecated! Replace it with `change`');\n        }\n        return changed(key, actual);\n    };\n    var decreased = function (key, actual, previous) {\n        if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n            return false;\n        }\n        try {\n            return (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.compareNumbers)(previousData, data, { key: key, actual: actual, previous: previous, type: 'decreased' });\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var emptied = function (key) {\n        try {\n            var _a = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.getIterables)(previousData, data, { key: key }), left = _a[0], right = _a[1];\n            return !!left.length && !right.length;\n        }\n        catch (_b) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var filled = function (key) {\n        try {\n            var _a = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.getIterables)(previousData, data, { key: key }), left = _a[0], right = _a[1];\n            return !left.length && !!right.length;\n        }\n        catch (_b) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var increased = function (key, actual, previous) {\n        if (!is_lite__WEBPACK_IMPORTED_MODULE_0__[\"default\"].defined(key)) {\n            return false;\n        }\n        try {\n            return (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.compareNumbers)(previousData, data, { key: key, actual: actual, previous: previous, type: 'increased' });\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    var removed = function (key, value) {\n        try {\n            return (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.compareValues)(previousData, data, { key: key, type: 'removed', value: value });\n        }\n        catch (_a) {\n            /* istanbul ignore next */\n            return false;\n        }\n    };\n    return { added: added, changed: changed, changedFrom: changedFrom, changedTo: changedTo, decreased: decreased, emptied: emptied, filled: filled, increased: increased, removed: removed };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/index.js\n");

/***/ })

};
;