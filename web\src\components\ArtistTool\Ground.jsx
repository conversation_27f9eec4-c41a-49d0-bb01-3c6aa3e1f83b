import { useTexture } from "@react-three/drei";
import { RigidBody } from "@react-three/rapier";
import { RepeatWrapping } from "three";

export function Ground() {
  const [colorMap, normalMap, roughnessMap, aoMap, displacementMap] =
    useTexture([
      "/textures/Grass001_1K_Color.jpg",
      "/textures/Grass001_1K_Normal.jpg",
      "/textures/Grass001_1K_Roughness.jpg",
      "/textures/Grass001_1K_AmbientOcclusion.jpg",
      "/textures/Grass001_1K_Displacement.jpg",
    ]);

  [colorMap, normalMap, roughnessMap, aoMap, displacementMap].forEach((map) => {
    map.wrapS = map.wrapT = RepeatWrapping;
    map.repeat.set(50, 50);
  });

  return (
    <RigidBody type="fixed" restitution={0.2} friction={1}>
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, 0, 0]} receiveShadow>
        <planeGeometry args={[100, 100, 64, 64]} />
        <meshStandardMaterial
          map={colorMap}
          normalMap={normalMap}
          normalScale={[0.8, 0.8]}
          roughnessMap={roughnessMap}
          aoMap={aoMap}
          displacementMap={displacementMap}
          displacementScale={0.15}
          roughness={1}
          metalness={0}
        />
      </mesh>
    </RigidBody>
  );
}
