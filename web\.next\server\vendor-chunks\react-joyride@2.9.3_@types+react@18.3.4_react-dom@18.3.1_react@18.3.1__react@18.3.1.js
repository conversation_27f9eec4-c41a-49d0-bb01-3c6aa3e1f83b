"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-joyride@2.9.3_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/react-joyride@2.9.3_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-joyride@2.9.3_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-joyride/dist/index.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-joyride@2.9.3_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-joyride/dist/index.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTIONS: () => (/* binding */ ACTIONS),\n/* harmony export */   EVENTS: () => (/* binding */ EVENTS),\n/* harmony export */   LIFECYCLE: () => (/* binding */ LIFECYCLE),\n/* harmony export */   ORIGIN: () => (/* binding */ ORIGIN),\n/* harmony export */   STATUS: () => (/* binding */ STATUS),\n/* harmony export */   \"default\": () => (/* binding */ components_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @gilbarbara/deep-equal */ \"(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.3.1/node_modules/@gilbarbara/deep-equal/dist/index.mjs\");\n/* harmony import */ var is_lite__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! is-lite */ \"(ssr)/./node_modules/.pnpm/is-lite@1.2.1/node_modules/is-lite/dist/index.mjs\");\n/* harmony import */ var tree_changes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tree-changes */ \"(ssr)/./node_modules/.pnpm/tree-changes@0.11.3/node_modules/tree-changes/dist/index.mjs\");\n/* harmony import */ var scroll__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! scroll */ \"(ssr)/./node_modules/.pnpm/scroll@3.0.1/node_modules/scroll/index.js\");\n/* harmony import */ var scrollparent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! scrollparent */ \"(ssr)/./node_modules/.pnpm/scrollparent@2.1.0/node_modules/scrollparent/scrollparent.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_innertext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-innertext */ \"(ssr)/./node_modules/.pnpm/react-innertext@1.1.5_@types+react@18.3.4_react@18.3.1/node_modules/react-innertext/index.js\");\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js\");\n/* harmony import */ var react_floater__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-floater */ \"(ssr)/./node_modules/.pnpm/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-floater/es/index.js\");\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n\n// src/literals/index.ts\nvar ACTIONS = {\n  INIT: \"init\",\n  START: \"start\",\n  STOP: \"stop\",\n  RESET: \"reset\",\n  PREV: \"prev\",\n  NEXT: \"next\",\n  GO: \"go\",\n  CLOSE: \"close\",\n  SKIP: \"skip\",\n  UPDATE: \"update\"\n};\nvar EVENTS = {\n  TOUR_START: \"tour:start\",\n  STEP_BEFORE: \"step:before\",\n  BEACON: \"beacon\",\n  TOOLTIP: \"tooltip\",\n  STEP_AFTER: \"step:after\",\n  TOUR_END: \"tour:end\",\n  TOUR_STATUS: \"tour:status\",\n  TARGET_NOT_FOUND: \"error:target_not_found\",\n  ERROR: \"error\"\n};\nvar LIFECYCLE = {\n  INIT: \"init\",\n  READY: \"ready\",\n  BEACON: \"beacon\",\n  TOOLTIP: \"tooltip\",\n  COMPLETE: \"complete\",\n  ERROR: \"error\"\n};\nvar ORIGIN = {\n  BUTTON_CLOSE: \"button_close\",\n  BUTTON_PRIMARY: \"button_primary\",\n  KEYBOARD: \"keyboard\",\n  OVERLAY: \"overlay\"\n};\nvar STATUS = {\n  IDLE: \"idle\",\n  READY: \"ready\",\n  WAITING: \"waiting\",\n  RUNNING: \"running\",\n  PAUSED: \"paused\",\n  SKIPPED: \"skipped\",\n  FINISHED: \"finished\",\n  ERROR: \"error\"\n};\n\n// src/components/index.tsx\n\n\n\n\n\n// src/modules/dom.ts\n\n\nfunction canUseDOM() {\n  var _a;\n  return !!(typeof window !== \"undefined\" && ((_a = window.document) == null ? void 0 : _a.createElement));\n}\nfunction getClientRect(element) {\n  if (!element) {\n    return null;\n  }\n  return element.getBoundingClientRect();\n}\nfunction getDocumentHeight(median = false) {\n  const { body, documentElement } = document;\n  if (!body || !documentElement) {\n    return 0;\n  }\n  if (median) {\n    const heights = [\n      body.scrollHeight,\n      body.offsetHeight,\n      documentElement.clientHeight,\n      documentElement.scrollHeight,\n      documentElement.offsetHeight\n    ].sort((a, b) => a - b);\n    const middle = Math.floor(heights.length / 2);\n    if (heights.length % 2 === 0) {\n      return (heights[middle - 1] + heights[middle]) / 2;\n    }\n    return heights[middle];\n  }\n  return Math.max(\n    body.scrollHeight,\n    body.offsetHeight,\n    documentElement.clientHeight,\n    documentElement.scrollHeight,\n    documentElement.offsetHeight\n  );\n}\nfunction getElement(element) {\n  if (typeof element === \"string\") {\n    try {\n      return document.querySelector(element);\n    } catch (error) {\n      if (true) {\n        console.error(error);\n      }\n      return null;\n    }\n  }\n  return element;\n}\nfunction getStyleComputedProperty(el) {\n  if (!el || el.nodeType !== 1) {\n    return null;\n  }\n  return getComputedStyle(el);\n}\nfunction getScrollParent(element, skipFix, forListener) {\n  if (!element) {\n    return scrollDocument();\n  }\n  const parent = scrollparent__WEBPACK_IMPORTED_MODULE_2__(element);\n  if (parent) {\n    if (parent.isSameNode(scrollDocument())) {\n      if (forListener) {\n        return document;\n      }\n      return scrollDocument();\n    }\n    const hasScrolling = parent.scrollHeight > parent.offsetHeight;\n    if (!hasScrolling && !skipFix) {\n      parent.style.overflow = \"initial\";\n      return scrollDocument();\n    }\n  }\n  return parent;\n}\nfunction hasCustomScrollParent(element, skipFix) {\n  if (!element) {\n    return false;\n  }\n  const parent = getScrollParent(element, skipFix);\n  return parent ? !parent.isSameNode(scrollDocument()) : false;\n}\nfunction hasCustomOffsetParent(element) {\n  return element.offsetParent !== document.body;\n}\nfunction hasPosition(el, type = \"fixed\") {\n  if (!el || !(el instanceof HTMLElement)) {\n    return false;\n  }\n  const { nodeName } = el;\n  const styles = getStyleComputedProperty(el);\n  if (nodeName === \"BODY\" || nodeName === \"HTML\") {\n    return false;\n  }\n  if (styles && styles.position === type) {\n    return true;\n  }\n  if (!el.parentNode) {\n    return false;\n  }\n  return hasPosition(el.parentNode, type);\n}\nfunction isElementVisible(element) {\n  var _a;\n  if (!element) {\n    return false;\n  }\n  let parentElement = element;\n  while (parentElement) {\n    if (parentElement === document.body) {\n      break;\n    }\n    if (parentElement instanceof HTMLElement) {\n      const { display, visibility } = getComputedStyle(parentElement);\n      if (display === \"none\" || visibility === \"hidden\") {\n        return false;\n      }\n    }\n    parentElement = (_a = parentElement.parentElement) != null ? _a : null;\n  }\n  return true;\n}\nfunction getElementPosition(element, offset, skipFix) {\n  var _a, _b, _c;\n  const elementRect = getClientRect(element);\n  const parent = getScrollParent(element, skipFix);\n  const hasScrollParent = hasCustomScrollParent(element, skipFix);\n  const isFixedTarget = hasPosition(element);\n  let parentTop = 0;\n  let top = (_a = elementRect == null ? void 0 : elementRect.top) != null ? _a : 0;\n  if (hasScrollParent && isFixedTarget) {\n    const offsetTop = (_b = element == null ? void 0 : element.offsetTop) != null ? _b : 0;\n    const parentScrollTop = (_c = parent == null ? void 0 : parent.scrollTop) != null ? _c : 0;\n    top = offsetTop - parentScrollTop;\n  } else if (parent instanceof HTMLElement) {\n    parentTop = parent.scrollTop;\n    if (!hasScrollParent && !hasPosition(element)) {\n      top += parentTop;\n    }\n    if (!parent.isSameNode(scrollDocument())) {\n      top += scrollDocument().scrollTop;\n    }\n  }\n  return Math.floor(top - offset);\n}\nfunction getScrollTo(element, offset, skipFix) {\n  var _a;\n  if (!element) {\n    return 0;\n  }\n  const { offsetTop = 0, scrollTop = 0 } = (_a = scrollparent__WEBPACK_IMPORTED_MODULE_2__(element)) != null ? _a : {};\n  let top = element.getBoundingClientRect().top + scrollTop;\n  if (!!offsetTop && (hasCustomScrollParent(element, skipFix) || hasCustomOffsetParent(element))) {\n    top -= offsetTop;\n  }\n  const output = Math.floor(top - offset);\n  return output < 0 ? 0 : output;\n}\nfunction scrollDocument() {\n  var _a;\n  return (_a = document.scrollingElement) != null ? _a : document.documentElement;\n}\nfunction scrollTo(value, options) {\n  const { duration, element } = options;\n  return new Promise((resolve, reject) => {\n    const { scrollTop } = element;\n    const limit = value > scrollTop ? value - scrollTop : scrollTop - value;\n    scroll__WEBPACK_IMPORTED_MODULE_1__.top(element, value, { duration: limit < 100 ? 50 : duration }, (error) => {\n      if (error && error.message !== \"Element already at target scroll position\") {\n        return reject(error);\n      }\n      return resolve();\n    });\n  });\n}\n\n// src/modules/helpers.tsx\n\n\n\n\nvar isReact16 = react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal !== void 0;\nfunction getBrowser(userAgent = navigator.userAgent) {\n  let browser = userAgent;\n  if (typeof window === \"undefined\") {\n    browser = \"node\";\n  } else if (document.documentMode) {\n    browser = \"ie\";\n  } else if (/Edge/.test(userAgent)) {\n    browser = \"edge\";\n  } else if (Boolean(window.opera) || userAgent.includes(\" OPR/\")) {\n    browser = \"opera\";\n  } else if (typeof window.InstallTrigger !== \"undefined\") {\n    browser = \"firefox\";\n  } else if (window.chrome) {\n    browser = \"chrome\";\n  } else if (/(Version\\/([\\d._]+).*Safari|CriOS|FxiOS| Mobile\\/)/.test(userAgent)) {\n    browser = \"safari\";\n  }\n  return browser;\n}\nfunction getObjectType(value) {\n  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();\n}\nfunction getReactNodeText(input, options = {}) {\n  const { defaultValue, step, steps } = options;\n  let text = react_innertext__WEBPACK_IMPORTED_MODULE_4__(input);\n  if (!text) {\n    if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(input) && !Object.values(input.props).length && getObjectType(input.type) === \"function\") {\n      const component = input.type({});\n      text = getReactNodeText(component, options);\n    } else {\n      text = react_innertext__WEBPACK_IMPORTED_MODULE_4__(defaultValue);\n    }\n  } else if ((text.includes(\"{step}\") || text.includes(\"{steps}\")) && step && steps) {\n    text = text.replace(\"{step}\", step.toString()).replace(\"{steps}\", steps.toString());\n  }\n  return text;\n}\nfunction hasValidKeys(object, keys) {\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject(object) || !is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].array(keys)) {\n    return false;\n  }\n  return Object.keys(object).every((d) => keys.includes(d));\n}\nfunction hexToRGB(hex) {\n  const shorthandRegex = /^#?([\\da-f])([\\da-f])([\\da-f])$/i;\n  const properHex = hex.replace(shorthandRegex, (_m, r, g, b) => r + r + g + g + b + b);\n  const result = /^#?([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/i.exec(properHex);\n  return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];\n}\nfunction hideBeacon(step) {\n  return step.disableBeacon || step.placement === \"center\";\n}\nfunction isLegacy() {\n  return ![\"chrome\", \"safari\", \"firefox\", \"opera\"].includes(getBrowser());\n}\nfunction log({ data, debug = false, title, warn = false }) {\n  const logFn = warn ? console.warn || console.error : console.log;\n  if (debug) {\n    if (title && data) {\n      console.groupCollapsed(\n        `%creact-joyride: ${title}`,\n        \"color: #ff0044; font-weight: bold; font-size: 12px;\"\n      );\n      if (Array.isArray(data)) {\n        data.forEach((d) => {\n          if (is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject(d) && d.key) {\n            logFn.apply(console, [d.key, d.value]);\n          } else {\n            logFn.apply(console, [d]);\n          }\n        });\n      } else {\n        logFn.apply(console, [data]);\n      }\n      console.groupEnd();\n    } else {\n      console.error(\"Missing title or data props\");\n    }\n  }\n}\nfunction noop() {\n  return void 0;\n}\nfunction objectKeys(input) {\n  return Object.keys(input);\n}\nfunction omit(input, ...filter) {\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject(input)) {\n    throw new TypeError(\"Expected an object\");\n  }\n  const output = {};\n  for (const key in input) {\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (!filter.includes(key)) {\n        output[key] = input[key];\n      }\n    }\n  }\n  return output;\n}\nfunction pick(input, ...filter) {\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject(input)) {\n    throw new TypeError(\"Expected an object\");\n  }\n  if (!filter.length) {\n    return input;\n  }\n  const output = {};\n  for (const key in input) {\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (filter.includes(key)) {\n        output[key] = input[key];\n      }\n    }\n  }\n  return output;\n}\nfunction replaceLocaleContent(input, step, steps) {\n  const replacer = (text) => text.replace(\"{step}\", String(step)).replace(\"{steps}\", String(steps));\n  if (getObjectType(input) === \"string\") {\n    return replacer(input);\n  }\n  if (!(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(input)) {\n    return input;\n  }\n  const { children } = input.props;\n  if (getObjectType(children) === \"string\" && children.includes(\"{step}\")) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(input, {\n      children: replacer(children)\n    });\n  }\n  if (Array.isArray(children)) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(input, {\n      children: children.map((child) => {\n        if (typeof child === \"string\") {\n          return replacer(child);\n        }\n        return replaceLocaleContent(child, step, steps);\n      })\n    });\n  }\n  if (getObjectType(input.type) === \"function\" && !Object.values(input.props).length) {\n    const component = input.type({});\n    return replaceLocaleContent(component, step, steps);\n  }\n  return input;\n}\nfunction shouldScroll(options) {\n  const { isFirstStep, lifecycle, previousLifecycle, scrollToFirstStep, step, target } = options;\n  return !step.disableScrolling && (!isFirstStep || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) && step.placement !== \"center\" && (!step.isFixed || !hasPosition(target)) && // fixed steps don't need to scroll\n  previousLifecycle !== lifecycle && [LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP].includes(lifecycle);\n}\n\n// src/modules/step.ts\n\n\n\n// src/defaults.ts\nvar defaultFloaterProps = {\n  options: {\n    preventOverflow: {\n      boundariesElement: \"scrollParent\"\n    }\n  },\n  wrapperOptions: {\n    offset: -18,\n    position: true\n  }\n};\nvar defaultLocale = {\n  back: \"Back\",\n  close: \"Close\",\n  last: \"Last\",\n  next: \"Next\",\n  nextLabelWithProgress: \"Next (Step {step} of {steps})\",\n  open: \"Open the dialog\",\n  skip: \"Skip\"\n};\nvar defaultStep = {\n  event: \"click\",\n  placement: \"bottom\",\n  offset: 10,\n  disableBeacon: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrollParentFix: false,\n  disableScrolling: false,\n  hideBackButton: false,\n  hideCloseButton: false,\n  hideFooter: false,\n  isFixed: false,\n  locale: defaultLocale,\n  showProgress: false,\n  showSkipButton: false,\n  spotlightClicks: false,\n  spotlightPadding: 10\n};\nvar defaultProps = {\n  continuous: false,\n  debug: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrolling: false,\n  disableScrollParentFix: false,\n  getHelpers: noop(),\n  hideBackButton: false,\n  run: true,\n  scrollOffset: 20,\n  scrollDuration: 300,\n  scrollToFirstStep: false,\n  showSkipButton: false,\n  showProgress: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n  steps: []\n};\n\n// src/styles.ts\n\nvar defaultOptions = {\n  arrowColor: \"#fff\",\n  backgroundColor: \"#fff\",\n  beaconSize: 36,\n  overlayColor: \"rgba(0, 0, 0, 0.5)\",\n  primaryColor: \"#f04\",\n  spotlightShadow: \"0 0 15px rgba(0, 0, 0, 0.5)\",\n  textColor: \"#333\",\n  width: 380,\n  zIndex: 100\n};\nvar buttonBase = {\n  backgroundColor: \"transparent\",\n  border: 0,\n  borderRadius: 0,\n  color: \"#555\",\n  cursor: \"pointer\",\n  fontSize: 16,\n  lineHeight: 1,\n  padding: 8,\n  WebkitAppearance: \"none\"\n};\nvar spotlight = {\n  borderRadius: 4,\n  position: \"absolute\"\n};\nfunction getStyles(props, step) {\n  var _a, _b, _c, _d, _e;\n  const { floaterProps, styles } = props;\n  const mergedFloaterProps = deepmerge__WEBPACK_IMPORTED_MODULE_6__((_a = step.floaterProps) != null ? _a : {}, floaterProps != null ? floaterProps : {});\n  const mergedStyles = deepmerge__WEBPACK_IMPORTED_MODULE_6__(styles != null ? styles : {}, (_b = step.styles) != null ? _b : {});\n  const options = deepmerge__WEBPACK_IMPORTED_MODULE_6__(defaultOptions, mergedStyles.options || {});\n  const hideBeacon2 = step.placement === \"center\" || step.disableBeacon;\n  let { width } = options;\n  if (window.innerWidth > 480) {\n    width = 380;\n  }\n  if (\"width\" in options) {\n    width = typeof options.width === \"number\" && window.innerWidth < options.width ? window.innerWidth - 30 : options.width;\n  }\n  const overlay = {\n    bottom: 0,\n    left: 0,\n    overflow: \"hidden\",\n    position: \"absolute\",\n    right: 0,\n    top: 0,\n    zIndex: options.zIndex\n  };\n  const defaultStyles = {\n    beacon: {\n      ...buttonBase,\n      display: hideBeacon2 ? \"none\" : \"inline-block\",\n      height: options.beaconSize,\n      position: \"relative\",\n      width: options.beaconSize,\n      zIndex: options.zIndex\n    },\n    beaconInner: {\n      animation: \"joyride-beacon-inner 1.2s infinite ease-in-out\",\n      backgroundColor: options.primaryColor,\n      borderRadius: \"50%\",\n      display: \"block\",\n      height: \"50%\",\n      left: \"50%\",\n      opacity: 0.7,\n      position: \"absolute\",\n      top: \"50%\",\n      transform: \"translate(-50%, -50%)\",\n      width: \"50%\"\n    },\n    beaconOuter: {\n      animation: \"joyride-beacon-outer 1.2s infinite ease-in-out\",\n      backgroundColor: `rgba(${hexToRGB(options.primaryColor).join(\",\")}, 0.2)`,\n      border: `2px solid ${options.primaryColor}`,\n      borderRadius: \"50%\",\n      boxSizing: \"border-box\",\n      display: \"block\",\n      height: \"100%\",\n      left: 0,\n      opacity: 0.9,\n      position: \"absolute\",\n      top: 0,\n      transformOrigin: \"center\",\n      width: \"100%\"\n    },\n    tooltip: {\n      backgroundColor: options.backgroundColor,\n      borderRadius: 5,\n      boxSizing: \"border-box\",\n      color: options.textColor,\n      fontSize: 16,\n      maxWidth: \"100%\",\n      padding: 15,\n      position: \"relative\",\n      width\n    },\n    tooltipContainer: {\n      lineHeight: 1.4,\n      textAlign: \"center\"\n    },\n    tooltipTitle: {\n      fontSize: 18,\n      margin: 0\n    },\n    tooltipContent: {\n      padding: \"20px 10px\"\n    },\n    tooltipFooter: {\n      alignItems: \"center\",\n      display: \"flex\",\n      justifyContent: \"flex-end\",\n      marginTop: 15\n    },\n    tooltipFooterSpacer: {\n      flex: 1\n    },\n    buttonNext: {\n      ...buttonBase,\n      backgroundColor: options.primaryColor,\n      borderRadius: 4,\n      color: \"#fff\"\n    },\n    buttonBack: {\n      ...buttonBase,\n      color: options.primaryColor,\n      marginLeft: \"auto\",\n      marginRight: 5\n    },\n    buttonClose: {\n      ...buttonBase,\n      color: options.textColor,\n      height: 14,\n      padding: 15,\n      position: \"absolute\",\n      right: 0,\n      top: 0,\n      width: 14\n    },\n    buttonSkip: {\n      ...buttonBase,\n      color: options.textColor,\n      fontSize: 14\n    },\n    overlay: {\n      ...overlay,\n      backgroundColor: options.overlayColor,\n      mixBlendMode: \"hard-light\"\n    },\n    overlayLegacy: {\n      ...overlay\n    },\n    overlayLegacyCenter: {\n      ...overlay,\n      backgroundColor: options.overlayColor\n    },\n    spotlight: {\n      ...spotlight,\n      backgroundColor: \"gray\"\n    },\n    spotlightLegacy: {\n      ...spotlight,\n      boxShadow: `0 0 0 9999px ${options.overlayColor}, ${options.spotlightShadow}`\n    },\n    floaterStyles: {\n      arrow: {\n        color: (_e = (_d = (_c = mergedFloaterProps == null ? void 0 : mergedFloaterProps.styles) == null ? void 0 : _c.arrow) == null ? void 0 : _d.color) != null ? _e : options.arrowColor\n      },\n      options: {\n        zIndex: options.zIndex + 100\n      }\n    },\n    options\n  };\n  return deepmerge__WEBPACK_IMPORTED_MODULE_6__(defaultStyles, mergedStyles);\n}\n\n// src/modules/step.ts\nfunction getTourProps(props) {\n  return pick(\n    props,\n    \"beaconComponent\",\n    \"disableCloseOnEsc\",\n    \"disableOverlay\",\n    \"disableOverlayClose\",\n    \"disableScrolling\",\n    \"disableScrollParentFix\",\n    \"floaterProps\",\n    \"hideBackButton\",\n    \"hideCloseButton\",\n    \"locale\",\n    \"showProgress\",\n    \"showSkipButton\",\n    \"spotlightClicks\",\n    \"spotlightPadding\",\n    \"styles\",\n    \"tooltipComponent\"\n  );\n}\nfunction getMergedStep(props, currentStep) {\n  var _a, _b, _c, _d, _e, _f;\n  const step = currentStep != null ? currentStep : {};\n  const mergedStep = deepmerge__WEBPACK_IMPORTED_MODULE_6__.all([defaultStep, getTourProps(props), step], {\n    isMergeableObject: is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject\n  });\n  const mergedStyles = getStyles(props, mergedStep);\n  const scrollParent2 = hasCustomScrollParent(\n    getElement(mergedStep.target),\n    mergedStep.disableScrollParentFix\n  );\n  const floaterProps = deepmerge__WEBPACK_IMPORTED_MODULE_6__.all([\n    defaultFloaterProps,\n    (_a = props.floaterProps) != null ? _a : {},\n    (_b = mergedStep.floaterProps) != null ? _b : {}\n  ]);\n  floaterProps.offset = mergedStep.offset;\n  floaterProps.styles = deepmerge__WEBPACK_IMPORTED_MODULE_6__((_c = floaterProps.styles) != null ? _c : {}, mergedStyles.floaterStyles);\n  floaterProps.offset += (_e = (_d = props.spotlightPadding) != null ? _d : mergedStep.spotlightPadding) != null ? _e : 0;\n  if (mergedStep.placementBeacon && floaterProps.wrapperOptions) {\n    floaterProps.wrapperOptions.placement = mergedStep.placementBeacon;\n  }\n  if (scrollParent2 && floaterProps.options.preventOverflow) {\n    floaterProps.options.preventOverflow.boundariesElement = \"window\";\n  }\n  return {\n    ...mergedStep,\n    locale: deepmerge__WEBPACK_IMPORTED_MODULE_6__.all([defaultLocale, (_f = props.locale) != null ? _f : {}, mergedStep.locale || {}]),\n    floaterProps,\n    styles: omit(mergedStyles, \"floaterStyles\")\n  };\n}\nfunction validateStep(step, debug = false) {\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].plainObject(step)) {\n    log({\n      title: \"validateStep\",\n      data: \"step must be an object\",\n      warn: true,\n      debug\n    });\n    return false;\n  }\n  if (!step.target) {\n    log({\n      title: \"validateStep\",\n      data: \"target is missing from the step\",\n      warn: true,\n      debug\n    });\n    return false;\n  }\n  return true;\n}\nfunction validateSteps(steps, debug = false) {\n  if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].array(steps)) {\n    log({\n      title: \"validateSteps\",\n      data: \"steps must be an array\",\n      warn: true,\n      debug\n    });\n    return false;\n  }\n  return steps.every((d) => validateStep(d, debug));\n}\n\n// src/modules/store.ts\n\nvar defaultState = {\n  action: \"init\",\n  controlled: false,\n  index: 0,\n  lifecycle: LIFECYCLE.INIT,\n  origin: null,\n  size: 0,\n  status: STATUS.IDLE\n};\nvar validKeys = objectKeys(omit(defaultState, \"controlled\", \"size\"));\nvar Store = class {\n  constructor(options) {\n    __publicField(this, \"beaconPopper\");\n    __publicField(this, \"tooltipPopper\");\n    __publicField(this, \"data\", /* @__PURE__ */ new Map());\n    __publicField(this, \"listener\");\n    __publicField(this, \"store\", /* @__PURE__ */ new Map());\n    __publicField(this, \"addListener\", (listener) => {\n      this.listener = listener;\n    });\n    __publicField(this, \"setSteps\", (steps) => {\n      const { size, status } = this.getState();\n      const state = {\n        size: steps.length,\n        status\n      };\n      this.data.set(\"steps\", steps);\n      if (status === STATUS.WAITING && !size && steps.length) {\n        state.status = STATUS.RUNNING;\n      }\n      this.setState(state);\n    });\n    __publicField(this, \"getPopper\", (name) => {\n      if (name === \"beacon\") {\n        return this.beaconPopper;\n      }\n      return this.tooltipPopper;\n    });\n    __publicField(this, \"setPopper\", (name, popper) => {\n      if (name === \"beacon\") {\n        this.beaconPopper = popper;\n      } else {\n        this.tooltipPopper = popper;\n      }\n    });\n    __publicField(this, \"cleanupPoppers\", () => {\n      this.beaconPopper = null;\n      this.tooltipPopper = null;\n    });\n    __publicField(this, \"close\", (origin = null) => {\n      const { index, status } = this.getState();\n      if (status !== STATUS.RUNNING) {\n        return;\n      }\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.CLOSE, index: index + 1, origin })\n      });\n    });\n    __publicField(this, \"go\", (nextIndex) => {\n      const { controlled, status } = this.getState();\n      if (controlled || status !== STATUS.RUNNING) {\n        return;\n      }\n      const step = this.getSteps()[nextIndex];\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.GO, index: nextIndex }),\n        status: step ? status : STATUS.FINISHED\n      });\n    });\n    __publicField(this, \"info\", () => this.getState());\n    __publicField(this, \"next\", () => {\n      const { index, status } = this.getState();\n      if (status !== STATUS.RUNNING) {\n        return;\n      }\n      this.setState(this.getNextState({ action: ACTIONS.NEXT, index: index + 1 }));\n    });\n    __publicField(this, \"open\", () => {\n      const { status } = this.getState();\n      if (status !== STATUS.RUNNING) {\n        return;\n      }\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.UPDATE, lifecycle: LIFECYCLE.TOOLTIP })\n      });\n    });\n    __publicField(this, \"prev\", () => {\n      const { index, status } = this.getState();\n      if (status !== STATUS.RUNNING) {\n        return;\n      }\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.PREV, index: index - 1 })\n      });\n    });\n    __publicField(this, \"reset\", (restart = false) => {\n      const { controlled } = this.getState();\n      if (controlled) {\n        return;\n      }\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.RESET, index: 0 }),\n        status: restart ? STATUS.RUNNING : STATUS.READY\n      });\n    });\n    __publicField(this, \"skip\", () => {\n      const { status } = this.getState();\n      if (status !== STATUS.RUNNING) {\n        return;\n      }\n      this.setState({\n        action: ACTIONS.SKIP,\n        lifecycle: LIFECYCLE.INIT,\n        status: STATUS.SKIPPED\n      });\n    });\n    __publicField(this, \"start\", (nextIndex) => {\n      const { index, size } = this.getState();\n      this.setState({\n        ...this.getNextState(\n          {\n            action: ACTIONS.START,\n            index: is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(nextIndex) ? nextIndex : index\n          },\n          true\n        ),\n        status: size ? STATUS.RUNNING : STATUS.WAITING\n      });\n    });\n    __publicField(this, \"stop\", (advance = false) => {\n      const { index, status } = this.getState();\n      if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {\n        return;\n      }\n      this.setState({\n        ...this.getNextState({ action: ACTIONS.STOP, index: index + (advance ? 1 : 0) }),\n        status: STATUS.PAUSED\n      });\n    });\n    __publicField(this, \"update\", (state) => {\n      var _a, _b;\n      if (!hasValidKeys(state, validKeys)) {\n        throw new Error(`State is not valid. Valid keys: ${validKeys.join(\", \")}`);\n      }\n      this.setState({\n        ...this.getNextState(\n          {\n            ...this.getState(),\n            ...state,\n            action: (_a = state.action) != null ? _a : ACTIONS.UPDATE,\n            origin: (_b = state.origin) != null ? _b : null\n          },\n          true\n        )\n      });\n    });\n    const { continuous = false, stepIndex, steps = [] } = options != null ? options : {};\n    this.setState(\n      {\n        action: ACTIONS.INIT,\n        controlled: is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(stepIndex),\n        continuous,\n        index: is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(stepIndex) ? stepIndex : 0,\n        lifecycle: LIFECYCLE.INIT,\n        origin: null,\n        status: steps.length ? STATUS.READY : STATUS.IDLE\n      },\n      true\n    );\n    this.beaconPopper = null;\n    this.tooltipPopper = null;\n    this.listener = null;\n    this.setSteps(steps);\n  }\n  getState() {\n    if (!this.store.size) {\n      return { ...defaultState };\n    }\n    return {\n      action: this.store.get(\"action\") || \"\",\n      controlled: this.store.get(\"controlled\") || false,\n      index: parseInt(this.store.get(\"index\"), 10),\n      lifecycle: this.store.get(\"lifecycle\") || \"\",\n      origin: this.store.get(\"origin\") || null,\n      size: this.store.get(\"size\") || 0,\n      status: this.store.get(\"status\") || \"\"\n    };\n  }\n  getNextState(state, force = false) {\n    var _a, _b, _c, _d, _e;\n    const { action, controlled, index, size, status } = this.getState();\n    const newIndex = is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(state.index) ? state.index : index;\n    const nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);\n    return {\n      action: (_a = state.action) != null ? _a : action,\n      controlled,\n      index: nextIndex,\n      lifecycle: (_b = state.lifecycle) != null ? _b : LIFECYCLE.INIT,\n      origin: (_c = state.origin) != null ? _c : null,\n      size: (_d = state.size) != null ? _d : size,\n      status: nextIndex === size ? STATUS.FINISHED : (_e = state.status) != null ? _e : status\n    };\n  }\n  getSteps() {\n    const steps = this.data.get(\"steps\");\n    return Array.isArray(steps) ? steps : [];\n  }\n  hasUpdatedState(oldState) {\n    const before = JSON.stringify(oldState);\n    const after = JSON.stringify(this.getState());\n    return before !== after;\n  }\n  setState(nextState, initial = false) {\n    const state = this.getState();\n    const {\n      action,\n      index,\n      lifecycle,\n      origin = null,\n      size,\n      status\n    } = {\n      ...state,\n      ...nextState\n    };\n    this.store.set(\"action\", action);\n    this.store.set(\"index\", index);\n    this.store.set(\"lifecycle\", lifecycle);\n    this.store.set(\"origin\", origin);\n    this.store.set(\"size\", size);\n    this.store.set(\"status\", status);\n    if (initial) {\n      this.store.set(\"controlled\", nextState.controlled);\n      this.store.set(\"continuous\", nextState.continuous);\n    }\n    if (this.listener && this.hasUpdatedState(state)) {\n      this.listener(this.getState());\n    }\n  }\n  getHelpers() {\n    return {\n      close: this.close,\n      go: this.go,\n      info: this.info,\n      next: this.next,\n      open: this.open,\n      prev: this.prev,\n      reset: this.reset,\n      skip: this.skip\n    };\n  }\n};\nfunction createStore(options) {\n  return new Store(options);\n}\n\n// src/components/Overlay.tsx\n\n\n\n// src/components/Spotlight.tsx\n\nfunction JoyrideSpotlight({ styles }) {\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      key: \"JoyrideSpotlight\",\n      className: \"react-joyride__spotlight\",\n      \"data-test-id\": \"spotlight\",\n      style: styles\n    }\n  );\n}\nvar Spotlight_default = JoyrideSpotlight;\n\n// src/components/Overlay.tsx\nvar JoyrideOverlay = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"isActive\", false);\n    __publicField(this, \"resizeTimeout\");\n    __publicField(this, \"scrollTimeout\");\n    __publicField(this, \"scrollParent\");\n    __publicField(this, \"state\", {\n      isScrolling: false,\n      mouseOverSpotlight: false,\n      showSpotlight: true\n    });\n    __publicField(this, \"hideSpotlight\", () => {\n      const { continuous, disableOverlay, lifecycle } = this.props;\n      const hiddenLifecycles = [\n        LIFECYCLE.INIT,\n        LIFECYCLE.BEACON,\n        LIFECYCLE.COMPLETE,\n        LIFECYCLE.ERROR\n      ];\n      return disableOverlay || (continuous ? hiddenLifecycles.includes(lifecycle) : lifecycle !== LIFECYCLE.TOOLTIP);\n    });\n    __publicField(this, \"handleMouseMove\", (event) => {\n      const { mouseOverSpotlight } = this.state;\n      const { height, left, position, top, width } = this.spotlightStyles;\n      const offsetY = position === \"fixed\" ? event.clientY : event.pageY;\n      const offsetX = position === \"fixed\" ? event.clientX : event.pageX;\n      const inSpotlightHeight = offsetY >= top && offsetY <= top + height;\n      const inSpotlightWidth = offsetX >= left && offsetX <= left + width;\n      const inSpotlight = inSpotlightWidth && inSpotlightHeight;\n      if (inSpotlight !== mouseOverSpotlight) {\n        this.updateState({ mouseOverSpotlight: inSpotlight });\n      }\n    });\n    __publicField(this, \"handleScroll\", () => {\n      const { target } = this.props;\n      const element = getElement(target);\n      if (this.scrollParent !== document) {\n        const { isScrolling } = this.state;\n        if (!isScrolling) {\n          this.updateState({ isScrolling: true, showSpotlight: false });\n        }\n        clearTimeout(this.scrollTimeout);\n        this.scrollTimeout = window.setTimeout(() => {\n          this.updateState({ isScrolling: false, showSpotlight: true });\n        }, 50);\n      } else if (hasPosition(element, \"sticky\")) {\n        this.updateState({});\n      }\n    });\n    __publicField(this, \"handleResize\", () => {\n      clearTimeout(this.resizeTimeout);\n      this.resizeTimeout = window.setTimeout(() => {\n        if (!this.isActive) {\n          return;\n        }\n        this.forceUpdate();\n      }, 100);\n    });\n  }\n  componentDidMount() {\n    const { debug, disableScrolling, disableScrollParentFix = false, target } = this.props;\n    const element = getElement(target);\n    this.scrollParent = getScrollParent(element != null ? element : document.body, disableScrollParentFix, true);\n    this.isActive = true;\n    if (true) {\n      if (!disableScrolling && hasCustomScrollParent(element, true)) {\n        log({\n          title: \"step has a custom scroll parent and can cause trouble with scrolling\",\n          data: [{ key: \"parent\", value: this.scrollParent }],\n          debug\n        });\n      }\n    }\n    window.addEventListener(\"resize\", this.handleResize);\n  }\n  componentDidUpdate(previousProps) {\n    var _a;\n    const { disableScrollParentFix, lifecycle, spotlightClicks, target } = this.props;\n    const { changed } = (0,tree_changes__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(previousProps, this.props);\n    if (changed(\"target\") || changed(\"disableScrollParentFix\")) {\n      const element = getElement(target);\n      this.scrollParent = getScrollParent(element != null ? element : document.body, disableScrollParentFix, true);\n    }\n    if (changed(\"lifecycle\", LIFECYCLE.TOOLTIP)) {\n      (_a = this.scrollParent) == null ? void 0 : _a.addEventListener(\"scroll\", this.handleScroll, { passive: true });\n      setTimeout(() => {\n        const { isScrolling } = this.state;\n        if (!isScrolling) {\n          this.updateState({ showSpotlight: true });\n        }\n      }, 100);\n    }\n    if (changed(\"spotlightClicks\") || changed(\"disableOverlay\") || changed(\"lifecycle\")) {\n      if (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP) {\n        window.addEventListener(\"mousemove\", this.handleMouseMove, false);\n      } else if (lifecycle !== LIFECYCLE.TOOLTIP) {\n        window.removeEventListener(\"mousemove\", this.handleMouseMove);\n      }\n    }\n  }\n  componentWillUnmount() {\n    var _a;\n    this.isActive = false;\n    window.removeEventListener(\"mousemove\", this.handleMouseMove);\n    window.removeEventListener(\"resize\", this.handleResize);\n    clearTimeout(this.resizeTimeout);\n    clearTimeout(this.scrollTimeout);\n    (_a = this.scrollParent) == null ? void 0 : _a.removeEventListener(\"scroll\", this.handleScroll);\n  }\n  get overlayStyles() {\n    const { mouseOverSpotlight } = this.state;\n    const { disableOverlayClose, placement, styles } = this.props;\n    let baseStyles = styles.overlay;\n    if (isLegacy()) {\n      baseStyles = placement === \"center\" ? styles.overlayLegacyCenter : styles.overlayLegacy;\n    }\n    return {\n      cursor: disableOverlayClose ? \"default\" : \"pointer\",\n      height: getDocumentHeight(),\n      pointerEvents: mouseOverSpotlight ? \"none\" : \"auto\",\n      ...baseStyles\n    };\n  }\n  get spotlightStyles() {\n    var _a, _b, _c;\n    const { showSpotlight } = this.state;\n    const {\n      disableScrollParentFix = false,\n      spotlightClicks,\n      spotlightPadding = 0,\n      styles,\n      target\n    } = this.props;\n    const element = getElement(target);\n    const elementRect = getClientRect(element);\n    const isFixedTarget = hasPosition(element);\n    const top = getElementPosition(element, spotlightPadding, disableScrollParentFix);\n    return {\n      ...isLegacy() ? styles.spotlightLegacy : styles.spotlight,\n      height: Math.round(((_a = elementRect == null ? void 0 : elementRect.height) != null ? _a : 0) + spotlightPadding * 2),\n      left: Math.round(((_b = elementRect == null ? void 0 : elementRect.left) != null ? _b : 0) - spotlightPadding),\n      opacity: showSpotlight ? 1 : 0,\n      pointerEvents: spotlightClicks ? \"none\" : \"auto\",\n      position: isFixedTarget ? \"fixed\" : \"absolute\",\n      top,\n      transition: \"opacity 0.2s\",\n      width: Math.round(((_c = elementRect == null ? void 0 : elementRect.width) != null ? _c : 0) + spotlightPadding * 2)\n    };\n  }\n  updateState(state) {\n    if (!this.isActive) {\n      return;\n    }\n    this.setState((previousState) => ({ ...previousState, ...state }));\n  }\n  render() {\n    const { showSpotlight } = this.state;\n    const { onClickOverlay, placement } = this.props;\n    const { hideSpotlight, overlayStyles, spotlightStyles } = this;\n    if (hideSpotlight()) {\n      return null;\n    }\n    let spotlight2 = placement !== \"center\" && showSpotlight && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Spotlight_default, { styles: spotlightStyles });\n    if (getBrowser() === \"safari\") {\n      const { mixBlendMode, zIndex, ...safariOverlay } = overlayStyles;\n      spotlight2 = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: { ...safariOverlay } }, spotlight2);\n      delete overlayStyles.backgroundColor;\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"div\",\n      {\n        className: \"react-joyride__overlay\",\n        \"data-test-id\": \"overlay\",\n        onClick: onClickOverlay,\n        role: \"presentation\",\n        style: overlayStyles\n      },\n      spotlight2\n    );\n  }\n};\n\n// src/components/Portal.tsx\n\n\nvar JoyridePortal = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"node\", null);\n  }\n  componentDidMount() {\n    const { id } = this.props;\n    if (!canUseDOM()) {\n      return;\n    }\n    this.node = document.createElement(\"div\");\n    this.node.id = id;\n    document.body.appendChild(this.node);\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n  componentDidUpdate() {\n    if (!canUseDOM()) {\n      return;\n    }\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n  componentWillUnmount() {\n    if (!canUseDOM() || !this.node) {\n      return;\n    }\n    if (!isReact16) {\n      react_dom__WEBPACK_IMPORTED_MODULE_3__.unmountComponentAtNode(this.node);\n    }\n    if (this.node.parentNode === document.body) {\n      document.body.removeChild(this.node);\n      this.node = null;\n    }\n  }\n  renderReact15() {\n    if (!canUseDOM()) {\n      return;\n    }\n    const { children } = this.props;\n    if (this.node) {\n      react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_renderSubtreeIntoContainer(this, children, this.node);\n    }\n  }\n  renderReact16() {\n    if (!canUseDOM() || !isReact16) {\n      return null;\n    }\n    const { children } = this.props;\n    if (!this.node) {\n      return null;\n    }\n    return react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal(children, this.node);\n  }\n  render() {\n    if (!isReact16) {\n      return null;\n    }\n    return this.renderReact16();\n  }\n};\n\n// src/components/Step.tsx\n\n\n\n\n\n// src/modules/scope.ts\nvar Scope = class {\n  constructor(element, options) {\n    __publicField(this, \"element\");\n    __publicField(this, \"options\");\n    __publicField(this, \"canBeTabbed\", (element) => {\n      const { tabIndex } = element;\n      if (tabIndex === null || tabIndex < 0) {\n        return false;\n      }\n      return this.canHaveFocus(element);\n    });\n    __publicField(this, \"canHaveFocus\", (element) => {\n      const validTabNodes = /input|select|textarea|button|object/;\n      const nodeName = element.nodeName.toLowerCase();\n      const isValid = validTabNodes.test(nodeName) && !element.getAttribute(\"disabled\") || nodeName === \"a\" && !!element.getAttribute(\"href\");\n      return isValid && this.isVisible(element);\n    });\n    __publicField(this, \"findValidTabElements\", () => [].slice.call(this.element.querySelectorAll(\"*\"), 0).filter(this.canBeTabbed));\n    __publicField(this, \"handleKeyDown\", (event) => {\n      const { code = \"Tab\" } = this.options;\n      if (event.code === code) {\n        this.interceptTab(event);\n      }\n    });\n    __publicField(this, \"interceptTab\", (event) => {\n      event.preventDefault();\n      const elements = this.findValidTabElements();\n      const { shiftKey } = event;\n      if (!elements.length) {\n        return;\n      }\n      let x = document.activeElement ? elements.indexOf(document.activeElement) : 0;\n      if (x === -1 || !shiftKey && x + 1 === elements.length) {\n        x = 0;\n      } else if (shiftKey && x === 0) {\n        x = elements.length - 1;\n      } else {\n        x += shiftKey ? -1 : 1;\n      }\n      elements[x].focus();\n    });\n    // eslint-disable-next-line class-methods-use-this\n    __publicField(this, \"isHidden\", (element) => {\n      const noSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n      const style = window.getComputedStyle(element);\n      if (noSize && !element.innerHTML) {\n        return true;\n      }\n      return noSize && style.getPropertyValue(\"overflow\") !== \"visible\" || style.getPropertyValue(\"display\") === \"none\";\n    });\n    __publicField(this, \"isVisible\", (element) => {\n      let parentElement = element;\n      while (parentElement) {\n        if (parentElement instanceof HTMLElement) {\n          if (parentElement === document.body) {\n            break;\n          }\n          if (this.isHidden(parentElement)) {\n            return false;\n          }\n          parentElement = parentElement.parentNode;\n        }\n      }\n      return true;\n    });\n    __publicField(this, \"removeScope\", () => {\n      window.removeEventListener(\"keydown\", this.handleKeyDown);\n    });\n    __publicField(this, \"checkFocus\", (target) => {\n      if (document.activeElement !== target) {\n        target.focus();\n        window.requestAnimationFrame(() => this.checkFocus(target));\n      }\n    });\n    __publicField(this, \"setFocus\", () => {\n      const { selector } = this.options;\n      if (!selector) {\n        return;\n      }\n      const target = this.element.querySelector(selector);\n      if (target) {\n        window.requestAnimationFrame(() => this.checkFocus(target));\n      }\n    });\n    if (!(element instanceof HTMLElement)) {\n      throw new TypeError(\"Invalid parameter: element must be an HTMLElement\");\n    }\n    this.element = element;\n    this.options = options;\n    window.addEventListener(\"keydown\", this.handleKeyDown, false);\n    this.setFocus();\n  }\n};\n\n// src/components/Beacon.tsx\n\n\nvar JoyrideBeacon = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(props) {\n    super(props);\n    __publicField(this, \"beacon\", null);\n    __publicField(this, \"setBeaconRef\", (c) => {\n      this.beacon = c;\n    });\n    if (props.beaconComponent) {\n      return;\n    }\n    const head = document.head || document.getElementsByTagName(\"head\")[0];\n    const style = document.createElement(\"style\");\n    style.id = \"joyride-beacon-animation\";\n    if (props.nonce) {\n      style.setAttribute(\"nonce\", props.nonce);\n    }\n    const css = `\n        @keyframes joyride-beacon-inner {\n          20% {\n            opacity: 0.9;\n          }\n        \n          90% {\n            opacity: 0.7;\n          }\n        }\n        \n        @keyframes joyride-beacon-outer {\n          0% {\n            transform: scale(1);\n          }\n        \n          45% {\n            opacity: 0.7;\n            transform: scale(0.75);\n          }\n        \n          100% {\n            opacity: 0.9;\n            transform: scale(1);\n          }\n        }\n      `;\n    style.appendChild(document.createTextNode(css));\n    head.appendChild(style);\n  }\n  componentDidMount() {\n    const { shouldFocus } = this.props;\n    if (true) {\n      if (!is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].domElement(this.beacon)) {\n        console.warn(\"beacon is not a valid DOM element\");\n      }\n    }\n    setTimeout(() => {\n      if (is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].domElement(this.beacon) && shouldFocus) {\n        this.beacon.focus();\n      }\n    }, 0);\n  }\n  componentWillUnmount() {\n    const style = document.getElementById(\"joyride-beacon-animation\");\n    if (style == null ? void 0 : style.parentNode) {\n      style.parentNode.removeChild(style);\n    }\n  }\n  render() {\n    const {\n      beaconComponent,\n      continuous,\n      index,\n      isLastStep,\n      locale,\n      onClickOrHover,\n      size,\n      step,\n      styles\n    } = this.props;\n    const title = getReactNodeText(locale.open);\n    const sharedProps = {\n      \"aria-label\": title,\n      onClick: onClickOrHover,\n      onMouseEnter: onClickOrHover,\n      ref: this.setBeaconRef,\n      title\n    };\n    let component;\n    if (beaconComponent) {\n      const BeaconComponent = beaconComponent;\n      component = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        BeaconComponent,\n        {\n          continuous,\n          index,\n          isLastStep,\n          size,\n          step,\n          ...sharedProps\n        }\n      );\n    } else {\n      component = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"button\",\n        {\n          key: \"JoyrideBeacon\",\n          className: \"react-joyride__beacon\",\n          \"data-test-id\": \"button-beacon\",\n          style: styles.beacon,\n          type: \"button\",\n          ...sharedProps\n        },\n        /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { style: styles.beaconInner }),\n        /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { style: styles.beaconOuter })\n      );\n    }\n    return component;\n  }\n};\n\n// src/components/Tooltip/index.tsx\n\n\n// src/components/Tooltip/Container.tsx\n\n\n// src/components/Tooltip/CloseButton.tsx\n\nfunction JoyrideTooltipCloseButton({ styles, ...props }) {\n  const { color, height, width, ...style } = styles;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", { style, type: \"button\", ...props }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"svg\",\n    {\n      height: typeof height === \"number\" ? `${height}px` : height,\n      preserveAspectRatio: \"xMidYMid\",\n      version: \"1.1\",\n      viewBox: \"0 0 18 18\",\n      width: typeof width === \"number\" ? `${width}px` : width,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"path\",\n      {\n        d: \"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",\n        fill: color\n      }\n    ))\n  ));\n}\nvar CloseButton_default = JoyrideTooltipCloseButton;\n\n// src/components/Tooltip/Container.tsx\nfunction JoyrideTooltipContainer(props) {\n  const { backProps, closeProps, index, isLastStep, primaryProps, skipProps, step, tooltipProps } = props;\n  const { content, hideBackButton, hideCloseButton, hideFooter, showSkipButton, styles, title } = step;\n  const output = {};\n  output.primary = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"button\",\n    {\n      \"data-test-id\": \"button-primary\",\n      style: styles.buttonNext,\n      type: \"button\",\n      ...primaryProps\n    }\n  );\n  if (showSkipButton && !isLastStep) {\n    output.skip = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"button\",\n      {\n        \"aria-live\": \"off\",\n        \"data-test-id\": \"button-skip\",\n        style: styles.buttonSkip,\n        type: \"button\",\n        ...skipProps\n      }\n    );\n  }\n  if (!hideBackButton && index > 0) {\n    output.back = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", { \"data-test-id\": \"button-back\", style: styles.buttonBack, type: \"button\", ...backProps });\n  }\n  output.close = !hideCloseButton && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CloseButton_default, { \"data-test-id\": \"button-close\", styles: styles.buttonClose, ...closeProps });\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      key: \"JoyrideTooltip\",\n      \"aria-label\": getReactNodeText(title != null ? title : content),\n      className: \"react-joyride__tooltip\",\n      style: styles.tooltip,\n      ...tooltipProps\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.tooltipContainer }, title && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h1\", { \"aria-label\": getReactNodeText(title), style: styles.tooltipTitle }, title), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.tooltipContent }, content)),\n    !hideFooter && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.tooltipFooter }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.tooltipFooterSpacer }, output.skip), output.back, output.primary),\n    output.close\n  );\n}\nvar Container_default = JoyrideTooltipContainer;\n\n// src/components/Tooltip/index.tsx\nvar JoyrideTooltip = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"handleClickBack\", (event) => {\n      event.preventDefault();\n      const { helpers } = this.props;\n      helpers.prev();\n    });\n    __publicField(this, \"handleClickClose\", (event) => {\n      event.preventDefault();\n      const { helpers } = this.props;\n      helpers.close(\"button_close\");\n    });\n    __publicField(this, \"handleClickPrimary\", (event) => {\n      event.preventDefault();\n      const { continuous, helpers } = this.props;\n      if (!continuous) {\n        helpers.close(\"button_primary\");\n        return;\n      }\n      helpers.next();\n    });\n    __publicField(this, \"handleClickSkip\", (event) => {\n      event.preventDefault();\n      const { helpers } = this.props;\n      helpers.skip();\n    });\n    __publicField(this, \"getElementsProps\", () => {\n      const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n      const { back, close, last, next, nextLabelWithProgress, skip } = step.locale;\n      const backText = getReactNodeText(back);\n      const closeText = getReactNodeText(close);\n      const lastText = getReactNodeText(last);\n      const nextText = getReactNodeText(next);\n      const skipText = getReactNodeText(skip);\n      let primary = close;\n      let primaryText = closeText;\n      if (continuous) {\n        primary = next;\n        primaryText = nextText;\n        if (step.showProgress && !isLastStep) {\n          const labelWithProgress = getReactNodeText(nextLabelWithProgress, {\n            step: index + 1,\n            steps: size\n          });\n          primary = replaceLocaleContent(nextLabelWithProgress, index + 1, size);\n          primaryText = labelWithProgress;\n        }\n        if (isLastStep) {\n          primary = last;\n          primaryText = lastText;\n        }\n      }\n      return {\n        backProps: {\n          \"aria-label\": backText,\n          children: back,\n          \"data-action\": \"back\",\n          onClick: this.handleClickBack,\n          role: \"button\",\n          title: backText\n        },\n        closeProps: {\n          \"aria-label\": closeText,\n          children: close,\n          \"data-action\": \"close\",\n          onClick: this.handleClickClose,\n          role: \"button\",\n          title: closeText\n        },\n        primaryProps: {\n          \"aria-label\": primaryText,\n          children: primary,\n          \"data-action\": \"primary\",\n          onClick: this.handleClickPrimary,\n          role: \"button\",\n          title: primaryText\n        },\n        skipProps: {\n          \"aria-label\": skipText,\n          children: skip,\n          \"data-action\": \"skip\",\n          onClick: this.handleClickSkip,\n          role: \"button\",\n          title: skipText\n        },\n        tooltipProps: {\n          \"aria-modal\": true,\n          ref: setTooltipRef,\n          role: \"alertdialog\"\n        }\n      };\n    });\n  }\n  render() {\n    const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n    const { beaconComponent, tooltipComponent, ...cleanStep } = step;\n    let component;\n    if (tooltipComponent) {\n      const renderProps = {\n        ...this.getElementsProps(),\n        continuous,\n        index,\n        isLastStep,\n        size,\n        step: cleanStep,\n        setTooltipRef\n      };\n      const TooltipComponent = tooltipComponent;\n      component = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TooltipComponent, { ...renderProps });\n    } else {\n      component = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        Container_default,\n        {\n          ...this.getElementsProps(),\n          continuous,\n          index,\n          isLastStep,\n          size,\n          step\n        }\n      );\n    }\n    return component;\n  }\n};\n\n// src/components/Step.tsx\nvar JoyrideStep = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"scope\", null);\n    __publicField(this, \"tooltip\", null);\n    /**\n     * Beacon click/hover event listener\n     */\n    __publicField(this, \"handleClickHoverBeacon\", (event) => {\n      const { step, store } = this.props;\n      if (event.type === \"mouseenter\" && step.event !== \"hover\") {\n        return;\n      }\n      store.update({ lifecycle: LIFECYCLE.TOOLTIP });\n    });\n    __publicField(this, \"setTooltipRef\", (element) => {\n      this.tooltip = element;\n    });\n    __publicField(this, \"setPopper\", (popper, type) => {\n      var _a;\n      const { action, lifecycle, step, store } = this.props;\n      if (type === \"wrapper\") {\n        store.setPopper(\"beacon\", popper);\n      } else {\n        store.setPopper(\"tooltip\", popper);\n      }\n      if (store.getPopper(\"beacon\") && (store.getPopper(\"tooltip\") || step.placement === \"center\") && lifecycle === LIFECYCLE.INIT) {\n        store.update({\n          action,\n          lifecycle: LIFECYCLE.READY\n        });\n      }\n      if ((_a = step.floaterProps) == null ? void 0 : _a.getPopper) {\n        step.floaterProps.getPopper(popper, type);\n      }\n    });\n    __publicField(this, \"renderTooltip\", (renderProps) => {\n      const { continuous, helpers, index, size, step } = this.props;\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        JoyrideTooltip,\n        {\n          continuous,\n          helpers,\n          index,\n          isLastStep: index + 1 === size,\n          setTooltipRef: this.setTooltipRef,\n          size,\n          step,\n          ...renderProps\n        }\n      );\n    });\n  }\n  componentDidMount() {\n    const { debug, index } = this.props;\n    log({\n      title: `step:${index}`,\n      data: [{ key: \"props\", value: this.props }],\n      debug\n    });\n  }\n  componentDidUpdate(previousProps) {\n    var _a;\n    const {\n      action,\n      callback,\n      continuous,\n      controlled,\n      debug,\n      helpers,\n      index,\n      lifecycle,\n      shouldScroll: shouldScroll2,\n      status,\n      step,\n      store\n    } = this.props;\n    const { changed, changedFrom } = (0,tree_changes__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(previousProps, this.props);\n    const state = helpers.info();\n    const skipBeacon = continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV);\n    const hasStoreChanged = changed(\"action\") || changed(\"index\") || changed(\"lifecycle\") || changed(\"status\");\n    const isInitial = changedFrom(\"lifecycle\", [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT);\n    const isAfterAction = changed(\"action\", [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE\n    ]);\n    const isControlled = controlled && index === previousProps.index;\n    if (isAfterAction && (isInitial || isControlled)) {\n      callback({\n        ...state,\n        index: previousProps.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousProps.step,\n        type: EVENTS.STEP_AFTER\n      });\n    }\n    if (step.placement === \"center\" && status === STATUS.RUNNING && changed(\"index\") && action !== ACTIONS.START && lifecycle === LIFECYCLE.INIT) {\n      store.update({ lifecycle: LIFECYCLE.READY });\n    }\n    if (hasStoreChanged) {\n      const element = getElement(step.target);\n      const elementExists = !!element;\n      const hasRenderedTarget = elementExists && isElementVisible(element);\n      if (hasRenderedTarget) {\n        if (changedFrom(\"status\", STATUS.READY, STATUS.RUNNING) || changedFrom(\"lifecycle\", LIFECYCLE.INIT, LIFECYCLE.READY)) {\n          callback({\n            ...state,\n            step,\n            type: EVENTS.STEP_BEFORE\n          });\n        }\n      } else {\n        console.warn(elementExists ? \"Target not visible\" : \"Target not mounted\", step);\n        callback({\n          ...state,\n          type: EVENTS.TARGET_NOT_FOUND,\n          step\n        });\n        if (!controlled) {\n          store.update({ index: index + (action === ACTIONS.PREV ? -1 : 1) });\n        }\n      }\n    }\n    if (changedFrom(\"lifecycle\", LIFECYCLE.INIT, LIFECYCLE.READY)) {\n      store.update({\n        lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON\n      });\n    }\n    if (changed(\"index\")) {\n      log({\n        title: `step:${lifecycle}`,\n        data: [{ key: \"props\", value: this.props }],\n        debug\n      });\n    }\n    if (changed(\"lifecycle\", LIFECYCLE.BEACON)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.BEACON\n      });\n    }\n    if (changed(\"lifecycle\", LIFECYCLE.TOOLTIP)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.TOOLTIP\n      });\n      if (shouldScroll2 && this.tooltip) {\n        this.scope = new Scope(this.tooltip, { selector: \"[data-action=primary]\" });\n        this.scope.setFocus();\n      }\n    }\n    if (changedFrom(\"lifecycle\", [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT)) {\n      (_a = this.scope) == null ? void 0 : _a.removeScope();\n      store.cleanupPoppers();\n    }\n  }\n  componentWillUnmount() {\n    var _a;\n    (_a = this.scope) == null ? void 0 : _a.removeScope();\n  }\n  get open() {\n    const { lifecycle, step } = this.props;\n    return hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP;\n  }\n  render() {\n    const { continuous, debug, index, nonce, shouldScroll: shouldScroll2, size, step } = this.props;\n    const target = getElement(step.target);\n    if (!validateStep(step) || !is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].domElement(target)) {\n      return null;\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { key: `JoyrideStep-${index}`, className: \"react-joyride__step\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      react_floater__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n      {\n        ...step.floaterProps,\n        component: this.renderTooltip,\n        debug,\n        getPopper: this.setPopper,\n        id: `react-joyride-step-${index}`,\n        open: this.open,\n        placement: step.placement,\n        target: step.target\n      },\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        JoyrideBeacon,\n        {\n          beaconComponent: step.beaconComponent,\n          continuous,\n          index,\n          isLastStep: index + 1 === size,\n          locale: step.locale,\n          nonce,\n          onClickOrHover: this.handleClickHoverBeacon,\n          shouldFocus: shouldScroll2,\n          size,\n          step,\n          styles: step.styles\n        }\n      )\n    ));\n  }\n};\n\n// src/components/index.tsx\nvar Joyride = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(props) {\n    super(props);\n    __publicField(this, \"helpers\");\n    __publicField(this, \"store\");\n    /**\n     * Trigger the callback.\n     */\n    __publicField(this, \"callback\", (data) => {\n      const { callback } = this.props;\n      if (is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].function(callback)) {\n        callback(data);\n      }\n    });\n    /**\n     * Keydown event listener\n     */\n    __publicField(this, \"handleKeyboard\", (event) => {\n      const { index, lifecycle } = this.state;\n      const { steps } = this.props;\n      const step = steps[index];\n      if (lifecycle === LIFECYCLE.TOOLTIP) {\n        if (event.code === \"Escape\" && step && !step.disableCloseOnEsc) {\n          this.store.close(\"keyboard\");\n        }\n      }\n    });\n    __publicField(this, \"handleClickOverlay\", () => {\n      const { index } = this.state;\n      const { steps } = this.props;\n      const step = getMergedStep(this.props, steps[index]);\n      if (!step.disableOverlayClose) {\n        this.helpers.close(\"overlay\");\n      }\n    });\n    /**\n     * Sync the store with the component's state\n     */\n    __publicField(this, \"syncState\", (state) => {\n      this.setState(state);\n    });\n    const { debug, getHelpers, run = true, stepIndex } = props;\n    this.store = createStore({\n      ...props,\n      controlled: run && is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(stepIndex)\n    });\n    this.helpers = this.store.getHelpers();\n    const { addListener } = this.store;\n    log({\n      title: \"init\",\n      data: [\n        { key: \"props\", value: this.props },\n        { key: \"state\", value: this.state }\n      ],\n      debug\n    });\n    addListener(this.syncState);\n    if (getHelpers) {\n      getHelpers(this.helpers);\n    }\n    this.state = this.store.getState();\n  }\n  componentDidMount() {\n    if (!canUseDOM()) {\n      return;\n    }\n    const { debug, disableCloseOnEsc, run, steps } = this.props;\n    const { start } = this.store;\n    if (validateSteps(steps, debug) && run) {\n      start();\n    }\n    if (!disableCloseOnEsc) {\n      document.body.addEventListener(\"keydown\", this.handleKeyboard, { passive: true });\n    }\n  }\n  componentDidUpdate(previousProps, previousState) {\n    if (!canUseDOM()) {\n      return;\n    }\n    const { action, controlled, index, status } = this.state;\n    const { debug, run, stepIndex, steps } = this.props;\n    const { stepIndex: previousStepIndex, steps: previousSteps } = previousProps;\n    const { reset, setSteps, start, stop, update } = this.store;\n    const { changed: changedProps } = (0,tree_changes__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(previousProps, this.props);\n    const { changed, changedFrom } = (0,tree_changes__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(previousState, this.state);\n    const step = getMergedStep(this.props, steps[index]);\n    const stepsChanged = !(0,_gilbarbara_deep_equal__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(previousSteps, steps);\n    const stepIndexChanged = is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(stepIndex) && changedProps(\"stepIndex\");\n    const target = getElement(step.target);\n    if (stepsChanged) {\n      if (validateSteps(steps, debug)) {\n        setSteps(steps);\n      } else {\n        console.warn(\"Steps are not valid\", steps);\n      }\n    }\n    if (changedProps(\"run\")) {\n      if (run) {\n        start(stepIndex);\n      } else {\n        stop();\n      }\n    }\n    if (stepIndexChanged) {\n      let nextAction = is_lite__WEBPACK_IMPORTED_MODULE_5__[\"default\"].number(previousStepIndex) && previousStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;\n      if (action === ACTIONS.STOP) {\n        nextAction = ACTIONS.START;\n      }\n      if (![STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {\n        update({\n          action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction,\n          index: stepIndex,\n          lifecycle: LIFECYCLE.INIT\n        });\n      }\n    }\n    if (!controlled && status === STATUS.RUNNING && index === 0 && !target) {\n      this.store.update({ index: index + 1 });\n      this.callback({\n        ...this.state,\n        type: EVENTS.TARGET_NOT_FOUND,\n        step\n      });\n    }\n    const callbackData = {\n      ...this.state,\n      index,\n      step\n    };\n    const isAfterAction = changed(\"action\", [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE\n    ]);\n    if (isAfterAction && changed(\"status\", STATUS.PAUSED)) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n      this.callback({\n        ...callbackData,\n        index: previousState.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousStep,\n        type: EVENTS.STEP_AFTER\n      });\n    }\n    if (changed(\"status\", [STATUS.FINISHED, STATUS.SKIPPED])) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n      if (!controlled) {\n        this.callback({\n          ...callbackData,\n          index: previousState.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: previousStep,\n          type: EVENTS.STEP_AFTER\n        });\n      }\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_END,\n        // Return the last step when the tour is finished\n        step: previousStep,\n        index: previousState.index\n      });\n      reset();\n    } else if (changedFrom(\"status\", [STATUS.IDLE, STATUS.READY], STATUS.RUNNING)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_START\n      });\n    } else if (changed(\"status\") || changed(\"action\", ACTIONS.RESET)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_STATUS\n      });\n    }\n    this.scrollToStep(previousState);\n  }\n  componentWillUnmount() {\n    const { disableCloseOnEsc } = this.props;\n    if (!disableCloseOnEsc) {\n      document.body.removeEventListener(\"keydown\", this.handleKeyboard);\n    }\n  }\n  scrollToStep(previousState) {\n    const { index, lifecycle, status } = this.state;\n    const {\n      debug,\n      disableScrollParentFix = false,\n      scrollDuration,\n      scrollOffset = 20,\n      scrollToFirstStep = false,\n      steps\n    } = this.props;\n    const step = getMergedStep(this.props, steps[index]);\n    const target = getElement(step.target);\n    const shouldScrollToStep = shouldScroll({\n      isFirstStep: index === 0,\n      lifecycle,\n      previousLifecycle: previousState.lifecycle,\n      scrollToFirstStep,\n      step,\n      target\n    });\n    if (status === STATUS.RUNNING && shouldScrollToStep) {\n      const hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix);\n      const scrollParent2 = getScrollParent(target, disableScrollParentFix);\n      let scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;\n      log({\n        title: \"scrollToStep\",\n        data: [\n          { key: \"index\", value: index },\n          { key: \"lifecycle\", value: lifecycle },\n          { key: \"status\", value: status }\n        ],\n        debug\n      });\n      const beaconPopper = this.store.getPopper(\"beacon\");\n      const tooltipPopper = this.store.getPopper(\"tooltip\");\n      if (lifecycle === LIFECYCLE.BEACON && beaconPopper) {\n        const { offsets, placement } = beaconPopper;\n        if (![\"bottom\"].includes(placement) && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        }\n      } else if (lifecycle === LIFECYCLE.TOOLTIP && tooltipPopper) {\n        const { flipped, offsets, placement } = tooltipPopper;\n        if ([\"top\", \"right\", \"left\"].includes(placement) && !flipped && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        } else {\n          scrollY -= step.spotlightPadding;\n        }\n      }\n      scrollY = scrollY >= 0 ? scrollY : 0;\n      if (status === STATUS.RUNNING) {\n        scrollTo(scrollY, { element: scrollParent2, duration: scrollDuration }).then(\n          () => {\n            setTimeout(() => {\n              var _a;\n              (_a = this.store.getPopper(\"tooltip\")) == null ? void 0 : _a.instance.update();\n            }, 10);\n          }\n        );\n      }\n    }\n  }\n  render() {\n    if (!canUseDOM()) {\n      return null;\n    }\n    const { index, lifecycle, status } = this.state;\n    const {\n      continuous = false,\n      debug = false,\n      nonce,\n      scrollToFirstStep = false,\n      steps\n    } = this.props;\n    const isRunning = status === STATUS.RUNNING;\n    const content = {};\n    if (isRunning && steps[index]) {\n      const step = getMergedStep(this.props, steps[index]);\n      content.step = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        JoyrideStep,\n        {\n          ...this.state,\n          callback: this.callback,\n          continuous,\n          debug,\n          helpers: this.helpers,\n          nonce,\n          shouldScroll: !step.disableScrolling && (index !== 0 || scrollToFirstStep),\n          step,\n          store: this.store\n        }\n      );\n      content.overlay = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(JoyridePortal, { id: \"react-joyride-portal\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        JoyrideOverlay,\n        {\n          ...step,\n          continuous,\n          debug,\n          lifecycle,\n          onClickOverlay: this.handleClickOverlay\n        }\n      ));\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"react-joyride\" }, content.step, content.overlay);\n  }\n};\n__publicField(Joyride, \"defaultProps\", defaultProps);\nvar components_default = Joyride;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3Qtam95cmlkZUAyLjkuM19AdHlwZXMrcmVhY3RAMTguMy40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL3JlYWN0LWpveXJpZGUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0EsOEVBQThFLDZEQUE2RDtBQUMzSTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDZ0M7QUFDYTtBQUNuQjtBQUNjOztBQUV4QztBQUM0QjtBQUNZO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHdCQUF3QjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLFVBQVUsSUFBcUM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix5Q0FBWTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxXQUFXO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHNCQUFzQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsK0JBQStCLFFBQVEseUNBQVk7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxvQkFBb0I7QUFDOUI7QUFDQSxZQUFZLFlBQVk7QUFDeEI7QUFDQSxJQUFJLHVDQUFVLG1CQUFtQix1Q0FBdUM7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIOztBQUVBO0FBQ3FEO0FBQ1o7QUFDRDtBQUNmO0FBQ3pCLGdCQUFnQixtREFBWTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QyxVQUFVLDRCQUE0QjtBQUN0QyxhQUFhLDRDQUFTO0FBQ3RCO0FBQ0EsUUFBUSxxREFBYztBQUN0QixxQ0FBcUM7QUFDckM7QUFDQSxNQUFNO0FBQ04sYUFBYSw0Q0FBUztBQUN0QjtBQUNBLElBQUksMEJBQTBCLEtBQUssc0JBQXNCLE1BQU07QUFDL0QsMEJBQTBCLEtBQUssOEJBQThCLE1BQU07QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLCtDQUFFLHlCQUF5QiwrQ0FBRTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixFQUFFLFVBQVUsRUFBRSxVQUFVLEVBQUU7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsMENBQTBDO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLE1BQU07QUFDbEMseUJBQXlCLG1CQUFtQixnQkFBZ0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsY0FBYywrQ0FBRTtBQUNoQjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sK0NBQUU7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTywrQ0FBRTtBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsS0FBSywyQkFBMkIsTUFBTTtBQUNsRjtBQUNBO0FBQ0E7QUFDQSxPQUFPLHFEQUFjO0FBQ3JCO0FBQ0E7QUFDQSxVQUFVLFdBQVc7QUFDckIsa0VBQWtFLEtBQUs7QUFDdkUsV0FBVyxtREFBWTtBQUN2QjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsV0FBVyxtREFBWTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDZFQUE2RTtBQUN2RjtBQUNBO0FBQ0E7O0FBRUE7QUFDbUM7QUFDVDs7QUFFMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLE1BQU0sSUFBSSxNQUFNO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2tDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx1QkFBdUI7QUFDakMsNkJBQTZCLHNDQUFTLDJDQUEyQywwQ0FBMEM7QUFDM0gsdUJBQXVCLHNDQUFTLDZCQUE2QixzQ0FBc0M7QUFDbkcsa0JBQWtCLHNDQUFTLDJDQUEyQztBQUN0RTtBQUNBLFFBQVEsUUFBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsK0JBQStCLHlDQUF5QztBQUN4RSwyQkFBMkIscUJBQXFCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGlDQUFpQyxxQkFBcUIsSUFBSSx3QkFBd0I7QUFDbEYsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFNBQVMsc0NBQVM7QUFDbEI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsMENBQWM7QUFDbkMsdUJBQXVCLCtDQUFHO0FBQzFCLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDBDQUFjO0FBQ3JDO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixzQ0FBVSw2Q0FBNkM7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSwwQ0FBYyxzREFBc0QseUJBQXlCO0FBQ3pHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLCtDQUFHO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLCtDQUFHO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUMwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGNBQWMsZUFBZTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxjQUFjLGdCQUFnQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixpREFBaUQ7QUFDaEYsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLGNBQWMscUJBQXFCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isc0NBQXNDO0FBQ3JFO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0EsY0FBYyxnQkFBZ0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLHdDQUF3QztBQUNoRixLQUFLO0FBQ0w7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isc0RBQXNEO0FBQ3JGLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQSxjQUFjLGdCQUFnQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix3Q0FBd0M7QUFDdkUsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLGNBQWMsYUFBYTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixpQ0FBaUM7QUFDaEU7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLGNBQWMsY0FBYztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBRztBQUN0QixXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLGNBQWMsZ0JBQWdCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLHdEQUF3RDtBQUN2RjtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELHFCQUFxQjtBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLFlBQVksNENBQTRDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiwrQ0FBRztBQUN2QjtBQUNBLGVBQWUsK0NBQUc7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksMENBQTBDO0FBQ3RELHFCQUFxQiwrQ0FBRztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNnQztBQUNPOztBQUV2QztBQUMrQjtBQUMvQiw0QkFBNEIsUUFBUTtBQUNwQyx5QkFBeUIsZ0RBQW1CO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsbUNBQW1DLDRDQUFnQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGNBQWMsd0NBQXdDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsY0FBYyxxQkFBcUI7QUFDbkMsY0FBYyxxQ0FBcUM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGlDQUFpQztBQUM1RDtBQUNBLEtBQUs7QUFDTDtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBO0FBQ0EsZ0JBQWdCLGNBQWM7QUFDOUI7QUFDQSw2QkFBNkIseUNBQXlDO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qix5Q0FBeUM7QUFDdEUsU0FBUztBQUNULFFBQVE7QUFDUiwyQkFBMkI7QUFDM0I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZLGtFQUFrRTtBQUM5RTtBQUNBO0FBQ0E7QUFDQSxRQUFRLElBQXFDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5Q0FBeUM7QUFDNUQ7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw2REFBNkQ7QUFDekUsWUFBWSxVQUFVLEVBQUUsd0RBQVc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFHQUFxRyxlQUFlO0FBQ3BIO0FBQ0EsZ0JBQWdCLGNBQWM7QUFDOUI7QUFDQSw2QkFBNkIscUJBQXFCO0FBQ2xEO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHFCQUFxQjtBQUNqQyxZQUFZLHlDQUF5QztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLDRCQUE0QjtBQUNwRTtBQUNBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUIsWUFBWSw0QkFBNEI7QUFDeEMsWUFBWSxnREFBZ0Q7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsZ0ZBQWdGLGdEQUFvQixzQkFBc0IseUJBQXlCO0FBQ25KO0FBQ0EsY0FBYyx5Q0FBeUM7QUFDdkQsbUNBQW1DLGdEQUFvQixVQUFVLFNBQVMsb0JBQW9CO0FBQzlGO0FBQ0E7QUFDQSwyQkFBMkIsZ0RBQW9CO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2dDO0FBQ007QUFDdEMsa0NBQWtDLDRDQUFnQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxLQUFLO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDZEQUErQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksV0FBVztBQUN2QjtBQUNBLE1BQU0sMEVBQTRDO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksV0FBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1EQUFxQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2dDO0FBQ0k7QUFDVjtBQUNjOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFdBQVc7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsY0FBYyxlQUFlO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxjQUFjLFdBQVc7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsY0FBYyxXQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNnQztBQUNOO0FBQzFCLGtDQUFrQyw0Q0FBZ0I7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQixRQUFRLElBQXFDO0FBQzdDLFdBQVcsK0NBQUc7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsK0NBQUc7QUFDYjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGdEQUFvQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixrQ0FBa0MsZ0RBQW9CO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Qsd0JBQXdCLGdEQUFvQixXQUFXLDJCQUEyQjtBQUNsRix3QkFBd0IsZ0RBQW9CLFdBQVcsMkJBQTJCO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDZ0M7O0FBRWhDO0FBQ2dDOztBQUVoQztBQUMyQjtBQUMzQixxQ0FBcUMsa0JBQWtCO0FBQ3ZELFVBQVUsaUNBQWlDO0FBQzNDLHlCQUF5QixnREFBb0IsYUFBYSxpQ0FBaUMsa0JBQWtCLGdEQUFvQjtBQUNqSTtBQUNBO0FBQ0EsOENBQThDLE9BQU87QUFDckQ7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLE1BQU07QUFDbEQ7QUFDQSxLQUFLO0FBQ0wsb0JBQW9CLGdEQUFvQiw0QkFBNEIsZ0RBQW9CO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVSx3RkFBd0Y7QUFDbEcsVUFBVSxzRkFBc0Y7QUFDaEc7QUFDQSxtQ0FBbUMsZ0RBQW9CO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxnREFBb0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxnREFBb0IsYUFBYSx1RkFBdUY7QUFDMUo7QUFDQSxxREFBcUQsZ0RBQW9CLHdCQUF3QiwyRUFBMkU7QUFDNUsseUJBQXlCLGdEQUFvQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxvQkFBb0IsZ0RBQW9CLFVBQVUsZ0NBQWdDLDJCQUEyQixnREFBb0IsU0FBUyxtRUFBbUUsMEJBQTBCLGdEQUFvQixVQUFVLDhCQUE4QjtBQUNuUyxtQ0FBbUMsZ0RBQW9CLFVBQVUsNkJBQTZCLGtCQUFrQixnREFBb0IsVUFBVSxtQ0FBbUM7QUFDakw7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxtQ0FBbUMsNENBQWdCO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxVQUFVO0FBQ3hCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxjQUFjLFVBQVU7QUFDeEI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsc0JBQXNCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsVUFBVTtBQUN4QjtBQUNBLEtBQUs7QUFDTDtBQUNBLGNBQWMsMkRBQTJEO0FBQ3pFLGNBQWMsdURBQXVEO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZLDJEQUEyRDtBQUN2RSxZQUFZLGtEQUFrRDtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsZ0RBQW9CLHFCQUFxQixnQkFBZ0I7QUFDM0YsTUFBTTtBQUNOLGtDQUFrQyxnREFBb0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGdDQUFnQyw0Q0FBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsY0FBYztBQUM1QjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsOEJBQThCO0FBQ25ELEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxjQUFjLGlDQUFpQztBQUMvQztBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGNBQWMseUNBQXlDO0FBQ3ZELDZCQUE2QixnREFBb0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZLGVBQWU7QUFDM0I7QUFDQSxxQkFBcUIsTUFBTTtBQUMzQixlQUFlLGlDQUFpQztBQUNoRDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixZQUFZLHVCQUF1QixFQUFFLHdEQUFZO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EscUJBQXFCLDRCQUE0QjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSx5QkFBeUIsbURBQW1EO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsVUFBVTtBQUNqQyxpQkFBaUIsaUNBQWlDO0FBQ2xEO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSwrQ0FBK0MsbUNBQW1DO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxrQkFBa0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsWUFBWSwyRUFBMkU7QUFDdkY7QUFDQSxnQ0FBZ0MsK0NBQUc7QUFDbkM7QUFDQTtBQUNBLDJCQUEyQixnREFBb0IsVUFBVSxvQkFBb0IsTUFBTSxxQ0FBcUMsa0JBQWtCLGdEQUFvQjtBQUM5SixNQUFNLHFEQUFPO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxNQUFNO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxzQkFBc0IsZ0RBQW9CO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDRCQUE0Qiw0Q0FBZ0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsV0FBVztBQUN6QixVQUFVLCtDQUFHO0FBQ2I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbUJBQW1CO0FBQ2pDLGNBQWMsUUFBUTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxjQUFjLFFBQVE7QUFDdEIsY0FBYyxRQUFRO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWSwyQ0FBMkM7QUFDdkQ7QUFDQTtBQUNBLHlCQUF5QiwrQ0FBRztBQUM1QixLQUFLO0FBQ0w7QUFDQSxZQUFZLGNBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsVUFBVSxpQ0FBaUM7QUFDM0MsVUFBVTtBQUNWO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVDQUF1QztBQUNuRCxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RUFBdUUsZUFBZTtBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLG9DQUFvQztBQUNoRCxZQUFZLCtCQUErQjtBQUMzQyxZQUFZLHFEQUFxRDtBQUNqRSxZQUFZLHVDQUF1QztBQUNuRCxZQUFZLHdCQUF3QixFQUFFLHdEQUFZO0FBQ2xELFlBQVksdUJBQXVCLEVBQUUsd0RBQVk7QUFDakQ7QUFDQSwwQkFBMEIsa0VBQU87QUFDakMsNkJBQTZCLCtDQUFHO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwrQ0FBRztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixrQkFBa0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksb0JBQW9CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDJCQUEyQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDRCQUE0QjtBQUN4QyxZQUFZLG9DQUFvQztBQUNoRCxZQUFZO0FBQ1o7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IscUJBQXFCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUixnQkFBZ0IsOEJBQThCO0FBQzlDO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixrREFBa0Q7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSwyQkFBMkI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGdEQUFvQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxnREFBb0Isa0JBQWtCLDRCQUE0QixrQkFBa0IsZ0RBQW9CO0FBQ2hKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGdEQUFvQixVQUFVLDRCQUE0QjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQVFFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3Qtam95cmlkZUAyLjkuM19AdHlwZXMrcmVhY3RAMTguMy40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL3JlYWN0LWpveXJpZGUvZGlzdC9pbmRleC5tanM/NGIxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZGVmTm9ybWFsUHJvcCA9IChvYmosIGtleSwgdmFsdWUpID0+IGtleSBpbiBvYmogPyBfX2RlZlByb3Aob2JqLCBrZXksIHsgZW51bWVyYWJsZTogdHJ1ZSwgY29uZmlndXJhYmxlOiB0cnVlLCB3cml0YWJsZTogdHJ1ZSwgdmFsdWUgfSkgOiBvYmpba2V5XSA9IHZhbHVlO1xudmFyIF9fcHVibGljRmllbGQgPSAob2JqLCBrZXksIHZhbHVlKSA9PiBfX2RlZk5vcm1hbFByb3Aob2JqLCB0eXBlb2Yga2V5ICE9PSBcInN5bWJvbFwiID8ga2V5ICsgXCJcIiA6IGtleSwgdmFsdWUpO1xuXG4vLyBzcmMvbGl0ZXJhbHMvaW5kZXgudHNcbnZhciBBQ1RJT05TID0ge1xuICBJTklUOiBcImluaXRcIixcbiAgU1RBUlQ6IFwic3RhcnRcIixcbiAgU1RPUDogXCJzdG9wXCIsXG4gIFJFU0VUOiBcInJlc2V0XCIsXG4gIFBSRVY6IFwicHJldlwiLFxuICBORVhUOiBcIm5leHRcIixcbiAgR086IFwiZ29cIixcbiAgQ0xPU0U6IFwiY2xvc2VcIixcbiAgU0tJUDogXCJza2lwXCIsXG4gIFVQREFURTogXCJ1cGRhdGVcIlxufTtcbnZhciBFVkVOVFMgPSB7XG4gIFRPVVJfU1RBUlQ6IFwidG91cjpzdGFydFwiLFxuICBTVEVQX0JFRk9SRTogXCJzdGVwOmJlZm9yZVwiLFxuICBCRUFDT046IFwiYmVhY29uXCIsXG4gIFRPT0xUSVA6IFwidG9vbHRpcFwiLFxuICBTVEVQX0FGVEVSOiBcInN0ZXA6YWZ0ZXJcIixcbiAgVE9VUl9FTkQ6IFwidG91cjplbmRcIixcbiAgVE9VUl9TVEFUVVM6IFwidG91cjpzdGF0dXNcIixcbiAgVEFSR0VUX05PVF9GT1VORDogXCJlcnJvcjp0YXJnZXRfbm90X2ZvdW5kXCIsXG4gIEVSUk9SOiBcImVycm9yXCJcbn07XG52YXIgTElGRUNZQ0xFID0ge1xuICBJTklUOiBcImluaXRcIixcbiAgUkVBRFk6IFwicmVhZHlcIixcbiAgQkVBQ09OOiBcImJlYWNvblwiLFxuICBUT09MVElQOiBcInRvb2x0aXBcIixcbiAgQ09NUExFVEU6IFwiY29tcGxldGVcIixcbiAgRVJST1I6IFwiZXJyb3JcIlxufTtcbnZhciBPUklHSU4gPSB7XG4gIEJVVFRPTl9DTE9TRTogXCJidXR0b25fY2xvc2VcIixcbiAgQlVUVE9OX1BSSU1BUlk6IFwiYnV0dG9uX3ByaW1hcnlcIixcbiAgS0VZQk9BUkQ6IFwia2V5Ym9hcmRcIixcbiAgT1ZFUkxBWTogXCJvdmVybGF5XCJcbn07XG52YXIgU1RBVFVTID0ge1xuICBJRExFOiBcImlkbGVcIixcbiAgUkVBRFk6IFwicmVhZHlcIixcbiAgV0FJVElORzogXCJ3YWl0aW5nXCIsXG4gIFJVTk5JTkc6IFwicnVubmluZ1wiLFxuICBQQVVTRUQ6IFwicGF1c2VkXCIsXG4gIFNLSVBQRUQ6IFwic2tpcHBlZFwiLFxuICBGSU5JU0hFRDogXCJmaW5pc2hlZFwiLFxuICBFUlJPUjogXCJlcnJvclwiXG59O1xuXG4vLyBzcmMvY29tcG9uZW50cy9pbmRleC50c3hcbmltcG9ydCAqIGFzIFJlYWN0OSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBpc0VxdWFsIGZyb20gXCJAZ2lsYmFyYmFyYS9kZWVwLWVxdWFsXCI7XG5pbXBvcnQgaXM2IGZyb20gXCJpcy1saXRlXCI7XG5pbXBvcnQgdHJlZUNoYW5nZXMzIGZyb20gXCJ0cmVlLWNoYW5nZXNcIjtcblxuLy8gc3JjL21vZHVsZXMvZG9tLnRzXG5pbXBvcnQgc2Nyb2xsIGZyb20gXCJzY3JvbGxcIjtcbmltcG9ydCBzY3JvbGxQYXJlbnQgZnJvbSBcInNjcm9sbHBhcmVudFwiO1xuZnVuY3Rpb24gY2FuVXNlRE9NKCkge1xuICB2YXIgX2E7XG4gIHJldHVybiAhISh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmICgoX2EgPSB3aW5kb3cuZG9jdW1lbnQpID09IG51bGwgPyB2b2lkIDAgOiBfYS5jcmVhdGVFbGVtZW50KSk7XG59XG5mdW5jdGlvbiBnZXRDbGllbnRSZWN0KGVsZW1lbnQpIHtcbiAgaWYgKCFlbGVtZW50KSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgcmV0dXJuIGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XG59XG5mdW5jdGlvbiBnZXREb2N1bWVudEhlaWdodChtZWRpYW4gPSBmYWxzZSkge1xuICBjb25zdCB7IGJvZHksIGRvY3VtZW50RWxlbWVudCB9ID0gZG9jdW1lbnQ7XG4gIGlmICghYm9keSB8fCAhZG9jdW1lbnRFbGVtZW50KSB7XG4gICAgcmV0dXJuIDA7XG4gIH1cbiAgaWYgKG1lZGlhbikge1xuICAgIGNvbnN0IGhlaWdodHMgPSBbXG4gICAgICBib2R5LnNjcm9sbEhlaWdodCxcbiAgICAgIGJvZHkub2Zmc2V0SGVpZ2h0LFxuICAgICAgZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCxcbiAgICAgIGRvY3VtZW50RWxlbWVudC5zY3JvbGxIZWlnaHQsXG4gICAgICBkb2N1bWVudEVsZW1lbnQub2Zmc2V0SGVpZ2h0XG4gICAgXS5zb3J0KChhLCBiKSA9PiBhIC0gYik7XG4gICAgY29uc3QgbWlkZGxlID0gTWF0aC5mbG9vcihoZWlnaHRzLmxlbmd0aCAvIDIpO1xuICAgIGlmIChoZWlnaHRzLmxlbmd0aCAlIDIgPT09IDApIHtcbiAgICAgIHJldHVybiAoaGVpZ2h0c1ttaWRkbGUgLSAxXSArIGhlaWdodHNbbWlkZGxlXSkgLyAyO1xuICAgIH1cbiAgICByZXR1cm4gaGVpZ2h0c1ttaWRkbGVdO1xuICB9XG4gIHJldHVybiBNYXRoLm1heChcbiAgICBib2R5LnNjcm9sbEhlaWdodCxcbiAgICBib2R5Lm9mZnNldEhlaWdodCxcbiAgICBkb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0LFxuICAgIGRvY3VtZW50RWxlbWVudC5zY3JvbGxIZWlnaHQsXG4gICAgZG9jdW1lbnRFbGVtZW50Lm9mZnNldEhlaWdodFxuICApO1xufVxuZnVuY3Rpb24gZ2V0RWxlbWVudChlbGVtZW50KSB7XG4gIGlmICh0eXBlb2YgZWxlbWVudCA9PT0gXCJzdHJpbmdcIikge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihlbGVtZW50KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfVxuICByZXR1cm4gZWxlbWVudDtcbn1cbmZ1bmN0aW9uIGdldFN0eWxlQ29tcHV0ZWRQcm9wZXJ0eShlbCkge1xuICBpZiAoIWVsIHx8IGVsLm5vZGVUeXBlICE9PSAxKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgcmV0dXJuIGdldENvbXB1dGVkU3R5bGUoZWwpO1xufVxuZnVuY3Rpb24gZ2V0U2Nyb2xsUGFyZW50KGVsZW1lbnQsIHNraXBGaXgsIGZvckxpc3RlbmVyKSB7XG4gIGlmICghZWxlbWVudCkge1xuICAgIHJldHVybiBzY3JvbGxEb2N1bWVudCgpO1xuICB9XG4gIGNvbnN0IHBhcmVudCA9IHNjcm9sbFBhcmVudChlbGVtZW50KTtcbiAgaWYgKHBhcmVudCkge1xuICAgIGlmIChwYXJlbnQuaXNTYW1lTm9kZShzY3JvbGxEb2N1bWVudCgpKSkge1xuICAgICAgaWYgKGZvckxpc3RlbmVyKSB7XG4gICAgICAgIHJldHVybiBkb2N1bWVudDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzY3JvbGxEb2N1bWVudCgpO1xuICAgIH1cbiAgICBjb25zdCBoYXNTY3JvbGxpbmcgPSBwYXJlbnQuc2Nyb2xsSGVpZ2h0ID4gcGFyZW50Lm9mZnNldEhlaWdodDtcbiAgICBpZiAoIWhhc1Njcm9sbGluZyAmJiAhc2tpcEZpeCkge1xuICAgICAgcGFyZW50LnN0eWxlLm92ZXJmbG93ID0gXCJpbml0aWFsXCI7XG4gICAgICByZXR1cm4gc2Nyb2xsRG9jdW1lbnQoKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHBhcmVudDtcbn1cbmZ1bmN0aW9uIGhhc0N1c3RvbVNjcm9sbFBhcmVudChlbGVtZW50LCBza2lwRml4KSB7XG4gIGlmICghZWxlbWVudCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjb25zdCBwYXJlbnQgPSBnZXRTY3JvbGxQYXJlbnQoZWxlbWVudCwgc2tpcEZpeCk7XG4gIHJldHVybiBwYXJlbnQgPyAhcGFyZW50LmlzU2FtZU5vZGUoc2Nyb2xsRG9jdW1lbnQoKSkgOiBmYWxzZTtcbn1cbmZ1bmN0aW9uIGhhc0N1c3RvbU9mZnNldFBhcmVudChlbGVtZW50KSB7XG4gIHJldHVybiBlbGVtZW50Lm9mZnNldFBhcmVudCAhPT0gZG9jdW1lbnQuYm9keTtcbn1cbmZ1bmN0aW9uIGhhc1Bvc2l0aW9uKGVsLCB0eXBlID0gXCJmaXhlZFwiKSB7XG4gIGlmICghZWwgfHwgIShlbCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50KSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjb25zdCB7IG5vZGVOYW1lIH0gPSBlbDtcbiAgY29uc3Qgc3R5bGVzID0gZ2V0U3R5bGVDb21wdXRlZFByb3BlcnR5KGVsKTtcbiAgaWYgKG5vZGVOYW1lID09PSBcIkJPRFlcIiB8fCBub2RlTmFtZSA9PT0gXCJIVE1MXCIpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgaWYgKHN0eWxlcyAmJiBzdHlsZXMucG9zaXRpb24gPT09IHR5cGUpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICBpZiAoIWVsLnBhcmVudE5vZGUpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgcmV0dXJuIGhhc1Bvc2l0aW9uKGVsLnBhcmVudE5vZGUsIHR5cGUpO1xufVxuZnVuY3Rpb24gaXNFbGVtZW50VmlzaWJsZShlbGVtZW50KSB7XG4gIHZhciBfYTtcbiAgaWYgKCFlbGVtZW50KSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGxldCBwYXJlbnRFbGVtZW50ID0gZWxlbWVudDtcbiAgd2hpbGUgKHBhcmVudEVsZW1lbnQpIHtcbiAgICBpZiAocGFyZW50RWxlbWVudCA9PT0gZG9jdW1lbnQuYm9keSkge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGlmIChwYXJlbnRFbGVtZW50IGluc3RhbmNlb2YgSFRNTEVsZW1lbnQpIHtcbiAgICAgIGNvbnN0IHsgZGlzcGxheSwgdmlzaWJpbGl0eSB9ID0gZ2V0Q29tcHV0ZWRTdHlsZShwYXJlbnRFbGVtZW50KTtcbiAgICAgIGlmIChkaXNwbGF5ID09PSBcIm5vbmVcIiB8fCB2aXNpYmlsaXR5ID09PSBcImhpZGRlblwiKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG4gICAgcGFyZW50RWxlbWVudCA9IChfYSA9IHBhcmVudEVsZW1lbnQucGFyZW50RWxlbWVudCkgIT0gbnVsbCA/IF9hIDogbnVsbDtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIGdldEVsZW1lbnRQb3NpdGlvbihlbGVtZW50LCBvZmZzZXQsIHNraXBGaXgpIHtcbiAgdmFyIF9hLCBfYiwgX2M7XG4gIGNvbnN0IGVsZW1lbnRSZWN0ID0gZ2V0Q2xpZW50UmVjdChlbGVtZW50KTtcbiAgY29uc3QgcGFyZW50ID0gZ2V0U2Nyb2xsUGFyZW50KGVsZW1lbnQsIHNraXBGaXgpO1xuICBjb25zdCBoYXNTY3JvbGxQYXJlbnQgPSBoYXNDdXN0b21TY3JvbGxQYXJlbnQoZWxlbWVudCwgc2tpcEZpeCk7XG4gIGNvbnN0IGlzRml4ZWRUYXJnZXQgPSBoYXNQb3NpdGlvbihlbGVtZW50KTtcbiAgbGV0IHBhcmVudFRvcCA9IDA7XG4gIGxldCB0b3AgPSAoX2EgPSBlbGVtZW50UmVjdCA9PSBudWxsID8gdm9pZCAwIDogZWxlbWVudFJlY3QudG9wKSAhPSBudWxsID8gX2EgOiAwO1xuICBpZiAoaGFzU2Nyb2xsUGFyZW50ICYmIGlzRml4ZWRUYXJnZXQpIHtcbiAgICBjb25zdCBvZmZzZXRUb3AgPSAoX2IgPSBlbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBlbGVtZW50Lm9mZnNldFRvcCkgIT0gbnVsbCA/IF9iIDogMDtcbiAgICBjb25zdCBwYXJlbnRTY3JvbGxUb3AgPSAoX2MgPSBwYXJlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBhcmVudC5zY3JvbGxUb3ApICE9IG51bGwgPyBfYyA6IDA7XG4gICAgdG9wID0gb2Zmc2V0VG9wIC0gcGFyZW50U2Nyb2xsVG9wO1xuICB9IGVsc2UgaWYgKHBhcmVudCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50KSB7XG4gICAgcGFyZW50VG9wID0gcGFyZW50LnNjcm9sbFRvcDtcbiAgICBpZiAoIWhhc1Njcm9sbFBhcmVudCAmJiAhaGFzUG9zaXRpb24oZWxlbWVudCkpIHtcbiAgICAgIHRvcCArPSBwYXJlbnRUb3A7XG4gICAgfVxuICAgIGlmICghcGFyZW50LmlzU2FtZU5vZGUoc2Nyb2xsRG9jdW1lbnQoKSkpIHtcbiAgICAgIHRvcCArPSBzY3JvbGxEb2N1bWVudCgpLnNjcm9sbFRvcDtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIE1hdGguZmxvb3IodG9wIC0gb2Zmc2V0KTtcbn1cbmZ1bmN0aW9uIGdldFNjcm9sbFRvKGVsZW1lbnQsIG9mZnNldCwgc2tpcEZpeCkge1xuICB2YXIgX2E7XG4gIGlmICghZWxlbWVudCkge1xuICAgIHJldHVybiAwO1xuICB9XG4gIGNvbnN0IHsgb2Zmc2V0VG9wID0gMCwgc2Nyb2xsVG9wID0gMCB9ID0gKF9hID0gc2Nyb2xsUGFyZW50KGVsZW1lbnQpKSAhPSBudWxsID8gX2EgOiB7fTtcbiAgbGV0IHRvcCA9IGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkudG9wICsgc2Nyb2xsVG9wO1xuICBpZiAoISFvZmZzZXRUb3AgJiYgKGhhc0N1c3RvbVNjcm9sbFBhcmVudChlbGVtZW50LCBza2lwRml4KSB8fCBoYXNDdXN0b21PZmZzZXRQYXJlbnQoZWxlbWVudCkpKSB7XG4gICAgdG9wIC09IG9mZnNldFRvcDtcbiAgfVxuICBjb25zdCBvdXRwdXQgPSBNYXRoLmZsb29yKHRvcCAtIG9mZnNldCk7XG4gIHJldHVybiBvdXRwdXQgPCAwID8gMCA6IG91dHB1dDtcbn1cbmZ1bmN0aW9uIHNjcm9sbERvY3VtZW50KCkge1xuICB2YXIgX2E7XG4gIHJldHVybiAoX2EgPSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50KSAhPSBudWxsID8gX2EgOiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7XG59XG5mdW5jdGlvbiBzY3JvbGxUbyh2YWx1ZSwgb3B0aW9ucykge1xuICBjb25zdCB7IGR1cmF0aW9uLCBlbGVtZW50IH0gPSBvcHRpb25zO1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGNvbnN0IHsgc2Nyb2xsVG9wIH0gPSBlbGVtZW50O1xuICAgIGNvbnN0IGxpbWl0ID0gdmFsdWUgPiBzY3JvbGxUb3AgPyB2YWx1ZSAtIHNjcm9sbFRvcCA6IHNjcm9sbFRvcCAtIHZhbHVlO1xuICAgIHNjcm9sbC50b3AoZWxlbWVudCwgdmFsdWUsIHsgZHVyYXRpb246IGxpbWl0IDwgMTAwID8gNTAgOiBkdXJhdGlvbiB9LCAoZXJyb3IpID0+IHtcbiAgICAgIGlmIChlcnJvciAmJiBlcnJvci5tZXNzYWdlICE9PSBcIkVsZW1lbnQgYWxyZWFkeSBhdCB0YXJnZXQgc2Nyb2xsIHBvc2l0aW9uXCIpIHtcbiAgICAgICAgcmV0dXJuIHJlamVjdChlcnJvcik7XG4gICAgICB9XG4gICAgICByZXR1cm4gcmVzb2x2ZSgpO1xuICAgIH0pO1xuICB9KTtcbn1cblxuLy8gc3JjL21vZHVsZXMvaGVscGVycy50c3hcbmltcG9ydCB7IGNsb25lRWxlbWVudCwgaXNWYWxpZEVsZW1lbnQgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGNyZWF0ZVBvcnRhbCB9IGZyb20gXCJyZWFjdC1kb21cIjtcbmltcG9ydCBpbm5lclRleHQgZnJvbSBcInJlYWN0LWlubmVydGV4dFwiO1xuaW1wb3J0IGlzIGZyb20gXCJpcy1saXRlXCI7XG52YXIgaXNSZWFjdDE2ID0gY3JlYXRlUG9ydGFsICE9PSB2b2lkIDA7XG5mdW5jdGlvbiBnZXRCcm93c2VyKHVzZXJBZ2VudCA9IG5hdmlnYXRvci51c2VyQWdlbnQpIHtcbiAgbGV0IGJyb3dzZXIgPSB1c2VyQWdlbnQ7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgYnJvd3NlciA9IFwibm9kZVwiO1xuICB9IGVsc2UgaWYgKGRvY3VtZW50LmRvY3VtZW50TW9kZSkge1xuICAgIGJyb3dzZXIgPSBcImllXCI7XG4gIH0gZWxzZSBpZiAoL0VkZ2UvLnRlc3QodXNlckFnZW50KSkge1xuICAgIGJyb3dzZXIgPSBcImVkZ2VcIjtcbiAgfSBlbHNlIGlmIChCb29sZWFuKHdpbmRvdy5vcGVyYSkgfHwgdXNlckFnZW50LmluY2x1ZGVzKFwiIE9QUi9cIikpIHtcbiAgICBicm93c2VyID0gXCJvcGVyYVwiO1xuICB9IGVsc2UgaWYgKHR5cGVvZiB3aW5kb3cuSW5zdGFsbFRyaWdnZXIgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICBicm93c2VyID0gXCJmaXJlZm94XCI7XG4gIH0gZWxzZSBpZiAod2luZG93LmNocm9tZSkge1xuICAgIGJyb3dzZXIgPSBcImNocm9tZVwiO1xuICB9IGVsc2UgaWYgKC8oVmVyc2lvblxcLyhbXFxkLl9dKykuKlNhZmFyaXxDcmlPU3xGeGlPU3wgTW9iaWxlXFwvKS8udGVzdCh1c2VyQWdlbnQpKSB7XG4gICAgYnJvd3NlciA9IFwic2FmYXJpXCI7XG4gIH1cbiAgcmV0dXJuIGJyb3dzZXI7XG59XG5mdW5jdGlvbiBnZXRPYmplY3RUeXBlKHZhbHVlKSB7XG4gIHJldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodmFsdWUpLnNsaWNlKDgsIC0xKS50b0xvd2VyQ2FzZSgpO1xufVxuZnVuY3Rpb24gZ2V0UmVhY3ROb2RlVGV4dChpbnB1dCwgb3B0aW9ucyA9IHt9KSB7XG4gIGNvbnN0IHsgZGVmYXVsdFZhbHVlLCBzdGVwLCBzdGVwcyB9ID0gb3B0aW9ucztcbiAgbGV0IHRleHQgPSBpbm5lclRleHQoaW5wdXQpO1xuICBpZiAoIXRleHQpIHtcbiAgICBpZiAoaXNWYWxpZEVsZW1lbnQoaW5wdXQpICYmICFPYmplY3QudmFsdWVzKGlucHV0LnByb3BzKS5sZW5ndGggJiYgZ2V0T2JqZWN0VHlwZShpbnB1dC50eXBlKSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICBjb25zdCBjb21wb25lbnQgPSBpbnB1dC50eXBlKHt9KTtcbiAgICAgIHRleHQgPSBnZXRSZWFjdE5vZGVUZXh0KGNvbXBvbmVudCwgb3B0aW9ucyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRleHQgPSBpbm5lclRleHQoZGVmYXVsdFZhbHVlKTtcbiAgICB9XG4gIH0gZWxzZSBpZiAoKHRleHQuaW5jbHVkZXMoXCJ7c3RlcH1cIikgfHwgdGV4dC5pbmNsdWRlcyhcIntzdGVwc31cIikpICYmIHN0ZXAgJiYgc3RlcHMpIHtcbiAgICB0ZXh0ID0gdGV4dC5yZXBsYWNlKFwie3N0ZXB9XCIsIHN0ZXAudG9TdHJpbmcoKSkucmVwbGFjZShcIntzdGVwc31cIiwgc3RlcHMudG9TdHJpbmcoKSk7XG4gIH1cbiAgcmV0dXJuIHRleHQ7XG59XG5mdW5jdGlvbiBoYXNWYWxpZEtleXMob2JqZWN0LCBrZXlzKSB7XG4gIGlmICghaXMucGxhaW5PYmplY3Qob2JqZWN0KSB8fCAhaXMuYXJyYXkoa2V5cykpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgcmV0dXJuIE9iamVjdC5rZXlzKG9iamVjdCkuZXZlcnkoKGQpID0+IGtleXMuaW5jbHVkZXMoZCkpO1xufVxuZnVuY3Rpb24gaGV4VG9SR0IoaGV4KSB7XG4gIGNvbnN0IHNob3J0aGFuZFJlZ2V4ID0gL14jPyhbXFxkYS1mXSkoW1xcZGEtZl0pKFtcXGRhLWZdKSQvaTtcbiAgY29uc3QgcHJvcGVySGV4ID0gaGV4LnJlcGxhY2Uoc2hvcnRoYW5kUmVnZXgsIChfbSwgciwgZywgYikgPT4gciArIHIgKyBnICsgZyArIGIgKyBiKTtcbiAgY29uc3QgcmVzdWx0ID0gL14jPyhbXFxkYS1mXXsyfSkoW1xcZGEtZl17Mn0pKFtcXGRhLWZdezJ9KSQvaS5leGVjKHByb3BlckhleCk7XG4gIHJldHVybiByZXN1bHQgPyBbcGFyc2VJbnQocmVzdWx0WzFdLCAxNiksIHBhcnNlSW50KHJlc3VsdFsyXSwgMTYpLCBwYXJzZUludChyZXN1bHRbM10sIDE2KV0gOiBbXTtcbn1cbmZ1bmN0aW9uIGhpZGVCZWFjb24oc3RlcCkge1xuICByZXR1cm4gc3RlcC5kaXNhYmxlQmVhY29uIHx8IHN0ZXAucGxhY2VtZW50ID09PSBcImNlbnRlclwiO1xufVxuZnVuY3Rpb24gaXNMZWdhY3koKSB7XG4gIHJldHVybiAhW1wiY2hyb21lXCIsIFwic2FmYXJpXCIsIFwiZmlyZWZveFwiLCBcIm9wZXJhXCJdLmluY2x1ZGVzKGdldEJyb3dzZXIoKSk7XG59XG5mdW5jdGlvbiBsb2coeyBkYXRhLCBkZWJ1ZyA9IGZhbHNlLCB0aXRsZSwgd2FybiA9IGZhbHNlIH0pIHtcbiAgY29uc3QgbG9nRm4gPSB3YXJuID8gY29uc29sZS53YXJuIHx8IGNvbnNvbGUuZXJyb3IgOiBjb25zb2xlLmxvZztcbiAgaWYgKGRlYnVnKSB7XG4gICAgaWYgKHRpdGxlICYmIGRhdGEpIHtcbiAgICAgIGNvbnNvbGUuZ3JvdXBDb2xsYXBzZWQoXG4gICAgICAgIGAlY3JlYWN0LWpveXJpZGU6ICR7dGl0bGV9YCxcbiAgICAgICAgXCJjb2xvcjogI2ZmMDA0NDsgZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTJweDtcIlxuICAgICAgKTtcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpKSB7XG4gICAgICAgIGRhdGEuZm9yRWFjaCgoZCkgPT4ge1xuICAgICAgICAgIGlmIChpcy5wbGFpbk9iamVjdChkKSAmJiBkLmtleSkge1xuICAgICAgICAgICAgbG9nRm4uYXBwbHkoY29uc29sZSwgW2Qua2V5LCBkLnZhbHVlXSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGxvZ0ZuLmFwcGx5KGNvbnNvbGUsIFtkXSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGxvZ0ZuLmFwcGx5KGNvbnNvbGUsIFtkYXRhXSk7XG4gICAgICB9XG4gICAgICBjb25zb2xlLmdyb3VwRW5kKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJNaXNzaW5nIHRpdGxlIG9yIGRhdGEgcHJvcHNcIik7XG4gICAgfVxuICB9XG59XG5mdW5jdGlvbiBub29wKCkge1xuICByZXR1cm4gdm9pZCAwO1xufVxuZnVuY3Rpb24gb2JqZWN0S2V5cyhpbnB1dCkge1xuICByZXR1cm4gT2JqZWN0LmtleXMoaW5wdXQpO1xufVxuZnVuY3Rpb24gb21pdChpbnB1dCwgLi4uZmlsdGVyKSB7XG4gIGlmICghaXMucGxhaW5PYmplY3QoaW5wdXQpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkV4cGVjdGVkIGFuIG9iamVjdFwiKTtcbiAgfVxuICBjb25zdCBvdXRwdXQgPSB7fTtcbiAgZm9yIChjb25zdCBrZXkgaW4gaW5wdXQpIHtcbiAgICBpZiAoe30uaGFzT3duUHJvcGVydHkuY2FsbChpbnB1dCwga2V5KSkge1xuICAgICAgaWYgKCFmaWx0ZXIuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICBvdXRwdXRba2V5XSA9IGlucHV0W2tleV07XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBvdXRwdXQ7XG59XG5mdW5jdGlvbiBwaWNrKGlucHV0LCAuLi5maWx0ZXIpIHtcbiAgaWYgKCFpcy5wbGFpbk9iamVjdChpbnB1dCkpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiRXhwZWN0ZWQgYW4gb2JqZWN0XCIpO1xuICB9XG4gIGlmICghZmlsdGVyLmxlbmd0aCkge1xuICAgIHJldHVybiBpbnB1dDtcbiAgfVxuICBjb25zdCBvdXRwdXQgPSB7fTtcbiAgZm9yIChjb25zdCBrZXkgaW4gaW5wdXQpIHtcbiAgICBpZiAoe30uaGFzT3duUHJvcGVydHkuY2FsbChpbnB1dCwga2V5KSkge1xuICAgICAgaWYgKGZpbHRlci5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgIG91dHB1dFtrZXldID0gaW5wdXRba2V5XTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG91dHB1dDtcbn1cbmZ1bmN0aW9uIHJlcGxhY2VMb2NhbGVDb250ZW50KGlucHV0LCBzdGVwLCBzdGVwcykge1xuICBjb25zdCByZXBsYWNlciA9ICh0ZXh0KSA9PiB0ZXh0LnJlcGxhY2UoXCJ7c3RlcH1cIiwgU3RyaW5nKHN0ZXApKS5yZXBsYWNlKFwie3N0ZXBzfVwiLCBTdHJpbmcoc3RlcHMpKTtcbiAgaWYgKGdldE9iamVjdFR5cGUoaW5wdXQpID09PSBcInN0cmluZ1wiKSB7XG4gICAgcmV0dXJuIHJlcGxhY2VyKGlucHV0KTtcbiAgfVxuICBpZiAoIWlzVmFsaWRFbGVtZW50KGlucHV0KSkge1xuICAgIHJldHVybiBpbnB1dDtcbiAgfVxuICBjb25zdCB7IGNoaWxkcmVuIH0gPSBpbnB1dC5wcm9wcztcbiAgaWYgKGdldE9iamVjdFR5cGUoY2hpbGRyZW4pID09PSBcInN0cmluZ1wiICYmIGNoaWxkcmVuLmluY2x1ZGVzKFwie3N0ZXB9XCIpKSB7XG4gICAgcmV0dXJuIGNsb25lRWxlbWVudChpbnB1dCwge1xuICAgICAgY2hpbGRyZW46IHJlcGxhY2VyKGNoaWxkcmVuKVxuICAgIH0pO1xuICB9XG4gIGlmIChBcnJheS5pc0FycmF5KGNoaWxkcmVuKSkge1xuICAgIHJldHVybiBjbG9uZUVsZW1lbnQoaW5wdXQsIHtcbiAgICAgIGNoaWxkcmVuOiBjaGlsZHJlbi5tYXAoKGNoaWxkKSA9PiB7XG4gICAgICAgIGlmICh0eXBlb2YgY2hpbGQgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICByZXR1cm4gcmVwbGFjZXIoY2hpbGQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXBsYWNlTG9jYWxlQ29udGVudChjaGlsZCwgc3RlcCwgc3RlcHMpO1xuICAgICAgfSlcbiAgICB9KTtcbiAgfVxuICBpZiAoZ2V0T2JqZWN0VHlwZShpbnB1dC50eXBlKSA9PT0gXCJmdW5jdGlvblwiICYmICFPYmplY3QudmFsdWVzKGlucHV0LnByb3BzKS5sZW5ndGgpIHtcbiAgICBjb25zdCBjb21wb25lbnQgPSBpbnB1dC50eXBlKHt9KTtcbiAgICByZXR1cm4gcmVwbGFjZUxvY2FsZUNvbnRlbnQoY29tcG9uZW50LCBzdGVwLCBzdGVwcyk7XG4gIH1cbiAgcmV0dXJuIGlucHV0O1xufVxuZnVuY3Rpb24gc2hvdWxkU2Nyb2xsKG9wdGlvbnMpIHtcbiAgY29uc3QgeyBpc0ZpcnN0U3RlcCwgbGlmZWN5Y2xlLCBwcmV2aW91c0xpZmVjeWNsZSwgc2Nyb2xsVG9GaXJzdFN0ZXAsIHN0ZXAsIHRhcmdldCB9ID0gb3B0aW9ucztcbiAgcmV0dXJuICFzdGVwLmRpc2FibGVTY3JvbGxpbmcgJiYgKCFpc0ZpcnN0U3RlcCB8fCBzY3JvbGxUb0ZpcnN0U3RlcCB8fCBsaWZlY3ljbGUgPT09IExJRkVDWUNMRS5UT09MVElQKSAmJiBzdGVwLnBsYWNlbWVudCAhPT0gXCJjZW50ZXJcIiAmJiAoIXN0ZXAuaXNGaXhlZCB8fCAhaGFzUG9zaXRpb24odGFyZ2V0KSkgJiYgLy8gZml4ZWQgc3RlcHMgZG9uJ3QgbmVlZCB0byBzY3JvbGxcbiAgcHJldmlvdXNMaWZlY3ljbGUgIT09IGxpZmVjeWNsZSAmJiBbTElGRUNZQ0xFLkJFQUNPTiwgTElGRUNZQ0xFLlRPT0xUSVBdLmluY2x1ZGVzKGxpZmVjeWNsZSk7XG59XG5cbi8vIHNyYy9tb2R1bGVzL3N0ZXAudHNcbmltcG9ydCBkZWVwbWVyZ2UyIGZyb20gXCJkZWVwbWVyZ2VcIjtcbmltcG9ydCBpczIgZnJvbSBcImlzLWxpdGVcIjtcblxuLy8gc3JjL2RlZmF1bHRzLnRzXG52YXIgZGVmYXVsdEZsb2F0ZXJQcm9wcyA9IHtcbiAgb3B0aW9uczoge1xuICAgIHByZXZlbnRPdmVyZmxvdzoge1xuICAgICAgYm91bmRhcmllc0VsZW1lbnQ6IFwic2Nyb2xsUGFyZW50XCJcbiAgICB9XG4gIH0sXG4gIHdyYXBwZXJPcHRpb25zOiB7XG4gICAgb2Zmc2V0OiAtMTgsXG4gICAgcG9zaXRpb246IHRydWVcbiAgfVxufTtcbnZhciBkZWZhdWx0TG9jYWxlID0ge1xuICBiYWNrOiBcIkJhY2tcIixcbiAgY2xvc2U6IFwiQ2xvc2VcIixcbiAgbGFzdDogXCJMYXN0XCIsXG4gIG5leHQ6IFwiTmV4dFwiLFxuICBuZXh0TGFiZWxXaXRoUHJvZ3Jlc3M6IFwiTmV4dCAoU3RlcCB7c3RlcH0gb2Yge3N0ZXBzfSlcIixcbiAgb3BlbjogXCJPcGVuIHRoZSBkaWFsb2dcIixcbiAgc2tpcDogXCJTa2lwXCJcbn07XG52YXIgZGVmYXVsdFN0ZXAgPSB7XG4gIGV2ZW50OiBcImNsaWNrXCIsXG4gIHBsYWNlbWVudDogXCJib3R0b21cIixcbiAgb2Zmc2V0OiAxMCxcbiAgZGlzYWJsZUJlYWNvbjogZmFsc2UsXG4gIGRpc2FibGVDbG9zZU9uRXNjOiBmYWxzZSxcbiAgZGlzYWJsZU92ZXJsYXk6IGZhbHNlLFxuICBkaXNhYmxlT3ZlcmxheUNsb3NlOiBmYWxzZSxcbiAgZGlzYWJsZVNjcm9sbFBhcmVudEZpeDogZmFsc2UsXG4gIGRpc2FibGVTY3JvbGxpbmc6IGZhbHNlLFxuICBoaWRlQmFja0J1dHRvbjogZmFsc2UsXG4gIGhpZGVDbG9zZUJ1dHRvbjogZmFsc2UsXG4gIGhpZGVGb290ZXI6IGZhbHNlLFxuICBpc0ZpeGVkOiBmYWxzZSxcbiAgbG9jYWxlOiBkZWZhdWx0TG9jYWxlLFxuICBzaG93UHJvZ3Jlc3M6IGZhbHNlLFxuICBzaG93U2tpcEJ1dHRvbjogZmFsc2UsXG4gIHNwb3RsaWdodENsaWNrczogZmFsc2UsXG4gIHNwb3RsaWdodFBhZGRpbmc6IDEwXG59O1xudmFyIGRlZmF1bHRQcm9wcyA9IHtcbiAgY29udGludW91czogZmFsc2UsXG4gIGRlYnVnOiBmYWxzZSxcbiAgZGlzYWJsZUNsb3NlT25Fc2M6IGZhbHNlLFxuICBkaXNhYmxlT3ZlcmxheTogZmFsc2UsXG4gIGRpc2FibGVPdmVybGF5Q2xvc2U6IGZhbHNlLFxuICBkaXNhYmxlU2Nyb2xsaW5nOiBmYWxzZSxcbiAgZGlzYWJsZVNjcm9sbFBhcmVudEZpeDogZmFsc2UsXG4gIGdldEhlbHBlcnM6IG5vb3AoKSxcbiAgaGlkZUJhY2tCdXR0b246IGZhbHNlLFxuICBydW46IHRydWUsXG4gIHNjcm9sbE9mZnNldDogMjAsXG4gIHNjcm9sbER1cmF0aW9uOiAzMDAsXG4gIHNjcm9sbFRvRmlyc3RTdGVwOiBmYWxzZSxcbiAgc2hvd1NraXBCdXR0b246IGZhbHNlLFxuICBzaG93UHJvZ3Jlc3M6IGZhbHNlLFxuICBzcG90bGlnaHRDbGlja3M6IGZhbHNlLFxuICBzcG90bGlnaHRQYWRkaW5nOiAxMCxcbiAgc3RlcHM6IFtdXG59O1xuXG4vLyBzcmMvc3R5bGVzLnRzXG5pbXBvcnQgZGVlcG1lcmdlIGZyb20gXCJkZWVwbWVyZ2VcIjtcbnZhciBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgYXJyb3dDb2xvcjogXCIjZmZmXCIsXG4gIGJhY2tncm91bmRDb2xvcjogXCIjZmZmXCIsXG4gIGJlYWNvblNpemU6IDM2LFxuICBvdmVybGF5Q29sb3I6IFwicmdiYSgwLCAwLCAwLCAwLjUpXCIsXG4gIHByaW1hcnlDb2xvcjogXCIjZjA0XCIsXG4gIHNwb3RsaWdodFNoYWRvdzogXCIwIDAgMTVweCByZ2JhKDAsIDAsIDAsIDAuNSlcIixcbiAgdGV4dENvbG9yOiBcIiMzMzNcIixcbiAgd2lkdGg6IDM4MCxcbiAgekluZGV4OiAxMDBcbn07XG52YXIgYnV0dG9uQmFzZSA9IHtcbiAgYmFja2dyb3VuZENvbG9yOiBcInRyYW5zcGFyZW50XCIsXG4gIGJvcmRlcjogMCxcbiAgYm9yZGVyUmFkaXVzOiAwLFxuICBjb2xvcjogXCIjNTU1XCIsXG4gIGN1cnNvcjogXCJwb2ludGVyXCIsXG4gIGZvbnRTaXplOiAxNixcbiAgbGluZUhlaWdodDogMSxcbiAgcGFkZGluZzogOCxcbiAgV2Via2l0QXBwZWFyYW5jZTogXCJub25lXCJcbn07XG52YXIgc3BvdGxpZ2h0ID0ge1xuICBib3JkZXJSYWRpdXM6IDQsXG4gIHBvc2l0aW9uOiBcImFic29sdXRlXCJcbn07XG5mdW5jdGlvbiBnZXRTdHlsZXMocHJvcHMsIHN0ZXApIHtcbiAgdmFyIF9hLCBfYiwgX2MsIF9kLCBfZTtcbiAgY29uc3QgeyBmbG9hdGVyUHJvcHMsIHN0eWxlcyB9ID0gcHJvcHM7XG4gIGNvbnN0IG1lcmdlZEZsb2F0ZXJQcm9wcyA9IGRlZXBtZXJnZSgoX2EgPSBzdGVwLmZsb2F0ZXJQcm9wcykgIT0gbnVsbCA/IF9hIDoge30sIGZsb2F0ZXJQcm9wcyAhPSBudWxsID8gZmxvYXRlclByb3BzIDoge30pO1xuICBjb25zdCBtZXJnZWRTdHlsZXMgPSBkZWVwbWVyZ2Uoc3R5bGVzICE9IG51bGwgPyBzdHlsZXMgOiB7fSwgKF9iID0gc3RlcC5zdHlsZXMpICE9IG51bGwgPyBfYiA6IHt9KTtcbiAgY29uc3Qgb3B0aW9ucyA9IGRlZXBtZXJnZShkZWZhdWx0T3B0aW9ucywgbWVyZ2VkU3R5bGVzLm9wdGlvbnMgfHwge30pO1xuICBjb25zdCBoaWRlQmVhY29uMiA9IHN0ZXAucGxhY2VtZW50ID09PSBcImNlbnRlclwiIHx8IHN0ZXAuZGlzYWJsZUJlYWNvbjtcbiAgbGV0IHsgd2lkdGggfSA9IG9wdGlvbnM7XG4gIGlmICh3aW5kb3cuaW5uZXJXaWR0aCA+IDQ4MCkge1xuICAgIHdpZHRoID0gMzgwO1xuICB9XG4gIGlmIChcIndpZHRoXCIgaW4gb3B0aW9ucykge1xuICAgIHdpZHRoID0gdHlwZW9mIG9wdGlvbnMud2lkdGggPT09IFwibnVtYmVyXCIgJiYgd2luZG93LmlubmVyV2lkdGggPCBvcHRpb25zLndpZHRoID8gd2luZG93LmlubmVyV2lkdGggLSAzMCA6IG9wdGlvbnMud2lkdGg7XG4gIH1cbiAgY29uc3Qgb3ZlcmxheSA9IHtcbiAgICBib3R0b206IDAsXG4gICAgbGVmdDogMCxcbiAgICBvdmVyZmxvdzogXCJoaWRkZW5cIixcbiAgICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxuICAgIHJpZ2h0OiAwLFxuICAgIHRvcDogMCxcbiAgICB6SW5kZXg6IG9wdGlvbnMuekluZGV4XG4gIH07XG4gIGNvbnN0IGRlZmF1bHRTdHlsZXMgPSB7XG4gICAgYmVhY29uOiB7XG4gICAgICAuLi5idXR0b25CYXNlLFxuICAgICAgZGlzcGxheTogaGlkZUJlYWNvbjIgPyBcIm5vbmVcIiA6IFwiaW5saW5lLWJsb2NrXCIsXG4gICAgICBoZWlnaHQ6IG9wdGlvbnMuYmVhY29uU2l6ZSxcbiAgICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXG4gICAgICB3aWR0aDogb3B0aW9ucy5iZWFjb25TaXplLFxuICAgICAgekluZGV4OiBvcHRpb25zLnpJbmRleFxuICAgIH0sXG4gICAgYmVhY29uSW5uZXI6IHtcbiAgICAgIGFuaW1hdGlvbjogXCJqb3lyaWRlLWJlYWNvbi1pbm5lciAxLjJzIGluZmluaXRlIGVhc2UtaW4tb3V0XCIsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IG9wdGlvbnMucHJpbWFyeUNvbG9yLFxuICAgICAgYm9yZGVyUmFkaXVzOiBcIjUwJVwiLFxuICAgICAgZGlzcGxheTogXCJibG9ja1wiLFxuICAgICAgaGVpZ2h0OiBcIjUwJVwiLFxuICAgICAgbGVmdDogXCI1MCVcIixcbiAgICAgIG9wYWNpdHk6IDAuNyxcbiAgICAgIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gICAgICB0b3A6IFwiNTAlXCIsXG4gICAgICB0cmFuc2Zvcm06IFwidHJhbnNsYXRlKC01MCUsIC01MCUpXCIsXG4gICAgICB3aWR0aDogXCI1MCVcIlxuICAgIH0sXG4gICAgYmVhY29uT3V0ZXI6IHtcbiAgICAgIGFuaW1hdGlvbjogXCJqb3lyaWRlLWJlYWNvbi1vdXRlciAxLjJzIGluZmluaXRlIGVhc2UtaW4tb3V0XCIsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGByZ2JhKCR7aGV4VG9SR0Iob3B0aW9ucy5wcmltYXJ5Q29sb3IpLmpvaW4oXCIsXCIpfSwgMC4yKWAsXG4gICAgICBib3JkZXI6IGAycHggc29saWQgJHtvcHRpb25zLnByaW1hcnlDb2xvcn1gLFxuICAgICAgYm9yZGVyUmFkaXVzOiBcIjUwJVwiLFxuICAgICAgYm94U2l6aW5nOiBcImJvcmRlci1ib3hcIixcbiAgICAgIGRpc3BsYXk6IFwiYmxvY2tcIixcbiAgICAgIGhlaWdodDogXCIxMDAlXCIsXG4gICAgICBsZWZ0OiAwLFxuICAgICAgb3BhY2l0eTogMC45LFxuICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgICAgIHRvcDogMCxcbiAgICAgIHRyYW5zZm9ybU9yaWdpbjogXCJjZW50ZXJcIixcbiAgICAgIHdpZHRoOiBcIjEwMCVcIlxuICAgIH0sXG4gICAgdG9vbHRpcDoge1xuICAgICAgYmFja2dyb3VuZENvbG9yOiBvcHRpb25zLmJhY2tncm91bmRDb2xvcixcbiAgICAgIGJvcmRlclJhZGl1czogNSxcbiAgICAgIGJveFNpemluZzogXCJib3JkZXItYm94XCIsXG4gICAgICBjb2xvcjogb3B0aW9ucy50ZXh0Q29sb3IsXG4gICAgICBmb250U2l6ZTogMTYsXG4gICAgICBtYXhXaWR0aDogXCIxMDAlXCIsXG4gICAgICBwYWRkaW5nOiAxNSxcbiAgICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXG4gICAgICB3aWR0aFxuICAgIH0sXG4gICAgdG9vbHRpcENvbnRhaW5lcjoge1xuICAgICAgbGluZUhlaWdodDogMS40LFxuICAgICAgdGV4dEFsaWduOiBcImNlbnRlclwiXG4gICAgfSxcbiAgICB0b29sdGlwVGl0bGU6IHtcbiAgICAgIGZvbnRTaXplOiAxOCxcbiAgICAgIG1hcmdpbjogMFxuICAgIH0sXG4gICAgdG9vbHRpcENvbnRlbnQ6IHtcbiAgICAgIHBhZGRpbmc6IFwiMjBweCAxMHB4XCJcbiAgICB9LFxuICAgIHRvb2x0aXBGb290ZXI6IHtcbiAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXG4gICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgIGp1c3RpZnlDb250ZW50OiBcImZsZXgtZW5kXCIsXG4gICAgICBtYXJnaW5Ub3A6IDE1XG4gICAgfSxcbiAgICB0b29sdGlwRm9vdGVyU3BhY2VyOiB7XG4gICAgICBmbGV4OiAxXG4gICAgfSxcbiAgICBidXR0b25OZXh0OiB7XG4gICAgICAuLi5idXR0b25CYXNlLFxuICAgICAgYmFja2dyb3VuZENvbG9yOiBvcHRpb25zLnByaW1hcnlDb2xvcixcbiAgICAgIGJvcmRlclJhZGl1czogNCxcbiAgICAgIGNvbG9yOiBcIiNmZmZcIlxuICAgIH0sXG4gICAgYnV0dG9uQmFjazoge1xuICAgICAgLi4uYnV0dG9uQmFzZSxcbiAgICAgIGNvbG9yOiBvcHRpb25zLnByaW1hcnlDb2xvcixcbiAgICAgIG1hcmdpbkxlZnQ6IFwiYXV0b1wiLFxuICAgICAgbWFyZ2luUmlnaHQ6IDVcbiAgICB9LFxuICAgIGJ1dHRvbkNsb3NlOiB7XG4gICAgICAuLi5idXR0b25CYXNlLFxuICAgICAgY29sb3I6IG9wdGlvbnMudGV4dENvbG9yLFxuICAgICAgaGVpZ2h0OiAxNCxcbiAgICAgIHBhZGRpbmc6IDE1LFxuICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgICAgIHJpZ2h0OiAwLFxuICAgICAgdG9wOiAwLFxuICAgICAgd2lkdGg6IDE0XG4gICAgfSxcbiAgICBidXR0b25Ta2lwOiB7XG4gICAgICAuLi5idXR0b25CYXNlLFxuICAgICAgY29sb3I6IG9wdGlvbnMudGV4dENvbG9yLFxuICAgICAgZm9udFNpemU6IDE0XG4gICAgfSxcbiAgICBvdmVybGF5OiB7XG4gICAgICAuLi5vdmVybGF5LFxuICAgICAgYmFja2dyb3VuZENvbG9yOiBvcHRpb25zLm92ZXJsYXlDb2xvcixcbiAgICAgIG1peEJsZW5kTW9kZTogXCJoYXJkLWxpZ2h0XCJcbiAgICB9LFxuICAgIG92ZXJsYXlMZWdhY3k6IHtcbiAgICAgIC4uLm92ZXJsYXlcbiAgICB9LFxuICAgIG92ZXJsYXlMZWdhY3lDZW50ZXI6IHtcbiAgICAgIC4uLm92ZXJsYXksXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IG9wdGlvbnMub3ZlcmxheUNvbG9yXG4gICAgfSxcbiAgICBzcG90bGlnaHQ6IHtcbiAgICAgIC4uLnNwb3RsaWdodCxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogXCJncmF5XCJcbiAgICB9LFxuICAgIHNwb3RsaWdodExlZ2FjeToge1xuICAgICAgLi4uc3BvdGxpZ2h0LFxuICAgICAgYm94U2hhZG93OiBgMCAwIDAgOTk5OXB4ICR7b3B0aW9ucy5vdmVybGF5Q29sb3J9LCAke29wdGlvbnMuc3BvdGxpZ2h0U2hhZG93fWBcbiAgICB9LFxuICAgIGZsb2F0ZXJTdHlsZXM6IHtcbiAgICAgIGFycm93OiB7XG4gICAgICAgIGNvbG9yOiAoX2UgPSAoX2QgPSAoX2MgPSBtZXJnZWRGbG9hdGVyUHJvcHMgPT0gbnVsbCA/IHZvaWQgMCA6IG1lcmdlZEZsb2F0ZXJQcm9wcy5zdHlsZXMpID09IG51bGwgPyB2b2lkIDAgOiBfYy5hcnJvdykgPT0gbnVsbCA/IHZvaWQgMCA6IF9kLmNvbG9yKSAhPSBudWxsID8gX2UgOiBvcHRpb25zLmFycm93Q29sb3JcbiAgICAgIH0sXG4gICAgICBvcHRpb25zOiB7XG4gICAgICAgIHpJbmRleDogb3B0aW9ucy56SW5kZXggKyAxMDBcbiAgICAgIH1cbiAgICB9LFxuICAgIG9wdGlvbnNcbiAgfTtcbiAgcmV0dXJuIGRlZXBtZXJnZShkZWZhdWx0U3R5bGVzLCBtZXJnZWRTdHlsZXMpO1xufVxuXG4vLyBzcmMvbW9kdWxlcy9zdGVwLnRzXG5mdW5jdGlvbiBnZXRUb3VyUHJvcHMocHJvcHMpIHtcbiAgcmV0dXJuIHBpY2soXG4gICAgcHJvcHMsXG4gICAgXCJiZWFjb25Db21wb25lbnRcIixcbiAgICBcImRpc2FibGVDbG9zZU9uRXNjXCIsXG4gICAgXCJkaXNhYmxlT3ZlcmxheVwiLFxuICAgIFwiZGlzYWJsZU92ZXJsYXlDbG9zZVwiLFxuICAgIFwiZGlzYWJsZVNjcm9sbGluZ1wiLFxuICAgIFwiZGlzYWJsZVNjcm9sbFBhcmVudEZpeFwiLFxuICAgIFwiZmxvYXRlclByb3BzXCIsXG4gICAgXCJoaWRlQmFja0J1dHRvblwiLFxuICAgIFwiaGlkZUNsb3NlQnV0dG9uXCIsXG4gICAgXCJsb2NhbGVcIixcbiAgICBcInNob3dQcm9ncmVzc1wiLFxuICAgIFwic2hvd1NraXBCdXR0b25cIixcbiAgICBcInNwb3RsaWdodENsaWNrc1wiLFxuICAgIFwic3BvdGxpZ2h0UGFkZGluZ1wiLFxuICAgIFwic3R5bGVzXCIsXG4gICAgXCJ0b29sdGlwQ29tcG9uZW50XCJcbiAgKTtcbn1cbmZ1bmN0aW9uIGdldE1lcmdlZFN0ZXAocHJvcHMsIGN1cnJlbnRTdGVwKSB7XG4gIHZhciBfYSwgX2IsIF9jLCBfZCwgX2UsIF9mO1xuICBjb25zdCBzdGVwID0gY3VycmVudFN0ZXAgIT0gbnVsbCA/IGN1cnJlbnRTdGVwIDoge307XG4gIGNvbnN0IG1lcmdlZFN0ZXAgPSBkZWVwbWVyZ2UyLmFsbChbZGVmYXVsdFN0ZXAsIGdldFRvdXJQcm9wcyhwcm9wcyksIHN0ZXBdLCB7XG4gICAgaXNNZXJnZWFibGVPYmplY3Q6IGlzMi5wbGFpbk9iamVjdFxuICB9KTtcbiAgY29uc3QgbWVyZ2VkU3R5bGVzID0gZ2V0U3R5bGVzKHByb3BzLCBtZXJnZWRTdGVwKTtcbiAgY29uc3Qgc2Nyb2xsUGFyZW50MiA9IGhhc0N1c3RvbVNjcm9sbFBhcmVudChcbiAgICBnZXRFbGVtZW50KG1lcmdlZFN0ZXAudGFyZ2V0KSxcbiAgICBtZXJnZWRTdGVwLmRpc2FibGVTY3JvbGxQYXJlbnRGaXhcbiAgKTtcbiAgY29uc3QgZmxvYXRlclByb3BzID0gZGVlcG1lcmdlMi5hbGwoW1xuICAgIGRlZmF1bHRGbG9hdGVyUHJvcHMsXG4gICAgKF9hID0gcHJvcHMuZmxvYXRlclByb3BzKSAhPSBudWxsID8gX2EgOiB7fSxcbiAgICAoX2IgPSBtZXJnZWRTdGVwLmZsb2F0ZXJQcm9wcykgIT0gbnVsbCA/IF9iIDoge31cbiAgXSk7XG4gIGZsb2F0ZXJQcm9wcy5vZmZzZXQgPSBtZXJnZWRTdGVwLm9mZnNldDtcbiAgZmxvYXRlclByb3BzLnN0eWxlcyA9IGRlZXBtZXJnZTIoKF9jID0gZmxvYXRlclByb3BzLnN0eWxlcykgIT0gbnVsbCA/IF9jIDoge30sIG1lcmdlZFN0eWxlcy5mbG9hdGVyU3R5bGVzKTtcbiAgZmxvYXRlclByb3BzLm9mZnNldCArPSAoX2UgPSAoX2QgPSBwcm9wcy5zcG90bGlnaHRQYWRkaW5nKSAhPSBudWxsID8gX2QgOiBtZXJnZWRTdGVwLnNwb3RsaWdodFBhZGRpbmcpICE9IG51bGwgPyBfZSA6IDA7XG4gIGlmIChtZXJnZWRTdGVwLnBsYWNlbWVudEJlYWNvbiAmJiBmbG9hdGVyUHJvcHMud3JhcHBlck9wdGlvbnMpIHtcbiAgICBmbG9hdGVyUHJvcHMud3JhcHBlck9wdGlvbnMucGxhY2VtZW50ID0gbWVyZ2VkU3RlcC5wbGFjZW1lbnRCZWFjb247XG4gIH1cbiAgaWYgKHNjcm9sbFBhcmVudDIgJiYgZmxvYXRlclByb3BzLm9wdGlvbnMucHJldmVudE92ZXJmbG93KSB7XG4gICAgZmxvYXRlclByb3BzLm9wdGlvbnMucHJldmVudE92ZXJmbG93LmJvdW5kYXJpZXNFbGVtZW50ID0gXCJ3aW5kb3dcIjtcbiAgfVxuICByZXR1cm4ge1xuICAgIC4uLm1lcmdlZFN0ZXAsXG4gICAgbG9jYWxlOiBkZWVwbWVyZ2UyLmFsbChbZGVmYXVsdExvY2FsZSwgKF9mID0gcHJvcHMubG9jYWxlKSAhPSBudWxsID8gX2YgOiB7fSwgbWVyZ2VkU3RlcC5sb2NhbGUgfHwge31dKSxcbiAgICBmbG9hdGVyUHJvcHMsXG4gICAgc3R5bGVzOiBvbWl0KG1lcmdlZFN0eWxlcywgXCJmbG9hdGVyU3R5bGVzXCIpXG4gIH07XG59XG5mdW5jdGlvbiB2YWxpZGF0ZVN0ZXAoc3RlcCwgZGVidWcgPSBmYWxzZSkge1xuICBpZiAoIWlzMi5wbGFpbk9iamVjdChzdGVwKSkge1xuICAgIGxvZyh7XG4gICAgICB0aXRsZTogXCJ2YWxpZGF0ZVN0ZXBcIixcbiAgICAgIGRhdGE6IFwic3RlcCBtdXN0IGJlIGFuIG9iamVjdFwiLFxuICAgICAgd2FybjogdHJ1ZSxcbiAgICAgIGRlYnVnXG4gICAgfSk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlmICghc3RlcC50YXJnZXQpIHtcbiAgICBsb2coe1xuICAgICAgdGl0bGU6IFwidmFsaWRhdGVTdGVwXCIsXG4gICAgICBkYXRhOiBcInRhcmdldCBpcyBtaXNzaW5nIGZyb20gdGhlIHN0ZXBcIixcbiAgICAgIHdhcm46IHRydWUsXG4gICAgICBkZWJ1Z1xuICAgIH0pO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIHZhbGlkYXRlU3RlcHMoc3RlcHMsIGRlYnVnID0gZmFsc2UpIHtcbiAgaWYgKCFpczIuYXJyYXkoc3RlcHMpKSB7XG4gICAgbG9nKHtcbiAgICAgIHRpdGxlOiBcInZhbGlkYXRlU3RlcHNcIixcbiAgICAgIGRhdGE6IFwic3RlcHMgbXVzdCBiZSBhbiBhcnJheVwiLFxuICAgICAgd2FybjogdHJ1ZSxcbiAgICAgIGRlYnVnXG4gICAgfSk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHJldHVybiBzdGVwcy5ldmVyeSgoZCkgPT4gdmFsaWRhdGVTdGVwKGQsIGRlYnVnKSk7XG59XG5cbi8vIHNyYy9tb2R1bGVzL3N0b3JlLnRzXG5pbXBvcnQgaXMzIGZyb20gXCJpcy1saXRlXCI7XG52YXIgZGVmYXVsdFN0YXRlID0ge1xuICBhY3Rpb246IFwiaW5pdFwiLFxuICBjb250cm9sbGVkOiBmYWxzZSxcbiAgaW5kZXg6IDAsXG4gIGxpZmVjeWNsZTogTElGRUNZQ0xFLklOSVQsXG4gIG9yaWdpbjogbnVsbCxcbiAgc2l6ZTogMCxcbiAgc3RhdHVzOiBTVEFUVVMuSURMRVxufTtcbnZhciB2YWxpZEtleXMgPSBvYmplY3RLZXlzKG9taXQoZGVmYXVsdFN0YXRlLCBcImNvbnRyb2xsZWRcIiwgXCJzaXplXCIpKTtcbnZhciBTdG9yZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJiZWFjb25Qb3BwZXJcIik7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInRvb2x0aXBQb3BwZXJcIik7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImRhdGFcIiwgLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImxpc3RlbmVyXCIpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJzdG9yZVwiLCAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiYWRkTGlzdGVuZXJcIiwgKGxpc3RlbmVyKSA9PiB7XG4gICAgICB0aGlzLmxpc3RlbmVyID0gbGlzdGVuZXI7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInNldFN0ZXBzXCIsIChzdGVwcykgPT4ge1xuICAgICAgY29uc3QgeyBzaXplLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIGNvbnN0IHN0YXRlID0ge1xuICAgICAgICBzaXplOiBzdGVwcy5sZW5ndGgsXG4gICAgICAgIHN0YXR1c1xuICAgICAgfTtcbiAgICAgIHRoaXMuZGF0YS5zZXQoXCJzdGVwc1wiLCBzdGVwcyk7XG4gICAgICBpZiAoc3RhdHVzID09PSBTVEFUVVMuV0FJVElORyAmJiAhc2l6ZSAmJiBzdGVwcy5sZW5ndGgpIHtcbiAgICAgICAgc3RhdGUuc3RhdHVzID0gU1RBVFVTLlJVTk5JTkc7XG4gICAgICB9XG4gICAgICB0aGlzLnNldFN0YXRlKHN0YXRlKTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiZ2V0UG9wcGVyXCIsIChuYW1lKSA9PiB7XG4gICAgICBpZiAobmFtZSA9PT0gXCJiZWFjb25cIikge1xuICAgICAgICByZXR1cm4gdGhpcy5iZWFjb25Qb3BwZXI7XG4gICAgICB9XG4gICAgICByZXR1cm4gdGhpcy50b29sdGlwUG9wcGVyO1xuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJzZXRQb3BwZXJcIiwgKG5hbWUsIHBvcHBlcikgPT4ge1xuICAgICAgaWYgKG5hbWUgPT09IFwiYmVhY29uXCIpIHtcbiAgICAgICAgdGhpcy5iZWFjb25Qb3BwZXIgPSBwb3BwZXI7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLnRvb2x0aXBQb3BwZXIgPSBwb3BwZXI7XG4gICAgICB9XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImNsZWFudXBQb3BwZXJzXCIsICgpID0+IHtcbiAgICAgIHRoaXMuYmVhY29uUG9wcGVyID0gbnVsbDtcbiAgICAgIHRoaXMudG9vbHRpcFBvcHBlciA9IG51bGw7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImNsb3NlXCIsIChvcmlnaW4gPSBudWxsKSA9PiB7XG4gICAgICBjb25zdCB7IGluZGV4LCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIGlmIChzdGF0dXMgIT09IFNUQVRVUy5SVU5OSU5HKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAuLi50aGlzLmdldE5leHRTdGF0ZSh7IGFjdGlvbjogQUNUSU9OUy5DTE9TRSwgaW5kZXg6IGluZGV4ICsgMSwgb3JpZ2luIH0pXG4gICAgICB9KTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiZ29cIiwgKG5leHRJbmRleCkgPT4ge1xuICAgICAgY29uc3QgeyBjb250cm9sbGVkLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIGlmIChjb250cm9sbGVkIHx8IHN0YXR1cyAhPT0gU1RBVFVTLlJVTk5JTkcpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgY29uc3Qgc3RlcCA9IHRoaXMuZ2V0U3RlcHMoKVtuZXh0SW5kZXhdO1xuICAgICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIC4uLnRoaXMuZ2V0TmV4dFN0YXRlKHsgYWN0aW9uOiBBQ1RJT05TLkdPLCBpbmRleDogbmV4dEluZGV4IH0pLFxuICAgICAgICBzdGF0dXM6IHN0ZXAgPyBzdGF0dXMgOiBTVEFUVVMuRklOSVNIRURcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJpbmZvXCIsICgpID0+IHRoaXMuZ2V0U3RhdGUoKSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcIm5leHRcIiwgKCkgPT4ge1xuICAgICAgY29uc3QgeyBpbmRleCwgc3RhdHVzIH0gPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgICBpZiAoc3RhdHVzICE9PSBTVEFUVVMuUlVOTklORykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICB0aGlzLnNldFN0YXRlKHRoaXMuZ2V0TmV4dFN0YXRlKHsgYWN0aW9uOiBBQ1RJT05TLk5FWFQsIGluZGV4OiBpbmRleCArIDEgfSkpO1xuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJvcGVuXCIsICgpID0+IHtcbiAgICAgIGNvbnN0IHsgc3RhdHVzIH0gPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgICBpZiAoc3RhdHVzICE9PSBTVEFUVVMuUlVOTklORykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgLi4udGhpcy5nZXROZXh0U3RhdGUoeyBhY3Rpb246IEFDVElPTlMuVVBEQVRFLCBsaWZlY3ljbGU6IExJRkVDWUNMRS5UT09MVElQIH0pXG4gICAgICB9KTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwicHJldlwiLCAoKSA9PiB7XG4gICAgICBjb25zdCB7IGluZGV4LCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIGlmIChzdGF0dXMgIT09IFNUQVRVUy5SVU5OSU5HKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAuLi50aGlzLmdldE5leHRTdGF0ZSh7IGFjdGlvbjogQUNUSU9OUy5QUkVWLCBpbmRleDogaW5kZXggLSAxIH0pXG4gICAgICB9KTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwicmVzZXRcIiwgKHJlc3RhcnQgPSBmYWxzZSkgPT4ge1xuICAgICAgY29uc3QgeyBjb250cm9sbGVkIH0gPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgICBpZiAoY29udHJvbGxlZCkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgLi4udGhpcy5nZXROZXh0U3RhdGUoeyBhY3Rpb246IEFDVElPTlMuUkVTRVQsIGluZGV4OiAwIH0pLFxuICAgICAgICBzdGF0dXM6IHJlc3RhcnQgPyBTVEFUVVMuUlVOTklORyA6IFNUQVRVUy5SRUFEWVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInNraXBcIiwgKCkgPT4ge1xuICAgICAgY29uc3QgeyBzdGF0dXMgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIGlmIChzdGF0dXMgIT09IFNUQVRVUy5SVU5OSU5HKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICBhY3Rpb246IEFDVElPTlMuU0tJUCxcbiAgICAgICAgbGlmZWN5Y2xlOiBMSUZFQ1lDTEUuSU5JVCxcbiAgICAgICAgc3RhdHVzOiBTVEFUVVMuU0tJUFBFRFxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInN0YXJ0XCIsIChuZXh0SW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IHsgaW5kZXgsIHNpemUgfSA9IHRoaXMuZ2V0U3RhdGUoKTtcbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAuLi50aGlzLmdldE5leHRTdGF0ZShcbiAgICAgICAgICB7XG4gICAgICAgICAgICBhY3Rpb246IEFDVElPTlMuU1RBUlQsXG4gICAgICAgICAgICBpbmRleDogaXMzLm51bWJlcihuZXh0SW5kZXgpID8gbmV4dEluZGV4IDogaW5kZXhcbiAgICAgICAgICB9LFxuICAgICAgICAgIHRydWVcbiAgICAgICAgKSxcbiAgICAgICAgc3RhdHVzOiBzaXplID8gU1RBVFVTLlJVTk5JTkcgOiBTVEFUVVMuV0FJVElOR1xuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInN0b3BcIiwgKGFkdmFuY2UgPSBmYWxzZSkgPT4ge1xuICAgICAgY29uc3QgeyBpbmRleCwgc3RhdHVzIH0gPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgICBpZiAoW1NUQVRVUy5GSU5JU0hFRCwgU1RBVFVTLlNLSVBQRURdLmluY2x1ZGVzKHN0YXR1cykpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIC4uLnRoaXMuZ2V0TmV4dFN0YXRlKHsgYWN0aW9uOiBBQ1RJT05TLlNUT1AsIGluZGV4OiBpbmRleCArIChhZHZhbmNlID8gMSA6IDApIH0pLFxuICAgICAgICBzdGF0dXM6IFNUQVRVUy5QQVVTRURcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJ1cGRhdGVcIiwgKHN0YXRlKSA9PiB7XG4gICAgICB2YXIgX2EsIF9iO1xuICAgICAgaWYgKCFoYXNWYWxpZEtleXMoc3RhdGUsIHZhbGlkS2V5cykpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBTdGF0ZSBpcyBub3QgdmFsaWQuIFZhbGlkIGtleXM6ICR7dmFsaWRLZXlzLmpvaW4oXCIsIFwiKX1gKTtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAuLi50aGlzLmdldE5leHRTdGF0ZShcbiAgICAgICAgICB7XG4gICAgICAgICAgICAuLi50aGlzLmdldFN0YXRlKCksXG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGFjdGlvbjogKF9hID0gc3RhdGUuYWN0aW9uKSAhPSBudWxsID8gX2EgOiBBQ1RJT05TLlVQREFURSxcbiAgICAgICAgICAgIG9yaWdpbjogKF9iID0gc3RhdGUub3JpZ2luKSAhPSBudWxsID8gX2IgOiBudWxsXG4gICAgICAgICAgfSxcbiAgICAgICAgICB0cnVlXG4gICAgICAgIClcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIGNvbnN0IHsgY29udGludW91cyA9IGZhbHNlLCBzdGVwSW5kZXgsIHN0ZXBzID0gW10gfSA9IG9wdGlvbnMgIT0gbnVsbCA/IG9wdGlvbnMgOiB7fTtcbiAgICB0aGlzLnNldFN0YXRlKFxuICAgICAge1xuICAgICAgICBhY3Rpb246IEFDVElPTlMuSU5JVCxcbiAgICAgICAgY29udHJvbGxlZDogaXMzLm51bWJlcihzdGVwSW5kZXgpLFxuICAgICAgICBjb250aW51b3VzLFxuICAgICAgICBpbmRleDogaXMzLm51bWJlcihzdGVwSW5kZXgpID8gc3RlcEluZGV4IDogMCxcbiAgICAgICAgbGlmZWN5Y2xlOiBMSUZFQ1lDTEUuSU5JVCxcbiAgICAgICAgb3JpZ2luOiBudWxsLFxuICAgICAgICBzdGF0dXM6IHN0ZXBzLmxlbmd0aCA/IFNUQVRVUy5SRUFEWSA6IFNUQVRVUy5JRExFXG4gICAgICB9LFxuICAgICAgdHJ1ZVxuICAgICk7XG4gICAgdGhpcy5iZWFjb25Qb3BwZXIgPSBudWxsO1xuICAgIHRoaXMudG9vbHRpcFBvcHBlciA9IG51bGw7XG4gICAgdGhpcy5saXN0ZW5lciA9IG51bGw7XG4gICAgdGhpcy5zZXRTdGVwcyhzdGVwcyk7XG4gIH1cbiAgZ2V0U3RhdGUoKSB7XG4gICAgaWYgKCF0aGlzLnN0b3JlLnNpemUpIHtcbiAgICAgIHJldHVybiB7IC4uLmRlZmF1bHRTdGF0ZSB9O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgYWN0aW9uOiB0aGlzLnN0b3JlLmdldChcImFjdGlvblwiKSB8fCBcIlwiLFxuICAgICAgY29udHJvbGxlZDogdGhpcy5zdG9yZS5nZXQoXCJjb250cm9sbGVkXCIpIHx8IGZhbHNlLFxuICAgICAgaW5kZXg6IHBhcnNlSW50KHRoaXMuc3RvcmUuZ2V0KFwiaW5kZXhcIiksIDEwKSxcbiAgICAgIGxpZmVjeWNsZTogdGhpcy5zdG9yZS5nZXQoXCJsaWZlY3ljbGVcIikgfHwgXCJcIixcbiAgICAgIG9yaWdpbjogdGhpcy5zdG9yZS5nZXQoXCJvcmlnaW5cIikgfHwgbnVsbCxcbiAgICAgIHNpemU6IHRoaXMuc3RvcmUuZ2V0KFwic2l6ZVwiKSB8fCAwLFxuICAgICAgc3RhdHVzOiB0aGlzLnN0b3JlLmdldChcInN0YXR1c1wiKSB8fCBcIlwiXG4gICAgfTtcbiAgfVxuICBnZXROZXh0U3RhdGUoc3RhdGUsIGZvcmNlID0gZmFsc2UpIHtcbiAgICB2YXIgX2EsIF9iLCBfYywgX2QsIF9lO1xuICAgIGNvbnN0IHsgYWN0aW9uLCBjb250cm9sbGVkLCBpbmRleCwgc2l6ZSwgc3RhdHVzIH0gPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgY29uc3QgbmV3SW5kZXggPSBpczMubnVtYmVyKHN0YXRlLmluZGV4KSA/IHN0YXRlLmluZGV4IDogaW5kZXg7XG4gICAgY29uc3QgbmV4dEluZGV4ID0gY29udHJvbGxlZCAmJiAhZm9yY2UgPyBpbmRleCA6IE1hdGgubWluKE1hdGgubWF4KG5ld0luZGV4LCAwKSwgc2l6ZSk7XG4gICAgcmV0dXJuIHtcbiAgICAgIGFjdGlvbjogKF9hID0gc3RhdGUuYWN0aW9uKSAhPSBudWxsID8gX2EgOiBhY3Rpb24sXG4gICAgICBjb250cm9sbGVkLFxuICAgICAgaW5kZXg6IG5leHRJbmRleCxcbiAgICAgIGxpZmVjeWNsZTogKF9iID0gc3RhdGUubGlmZWN5Y2xlKSAhPSBudWxsID8gX2IgOiBMSUZFQ1lDTEUuSU5JVCxcbiAgICAgIG9yaWdpbjogKF9jID0gc3RhdGUub3JpZ2luKSAhPSBudWxsID8gX2MgOiBudWxsLFxuICAgICAgc2l6ZTogKF9kID0gc3RhdGUuc2l6ZSkgIT0gbnVsbCA/IF9kIDogc2l6ZSxcbiAgICAgIHN0YXR1czogbmV4dEluZGV4ID09PSBzaXplID8gU1RBVFVTLkZJTklTSEVEIDogKF9lID0gc3RhdGUuc3RhdHVzKSAhPSBudWxsID8gX2UgOiBzdGF0dXNcbiAgICB9O1xuICB9XG4gIGdldFN0ZXBzKCkge1xuICAgIGNvbnN0IHN0ZXBzID0gdGhpcy5kYXRhLmdldChcInN0ZXBzXCIpO1xuICAgIHJldHVybiBBcnJheS5pc0FycmF5KHN0ZXBzKSA/IHN0ZXBzIDogW107XG4gIH1cbiAgaGFzVXBkYXRlZFN0YXRlKG9sZFN0YXRlKSB7XG4gICAgY29uc3QgYmVmb3JlID0gSlNPTi5zdHJpbmdpZnkob2xkU3RhdGUpO1xuICAgIGNvbnN0IGFmdGVyID0gSlNPTi5zdHJpbmdpZnkodGhpcy5nZXRTdGF0ZSgpKTtcbiAgICByZXR1cm4gYmVmb3JlICE9PSBhZnRlcjtcbiAgfVxuICBzZXRTdGF0ZShuZXh0U3RhdGUsIGluaXRpYWwgPSBmYWxzZSkge1xuICAgIGNvbnN0IHN0YXRlID0gdGhpcy5nZXRTdGF0ZSgpO1xuICAgIGNvbnN0IHtcbiAgICAgIGFjdGlvbixcbiAgICAgIGluZGV4LFxuICAgICAgbGlmZWN5Y2xlLFxuICAgICAgb3JpZ2luID0gbnVsbCxcbiAgICAgIHNpemUsXG4gICAgICBzdGF0dXNcbiAgICB9ID0ge1xuICAgICAgLi4uc3RhdGUsXG4gICAgICAuLi5uZXh0U3RhdGVcbiAgICB9O1xuICAgIHRoaXMuc3RvcmUuc2V0KFwiYWN0aW9uXCIsIGFjdGlvbik7XG4gICAgdGhpcy5zdG9yZS5zZXQoXCJpbmRleFwiLCBpbmRleCk7XG4gICAgdGhpcy5zdG9yZS5zZXQoXCJsaWZlY3ljbGVcIiwgbGlmZWN5Y2xlKTtcbiAgICB0aGlzLnN0b3JlLnNldChcIm9yaWdpblwiLCBvcmlnaW4pO1xuICAgIHRoaXMuc3RvcmUuc2V0KFwic2l6ZVwiLCBzaXplKTtcbiAgICB0aGlzLnN0b3JlLnNldChcInN0YXR1c1wiLCBzdGF0dXMpO1xuICAgIGlmIChpbml0aWFsKSB7XG4gICAgICB0aGlzLnN0b3JlLnNldChcImNvbnRyb2xsZWRcIiwgbmV4dFN0YXRlLmNvbnRyb2xsZWQpO1xuICAgICAgdGhpcy5zdG9yZS5zZXQoXCJjb250aW51b3VzXCIsIG5leHRTdGF0ZS5jb250aW51b3VzKTtcbiAgICB9XG4gICAgaWYgKHRoaXMubGlzdGVuZXIgJiYgdGhpcy5oYXNVcGRhdGVkU3RhdGUoc3RhdGUpKSB7XG4gICAgICB0aGlzLmxpc3RlbmVyKHRoaXMuZ2V0U3RhdGUoKSk7XG4gICAgfVxuICB9XG4gIGdldEhlbHBlcnMoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGNsb3NlOiB0aGlzLmNsb3NlLFxuICAgICAgZ286IHRoaXMuZ28sXG4gICAgICBpbmZvOiB0aGlzLmluZm8sXG4gICAgICBuZXh0OiB0aGlzLm5leHQsXG4gICAgICBvcGVuOiB0aGlzLm9wZW4sXG4gICAgICBwcmV2OiB0aGlzLnByZXYsXG4gICAgICByZXNldDogdGhpcy5yZXNldCxcbiAgICAgIHNraXA6IHRoaXMuc2tpcFxuICAgIH07XG4gIH1cbn07XG5mdW5jdGlvbiBjcmVhdGVTdG9yZShvcHRpb25zKSB7XG4gIHJldHVybiBuZXcgU3RvcmUob3B0aW9ucyk7XG59XG5cbi8vIHNyYy9jb21wb25lbnRzL092ZXJsYXkudHN4XG5pbXBvcnQgKiBhcyBSZWFjdDIgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgdHJlZUNoYW5nZXMgZnJvbSBcInRyZWUtY2hhbmdlc1wiO1xuXG4vLyBzcmMvY29tcG9uZW50cy9TcG90bGlnaHQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIEpveXJpZGVTcG90bGlnaHQoeyBzdHlsZXMgfSkge1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXG4gICAgXCJkaXZcIixcbiAgICB7XG4gICAgICBrZXk6IFwiSm95cmlkZVNwb3RsaWdodFwiLFxuICAgICAgY2xhc3NOYW1lOiBcInJlYWN0LWpveXJpZGVfX3Nwb3RsaWdodFwiLFxuICAgICAgXCJkYXRhLXRlc3QtaWRcIjogXCJzcG90bGlnaHRcIixcbiAgICAgIHN0eWxlOiBzdHlsZXNcbiAgICB9XG4gICk7XG59XG52YXIgU3BvdGxpZ2h0X2RlZmF1bHQgPSBKb3lyaWRlU3BvdGxpZ2h0O1xuXG4vLyBzcmMvY29tcG9uZW50cy9PdmVybGF5LnRzeFxudmFyIEpveXJpZGVPdmVybGF5ID0gY2xhc3MgZXh0ZW5kcyBSZWFjdDIuQ29tcG9uZW50IHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaXNBY3RpdmVcIiwgZmFsc2UpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJyZXNpemVUaW1lb3V0XCIpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJzY3JvbGxUaW1lb3V0XCIpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJzY3JvbGxQYXJlbnRcIik7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInN0YXRlXCIsIHtcbiAgICAgIGlzU2Nyb2xsaW5nOiBmYWxzZSxcbiAgICAgIG1vdXNlT3ZlclNwb3RsaWdodDogZmFsc2UsXG4gICAgICBzaG93U3BvdGxpZ2h0OiB0cnVlXG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhpZGVTcG90bGlnaHRcIiwgKCkgPT4ge1xuICAgICAgY29uc3QgeyBjb250aW51b3VzLCBkaXNhYmxlT3ZlcmxheSwgbGlmZWN5Y2xlIH0gPSB0aGlzLnByb3BzO1xuICAgICAgY29uc3QgaGlkZGVuTGlmZWN5Y2xlcyA9IFtcbiAgICAgICAgTElGRUNZQ0xFLklOSVQsXG4gICAgICAgIExJRkVDWUNMRS5CRUFDT04sXG4gICAgICAgIExJRkVDWUNMRS5DT01QTEVURSxcbiAgICAgICAgTElGRUNZQ0xFLkVSUk9SXG4gICAgICBdO1xuICAgICAgcmV0dXJuIGRpc2FibGVPdmVybGF5IHx8IChjb250aW51b3VzID8gaGlkZGVuTGlmZWN5Y2xlcy5pbmNsdWRlcyhsaWZlY3ljbGUpIDogbGlmZWN5Y2xlICE9PSBMSUZFQ1lDTEUuVE9PTFRJUCk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhhbmRsZU1vdXNlTW92ZVwiLCAoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHsgbW91c2VPdmVyU3BvdGxpZ2h0IH0gPSB0aGlzLnN0YXRlO1xuICAgICAgY29uc3QgeyBoZWlnaHQsIGxlZnQsIHBvc2l0aW9uLCB0b3AsIHdpZHRoIH0gPSB0aGlzLnNwb3RsaWdodFN0eWxlcztcbiAgICAgIGNvbnN0IG9mZnNldFkgPSBwb3NpdGlvbiA9PT0gXCJmaXhlZFwiID8gZXZlbnQuY2xpZW50WSA6IGV2ZW50LnBhZ2VZO1xuICAgICAgY29uc3Qgb2Zmc2V0WCA9IHBvc2l0aW9uID09PSBcImZpeGVkXCIgPyBldmVudC5jbGllbnRYIDogZXZlbnQucGFnZVg7XG4gICAgICBjb25zdCBpblNwb3RsaWdodEhlaWdodCA9IG9mZnNldFkgPj0gdG9wICYmIG9mZnNldFkgPD0gdG9wICsgaGVpZ2h0O1xuICAgICAgY29uc3QgaW5TcG90bGlnaHRXaWR0aCA9IG9mZnNldFggPj0gbGVmdCAmJiBvZmZzZXRYIDw9IGxlZnQgKyB3aWR0aDtcbiAgICAgIGNvbnN0IGluU3BvdGxpZ2h0ID0gaW5TcG90bGlnaHRXaWR0aCAmJiBpblNwb3RsaWdodEhlaWdodDtcbiAgICAgIGlmIChpblNwb3RsaWdodCAhPT0gbW91c2VPdmVyU3BvdGxpZ2h0KSB7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBtb3VzZU92ZXJTcG90bGlnaHQ6IGluU3BvdGxpZ2h0IH0pO1xuICAgICAgfVxuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJoYW5kbGVTY3JvbGxcIiwgKCkgPT4ge1xuICAgICAgY29uc3QgeyB0YXJnZXQgfSA9IHRoaXMucHJvcHM7XG4gICAgICBjb25zdCBlbGVtZW50ID0gZ2V0RWxlbWVudCh0YXJnZXQpO1xuICAgICAgaWYgKHRoaXMuc2Nyb2xsUGFyZW50ICE9PSBkb2N1bWVudCkge1xuICAgICAgICBjb25zdCB7IGlzU2Nyb2xsaW5nIH0gPSB0aGlzLnN0YXRlO1xuICAgICAgICBpZiAoIWlzU2Nyb2xsaW5nKSB7XG4gICAgICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7IGlzU2Nyb2xsaW5nOiB0cnVlLCBzaG93U3BvdGxpZ2h0OiBmYWxzZSB9KTtcbiAgICAgICAgfVxuICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5zY3JvbGxUaW1lb3V0KTtcbiAgICAgICAgdGhpcy5zY3JvbGxUaW1lb3V0ID0gd2luZG93LnNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBpc1Njcm9sbGluZzogZmFsc2UsIHNob3dTcG90bGlnaHQ6IHRydWUgfSk7XG4gICAgICAgIH0sIDUwKTtcbiAgICAgIH0gZWxzZSBpZiAoaGFzUG9zaXRpb24oZWxlbWVudCwgXCJzdGlja3lcIikpIHtcbiAgICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7fSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhhbmRsZVJlc2l6ZVwiLCAoKSA9PiB7XG4gICAgICBjbGVhclRpbWVvdXQodGhpcy5yZXNpemVUaW1lb3V0KTtcbiAgICAgIHRoaXMucmVzaXplVGltZW91dCA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKCF0aGlzLmlzQWN0aXZlKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuZm9yY2VVcGRhdGUoKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfSk7XG4gIH1cbiAgY29tcG9uZW50RGlkTW91bnQoKSB7XG4gICAgY29uc3QgeyBkZWJ1ZywgZGlzYWJsZVNjcm9sbGluZywgZGlzYWJsZVNjcm9sbFBhcmVudEZpeCA9IGZhbHNlLCB0YXJnZXQgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgZWxlbWVudCA9IGdldEVsZW1lbnQodGFyZ2V0KTtcbiAgICB0aGlzLnNjcm9sbFBhcmVudCA9IGdldFNjcm9sbFBhcmVudChlbGVtZW50ICE9IG51bGwgPyBlbGVtZW50IDogZG9jdW1lbnQuYm9keSwgZGlzYWJsZVNjcm9sbFBhcmVudEZpeCwgdHJ1ZSk7XG4gICAgdGhpcy5pc0FjdGl2ZSA9IHRydWU7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgaWYgKCFkaXNhYmxlU2Nyb2xsaW5nICYmIGhhc0N1c3RvbVNjcm9sbFBhcmVudChlbGVtZW50LCB0cnVlKSkge1xuICAgICAgICBsb2coe1xuICAgICAgICAgIHRpdGxlOiBcInN0ZXAgaGFzIGEgY3VzdG9tIHNjcm9sbCBwYXJlbnQgYW5kIGNhbiBjYXVzZSB0cm91YmxlIHdpdGggc2Nyb2xsaW5nXCIsXG4gICAgICAgICAgZGF0YTogW3sga2V5OiBcInBhcmVudFwiLCB2YWx1ZTogdGhpcy5zY3JvbGxQYXJlbnQgfV0sXG4gICAgICAgICAgZGVidWdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHRoaXMuaGFuZGxlUmVzaXplKTtcbiAgfVxuICBjb21wb25lbnREaWRVcGRhdGUocHJldmlvdXNQcm9wcykge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCB7IGRpc2FibGVTY3JvbGxQYXJlbnRGaXgsIGxpZmVjeWNsZSwgc3BvdGxpZ2h0Q2xpY2tzLCB0YXJnZXQgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgeyBjaGFuZ2VkIH0gPSB0cmVlQ2hhbmdlcyhwcmV2aW91c1Byb3BzLCB0aGlzLnByb3BzKTtcbiAgICBpZiAoY2hhbmdlZChcInRhcmdldFwiKSB8fCBjaGFuZ2VkKFwiZGlzYWJsZVNjcm9sbFBhcmVudEZpeFwiKSkge1xuICAgICAgY29uc3QgZWxlbWVudCA9IGdldEVsZW1lbnQodGFyZ2V0KTtcbiAgICAgIHRoaXMuc2Nyb2xsUGFyZW50ID0gZ2V0U2Nyb2xsUGFyZW50KGVsZW1lbnQgIT0gbnVsbCA/IGVsZW1lbnQgOiBkb2N1bWVudC5ib2R5LCBkaXNhYmxlU2Nyb2xsUGFyZW50Rml4LCB0cnVlKTtcbiAgICB9XG4gICAgaWYgKGNoYW5nZWQoXCJsaWZlY3ljbGVcIiwgTElGRUNZQ0xFLlRPT0xUSVApKSB7XG4gICAgICAoX2EgPSB0aGlzLnNjcm9sbFBhcmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmFkZEV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgdGhpcy5oYW5kbGVTY3JvbGwsIHsgcGFzc2l2ZTogdHJ1ZSB9KTtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjb25zdCB7IGlzU2Nyb2xsaW5nIH0gPSB0aGlzLnN0YXRlO1xuICAgICAgICBpZiAoIWlzU2Nyb2xsaW5nKSB7XG4gICAgICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7IHNob3dTcG90bGlnaHQ6IHRydWUgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMCk7XG4gICAgfVxuICAgIGlmIChjaGFuZ2VkKFwic3BvdGxpZ2h0Q2xpY2tzXCIpIHx8IGNoYW5nZWQoXCJkaXNhYmxlT3ZlcmxheVwiKSB8fCBjaGFuZ2VkKFwibGlmZWN5Y2xlXCIpKSB7XG4gICAgICBpZiAoc3BvdGxpZ2h0Q2xpY2tzICYmIGxpZmVjeWNsZSA9PT0gTElGRUNZQ0xFLlRPT0xUSVApIHtcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJtb3VzZW1vdmVcIiwgdGhpcy5oYW5kbGVNb3VzZU1vdmUsIGZhbHNlKTtcbiAgICAgIH0gZWxzZSBpZiAobGlmZWN5Y2xlICE9PSBMSUZFQ1lDTEUuVE9PTFRJUCkge1xuICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm1vdXNlbW92ZVwiLCB0aGlzLmhhbmRsZU1vdXNlTW92ZSk7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGNvbXBvbmVudFdpbGxVbm1vdW50KCkge1xuICAgIHZhciBfYTtcbiAgICB0aGlzLmlzQWN0aXZlID0gZmFsc2U7XG4gICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJtb3VzZW1vdmVcIiwgdGhpcy5oYW5kbGVNb3VzZU1vdmUpO1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHRoaXMuaGFuZGxlUmVzaXplKTtcbiAgICBjbGVhclRpbWVvdXQodGhpcy5yZXNpemVUaW1lb3V0KTtcbiAgICBjbGVhclRpbWVvdXQodGhpcy5zY3JvbGxUaW1lb3V0KTtcbiAgICAoX2EgPSB0aGlzLnNjcm9sbFBhcmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgdGhpcy5oYW5kbGVTY3JvbGwpO1xuICB9XG4gIGdldCBvdmVybGF5U3R5bGVzKCkge1xuICAgIGNvbnN0IHsgbW91c2VPdmVyU3BvdGxpZ2h0IH0gPSB0aGlzLnN0YXRlO1xuICAgIGNvbnN0IHsgZGlzYWJsZU92ZXJsYXlDbG9zZSwgcGxhY2VtZW50LCBzdHlsZXMgfSA9IHRoaXMucHJvcHM7XG4gICAgbGV0IGJhc2VTdHlsZXMgPSBzdHlsZXMub3ZlcmxheTtcbiAgICBpZiAoaXNMZWdhY3koKSkge1xuICAgICAgYmFzZVN0eWxlcyA9IHBsYWNlbWVudCA9PT0gXCJjZW50ZXJcIiA/IHN0eWxlcy5vdmVybGF5TGVnYWN5Q2VudGVyIDogc3R5bGVzLm92ZXJsYXlMZWdhY3k7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICBjdXJzb3I6IGRpc2FibGVPdmVybGF5Q2xvc2UgPyBcImRlZmF1bHRcIiA6IFwicG9pbnRlclwiLFxuICAgICAgaGVpZ2h0OiBnZXREb2N1bWVudEhlaWdodCgpLFxuICAgICAgcG9pbnRlckV2ZW50czogbW91c2VPdmVyU3BvdGxpZ2h0ID8gXCJub25lXCIgOiBcImF1dG9cIixcbiAgICAgIC4uLmJhc2VTdHlsZXNcbiAgICB9O1xuICB9XG4gIGdldCBzcG90bGlnaHRTdHlsZXMoKSB7XG4gICAgdmFyIF9hLCBfYiwgX2M7XG4gICAgY29uc3QgeyBzaG93U3BvdGxpZ2h0IH0gPSB0aGlzLnN0YXRlO1xuICAgIGNvbnN0IHtcbiAgICAgIGRpc2FibGVTY3JvbGxQYXJlbnRGaXggPSBmYWxzZSxcbiAgICAgIHNwb3RsaWdodENsaWNrcyxcbiAgICAgIHNwb3RsaWdodFBhZGRpbmcgPSAwLFxuICAgICAgc3R5bGVzLFxuICAgICAgdGFyZ2V0XG4gICAgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgZWxlbWVudCA9IGdldEVsZW1lbnQodGFyZ2V0KTtcbiAgICBjb25zdCBlbGVtZW50UmVjdCA9IGdldENsaWVudFJlY3QoZWxlbWVudCk7XG4gICAgY29uc3QgaXNGaXhlZFRhcmdldCA9IGhhc1Bvc2l0aW9uKGVsZW1lbnQpO1xuICAgIGNvbnN0IHRvcCA9IGdldEVsZW1lbnRQb3NpdGlvbihlbGVtZW50LCBzcG90bGlnaHRQYWRkaW5nLCBkaXNhYmxlU2Nyb2xsUGFyZW50Rml4KTtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uaXNMZWdhY3koKSA/IHN0eWxlcy5zcG90bGlnaHRMZWdhY3kgOiBzdHlsZXMuc3BvdGxpZ2h0LFxuICAgICAgaGVpZ2h0OiBNYXRoLnJvdW5kKCgoX2EgPSBlbGVtZW50UmVjdCA9PSBudWxsID8gdm9pZCAwIDogZWxlbWVudFJlY3QuaGVpZ2h0KSAhPSBudWxsID8gX2EgOiAwKSArIHNwb3RsaWdodFBhZGRpbmcgKiAyKSxcbiAgICAgIGxlZnQ6IE1hdGgucm91bmQoKChfYiA9IGVsZW1lbnRSZWN0ID09IG51bGwgPyB2b2lkIDAgOiBlbGVtZW50UmVjdC5sZWZ0KSAhPSBudWxsID8gX2IgOiAwKSAtIHNwb3RsaWdodFBhZGRpbmcpLFxuICAgICAgb3BhY2l0eTogc2hvd1Nwb3RsaWdodCA/IDEgOiAwLFxuICAgICAgcG9pbnRlckV2ZW50czogc3BvdGxpZ2h0Q2xpY2tzID8gXCJub25lXCIgOiBcImF1dG9cIixcbiAgICAgIHBvc2l0aW9uOiBpc0ZpeGVkVGFyZ2V0ID8gXCJmaXhlZFwiIDogXCJhYnNvbHV0ZVwiLFxuICAgICAgdG9wLFxuICAgICAgdHJhbnNpdGlvbjogXCJvcGFjaXR5IDAuMnNcIixcbiAgICAgIHdpZHRoOiBNYXRoLnJvdW5kKCgoX2MgPSBlbGVtZW50UmVjdCA9PSBudWxsID8gdm9pZCAwIDogZWxlbWVudFJlY3Qud2lkdGgpICE9IG51bGwgPyBfYyA6IDApICsgc3BvdGxpZ2h0UGFkZGluZyAqIDIpXG4gICAgfTtcbiAgfVxuICB1cGRhdGVTdGF0ZShzdGF0ZSkge1xuICAgIGlmICghdGhpcy5pc0FjdGl2ZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aGlzLnNldFN0YXRlKChwcmV2aW91c1N0YXRlKSA9PiAoeyAuLi5wcmV2aW91c1N0YXRlLCAuLi5zdGF0ZSB9KSk7XG4gIH1cbiAgcmVuZGVyKCkge1xuICAgIGNvbnN0IHsgc2hvd1Nwb3RsaWdodCB9ID0gdGhpcy5zdGF0ZTtcbiAgICBjb25zdCB7IG9uQ2xpY2tPdmVybGF5LCBwbGFjZW1lbnQgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgeyBoaWRlU3BvdGxpZ2h0LCBvdmVybGF5U3R5bGVzLCBzcG90bGlnaHRTdHlsZXMgfSA9IHRoaXM7XG4gICAgaWYgKGhpZGVTcG90bGlnaHQoKSkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGxldCBzcG90bGlnaHQyID0gcGxhY2VtZW50ICE9PSBcImNlbnRlclwiICYmIHNob3dTcG90bGlnaHQgJiYgLyogQF9fUFVSRV9fICovIFJlYWN0Mi5jcmVhdGVFbGVtZW50KFNwb3RsaWdodF9kZWZhdWx0LCB7IHN0eWxlczogc3BvdGxpZ2h0U3R5bGVzIH0pO1xuICAgIGlmIChnZXRCcm93c2VyKCkgPT09IFwic2FmYXJpXCIpIHtcbiAgICAgIGNvbnN0IHsgbWl4QmxlbmRNb2RlLCB6SW5kZXgsIC4uLnNhZmFyaU92ZXJsYXkgfSA9IG92ZXJsYXlTdHlsZXM7XG4gICAgICBzcG90bGlnaHQyID0gLyogQF9fUFVSRV9fICovIFJlYWN0Mi5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgc3R5bGU6IHsgLi4uc2FmYXJpT3ZlcmxheSB9IH0sIHNwb3RsaWdodDIpO1xuICAgICAgZGVsZXRlIG92ZXJsYXlTdHlsZXMuYmFja2dyb3VuZENvbG9yO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0Mi5jcmVhdGVFbGVtZW50KFxuICAgICAgXCJkaXZcIixcbiAgICAgIHtcbiAgICAgICAgY2xhc3NOYW1lOiBcInJlYWN0LWpveXJpZGVfX292ZXJsYXlcIixcbiAgICAgICAgXCJkYXRhLXRlc3QtaWRcIjogXCJvdmVybGF5XCIsXG4gICAgICAgIG9uQ2xpY2s6IG9uQ2xpY2tPdmVybGF5LFxuICAgICAgICByb2xlOiBcInByZXNlbnRhdGlvblwiLFxuICAgICAgICBzdHlsZTogb3ZlcmxheVN0eWxlc1xuICAgICAgfSxcbiAgICAgIHNwb3RsaWdodDJcbiAgICApO1xuICB9XG59O1xuXG4vLyBzcmMvY29tcG9uZW50cy9Qb3J0YWwudHN4XG5pbXBvcnQgKiBhcyBSZWFjdDMgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG52YXIgSm95cmlkZVBvcnRhbCA9IGNsYXNzIGV4dGVuZHMgUmVhY3QzLkNvbXBvbmVudCB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcIm5vZGVcIiwgbnVsbCk7XG4gIH1cbiAgY29tcG9uZW50RGlkTW91bnQoKSB7XG4gICAgY29uc3QgeyBpZCB9ID0gdGhpcy5wcm9wcztcbiAgICBpZiAoIWNhblVzZURPTSgpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHRoaXMubm9kZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIik7XG4gICAgdGhpcy5ub2RlLmlkID0gaWQ7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZCh0aGlzLm5vZGUpO1xuICAgIGlmICghaXNSZWFjdDE2KSB7XG4gICAgICB0aGlzLnJlbmRlclJlYWN0MTUoKTtcbiAgICB9XG4gIH1cbiAgY29tcG9uZW50RGlkVXBkYXRlKCkge1xuICAgIGlmICghY2FuVXNlRE9NKCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCFpc1JlYWN0MTYpIHtcbiAgICAgIHRoaXMucmVuZGVyUmVhY3QxNSgpO1xuICAgIH1cbiAgfVxuICBjb21wb25lbnRXaWxsVW5tb3VudCgpIHtcbiAgICBpZiAoIWNhblVzZURPTSgpIHx8ICF0aGlzLm5vZGUpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCFpc1JlYWN0MTYpIHtcbiAgICAgIFJlYWN0RE9NLnVubW91bnRDb21wb25lbnRBdE5vZGUodGhpcy5ub2RlKTtcbiAgICB9XG4gICAgaWYgKHRoaXMubm9kZS5wYXJlbnROb2RlID09PSBkb2N1bWVudC5ib2R5KSB7XG4gICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKHRoaXMubm9kZSk7XG4gICAgICB0aGlzLm5vZGUgPSBudWxsO1xuICAgIH1cbiAgfVxuICByZW5kZXJSZWFjdDE1KCkge1xuICAgIGlmICghY2FuVXNlRE9NKCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgeyBjaGlsZHJlbiB9ID0gdGhpcy5wcm9wcztcbiAgICBpZiAodGhpcy5ub2RlKSB7XG4gICAgICBSZWFjdERPTS51bnN0YWJsZV9yZW5kZXJTdWJ0cmVlSW50b0NvbnRhaW5lcih0aGlzLCBjaGlsZHJlbiwgdGhpcy5ub2RlKTtcbiAgICB9XG4gIH1cbiAgcmVuZGVyUmVhY3QxNigpIHtcbiAgICBpZiAoIWNhblVzZURPTSgpIHx8ICFpc1JlYWN0MTYpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBjb25zdCB7IGNoaWxkcmVuIH0gPSB0aGlzLnByb3BzO1xuICAgIGlmICghdGhpcy5ub2RlKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIFJlYWN0RE9NLmNyZWF0ZVBvcnRhbChjaGlsZHJlbiwgdGhpcy5ub2RlKTtcbiAgfVxuICByZW5kZXIoKSB7XG4gICAgaWYgKCFpc1JlYWN0MTYpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5yZW5kZXJSZWFjdDE2KCk7XG4gIH1cbn07XG5cbi8vIHNyYy9jb21wb25lbnRzL1N0ZXAudHN4XG5pbXBvcnQgKiBhcyBSZWFjdDggZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgRmxvYXRlciBmcm9tIFwicmVhY3QtZmxvYXRlclwiO1xuaW1wb3J0IGlzNSBmcm9tIFwiaXMtbGl0ZVwiO1xuaW1wb3J0IHRyZWVDaGFuZ2VzMiBmcm9tIFwidHJlZS1jaGFuZ2VzXCI7XG5cbi8vIHNyYy9tb2R1bGVzL3Njb3BlLnRzXG52YXIgU2NvcGUgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKGVsZW1lbnQsIG9wdGlvbnMpIHtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiZWxlbWVudFwiKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwib3B0aW9uc1wiKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiY2FuQmVUYWJiZWRcIiwgKGVsZW1lbnQpID0+IHtcbiAgICAgIGNvbnN0IHsgdGFiSW5kZXggfSA9IGVsZW1lbnQ7XG4gICAgICBpZiAodGFiSW5kZXggPT09IG51bGwgfHwgdGFiSW5kZXggPCAwKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0aGlzLmNhbkhhdmVGb2N1cyhlbGVtZW50KTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiY2FuSGF2ZUZvY3VzXCIsIChlbGVtZW50KSA9PiB7XG4gICAgICBjb25zdCB2YWxpZFRhYk5vZGVzID0gL2lucHV0fHNlbGVjdHx0ZXh0YXJlYXxidXR0b258b2JqZWN0LztcbiAgICAgIGNvbnN0IG5vZGVOYW1lID0gZWxlbWVudC5ub2RlTmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgY29uc3QgaXNWYWxpZCA9IHZhbGlkVGFiTm9kZXMudGVzdChub2RlTmFtZSkgJiYgIWVsZW1lbnQuZ2V0QXR0cmlidXRlKFwiZGlzYWJsZWRcIikgfHwgbm9kZU5hbWUgPT09IFwiYVwiICYmICEhZWxlbWVudC5nZXRBdHRyaWJ1dGUoXCJocmVmXCIpO1xuICAgICAgcmV0dXJuIGlzVmFsaWQgJiYgdGhpcy5pc1Zpc2libGUoZWxlbWVudCk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImZpbmRWYWxpZFRhYkVsZW1lbnRzXCIsICgpID0+IFtdLnNsaWNlLmNhbGwodGhpcy5lbGVtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCIqXCIpLCAwKS5maWx0ZXIodGhpcy5jYW5CZVRhYmJlZCkpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJoYW5kbGVLZXlEb3duXCIsIChldmVudCkgPT4ge1xuICAgICAgY29uc3QgeyBjb2RlID0gXCJUYWJcIiB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgaWYgKGV2ZW50LmNvZGUgPT09IGNvZGUpIHtcbiAgICAgICAgdGhpcy5pbnRlcmNlcHRUYWIoZXZlbnQpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJpbnRlcmNlcHRUYWJcIiwgKGV2ZW50KSA9PiB7XG4gICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgY29uc3QgZWxlbWVudHMgPSB0aGlzLmZpbmRWYWxpZFRhYkVsZW1lbnRzKCk7XG4gICAgICBjb25zdCB7IHNoaWZ0S2V5IH0gPSBldmVudDtcbiAgICAgIGlmICghZWxlbWVudHMubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGxldCB4ID0gZG9jdW1lbnQuYWN0aXZlRWxlbWVudCA/IGVsZW1lbnRzLmluZGV4T2YoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCkgOiAwO1xuICAgICAgaWYgKHggPT09IC0xIHx8ICFzaGlmdEtleSAmJiB4ICsgMSA9PT0gZWxlbWVudHMubGVuZ3RoKSB7XG4gICAgICAgIHggPSAwO1xuICAgICAgfSBlbHNlIGlmIChzaGlmdEtleSAmJiB4ID09PSAwKSB7XG4gICAgICAgIHggPSBlbGVtZW50cy5sZW5ndGggLSAxO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgeCArPSBzaGlmdEtleSA/IC0xIDogMTtcbiAgICAgIH1cbiAgICAgIGVsZW1lbnRzW3hdLmZvY3VzKCk7XG4gICAgfSk7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGNsYXNzLW1ldGhvZHMtdXNlLXRoaXNcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaXNIaWRkZW5cIiwgKGVsZW1lbnQpID0+IHtcbiAgICAgIGNvbnN0IG5vU2l6ZSA9IGVsZW1lbnQub2Zmc2V0V2lkdGggPD0gMCAmJiBlbGVtZW50Lm9mZnNldEhlaWdodCA8PSAwO1xuICAgICAgY29uc3Qgc3R5bGUgPSB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShlbGVtZW50KTtcbiAgICAgIGlmIChub1NpemUgJiYgIWVsZW1lbnQuaW5uZXJIVE1MKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG5vU2l6ZSAmJiBzdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKFwib3ZlcmZsb3dcIikgIT09IFwidmlzaWJsZVwiIHx8IHN0eWxlLmdldFByb3BlcnR5VmFsdWUoXCJkaXNwbGF5XCIpID09PSBcIm5vbmVcIjtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaXNWaXNpYmxlXCIsIChlbGVtZW50KSA9PiB7XG4gICAgICBsZXQgcGFyZW50RWxlbWVudCA9IGVsZW1lbnQ7XG4gICAgICB3aGlsZSAocGFyZW50RWxlbWVudCkge1xuICAgICAgICBpZiAocGFyZW50RWxlbWVudCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50KSB7XG4gICAgICAgICAgaWYgKHBhcmVudEVsZW1lbnQgPT09IGRvY3VtZW50LmJvZHkpIHtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAodGhpcy5pc0hpZGRlbihwYXJlbnRFbGVtZW50KSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgIH1cbiAgICAgICAgICBwYXJlbnRFbGVtZW50ID0gcGFyZW50RWxlbWVudC5wYXJlbnROb2RlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwicmVtb3ZlU2NvcGVcIiwgKCkgPT4ge1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIHRoaXMuaGFuZGxlS2V5RG93bik7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImNoZWNrRm9jdXNcIiwgKHRhcmdldCkgPT4ge1xuICAgICAgaWYgKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgIT09IHRhcmdldCkge1xuICAgICAgICB0YXJnZXQuZm9jdXMoKTtcbiAgICAgICAgd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB0aGlzLmNoZWNrRm9jdXModGFyZ2V0KSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInNldEZvY3VzXCIsICgpID0+IHtcbiAgICAgIGNvbnN0IHsgc2VsZWN0b3IgfSA9IHRoaXMub3B0aW9ucztcbiAgICAgIGlmICghc2VsZWN0b3IpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgY29uc3QgdGFyZ2V0ID0gdGhpcy5lbGVtZW50LnF1ZXJ5U2VsZWN0b3Ioc2VsZWN0b3IpO1xuICAgICAgaWYgKHRhcmdldCkge1xuICAgICAgICB3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IHRoaXMuY2hlY2tGb2N1cyh0YXJnZXQpKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBpZiAoIShlbGVtZW50IGluc3RhbmNlb2YgSFRNTEVsZW1lbnQpKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCBwYXJhbWV0ZXI6IGVsZW1lbnQgbXVzdCBiZSBhbiBIVE1MRWxlbWVudFwiKTtcbiAgICB9XG4gICAgdGhpcy5lbGVtZW50ID0gZWxlbWVudDtcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCB0aGlzLmhhbmRsZUtleURvd24sIGZhbHNlKTtcbiAgICB0aGlzLnNldEZvY3VzKCk7XG4gIH1cbn07XG5cbi8vIHNyYy9jb21wb25lbnRzL0JlYWNvbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0NCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBpczQgZnJvbSBcImlzLWxpdGVcIjtcbnZhciBKb3lyaWRlQmVhY29uID0gY2xhc3MgZXh0ZW5kcyBSZWFjdDQuQ29tcG9uZW50IHtcbiAgY29uc3RydWN0b3IocHJvcHMpIHtcbiAgICBzdXBlcihwcm9wcyk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImJlYWNvblwiLCBudWxsKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwic2V0QmVhY29uUmVmXCIsIChjKSA9PiB7XG4gICAgICB0aGlzLmJlYWNvbiA9IGM7XG4gICAgfSk7XG4gICAgaWYgKHByb3BzLmJlYWNvbkNvbXBvbmVudCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBoZWFkID0gZG9jdW1lbnQuaGVhZCB8fCBkb2N1bWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZShcImhlYWRcIilbMF07XG4gICAgY29uc3Qgc3R5bGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIik7XG4gICAgc3R5bGUuaWQgPSBcImpveXJpZGUtYmVhY29uLWFuaW1hdGlvblwiO1xuICAgIGlmIChwcm9wcy5ub25jZSkge1xuICAgICAgc3R5bGUuc2V0QXR0cmlidXRlKFwibm9uY2VcIiwgcHJvcHMubm9uY2UpO1xuICAgIH1cbiAgICBjb25zdCBjc3MgPSBgXG4gICAgICAgIEBrZXlmcmFtZXMgam95cmlkZS1iZWFjb24taW5uZXIge1xuICAgICAgICAgIDIwJSB7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjk7XG4gICAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgICA5MCUge1xuICAgICAgICAgICAgb3BhY2l0eTogMC43O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgQGtleWZyYW1lcyBqb3lyaWRlLWJlYWNvbi1vdXRlciB7XG4gICAgICAgICAgMCUge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAgIDQ1JSB7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjc7XG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDAuNzUpO1xuICAgICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgICAgMTAwJSB7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjk7XG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgYDtcbiAgICBzdHlsZS5hcHBlbmRDaGlsZChkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShjc3MpKTtcbiAgICBoZWFkLmFwcGVuZENoaWxkKHN0eWxlKTtcbiAgfVxuICBjb21wb25lbnREaWRNb3VudCgpIHtcbiAgICBjb25zdCB7IHNob3VsZEZvY3VzIH0gPSB0aGlzLnByb3BzO1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgIGlmICghaXM0LmRvbUVsZW1lbnQodGhpcy5iZWFjb24pKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihcImJlYWNvbiBpcyBub3QgYSB2YWxpZCBET00gZWxlbWVudFwiKTtcbiAgICAgIH1cbiAgICB9XG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBpZiAoaXM0LmRvbUVsZW1lbnQodGhpcy5iZWFjb24pICYmIHNob3VsZEZvY3VzKSB7XG4gICAgICAgIHRoaXMuYmVhY29uLmZvY3VzKCk7XG4gICAgICB9XG4gICAgfSwgMCk7XG4gIH1cbiAgY29tcG9uZW50V2lsbFVubW91bnQoKSB7XG4gICAgY29uc3Qgc3R5bGUgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcImpveXJpZGUtYmVhY29uLWFuaW1hdGlvblwiKTtcbiAgICBpZiAoc3R5bGUgPT0gbnVsbCA/IHZvaWQgMCA6IHN0eWxlLnBhcmVudE5vZGUpIHtcbiAgICAgIHN0eWxlLnBhcmVudE5vZGUucmVtb3ZlQ2hpbGQoc3R5bGUpO1xuICAgIH1cbiAgfVxuICByZW5kZXIoKSB7XG4gICAgY29uc3Qge1xuICAgICAgYmVhY29uQ29tcG9uZW50LFxuICAgICAgY29udGludW91cyxcbiAgICAgIGluZGV4LFxuICAgICAgaXNMYXN0U3RlcCxcbiAgICAgIGxvY2FsZSxcbiAgICAgIG9uQ2xpY2tPckhvdmVyLFxuICAgICAgc2l6ZSxcbiAgICAgIHN0ZXAsXG4gICAgICBzdHlsZXNcbiAgICB9ID0gdGhpcy5wcm9wcztcbiAgICBjb25zdCB0aXRsZSA9IGdldFJlYWN0Tm9kZVRleHQobG9jYWxlLm9wZW4pO1xuICAgIGNvbnN0IHNoYXJlZFByb3BzID0ge1xuICAgICAgXCJhcmlhLWxhYmVsXCI6IHRpdGxlLFxuICAgICAgb25DbGljazogb25DbGlja09ySG92ZXIsXG4gICAgICBvbk1vdXNlRW50ZXI6IG9uQ2xpY2tPckhvdmVyLFxuICAgICAgcmVmOiB0aGlzLnNldEJlYWNvblJlZixcbiAgICAgIHRpdGxlXG4gICAgfTtcbiAgICBsZXQgY29tcG9uZW50O1xuICAgIGlmIChiZWFjb25Db21wb25lbnQpIHtcbiAgICAgIGNvbnN0IEJlYWNvbkNvbXBvbmVudCA9IGJlYWNvbkNvbXBvbmVudDtcbiAgICAgIGNvbXBvbmVudCA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDQuY3JlYXRlRWxlbWVudChcbiAgICAgICAgQmVhY29uQ29tcG9uZW50LFxuICAgICAgICB7XG4gICAgICAgICAgY29udGludW91cyxcbiAgICAgICAgICBpbmRleCxcbiAgICAgICAgICBpc0xhc3RTdGVwLFxuICAgICAgICAgIHNpemUsXG4gICAgICAgICAgc3RlcCxcbiAgICAgICAgICAuLi5zaGFyZWRQcm9wc1xuICAgICAgICB9XG4gICAgICApO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb21wb25lbnQgPSAvKiBAX19QVVJFX18gKi8gUmVhY3Q0LmNyZWF0ZUVsZW1lbnQoXG4gICAgICAgIFwiYnV0dG9uXCIsXG4gICAgICAgIHtcbiAgICAgICAgICBrZXk6IFwiSm95cmlkZUJlYWNvblwiLFxuICAgICAgICAgIGNsYXNzTmFtZTogXCJyZWFjdC1qb3lyaWRlX19iZWFjb25cIixcbiAgICAgICAgICBcImRhdGEtdGVzdC1pZFwiOiBcImJ1dHRvbi1iZWFjb25cIixcbiAgICAgICAgICBzdHlsZTogc3R5bGVzLmJlYWNvbixcbiAgICAgICAgICB0eXBlOiBcImJ1dHRvblwiLFxuICAgICAgICAgIC4uLnNoYXJlZFByb3BzXG4gICAgICAgIH0sXG4gICAgICAgIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDQuY3JlYXRlRWxlbWVudChcInNwYW5cIiwgeyBzdHlsZTogc3R5bGVzLmJlYWNvbklubmVyIH0pLFxuICAgICAgICAvKiBAX19QVVJFX18gKi8gUmVhY3Q0LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIsIHsgc3R5bGU6IHN0eWxlcy5iZWFjb25PdXRlciB9KVxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbXBvbmVudDtcbiAgfVxufTtcblxuLy8gc3JjL2NvbXBvbmVudHMvVG9vbHRpcC9pbmRleC50c3hcbmltcG9ydCAqIGFzIFJlYWN0NyBmcm9tIFwicmVhY3RcIjtcblxuLy8gc3JjL2NvbXBvbmVudHMvVG9vbHRpcC9Db250YWluZXIudHN4XG5pbXBvcnQgKiBhcyBSZWFjdDYgZnJvbSBcInJlYWN0XCI7XG5cbi8vIHNyYy9jb21wb25lbnRzL1Rvb2x0aXAvQ2xvc2VCdXR0b24udHN4XG5pbXBvcnQgUmVhY3Q1IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gSm95cmlkZVRvb2x0aXBDbG9zZUJ1dHRvbih7IHN0eWxlcywgLi4ucHJvcHMgfSkge1xuICBjb25zdCB7IGNvbG9yLCBoZWlnaHQsIHdpZHRoLCAuLi5zdHlsZSB9ID0gc3R5bGVzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0NS5jcmVhdGVFbGVtZW50KFwiYnV0dG9uXCIsIHsgc3R5bGUsIHR5cGU6IFwiYnV0dG9uXCIsIC4uLnByb3BzIH0sIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDUuY3JlYXRlRWxlbWVudChcbiAgICBcInN2Z1wiLFxuICAgIHtcbiAgICAgIGhlaWdodDogdHlwZW9mIGhlaWdodCA9PT0gXCJudW1iZXJcIiA/IGAke2hlaWdodH1weGAgOiBoZWlnaHQsXG4gICAgICBwcmVzZXJ2ZUFzcGVjdFJhdGlvOiBcInhNaWRZTWlkXCIsXG4gICAgICB2ZXJzaW9uOiBcIjEuMVwiLFxuICAgICAgdmlld0JveDogXCIwIDAgMTggMThcIixcbiAgICAgIHdpZHRoOiB0eXBlb2Ygd2lkdGggPT09IFwibnVtYmVyXCIgPyBgJHt3aWR0aH1weGAgOiB3aWR0aCxcbiAgICAgIHhtbG5zOiBcImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICB9LFxuICAgIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDUuY3JlYXRlRWxlbWVudChcImdcIiwgbnVsbCwgLyogQF9fUFVSRV9fICovIFJlYWN0NS5jcmVhdGVFbGVtZW50KFxuICAgICAgXCJwYXRoXCIsXG4gICAgICB7XG4gICAgICAgIGQ6IFwiTTguMTM5MTExMjksOS4wMDI2ODE5MSBMMC4xNzE1MjE4MjcsMTcuMDI1ODQ2NyBDLTAuMDQ5ODAyNzA0OSwxNy4yNDg3MTUgLTAuMDQ5ODAyNzA0OSwxNy42MDk4Mzk0IDAuMTcxNTIxODI3LDE3LjgzMjc1NDUgQzAuMjgyMDQzNTQsMTcuOTQ0MzUyNiAwLjQyNzE4ODIwNiwxNy45OTk4NzA2IDAuNTcyMDUxNzY1LDE3Ljk5OTg3MDYgQzAuNzE3MTQ5NTgsMTcuOTk5ODcwNiAwLjg2MjAxMzEzOSwxNy45NDQzNTI2IDAuOTcyNTgxNzAzLDE3LjgzMjc1NDUgTDkuMDAwMDkzNyw5Ljc0OTI0NjE4IEwxNy4wMjc2MDU3LDE3LjgzMjc1NDUgQzE3LjEzODQwODUsMTcuOTQ0MzUyNiAxNy4yODMyNzIxLDE3Ljk5OTg3MDYgMTcuNDI4MTM1NiwxNy45OTk4NzA2IEMxNy41NzI5OTkyLDE3Ljk5OTg3MDYgMTcuNzE4MDk3LDE3Ljk0NDM1MjYgMTcuODI4NjY1NiwxNy44MzI3NTQ1IEMxOC4wNDk5OTAxLDE3LjYwOTg4NjIgMTguMDQ5OTkwMSwxNy4yNDg3NjE4IDE3LjgyODY2NTYsMTcuMDI1ODQ2NyBMOS44NjEzNTcyMiw5LjAwMjY4MTkxIEwxNy44MzQwMDY2LDAuOTczODQ4MjI1IEMxOC4wNTUzMzExLDAuNzUwOTc5OTM0IDE4LjA1NTMzMTEsMC4zODk4NTU1MzIgMTcuODM0MDA2NiwwLjE2Njk0MDM5IEMxNy42MTI2ODIxLC0wLjA1NTY0Njc5NjggMTcuMjU0MDM3LC0wLjA1NTY0Njc5NjggMTcuMDMyOTQ2NywwLjE2Njk0MDM5IEw5LjAwMDQyMTY2LDguMjU2MTE3NjUgTDAuOTY3MDA2NDI0LDAuMTY3MjY4MzQ1IEMwLjc0NTY4MTg5MiwtMC4wNTUzMTg4NDI2IDAuMzg3MzE3OTMxLC0wLjA1NTMxODg0MjYgMC4xNjU5OTMzOTksMC4xNjcyNjgzNDUgQy0wLjA1NTMzMTEzMzEsMC4zOTAxMzY2MzUgLTAuMDU1MzMxMTMzMSwwLjc1MTI2MTAzOCAwLjE2NTk5MzM5OSwwLjk3NDE3NjE3OSBMOC4xMzkyMDQ5OSw5LjAwMjY4MTkxIEw4LjEzOTExMTI5LDkuMDAyNjgxOTEgWlwiLFxuICAgICAgICBmaWxsOiBjb2xvclxuICAgICAgfVxuICAgICkpXG4gICkpO1xufVxudmFyIENsb3NlQnV0dG9uX2RlZmF1bHQgPSBKb3lyaWRlVG9vbHRpcENsb3NlQnV0dG9uO1xuXG4vLyBzcmMvY29tcG9uZW50cy9Ub29sdGlwL0NvbnRhaW5lci50c3hcbmZ1bmN0aW9uIEpveXJpZGVUb29sdGlwQ29udGFpbmVyKHByb3BzKSB7XG4gIGNvbnN0IHsgYmFja1Byb3BzLCBjbG9zZVByb3BzLCBpbmRleCwgaXNMYXN0U3RlcCwgcHJpbWFyeVByb3BzLCBza2lwUHJvcHMsIHN0ZXAsIHRvb2x0aXBQcm9wcyB9ID0gcHJvcHM7XG4gIGNvbnN0IHsgY29udGVudCwgaGlkZUJhY2tCdXR0b24sIGhpZGVDbG9zZUJ1dHRvbiwgaGlkZUZvb3Rlciwgc2hvd1NraXBCdXR0b24sIHN0eWxlcywgdGl0bGUgfSA9IHN0ZXA7XG4gIGNvbnN0IG91dHB1dCA9IHt9O1xuICBvdXRwdXQucHJpbWFyeSA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDYuY3JlYXRlRWxlbWVudChcbiAgICBcImJ1dHRvblwiLFxuICAgIHtcbiAgICAgIFwiZGF0YS10ZXN0LWlkXCI6IFwiYnV0dG9uLXByaW1hcnlcIixcbiAgICAgIHN0eWxlOiBzdHlsZXMuYnV0dG9uTmV4dCxcbiAgICAgIHR5cGU6IFwiYnV0dG9uXCIsXG4gICAgICAuLi5wcmltYXJ5UHJvcHNcbiAgICB9XG4gICk7XG4gIGlmIChzaG93U2tpcEJ1dHRvbiAmJiAhaXNMYXN0U3RlcCkge1xuICAgIG91dHB1dC5za2lwID0gLyogQF9fUFVSRV9fICovIFJlYWN0Ni5jcmVhdGVFbGVtZW50KFxuICAgICAgXCJidXR0b25cIixcbiAgICAgIHtcbiAgICAgICAgXCJhcmlhLWxpdmVcIjogXCJvZmZcIixcbiAgICAgICAgXCJkYXRhLXRlc3QtaWRcIjogXCJidXR0b24tc2tpcFwiLFxuICAgICAgICBzdHlsZTogc3R5bGVzLmJ1dHRvblNraXAsXG4gICAgICAgIHR5cGU6IFwiYnV0dG9uXCIsXG4gICAgICAgIC4uLnNraXBQcm9wc1xuICAgICAgfVxuICAgICk7XG4gIH1cbiAgaWYgKCFoaWRlQmFja0J1dHRvbiAmJiBpbmRleCA+IDApIHtcbiAgICBvdXRwdXQuYmFjayA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDYuY3JlYXRlRWxlbWVudChcImJ1dHRvblwiLCB7IFwiZGF0YS10ZXN0LWlkXCI6IFwiYnV0dG9uLWJhY2tcIiwgc3R5bGU6IHN0eWxlcy5idXR0b25CYWNrLCB0eXBlOiBcImJ1dHRvblwiLCAuLi5iYWNrUHJvcHMgfSk7XG4gIH1cbiAgb3V0cHV0LmNsb3NlID0gIWhpZGVDbG9zZUJ1dHRvbiAmJiAvKiBAX19QVVJFX18gKi8gUmVhY3Q2LmNyZWF0ZUVsZW1lbnQoQ2xvc2VCdXR0b25fZGVmYXVsdCwgeyBcImRhdGEtdGVzdC1pZFwiOiBcImJ1dHRvbi1jbG9zZVwiLCBzdHlsZXM6IHN0eWxlcy5idXR0b25DbG9zZSwgLi4uY2xvc2VQcm9wcyB9KTtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDYuY3JlYXRlRWxlbWVudChcbiAgICBcImRpdlwiLFxuICAgIHtcbiAgICAgIGtleTogXCJKb3lyaWRlVG9vbHRpcFwiLFxuICAgICAgXCJhcmlhLWxhYmVsXCI6IGdldFJlYWN0Tm9kZVRleHQodGl0bGUgIT0gbnVsbCA/IHRpdGxlIDogY29udGVudCksXG4gICAgICBjbGFzc05hbWU6IFwicmVhY3Qtam95cmlkZV9fdG9vbHRpcFwiLFxuICAgICAgc3R5bGU6IHN0eWxlcy50b29sdGlwLFxuICAgICAgLi4udG9vbHRpcFByb3BzXG4gICAgfSxcbiAgICAvKiBAX19QVVJFX18gKi8gUmVhY3Q2LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgeyBzdHlsZTogc3R5bGVzLnRvb2x0aXBDb250YWluZXIgfSwgdGl0bGUgJiYgLyogQF9fUFVSRV9fICovIFJlYWN0Ni5jcmVhdGVFbGVtZW50KFwiaDFcIiwgeyBcImFyaWEtbGFiZWxcIjogZ2V0UmVhY3ROb2RlVGV4dCh0aXRsZSksIHN0eWxlOiBzdHlsZXMudG9vbHRpcFRpdGxlIH0sIHRpdGxlKSwgLyogQF9fUFVSRV9fICovIFJlYWN0Ni5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgc3R5bGU6IHN0eWxlcy50b29sdGlwQ29udGVudCB9LCBjb250ZW50KSksXG4gICAgIWhpZGVGb290ZXIgJiYgLyogQF9fUFVSRV9fICovIFJlYWN0Ni5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgc3R5bGU6IHN0eWxlcy50b29sdGlwRm9vdGVyIH0sIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDYuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7IHN0eWxlOiBzdHlsZXMudG9vbHRpcEZvb3RlclNwYWNlciB9LCBvdXRwdXQuc2tpcCksIG91dHB1dC5iYWNrLCBvdXRwdXQucHJpbWFyeSksXG4gICAgb3V0cHV0LmNsb3NlXG4gICk7XG59XG52YXIgQ29udGFpbmVyX2RlZmF1bHQgPSBKb3lyaWRlVG9vbHRpcENvbnRhaW5lcjtcblxuLy8gc3JjL2NvbXBvbmVudHMvVG9vbHRpcC9pbmRleC50c3hcbnZhciBKb3lyaWRlVG9vbHRpcCA9IGNsYXNzIGV4dGVuZHMgUmVhY3Q3LkNvbXBvbmVudCB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhhbmRsZUNsaWNrQmFja1wiLCAoZXZlbnQpID0+IHtcbiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBjb25zdCB7IGhlbHBlcnMgfSA9IHRoaXMucHJvcHM7XG4gICAgICBoZWxwZXJzLnByZXYoKTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaGFuZGxlQ2xpY2tDbG9zZVwiLCAoZXZlbnQpID0+IHtcbiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBjb25zdCB7IGhlbHBlcnMgfSA9IHRoaXMucHJvcHM7XG4gICAgICBoZWxwZXJzLmNsb3NlKFwiYnV0dG9uX2Nsb3NlXCIpO1xuICAgIH0pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJoYW5kbGVDbGlja1ByaW1hcnlcIiwgKGV2ZW50KSA9PiB7XG4gICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgY29uc3QgeyBjb250aW51b3VzLCBoZWxwZXJzIH0gPSB0aGlzLnByb3BzO1xuICAgICAgaWYgKCFjb250aW51b3VzKSB7XG4gICAgICAgIGhlbHBlcnMuY2xvc2UoXCJidXR0b25fcHJpbWFyeVwiKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaGVscGVycy5uZXh0KCk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhhbmRsZUNsaWNrU2tpcFwiLCAoZXZlbnQpID0+IHtcbiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBjb25zdCB7IGhlbHBlcnMgfSA9IHRoaXMucHJvcHM7XG4gICAgICBoZWxwZXJzLnNraXAoKTtcbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiZ2V0RWxlbWVudHNQcm9wc1wiLCAoKSA9PiB7XG4gICAgICBjb25zdCB7IGNvbnRpbnVvdXMsIGluZGV4LCBpc0xhc3RTdGVwLCBzZXRUb29sdGlwUmVmLCBzaXplLCBzdGVwIH0gPSB0aGlzLnByb3BzO1xuICAgICAgY29uc3QgeyBiYWNrLCBjbG9zZSwgbGFzdCwgbmV4dCwgbmV4dExhYmVsV2l0aFByb2dyZXNzLCBza2lwIH0gPSBzdGVwLmxvY2FsZTtcbiAgICAgIGNvbnN0IGJhY2tUZXh0ID0gZ2V0UmVhY3ROb2RlVGV4dChiYWNrKTtcbiAgICAgIGNvbnN0IGNsb3NlVGV4dCA9IGdldFJlYWN0Tm9kZVRleHQoY2xvc2UpO1xuICAgICAgY29uc3QgbGFzdFRleHQgPSBnZXRSZWFjdE5vZGVUZXh0KGxhc3QpO1xuICAgICAgY29uc3QgbmV4dFRleHQgPSBnZXRSZWFjdE5vZGVUZXh0KG5leHQpO1xuICAgICAgY29uc3Qgc2tpcFRleHQgPSBnZXRSZWFjdE5vZGVUZXh0KHNraXApO1xuICAgICAgbGV0IHByaW1hcnkgPSBjbG9zZTtcbiAgICAgIGxldCBwcmltYXJ5VGV4dCA9IGNsb3NlVGV4dDtcbiAgICAgIGlmIChjb250aW51b3VzKSB7XG4gICAgICAgIHByaW1hcnkgPSBuZXh0O1xuICAgICAgICBwcmltYXJ5VGV4dCA9IG5leHRUZXh0O1xuICAgICAgICBpZiAoc3RlcC5zaG93UHJvZ3Jlc3MgJiYgIWlzTGFzdFN0ZXApIHtcbiAgICAgICAgICBjb25zdCBsYWJlbFdpdGhQcm9ncmVzcyA9IGdldFJlYWN0Tm9kZVRleHQobmV4dExhYmVsV2l0aFByb2dyZXNzLCB7XG4gICAgICAgICAgICBzdGVwOiBpbmRleCArIDEsXG4gICAgICAgICAgICBzdGVwczogc2l6ZVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHByaW1hcnkgPSByZXBsYWNlTG9jYWxlQ29udGVudChuZXh0TGFiZWxXaXRoUHJvZ3Jlc3MsIGluZGV4ICsgMSwgc2l6ZSk7XG4gICAgICAgICAgcHJpbWFyeVRleHQgPSBsYWJlbFdpdGhQcm9ncmVzcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNMYXN0U3RlcCkge1xuICAgICAgICAgIHByaW1hcnkgPSBsYXN0O1xuICAgICAgICAgIHByaW1hcnlUZXh0ID0gbGFzdFRleHQ7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGJhY2tQcm9wczoge1xuICAgICAgICAgIFwiYXJpYS1sYWJlbFwiOiBiYWNrVGV4dCxcbiAgICAgICAgICBjaGlsZHJlbjogYmFjayxcbiAgICAgICAgICBcImRhdGEtYWN0aW9uXCI6IFwiYmFja1wiLFxuICAgICAgICAgIG9uQ2xpY2s6IHRoaXMuaGFuZGxlQ2xpY2tCYWNrLFxuICAgICAgICAgIHJvbGU6IFwiYnV0dG9uXCIsXG4gICAgICAgICAgdGl0bGU6IGJhY2tUZXh0XG4gICAgICAgIH0sXG4gICAgICAgIGNsb3NlUHJvcHM6IHtcbiAgICAgICAgICBcImFyaWEtbGFiZWxcIjogY2xvc2VUZXh0LFxuICAgICAgICAgIGNoaWxkcmVuOiBjbG9zZSxcbiAgICAgICAgICBcImRhdGEtYWN0aW9uXCI6IFwiY2xvc2VcIixcbiAgICAgICAgICBvbkNsaWNrOiB0aGlzLmhhbmRsZUNsaWNrQ2xvc2UsXG4gICAgICAgICAgcm9sZTogXCJidXR0b25cIixcbiAgICAgICAgICB0aXRsZTogY2xvc2VUZXh0XG4gICAgICAgIH0sXG4gICAgICAgIHByaW1hcnlQcm9wczoge1xuICAgICAgICAgIFwiYXJpYS1sYWJlbFwiOiBwcmltYXJ5VGV4dCxcbiAgICAgICAgICBjaGlsZHJlbjogcHJpbWFyeSxcbiAgICAgICAgICBcImRhdGEtYWN0aW9uXCI6IFwicHJpbWFyeVwiLFxuICAgICAgICAgIG9uQ2xpY2s6IHRoaXMuaGFuZGxlQ2xpY2tQcmltYXJ5LFxuICAgICAgICAgIHJvbGU6IFwiYnV0dG9uXCIsXG4gICAgICAgICAgdGl0bGU6IHByaW1hcnlUZXh0XG4gICAgICAgIH0sXG4gICAgICAgIHNraXBQcm9wczoge1xuICAgICAgICAgIFwiYXJpYS1sYWJlbFwiOiBza2lwVGV4dCxcbiAgICAgICAgICBjaGlsZHJlbjogc2tpcCxcbiAgICAgICAgICBcImRhdGEtYWN0aW9uXCI6IFwic2tpcFwiLFxuICAgICAgICAgIG9uQ2xpY2s6IHRoaXMuaGFuZGxlQ2xpY2tTa2lwLFxuICAgICAgICAgIHJvbGU6IFwiYnV0dG9uXCIsXG4gICAgICAgICAgdGl0bGU6IHNraXBUZXh0XG4gICAgICAgIH0sXG4gICAgICAgIHRvb2x0aXBQcm9wczoge1xuICAgICAgICAgIFwiYXJpYS1tb2RhbFwiOiB0cnVlLFxuICAgICAgICAgIHJlZjogc2V0VG9vbHRpcFJlZixcbiAgICAgICAgICByb2xlOiBcImFsZXJ0ZGlhbG9nXCJcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9KTtcbiAgfVxuICByZW5kZXIoKSB7XG4gICAgY29uc3QgeyBjb250aW51b3VzLCBpbmRleCwgaXNMYXN0U3RlcCwgc2V0VG9vbHRpcFJlZiwgc2l6ZSwgc3RlcCB9ID0gdGhpcy5wcm9wcztcbiAgICBjb25zdCB7IGJlYWNvbkNvbXBvbmVudCwgdG9vbHRpcENvbXBvbmVudCwgLi4uY2xlYW5TdGVwIH0gPSBzdGVwO1xuICAgIGxldCBjb21wb25lbnQ7XG4gICAgaWYgKHRvb2x0aXBDb21wb25lbnQpIHtcbiAgICAgIGNvbnN0IHJlbmRlclByb3BzID0ge1xuICAgICAgICAuLi50aGlzLmdldEVsZW1lbnRzUHJvcHMoKSxcbiAgICAgICAgY29udGludW91cyxcbiAgICAgICAgaW5kZXgsXG4gICAgICAgIGlzTGFzdFN0ZXAsXG4gICAgICAgIHNpemUsXG4gICAgICAgIHN0ZXA6IGNsZWFuU3RlcCxcbiAgICAgICAgc2V0VG9vbHRpcFJlZlxuICAgICAgfTtcbiAgICAgIGNvbnN0IFRvb2x0aXBDb21wb25lbnQgPSB0b29sdGlwQ29tcG9uZW50O1xuICAgICAgY29tcG9uZW50ID0gLyogQF9fUFVSRV9fICovIFJlYWN0Ny5jcmVhdGVFbGVtZW50KFRvb2x0aXBDb21wb25lbnQsIHsgLi4ucmVuZGVyUHJvcHMgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbXBvbmVudCA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDcuY3JlYXRlRWxlbWVudChcbiAgICAgICAgQ29udGFpbmVyX2RlZmF1bHQsXG4gICAgICAgIHtcbiAgICAgICAgICAuLi50aGlzLmdldEVsZW1lbnRzUHJvcHMoKSxcbiAgICAgICAgICBjb250aW51b3VzLFxuICAgICAgICAgIGluZGV4LFxuICAgICAgICAgIGlzTGFzdFN0ZXAsXG4gICAgICAgICAgc2l6ZSxcbiAgICAgICAgICBzdGVwXG4gICAgICAgIH1cbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBjb21wb25lbnQ7XG4gIH1cbn07XG5cbi8vIHNyYy9jb21wb25lbnRzL1N0ZXAudHN4XG52YXIgSm95cmlkZVN0ZXAgPSBjbGFzcyBleHRlbmRzIFJlYWN0OC5Db21wb25lbnQge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJzY29wZVwiLCBudWxsKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwidG9vbHRpcFwiLCBudWxsKTtcbiAgICAvKipcbiAgICAgKiBCZWFjb24gY2xpY2svaG92ZXIgZXZlbnQgbGlzdGVuZXJcbiAgICAgKi9cbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaGFuZGxlQ2xpY2tIb3ZlckJlYWNvblwiLCAoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHsgc3RlcCwgc3RvcmUgfSA9IHRoaXMucHJvcHM7XG4gICAgICBpZiAoZXZlbnQudHlwZSA9PT0gXCJtb3VzZWVudGVyXCIgJiYgc3RlcC5ldmVudCAhPT0gXCJob3ZlclwiKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHN0b3JlLnVwZGF0ZSh7IGxpZmVjeWNsZTogTElGRUNZQ0xFLlRPT0xUSVAgfSk7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInNldFRvb2x0aXBSZWZcIiwgKGVsZW1lbnQpID0+IHtcbiAgICAgIHRoaXMudG9vbHRpcCA9IGVsZW1lbnQ7XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInNldFBvcHBlclwiLCAocG9wcGVyLCB0eXBlKSA9PiB7XG4gICAgICB2YXIgX2E7XG4gICAgICBjb25zdCB7IGFjdGlvbiwgbGlmZWN5Y2xlLCBzdGVwLCBzdG9yZSB9ID0gdGhpcy5wcm9wcztcbiAgICAgIGlmICh0eXBlID09PSBcIndyYXBwZXJcIikge1xuICAgICAgICBzdG9yZS5zZXRQb3BwZXIoXCJiZWFjb25cIiwgcG9wcGVyKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN0b3JlLnNldFBvcHBlcihcInRvb2x0aXBcIiwgcG9wcGVyKTtcbiAgICAgIH1cbiAgICAgIGlmIChzdG9yZS5nZXRQb3BwZXIoXCJiZWFjb25cIikgJiYgKHN0b3JlLmdldFBvcHBlcihcInRvb2x0aXBcIikgfHwgc3RlcC5wbGFjZW1lbnQgPT09IFwiY2VudGVyXCIpICYmIGxpZmVjeWNsZSA9PT0gTElGRUNZQ0xFLklOSVQpIHtcbiAgICAgICAgc3RvcmUudXBkYXRlKHtcbiAgICAgICAgICBhY3Rpb24sXG4gICAgICAgICAgbGlmZWN5Y2xlOiBMSUZFQ1lDTEUuUkVBRFlcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBpZiAoKF9hID0gc3RlcC5mbG9hdGVyUHJvcHMpID09IG51bGwgPyB2b2lkIDAgOiBfYS5nZXRQb3BwZXIpIHtcbiAgICAgICAgc3RlcC5mbG9hdGVyUHJvcHMuZ2V0UG9wcGVyKHBvcHBlciwgdHlwZSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInJlbmRlclRvb2x0aXBcIiwgKHJlbmRlclByb3BzKSA9PiB7XG4gICAgICBjb25zdCB7IGNvbnRpbnVvdXMsIGhlbHBlcnMsIGluZGV4LCBzaXplLCBzdGVwIH0gPSB0aGlzLnByb3BzO1xuICAgICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDguY3JlYXRlRWxlbWVudChcbiAgICAgICAgSm95cmlkZVRvb2x0aXAsXG4gICAgICAgIHtcbiAgICAgICAgICBjb250aW51b3VzLFxuICAgICAgICAgIGhlbHBlcnMsXG4gICAgICAgICAgaW5kZXgsXG4gICAgICAgICAgaXNMYXN0U3RlcDogaW5kZXggKyAxID09PSBzaXplLFxuICAgICAgICAgIHNldFRvb2x0aXBSZWY6IHRoaXMuc2V0VG9vbHRpcFJlZixcbiAgICAgICAgICBzaXplLFxuICAgICAgICAgIHN0ZXAsXG4gICAgICAgICAgLi4ucmVuZGVyUHJvcHNcbiAgICAgICAgfVxuICAgICAgKTtcbiAgICB9KTtcbiAgfVxuICBjb21wb25lbnREaWRNb3VudCgpIHtcbiAgICBjb25zdCB7IGRlYnVnLCBpbmRleCB9ID0gdGhpcy5wcm9wcztcbiAgICBsb2coe1xuICAgICAgdGl0bGU6IGBzdGVwOiR7aW5kZXh9YCxcbiAgICAgIGRhdGE6IFt7IGtleTogXCJwcm9wc1wiLCB2YWx1ZTogdGhpcy5wcm9wcyB9XSxcbiAgICAgIGRlYnVnXG4gICAgfSk7XG4gIH1cbiAgY29tcG9uZW50RGlkVXBkYXRlKHByZXZpb3VzUHJvcHMpIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3Qge1xuICAgICAgYWN0aW9uLFxuICAgICAgY2FsbGJhY2ssXG4gICAgICBjb250aW51b3VzLFxuICAgICAgY29udHJvbGxlZCxcbiAgICAgIGRlYnVnLFxuICAgICAgaGVscGVycyxcbiAgICAgIGluZGV4LFxuICAgICAgbGlmZWN5Y2xlLFxuICAgICAgc2hvdWxkU2Nyb2xsOiBzaG91bGRTY3JvbGwyLFxuICAgICAgc3RhdHVzLFxuICAgICAgc3RlcCxcbiAgICAgIHN0b3JlXG4gICAgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgeyBjaGFuZ2VkLCBjaGFuZ2VkRnJvbSB9ID0gdHJlZUNoYW5nZXMyKHByZXZpb3VzUHJvcHMsIHRoaXMucHJvcHMpO1xuICAgIGNvbnN0IHN0YXRlID0gaGVscGVycy5pbmZvKCk7XG4gICAgY29uc3Qgc2tpcEJlYWNvbiA9IGNvbnRpbnVvdXMgJiYgYWN0aW9uICE9PSBBQ1RJT05TLkNMT1NFICYmIChpbmRleCA+IDAgfHwgYWN0aW9uID09PSBBQ1RJT05TLlBSRVYpO1xuICAgIGNvbnN0IGhhc1N0b3JlQ2hhbmdlZCA9IGNoYW5nZWQoXCJhY3Rpb25cIikgfHwgY2hhbmdlZChcImluZGV4XCIpIHx8IGNoYW5nZWQoXCJsaWZlY3ljbGVcIikgfHwgY2hhbmdlZChcInN0YXR1c1wiKTtcbiAgICBjb25zdCBpc0luaXRpYWwgPSBjaGFuZ2VkRnJvbShcImxpZmVjeWNsZVwiLCBbTElGRUNZQ0xFLlRPT0xUSVAsIExJRkVDWUNMRS5JTklUXSwgTElGRUNZQ0xFLklOSVQpO1xuICAgIGNvbnN0IGlzQWZ0ZXJBY3Rpb24gPSBjaGFuZ2VkKFwiYWN0aW9uXCIsIFtcbiAgICAgIEFDVElPTlMuTkVYVCxcbiAgICAgIEFDVElPTlMuUFJFVixcbiAgICAgIEFDVElPTlMuU0tJUCxcbiAgICAgIEFDVElPTlMuQ0xPU0VcbiAgICBdKTtcbiAgICBjb25zdCBpc0NvbnRyb2xsZWQgPSBjb250cm9sbGVkICYmIGluZGV4ID09PSBwcmV2aW91c1Byb3BzLmluZGV4O1xuICAgIGlmIChpc0FmdGVyQWN0aW9uICYmIChpc0luaXRpYWwgfHwgaXNDb250cm9sbGVkKSkge1xuICAgICAgY2FsbGJhY2soe1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgaW5kZXg6IHByZXZpb3VzUHJvcHMuaW5kZXgsXG4gICAgICAgIGxpZmVjeWNsZTogTElGRUNZQ0xFLkNPTVBMRVRFLFxuICAgICAgICBzdGVwOiBwcmV2aW91c1Byb3BzLnN0ZXAsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5TVEVQX0FGVEVSXG4gICAgICB9KTtcbiAgICB9XG4gICAgaWYgKHN0ZXAucGxhY2VtZW50ID09PSBcImNlbnRlclwiICYmIHN0YXR1cyA9PT0gU1RBVFVTLlJVTk5JTkcgJiYgY2hhbmdlZChcImluZGV4XCIpICYmIGFjdGlvbiAhPT0gQUNUSU9OUy5TVEFSVCAmJiBsaWZlY3ljbGUgPT09IExJRkVDWUNMRS5JTklUKSB7XG4gICAgICBzdG9yZS51cGRhdGUoeyBsaWZlY3ljbGU6IExJRkVDWUNMRS5SRUFEWSB9KTtcbiAgICB9XG4gICAgaWYgKGhhc1N0b3JlQ2hhbmdlZCkge1xuICAgICAgY29uc3QgZWxlbWVudCA9IGdldEVsZW1lbnQoc3RlcC50YXJnZXQpO1xuICAgICAgY29uc3QgZWxlbWVudEV4aXN0cyA9ICEhZWxlbWVudDtcbiAgICAgIGNvbnN0IGhhc1JlbmRlcmVkVGFyZ2V0ID0gZWxlbWVudEV4aXN0cyAmJiBpc0VsZW1lbnRWaXNpYmxlKGVsZW1lbnQpO1xuICAgICAgaWYgKGhhc1JlbmRlcmVkVGFyZ2V0KSB7XG4gICAgICAgIGlmIChjaGFuZ2VkRnJvbShcInN0YXR1c1wiLCBTVEFUVVMuUkVBRFksIFNUQVRVUy5SVU5OSU5HKSB8fCBjaGFuZ2VkRnJvbShcImxpZmVjeWNsZVwiLCBMSUZFQ1lDTEUuSU5JVCwgTElGRUNZQ0xFLlJFQURZKSkge1xuICAgICAgICAgIGNhbGxiYWNrKHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgc3RlcCxcbiAgICAgICAgICAgIHR5cGU6IEVWRU5UUy5TVEVQX0JFRk9SRVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oZWxlbWVudEV4aXN0cyA/IFwiVGFyZ2V0IG5vdCB2aXNpYmxlXCIgOiBcIlRhcmdldCBub3QgbW91bnRlZFwiLCBzdGVwKTtcbiAgICAgICAgY2FsbGJhY2soe1xuICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgIHR5cGU6IEVWRU5UUy5UQVJHRVRfTk9UX0ZPVU5ELFxuICAgICAgICAgIHN0ZXBcbiAgICAgICAgfSk7XG4gICAgICAgIGlmICghY29udHJvbGxlZCkge1xuICAgICAgICAgIHN0b3JlLnVwZGF0ZSh7IGluZGV4OiBpbmRleCArIChhY3Rpb24gPT09IEFDVElPTlMuUFJFViA/IC0xIDogMSkgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKGNoYW5nZWRGcm9tKFwibGlmZWN5Y2xlXCIsIExJRkVDWUNMRS5JTklULCBMSUZFQ1lDTEUuUkVBRFkpKSB7XG4gICAgICBzdG9yZS51cGRhdGUoe1xuICAgICAgICBsaWZlY3ljbGU6IGhpZGVCZWFjb24oc3RlcCkgfHwgc2tpcEJlYWNvbiA/IExJRkVDWUNMRS5UT09MVElQIDogTElGRUNZQ0xFLkJFQUNPTlxuICAgICAgfSk7XG4gICAgfVxuICAgIGlmIChjaGFuZ2VkKFwiaW5kZXhcIikpIHtcbiAgICAgIGxvZyh7XG4gICAgICAgIHRpdGxlOiBgc3RlcDoke2xpZmVjeWNsZX1gLFxuICAgICAgICBkYXRhOiBbeyBrZXk6IFwicHJvcHNcIiwgdmFsdWU6IHRoaXMucHJvcHMgfV0sXG4gICAgICAgIGRlYnVnXG4gICAgICB9KTtcbiAgICB9XG4gICAgaWYgKGNoYW5nZWQoXCJsaWZlY3ljbGVcIiwgTElGRUNZQ0xFLkJFQUNPTikpIHtcbiAgICAgIGNhbGxiYWNrKHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHN0ZXAsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5CRUFDT05cbiAgICAgIH0pO1xuICAgIH1cbiAgICBpZiAoY2hhbmdlZChcImxpZmVjeWNsZVwiLCBMSUZFQ1lDTEUuVE9PTFRJUCkpIHtcbiAgICAgIGNhbGxiYWNrKHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHN0ZXAsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5UT09MVElQXG4gICAgICB9KTtcbiAgICAgIGlmIChzaG91bGRTY3JvbGwyICYmIHRoaXMudG9vbHRpcCkge1xuICAgICAgICB0aGlzLnNjb3BlID0gbmV3IFNjb3BlKHRoaXMudG9vbHRpcCwgeyBzZWxlY3RvcjogXCJbZGF0YS1hY3Rpb249cHJpbWFyeV1cIiB9KTtcbiAgICAgICAgdGhpcy5zY29wZS5zZXRGb2N1cygpO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoY2hhbmdlZEZyb20oXCJsaWZlY3ljbGVcIiwgW0xJRkVDWUNMRS5UT09MVElQLCBMSUZFQ1lDTEUuSU5JVF0sIExJRkVDWUNMRS5JTklUKSkge1xuICAgICAgKF9hID0gdGhpcy5zY29wZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnJlbW92ZVNjb3BlKCk7XG4gICAgICBzdG9yZS5jbGVhbnVwUG9wcGVycygpO1xuICAgIH1cbiAgfVxuICBjb21wb25lbnRXaWxsVW5tb3VudCgpIHtcbiAgICB2YXIgX2E7XG4gICAgKF9hID0gdGhpcy5zY29wZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnJlbW92ZVNjb3BlKCk7XG4gIH1cbiAgZ2V0IG9wZW4oKSB7XG4gICAgY29uc3QgeyBsaWZlY3ljbGUsIHN0ZXAgfSA9IHRoaXMucHJvcHM7XG4gICAgcmV0dXJuIGhpZGVCZWFjb24oc3RlcCkgfHwgbGlmZWN5Y2xlID09PSBMSUZFQ1lDTEUuVE9PTFRJUDtcbiAgfVxuICByZW5kZXIoKSB7XG4gICAgY29uc3QgeyBjb250aW51b3VzLCBkZWJ1ZywgaW5kZXgsIG5vbmNlLCBzaG91bGRTY3JvbGw6IHNob3VsZFNjcm9sbDIsIHNpemUsIHN0ZXAgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3QgdGFyZ2V0ID0gZ2V0RWxlbWVudChzdGVwLnRhcmdldCk7XG4gICAgaWYgKCF2YWxpZGF0ZVN0ZXAoc3RlcCkgfHwgIWlzNS5kb21FbGVtZW50KHRhcmdldCkpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0OC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsga2V5OiBgSm95cmlkZVN0ZXAtJHtpbmRleH1gLCBjbGFzc05hbWU6IFwicmVhY3Qtam95cmlkZV9fc3RlcFwiIH0sIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDguY3JlYXRlRWxlbWVudChcbiAgICAgIEZsb2F0ZXIsXG4gICAgICB7XG4gICAgICAgIC4uLnN0ZXAuZmxvYXRlclByb3BzLFxuICAgICAgICBjb21wb25lbnQ6IHRoaXMucmVuZGVyVG9vbHRpcCxcbiAgICAgICAgZGVidWcsXG4gICAgICAgIGdldFBvcHBlcjogdGhpcy5zZXRQb3BwZXIsXG4gICAgICAgIGlkOiBgcmVhY3Qtam95cmlkZS1zdGVwLSR7aW5kZXh9YCxcbiAgICAgICAgb3BlbjogdGhpcy5vcGVuLFxuICAgICAgICBwbGFjZW1lbnQ6IHN0ZXAucGxhY2VtZW50LFxuICAgICAgICB0YXJnZXQ6IHN0ZXAudGFyZ2V0XG4gICAgICB9LFxuICAgICAgLyogQF9fUFVSRV9fICovIFJlYWN0OC5jcmVhdGVFbGVtZW50KFxuICAgICAgICBKb3lyaWRlQmVhY29uLFxuICAgICAgICB7XG4gICAgICAgICAgYmVhY29uQ29tcG9uZW50OiBzdGVwLmJlYWNvbkNvbXBvbmVudCxcbiAgICAgICAgICBjb250aW51b3VzLFxuICAgICAgICAgIGluZGV4LFxuICAgICAgICAgIGlzTGFzdFN0ZXA6IGluZGV4ICsgMSA9PT0gc2l6ZSxcbiAgICAgICAgICBsb2NhbGU6IHN0ZXAubG9jYWxlLFxuICAgICAgICAgIG5vbmNlLFxuICAgICAgICAgIG9uQ2xpY2tPckhvdmVyOiB0aGlzLmhhbmRsZUNsaWNrSG92ZXJCZWFjb24sXG4gICAgICAgICAgc2hvdWxkRm9jdXM6IHNob3VsZFNjcm9sbDIsXG4gICAgICAgICAgc2l6ZSxcbiAgICAgICAgICBzdGVwLFxuICAgICAgICAgIHN0eWxlczogc3RlcC5zdHlsZXNcbiAgICAgICAgfVxuICAgICAgKVxuICAgICkpO1xuICB9XG59O1xuXG4vLyBzcmMvY29tcG9uZW50cy9pbmRleC50c3hcbnZhciBKb3lyaWRlID0gY2xhc3MgZXh0ZW5kcyBSZWFjdDkuQ29tcG9uZW50IHtcbiAgY29uc3RydWN0b3IocHJvcHMpIHtcbiAgICBzdXBlcihwcm9wcyk7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcImhlbHBlcnNcIik7XG4gICAgX19wdWJsaWNGaWVsZCh0aGlzLCBcInN0b3JlXCIpO1xuICAgIC8qKlxuICAgICAqIFRyaWdnZXIgdGhlIGNhbGxiYWNrLlxuICAgICAqL1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJjYWxsYmFja1wiLCAoZGF0YSkgPT4ge1xuICAgICAgY29uc3QgeyBjYWxsYmFjayB9ID0gdGhpcy5wcm9wcztcbiAgICAgIGlmIChpczYuZnVuY3Rpb24oY2FsbGJhY2spKSB7XG4gICAgICAgIGNhbGxiYWNrKGRhdGEpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIC8qKlxuICAgICAqIEtleWRvd24gZXZlbnQgbGlzdGVuZXJcbiAgICAgKi9cbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaGFuZGxlS2V5Ym9hcmRcIiwgKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCB7IGluZGV4LCBsaWZlY3ljbGUgfSA9IHRoaXMuc3RhdGU7XG4gICAgICBjb25zdCB7IHN0ZXBzIH0gPSB0aGlzLnByb3BzO1xuICAgICAgY29uc3Qgc3RlcCA9IHN0ZXBzW2luZGV4XTtcbiAgICAgIGlmIChsaWZlY3ljbGUgPT09IExJRkVDWUNMRS5UT09MVElQKSB7XG4gICAgICAgIGlmIChldmVudC5jb2RlID09PSBcIkVzY2FwZVwiICYmIHN0ZXAgJiYgIXN0ZXAuZGlzYWJsZUNsb3NlT25Fc2MpIHtcbiAgICAgICAgICB0aGlzLnN0b3JlLmNsb3NlKFwia2V5Ym9hcmRcIik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiaGFuZGxlQ2xpY2tPdmVybGF5XCIsICgpID0+IHtcbiAgICAgIGNvbnN0IHsgaW5kZXggfSA9IHRoaXMuc3RhdGU7XG4gICAgICBjb25zdCB7IHN0ZXBzIH0gPSB0aGlzLnByb3BzO1xuICAgICAgY29uc3Qgc3RlcCA9IGdldE1lcmdlZFN0ZXAodGhpcy5wcm9wcywgc3RlcHNbaW5kZXhdKTtcbiAgICAgIGlmICghc3RlcC5kaXNhYmxlT3ZlcmxheUNsb3NlKSB7XG4gICAgICAgIHRoaXMuaGVscGVycy5jbG9zZShcIm92ZXJsYXlcIik7XG4gICAgICB9XG4gICAgfSk7XG4gICAgLyoqXG4gICAgICogU3luYyB0aGUgc3RvcmUgd2l0aCB0aGUgY29tcG9uZW50J3Mgc3RhdGVcbiAgICAgKi9cbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwic3luY1N0YXRlXCIsIChzdGF0ZSkgPT4ge1xuICAgICAgdGhpcy5zZXRTdGF0ZShzdGF0ZSk7XG4gICAgfSk7XG4gICAgY29uc3QgeyBkZWJ1ZywgZ2V0SGVscGVycywgcnVuID0gdHJ1ZSwgc3RlcEluZGV4IH0gPSBwcm9wcztcbiAgICB0aGlzLnN0b3JlID0gY3JlYXRlU3RvcmUoe1xuICAgICAgLi4ucHJvcHMsXG4gICAgICBjb250cm9sbGVkOiBydW4gJiYgaXM2Lm51bWJlcihzdGVwSW5kZXgpXG4gICAgfSk7XG4gICAgdGhpcy5oZWxwZXJzID0gdGhpcy5zdG9yZS5nZXRIZWxwZXJzKCk7XG4gICAgY29uc3QgeyBhZGRMaXN0ZW5lciB9ID0gdGhpcy5zdG9yZTtcbiAgICBsb2coe1xuICAgICAgdGl0bGU6IFwiaW5pdFwiLFxuICAgICAgZGF0YTogW1xuICAgICAgICB7IGtleTogXCJwcm9wc1wiLCB2YWx1ZTogdGhpcy5wcm9wcyB9LFxuICAgICAgICB7IGtleTogXCJzdGF0ZVwiLCB2YWx1ZTogdGhpcy5zdGF0ZSB9XG4gICAgICBdLFxuICAgICAgZGVidWdcbiAgICB9KTtcbiAgICBhZGRMaXN0ZW5lcih0aGlzLnN5bmNTdGF0ZSk7XG4gICAgaWYgKGdldEhlbHBlcnMpIHtcbiAgICAgIGdldEhlbHBlcnModGhpcy5oZWxwZXJzKTtcbiAgICB9XG4gICAgdGhpcy5zdGF0ZSA9IHRoaXMuc3RvcmUuZ2V0U3RhdGUoKTtcbiAgfVxuICBjb21wb25lbnREaWRNb3VudCgpIHtcbiAgICBpZiAoIWNhblVzZURPTSgpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IHsgZGVidWcsIGRpc2FibGVDbG9zZU9uRXNjLCBydW4sIHN0ZXBzIH0gPSB0aGlzLnByb3BzO1xuICAgIGNvbnN0IHsgc3RhcnQgfSA9IHRoaXMuc3RvcmU7XG4gICAgaWYgKHZhbGlkYXRlU3RlcHMoc3RlcHMsIGRlYnVnKSAmJiBydW4pIHtcbiAgICAgIHN0YXJ0KCk7XG4gICAgfVxuICAgIGlmICghZGlzYWJsZUNsb3NlT25Fc2MpIHtcbiAgICAgIGRvY3VtZW50LmJvZHkuYWRkRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgdGhpcy5oYW5kbGVLZXlib2FyZCwgeyBwYXNzaXZlOiB0cnVlIH0pO1xuICAgIH1cbiAgfVxuICBjb21wb25lbnREaWRVcGRhdGUocHJldmlvdXNQcm9wcywgcHJldmlvdXNTdGF0ZSkge1xuICAgIGlmICghY2FuVXNlRE9NKCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgeyBhY3Rpb24sIGNvbnRyb2xsZWQsIGluZGV4LCBzdGF0dXMgfSA9IHRoaXMuc3RhdGU7XG4gICAgY29uc3QgeyBkZWJ1ZywgcnVuLCBzdGVwSW5kZXgsIHN0ZXBzIH0gPSB0aGlzLnByb3BzO1xuICAgIGNvbnN0IHsgc3RlcEluZGV4OiBwcmV2aW91c1N0ZXBJbmRleCwgc3RlcHM6IHByZXZpb3VzU3RlcHMgfSA9IHByZXZpb3VzUHJvcHM7XG4gICAgY29uc3QgeyByZXNldCwgc2V0U3RlcHMsIHN0YXJ0LCBzdG9wLCB1cGRhdGUgfSA9IHRoaXMuc3RvcmU7XG4gICAgY29uc3QgeyBjaGFuZ2VkOiBjaGFuZ2VkUHJvcHMgfSA9IHRyZWVDaGFuZ2VzMyhwcmV2aW91c1Byb3BzLCB0aGlzLnByb3BzKTtcbiAgICBjb25zdCB7IGNoYW5nZWQsIGNoYW5nZWRGcm9tIH0gPSB0cmVlQ2hhbmdlczMocHJldmlvdXNTdGF0ZSwgdGhpcy5zdGF0ZSk7XG4gICAgY29uc3Qgc3RlcCA9IGdldE1lcmdlZFN0ZXAodGhpcy5wcm9wcywgc3RlcHNbaW5kZXhdKTtcbiAgICBjb25zdCBzdGVwc0NoYW5nZWQgPSAhaXNFcXVhbChwcmV2aW91c1N0ZXBzLCBzdGVwcyk7XG4gICAgY29uc3Qgc3RlcEluZGV4Q2hhbmdlZCA9IGlzNi5udW1iZXIoc3RlcEluZGV4KSAmJiBjaGFuZ2VkUHJvcHMoXCJzdGVwSW5kZXhcIik7XG4gICAgY29uc3QgdGFyZ2V0ID0gZ2V0RWxlbWVudChzdGVwLnRhcmdldCk7XG4gICAgaWYgKHN0ZXBzQ2hhbmdlZCkge1xuICAgICAgaWYgKHZhbGlkYXRlU3RlcHMoc3RlcHMsIGRlYnVnKSkge1xuICAgICAgICBzZXRTdGVwcyhzdGVwcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oXCJTdGVwcyBhcmUgbm90IHZhbGlkXCIsIHN0ZXBzKTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKGNoYW5nZWRQcm9wcyhcInJ1blwiKSkge1xuICAgICAgaWYgKHJ1bikge1xuICAgICAgICBzdGFydChzdGVwSW5kZXgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3RvcCgpO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoc3RlcEluZGV4Q2hhbmdlZCkge1xuICAgICAgbGV0IG5leHRBY3Rpb24gPSBpczYubnVtYmVyKHByZXZpb3VzU3RlcEluZGV4KSAmJiBwcmV2aW91c1N0ZXBJbmRleCA8IHN0ZXBJbmRleCA/IEFDVElPTlMuTkVYVCA6IEFDVElPTlMuUFJFVjtcbiAgICAgIGlmIChhY3Rpb24gPT09IEFDVElPTlMuU1RPUCkge1xuICAgICAgICBuZXh0QWN0aW9uID0gQUNUSU9OUy5TVEFSVDtcbiAgICAgIH1cbiAgICAgIGlmICghW1NUQVRVUy5GSU5JU0hFRCwgU1RBVFVTLlNLSVBQRURdLmluY2x1ZGVzKHN0YXR1cykpIHtcbiAgICAgICAgdXBkYXRlKHtcbiAgICAgICAgICBhY3Rpb246IGFjdGlvbiA9PT0gQUNUSU9OUy5DTE9TRSA/IEFDVElPTlMuQ0xPU0UgOiBuZXh0QWN0aW9uLFxuICAgICAgICAgIGluZGV4OiBzdGVwSW5kZXgsXG4gICAgICAgICAgbGlmZWN5Y2xlOiBMSUZFQ1lDTEUuSU5JVFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKCFjb250cm9sbGVkICYmIHN0YXR1cyA9PT0gU1RBVFVTLlJVTk5JTkcgJiYgaW5kZXggPT09IDAgJiYgIXRhcmdldCkge1xuICAgICAgdGhpcy5zdG9yZS51cGRhdGUoeyBpbmRleDogaW5kZXggKyAxIH0pO1xuICAgICAgdGhpcy5jYWxsYmFjayh7XG4gICAgICAgIC4uLnRoaXMuc3RhdGUsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5UQVJHRVRfTk9UX0ZPVU5ELFxuICAgICAgICBzdGVwXG4gICAgICB9KTtcbiAgICB9XG4gICAgY29uc3QgY2FsbGJhY2tEYXRhID0ge1xuICAgICAgLi4udGhpcy5zdGF0ZSxcbiAgICAgIGluZGV4LFxuICAgICAgc3RlcFxuICAgIH07XG4gICAgY29uc3QgaXNBZnRlckFjdGlvbiA9IGNoYW5nZWQoXCJhY3Rpb25cIiwgW1xuICAgICAgQUNUSU9OUy5ORVhULFxuICAgICAgQUNUSU9OUy5QUkVWLFxuICAgICAgQUNUSU9OUy5TS0lQLFxuICAgICAgQUNUSU9OUy5DTE9TRVxuICAgIF0pO1xuICAgIGlmIChpc0FmdGVyQWN0aW9uICYmIGNoYW5nZWQoXCJzdGF0dXNcIiwgU1RBVFVTLlBBVVNFRCkpIHtcbiAgICAgIGNvbnN0IHByZXZpb3VzU3RlcCA9IGdldE1lcmdlZFN0ZXAodGhpcy5wcm9wcywgc3RlcHNbcHJldmlvdXNTdGF0ZS5pbmRleF0pO1xuICAgICAgdGhpcy5jYWxsYmFjayh7XG4gICAgICAgIC4uLmNhbGxiYWNrRGF0YSxcbiAgICAgICAgaW5kZXg6IHByZXZpb3VzU3RhdGUuaW5kZXgsXG4gICAgICAgIGxpZmVjeWNsZTogTElGRUNZQ0xFLkNPTVBMRVRFLFxuICAgICAgICBzdGVwOiBwcmV2aW91c1N0ZXAsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5TVEVQX0FGVEVSXG4gICAgICB9KTtcbiAgICB9XG4gICAgaWYgKGNoYW5nZWQoXCJzdGF0dXNcIiwgW1NUQVRVUy5GSU5JU0hFRCwgU1RBVFVTLlNLSVBQRURdKSkge1xuICAgICAgY29uc3QgcHJldmlvdXNTdGVwID0gZ2V0TWVyZ2VkU3RlcCh0aGlzLnByb3BzLCBzdGVwc1twcmV2aW91c1N0YXRlLmluZGV4XSk7XG4gICAgICBpZiAoIWNvbnRyb2xsZWQpIHtcbiAgICAgICAgdGhpcy5jYWxsYmFjayh7XG4gICAgICAgICAgLi4uY2FsbGJhY2tEYXRhLFxuICAgICAgICAgIGluZGV4OiBwcmV2aW91c1N0YXRlLmluZGV4LFxuICAgICAgICAgIGxpZmVjeWNsZTogTElGRUNZQ0xFLkNPTVBMRVRFLFxuICAgICAgICAgIHN0ZXA6IHByZXZpb3VzU3RlcCxcbiAgICAgICAgICB0eXBlOiBFVkVOVFMuU1RFUF9BRlRFUlxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIHRoaXMuY2FsbGJhY2soe1xuICAgICAgICAuLi5jYWxsYmFja0RhdGEsXG4gICAgICAgIHR5cGU6IEVWRU5UUy5UT1VSX0VORCxcbiAgICAgICAgLy8gUmV0dXJuIHRoZSBsYXN0IHN0ZXAgd2hlbiB0aGUgdG91ciBpcyBmaW5pc2hlZFxuICAgICAgICBzdGVwOiBwcmV2aW91c1N0ZXAsXG4gICAgICAgIGluZGV4OiBwcmV2aW91c1N0YXRlLmluZGV4XG4gICAgICB9KTtcbiAgICAgIHJlc2V0KCk7XG4gICAgfSBlbHNlIGlmIChjaGFuZ2VkRnJvbShcInN0YXR1c1wiLCBbU1RBVFVTLklETEUsIFNUQVRVUy5SRUFEWV0sIFNUQVRVUy5SVU5OSU5HKSkge1xuICAgICAgdGhpcy5jYWxsYmFjayh7XG4gICAgICAgIC4uLmNhbGxiYWNrRGF0YSxcbiAgICAgICAgdHlwZTogRVZFTlRTLlRPVVJfU1RBUlRcbiAgICAgIH0pO1xuICAgIH0gZWxzZSBpZiAoY2hhbmdlZChcInN0YXR1c1wiKSB8fCBjaGFuZ2VkKFwiYWN0aW9uXCIsIEFDVElPTlMuUkVTRVQpKSB7XG4gICAgICB0aGlzLmNhbGxiYWNrKHtcbiAgICAgICAgLi4uY2FsbGJhY2tEYXRhLFxuICAgICAgICB0eXBlOiBFVkVOVFMuVE9VUl9TVEFUVVNcbiAgICAgIH0pO1xuICAgIH1cbiAgICB0aGlzLnNjcm9sbFRvU3RlcChwcmV2aW91c1N0YXRlKTtcbiAgfVxuICBjb21wb25lbnRXaWxsVW5tb3VudCgpIHtcbiAgICBjb25zdCB7IGRpc2FibGVDbG9zZU9uRXNjIH0gPSB0aGlzLnByb3BzO1xuICAgIGlmICghZGlzYWJsZUNsb3NlT25Fc2MpIHtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgdGhpcy5oYW5kbGVLZXlib2FyZCk7XG4gICAgfVxuICB9XG4gIHNjcm9sbFRvU3RlcChwcmV2aW91c1N0YXRlKSB7XG4gICAgY29uc3QgeyBpbmRleCwgbGlmZWN5Y2xlLCBzdGF0dXMgfSA9IHRoaXMuc3RhdGU7XG4gICAgY29uc3Qge1xuICAgICAgZGVidWcsXG4gICAgICBkaXNhYmxlU2Nyb2xsUGFyZW50Rml4ID0gZmFsc2UsXG4gICAgICBzY3JvbGxEdXJhdGlvbixcbiAgICAgIHNjcm9sbE9mZnNldCA9IDIwLFxuICAgICAgc2Nyb2xsVG9GaXJzdFN0ZXAgPSBmYWxzZSxcbiAgICAgIHN0ZXBzXG4gICAgfSA9IHRoaXMucHJvcHM7XG4gICAgY29uc3Qgc3RlcCA9IGdldE1lcmdlZFN0ZXAodGhpcy5wcm9wcywgc3RlcHNbaW5kZXhdKTtcbiAgICBjb25zdCB0YXJnZXQgPSBnZXRFbGVtZW50KHN0ZXAudGFyZ2V0KTtcbiAgICBjb25zdCBzaG91bGRTY3JvbGxUb1N0ZXAgPSBzaG91bGRTY3JvbGwoe1xuICAgICAgaXNGaXJzdFN0ZXA6IGluZGV4ID09PSAwLFxuICAgICAgbGlmZWN5Y2xlLFxuICAgICAgcHJldmlvdXNMaWZlY3ljbGU6IHByZXZpb3VzU3RhdGUubGlmZWN5Y2xlLFxuICAgICAgc2Nyb2xsVG9GaXJzdFN0ZXAsXG4gICAgICBzdGVwLFxuICAgICAgdGFyZ2V0XG4gICAgfSk7XG4gICAgaWYgKHN0YXR1cyA9PT0gU1RBVFVTLlJVTk5JTkcgJiYgc2hvdWxkU2Nyb2xsVG9TdGVwKSB7XG4gICAgICBjb25zdCBoYXNDdXN0b21TY3JvbGwgPSBoYXNDdXN0b21TY3JvbGxQYXJlbnQodGFyZ2V0LCBkaXNhYmxlU2Nyb2xsUGFyZW50Rml4KTtcbiAgICAgIGNvbnN0IHNjcm9sbFBhcmVudDIgPSBnZXRTY3JvbGxQYXJlbnQodGFyZ2V0LCBkaXNhYmxlU2Nyb2xsUGFyZW50Rml4KTtcbiAgICAgIGxldCBzY3JvbGxZID0gTWF0aC5mbG9vcihnZXRTY3JvbGxUbyh0YXJnZXQsIHNjcm9sbE9mZnNldCwgZGlzYWJsZVNjcm9sbFBhcmVudEZpeCkpIHx8IDA7XG4gICAgICBsb2coe1xuICAgICAgICB0aXRsZTogXCJzY3JvbGxUb1N0ZXBcIixcbiAgICAgICAgZGF0YTogW1xuICAgICAgICAgIHsga2V5OiBcImluZGV4XCIsIHZhbHVlOiBpbmRleCB9LFxuICAgICAgICAgIHsga2V5OiBcImxpZmVjeWNsZVwiLCB2YWx1ZTogbGlmZWN5Y2xlIH0sXG4gICAgICAgICAgeyBrZXk6IFwic3RhdHVzXCIsIHZhbHVlOiBzdGF0dXMgfVxuICAgICAgICBdLFxuICAgICAgICBkZWJ1Z1xuICAgICAgfSk7XG4gICAgICBjb25zdCBiZWFjb25Qb3BwZXIgPSB0aGlzLnN0b3JlLmdldFBvcHBlcihcImJlYWNvblwiKTtcbiAgICAgIGNvbnN0IHRvb2x0aXBQb3BwZXIgPSB0aGlzLnN0b3JlLmdldFBvcHBlcihcInRvb2x0aXBcIik7XG4gICAgICBpZiAobGlmZWN5Y2xlID09PSBMSUZFQ1lDTEUuQkVBQ09OICYmIGJlYWNvblBvcHBlcikge1xuICAgICAgICBjb25zdCB7IG9mZnNldHMsIHBsYWNlbWVudCB9ID0gYmVhY29uUG9wcGVyO1xuICAgICAgICBpZiAoIVtcImJvdHRvbVwiXS5pbmNsdWRlcyhwbGFjZW1lbnQpICYmICFoYXNDdXN0b21TY3JvbGwpIHtcbiAgICAgICAgICBzY3JvbGxZID0gTWF0aC5mbG9vcihvZmZzZXRzLnBvcHBlci50b3AgLSBzY3JvbGxPZmZzZXQpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGxpZmVjeWNsZSA9PT0gTElGRUNZQ0xFLlRPT0xUSVAgJiYgdG9vbHRpcFBvcHBlcikge1xuICAgICAgICBjb25zdCB7IGZsaXBwZWQsIG9mZnNldHMsIHBsYWNlbWVudCB9ID0gdG9vbHRpcFBvcHBlcjtcbiAgICAgICAgaWYgKFtcInRvcFwiLCBcInJpZ2h0XCIsIFwibGVmdFwiXS5pbmNsdWRlcyhwbGFjZW1lbnQpICYmICFmbGlwcGVkICYmICFoYXNDdXN0b21TY3JvbGwpIHtcbiAgICAgICAgICBzY3JvbGxZID0gTWF0aC5mbG9vcihvZmZzZXRzLnBvcHBlci50b3AgLSBzY3JvbGxPZmZzZXQpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNjcm9sbFkgLT0gc3RlcC5zcG90bGlnaHRQYWRkaW5nO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBzY3JvbGxZID0gc2Nyb2xsWSA+PSAwID8gc2Nyb2xsWSA6IDA7XG4gICAgICBpZiAoc3RhdHVzID09PSBTVEFUVVMuUlVOTklORykge1xuICAgICAgICBzY3JvbGxUbyhzY3JvbGxZLCB7IGVsZW1lbnQ6IHNjcm9sbFBhcmVudDIsIGR1cmF0aW9uOiBzY3JvbGxEdXJhdGlvbiB9KS50aGVuKFxuICAgICAgICAgICgpID0+IHtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICAgIChfYSA9IHRoaXMuc3RvcmUuZ2V0UG9wcGVyKFwidG9vbHRpcFwiKSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmluc3RhbmNlLnVwZGF0ZSgpO1xuICAgICAgICAgICAgfSwgMTApO1xuICAgICAgICAgIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmVuZGVyKCkge1xuICAgIGlmICghY2FuVXNlRE9NKCkpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBjb25zdCB7IGluZGV4LCBsaWZlY3ljbGUsIHN0YXR1cyB9ID0gdGhpcy5zdGF0ZTtcbiAgICBjb25zdCB7XG4gICAgICBjb250aW51b3VzID0gZmFsc2UsXG4gICAgICBkZWJ1ZyA9IGZhbHNlLFxuICAgICAgbm9uY2UsXG4gICAgICBzY3JvbGxUb0ZpcnN0U3RlcCA9IGZhbHNlLFxuICAgICAgc3RlcHNcbiAgICB9ID0gdGhpcy5wcm9wcztcbiAgICBjb25zdCBpc1J1bm5pbmcgPSBzdGF0dXMgPT09IFNUQVRVUy5SVU5OSU5HO1xuICAgIGNvbnN0IGNvbnRlbnQgPSB7fTtcbiAgICBpZiAoaXNSdW5uaW5nICYmIHN0ZXBzW2luZGV4XSkge1xuICAgICAgY29uc3Qgc3RlcCA9IGdldE1lcmdlZFN0ZXAodGhpcy5wcm9wcywgc3RlcHNbaW5kZXhdKTtcbiAgICAgIGNvbnRlbnQuc3RlcCA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDkuY3JlYXRlRWxlbWVudChcbiAgICAgICAgSm95cmlkZVN0ZXAsXG4gICAgICAgIHtcbiAgICAgICAgICAuLi50aGlzLnN0YXRlLFxuICAgICAgICAgIGNhbGxiYWNrOiB0aGlzLmNhbGxiYWNrLFxuICAgICAgICAgIGNvbnRpbnVvdXMsXG4gICAgICAgICAgZGVidWcsXG4gICAgICAgICAgaGVscGVyczogdGhpcy5oZWxwZXJzLFxuICAgICAgICAgIG5vbmNlLFxuICAgICAgICAgIHNob3VsZFNjcm9sbDogIXN0ZXAuZGlzYWJsZVNjcm9sbGluZyAmJiAoaW5kZXggIT09IDAgfHwgc2Nyb2xsVG9GaXJzdFN0ZXApLFxuICAgICAgICAgIHN0ZXAsXG4gICAgICAgICAgc3RvcmU6IHRoaXMuc3RvcmVcbiAgICAgICAgfVxuICAgICAgKTtcbiAgICAgIGNvbnRlbnQub3ZlcmxheSA9IC8qIEBfX1BVUkVfXyAqLyBSZWFjdDkuY3JlYXRlRWxlbWVudChKb3lyaWRlUG9ydGFsLCB7IGlkOiBcInJlYWN0LWpveXJpZGUtcG9ydGFsXCIgfSwgLyogQF9fUFVSRV9fICovIFJlYWN0OS5jcmVhdGVFbGVtZW50KFxuICAgICAgICBKb3lyaWRlT3ZlcmxheSxcbiAgICAgICAge1xuICAgICAgICAgIC4uLnN0ZXAsXG4gICAgICAgICAgY29udGludW91cyxcbiAgICAgICAgICBkZWJ1ZyxcbiAgICAgICAgICBsaWZlY3ljbGUsXG4gICAgICAgICAgb25DbGlja092ZXJsYXk6IHRoaXMuaGFuZGxlQ2xpY2tPdmVybGF5XG4gICAgICAgIH1cbiAgICAgICkpO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0OS5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgY2xhc3NOYW1lOiBcInJlYWN0LWpveXJpZGVcIiB9LCBjb250ZW50LnN0ZXAsIGNvbnRlbnQub3ZlcmxheSk7XG4gIH1cbn07XG5fX3B1YmxpY0ZpZWxkKEpveXJpZGUsIFwiZGVmYXVsdFByb3BzXCIsIGRlZmF1bHRQcm9wcyk7XG52YXIgY29tcG9uZW50c19kZWZhdWx0ID0gSm95cmlkZTtcbmV4cG9ydCB7XG4gIEFDVElPTlMsXG4gIEVWRU5UUyxcbiAgTElGRUNZQ0xFLFxuICBPUklHSU4sXG4gIFNUQVRVUyxcbiAgY29tcG9uZW50c19kZWZhdWx0IGFzIGRlZmF1bHRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-joyride@2.9.3_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-joyride/dist/index.mjs\n");

/***/ })

};
;