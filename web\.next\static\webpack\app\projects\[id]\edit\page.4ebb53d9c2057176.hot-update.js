"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1776,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1777,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1790,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1798,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1793,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1822,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1834,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1851,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1860,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1881,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1939,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1817,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1967,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1966,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1961,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2012,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2011,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2018,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2024,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2030,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2039,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2050,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2049,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2074,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2086,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2112,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2120,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2121,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2115,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2136,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2130,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2154,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2009,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1989,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1983,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2188,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"6IFNDUogin6FzhDZLng4XiOccZ8=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});