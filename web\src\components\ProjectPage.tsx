"use client";
import <PERSON><PERSON>iewer from "@/components/ModelViewer";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  createComment,
  createCommentReply,
  deleteComment,
  deleteCommentReply,
  getWorkspaceComments,
  resolveComment,
  updateComment,
  updateCommentReply,
} from "@/lib/actions/comment.actions";
import { Comment } from "@/types";
import { HelpCircle, MessageCircle, MoreVertical, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState, useMemo, use<PERSON><PERSON>back } from "react";
import io from "socket.io-client";
import { toast } from "sonner";
import * as THREE from "three";
import { OnboardingTrigger, useOnboarding } from "./Onboarding";

interface ProjectPageProps {
  project: any;
  user: any;
}

// Performance Optimization: Memoized ModelViewer to prevent unnecessary re-renders
const MemoizedModelViewer = React.memo(ModelViewer);

export default function ProjectPage({ project, user }: ProjectPageProps) {
  const userId = user.id;
  const { startOnboarding } = useOnboarding();
  const [isCommentsMode, setIsCommentsMode] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [showCommentsList, setShowCommentsList] = useState(false);
  const [loadingComments, setLoadingComments] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const projectId = project._id;
  const modelPath = project.modelPath;
  const [commentLoading, setCommentLoading] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingReplyId, setEditingReplyId] = useState<string | null>(null);
  const [editingCommentText, setEditingCommentText] = useState<string>("");
  const [editCommentLoading, setEditCommentLoading] = useState(false);
  const [socket, setSocket] = useState<ReturnType<typeof io> | null>(null);
  const [activeCommentsTab, setActiveCommentsTab] = useState("unresolved");
  const [connectedUsers, setConnectedUsers] = useState<any[]>([]);

  // Performance Optimization: Memoized filtered comments to prevent re-filtering on every render
  const unresolvedComments = useMemo(
    () => comments.filter((comment) => !comment.resolved),
    [comments]
  );

  const resolvedComments = useMemo(
    () => comments.filter((comment) => comment.resolved),
    [comments]
  );

  // Performance Optimization: Memoized socket emit function to prevent unnecessary re-renders
  const emitSocketMessage = useCallback(
    (type: string, data: any) => {
      if (socket) {
        socket.emit("message", {
          type,
          workspaceId: projectId,
          ...data,
        });
      }
    },
    [socket, projectId]
  );

  useEffect(() => {
    async function fetchComments() {
      setLoadingComments(true);
      setError(null);
      try {
        const backendComments = await getWorkspaceComments({
          workspaceId: projectId,
          userId,
        });
        // Map backend comments to frontend Comment type
        const mapped = backendComments.map((c: any) => ({
          id: c._id,
          text: c.commentData,
          position: c.location,
          author: {
            name: c.userId?.name || "Unknown",
            image: c.userId?.image || "/icons/Gold user.svg",
          },
          createdAt: c.createdAt,
          reactions: c.reactions || {},
          replies: (c.replies || []).map((r: any) => ({
            id: r._id,
            author: {
              name: r.userId?.name || "Unknown",
              image: r.userId?.image || "/icons/Gold user.svg",
            },
            replyData: r.replyData,
            createdAt: r.createdAt,
          })),
          resolved: c.resolved || false,
          importance: c.importance || "low",
        }));
        setComments(mapped);
      } catch (err: any) {
        setError(err?.message || "Failed to fetch comments");
      } finally {
        setLoadingComments(false);
      }
    }
    if (userId && projectId) fetchComments();
  }, [userId, projectId]);

  useEffect(() => {
    // Establish WebSocket connection
    const newSocket = io(
      (process.env.WEB_SOCKET_URL as string) || "http://localhost:8000"
    );
    setSocket(newSocket);
    return () => {
      newSocket.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!socket) return;
    const handleMessage = (msg: any) => {
      if (msg.workspaceId !== projectId) return;
      if (msg.type === "comment") {
        setComments((prev) => [...prev, msg.comment]);
        setShowCommentsList(true);
      }
      if (msg.type === "resolveComment") {
        setComments(msg.updatedComments);
      }
      if (msg.type === "replyToComment") {
        setComments(msg.updatedComments);
        setShowCommentsList(true);
      }
      if (msg.type === "deleteComment") {
        setComments(msg.updatedComments);
      }
      if (msg.type === "editComment") {
        setComments(msg.updatedComments);
      }
      if (msg.type === "editReply") {
        setComments(msg.updatedComments);
      }
      if (msg.type === "deleteReply") {
        setComments(msg.updatedComments);
      }
    };
    socket.on("message", handleMessage);
    return () => {
      socket.off("message", handleMessage);
    };
  }, [socket, projectId]);

  useEffect(() => {
    if (!socket) return;
    // Emit userJoined when component mounts
    socket.emit("userJoined", { workspaceId: projectId, user });
    // Request the current list of users
    socket.emit("getWorkspaceUsers", { workspaceId: projectId });
    // Listen for workspaceUsers updates
    socket.on("workspaceUsers", (users: any[]) => {
      setConnectedUsers(users);
    });
    // Cleanup: emit userLeft and remove listener
    return () => {
      socket.emit("userLeft", { workspaceId: projectId, user });
      socket.off("workspaceUsers");
    };
  }, [socket, projectId, user]);

  // Start project page onboarding for first-time visitors
  useEffect(() => {
    const hasCompletedProjectOnboarding = localStorage.getItem(
      "project-onboarding-completed"
    );
    if (!hasCompletedProjectOnboarding) {
      setTimeout(() => {
        startOnboarding("project");
      }, 1500);
    }
  }, [startOnboarding]);

  if (!modelPath) {
    return (
      <div className="min-h-screen h-screen bg-[#18191E] flex items-center justify-center text-[#FDE9CE]">
        No model found
      </div>
    );
  }

  // Handler to add a new comment to state
  const handleCommentCreated = (backendComment: any) => {
    const newComment: Comment = {
      id: backendComment._id,
      text: backendComment.commentData,
      position: backendComment.location,
      author: {
        name: user.name || "Unknown",
        image: user.image || "/icons/Gold user.svg",
      },
      createdAt: backendComment.createdAt,
      reactions: backendComment.reactions || {},
      replies: (backendComment.replies || []).map((r: any) => ({
        id: r._id,
        author: {
          name: user.name || "Unknown",
          image: user.image || "/icons/Gold user.svg",
        },
        replyData: r.replyData,
        createdAt: r.createdAt,
      })),
      resolved: backendComment.resolved || false,
      importance: backendComment.importance || "low",
    };
    // setComments((prev) => [...prev, newComment]);
    setShowCommentsList(true);
    if (socket) {
      socket.emit(
        "message",
        {
          type: "comment",
          workspaceId: projectId,
          comment: newComment,
        },
        { broadcast: true }
      );
    }
  };

  // Handler to create a new comment (moved from ModelViewer)
  const handleCreateComment = async (
    text: string,
    importance: string,
    inputPosition: { x: number; y: number } | null,
    position: THREE.Vector3 | null
  ) => {
    if (!user.id || !projectId || !position) {
      toast.error("Missing user, workspace, or position context for comment.");
      return;
    }
    setCommentLoading(true);
    try {
      const backendComment = await createComment({
        workspaceId: projectId,
        userId: user.id,
        commentData: text,
        importance,
        location: {
          x: position.x,
          y: position.y,
          z: position.z,
        },
      });
      toast.success("Comment added!");
      handleCommentCreated(backendComment);
    } catch (err: any) {
      toast.error(err?.message || "Failed to add comment. Please try again.");
    } finally {
      setCommentLoading(false);
    }
  };

  const handleReplySubmit = async (text: string, commentId: string) => {
    const tempId = `temp-${Date.now()}-${Math.random()}`;

    // Immediately update local state for instant UI feedback
    setComments((prevComments) => {
      const updated = prevComments.map((comment) =>
        comment.id === commentId
          ? {
              ...comment,
              replies: [
                ...comment.replies,
                {
                  id: tempId,
                  author: {
                    name: user.name || "Unknown",
                    image: user.image || "/icons/Gold user.svg",
                  },
                  replyData: text,
                  createdAt: new Date(),
                },
              ],
            }
          : comment
      );

      return updated;
    });

    try {
      const backendReplies = await createCommentReply({
        commentId,
        userId,
        replyData: text,
      });

      // Get the newly created reply (last one in the array)
      const newReply = backendReplies[backendReplies.length - 1];

      // Replace temp reply with real reply from backend
      setComments((prevComments) => {
        const updated = prevComments.map((comment) =>
          comment.id === commentId
            ? {
                ...comment,
                replies: comment.replies.map((reply) =>
                  reply.id === tempId
                    ? {
                        id: newReply._id,
                        author: {
                          name: user.name || "Unknown",
                          image: user.image || "/icons/Gold user.svg",
                        },
                        replyData: newReply.replyData,
                        createdAt: newReply.createdAt,
                      }
                    : reply
                ),
              }
            : comment
        );

        if (socket) {
          socket.emit("message", {
            type: "replyToComment",
            updatedComments: updated,
            workspaceId: projectId,
          });
        }

        return updated;
      });

      toast.success("Reply added!");
    } catch (err: any) {
      // Remove optimistic reply on error
      setComments((prevComments) =>
        prevComments.map((comment) =>
          comment.id === commentId
            ? {
                ...comment,
                replies: comment.replies.filter((reply) => reply.id !== tempId),
              }
            : comment
        )
      );
      toast.error(err?.message || "Failed to add reply");
    }
  };

  const toggleCommentsMode = () => {
    setIsCommentsMode(!isCommentsMode);
    if (!isCommentsMode) {
      setShowCommentsList(true);
    } else {
      setShowCommentsList(false);
    }
  };

  const handleResolveComment = async (commentId: string) => {
    // Immediately update local state for instant UI feedback
    setComments((prevComments) => {
      const updated = prevComments.map((comment) =>
        comment.id === commentId ? { ...comment, resolved: true } : comment
      );
      // Emit socket event with updated comments
      if (socket) {
        socket.emit("message", {
          type: "resolveComment",
          updatedComments: updated,
          workspaceId: projectId,
        });
      }
      return updated;
    });
    try {
      await resolveComment({ commentId, userId });
      toast.success("Comment resolved!");
    } catch (err: any) {
      toast.error(err?.message || "Failed to resolve comment");
    }
  };

  const handleUnresolveComment = async (commentId: string) => {
    // Immediately update local state for instant UI feedback
    setComments((prevComments) => {
      const updated = prevComments.map((comment) =>
        comment.id === commentId ? { ...comment, resolved: false } : comment
      );
      // Emit socket event with updated comments
      if (socket) {
        socket.emit("message", {
          type: "resolveComment",
          updatedComments: updated,
          workspaceId: projectId,
        });
      }
      return updated;
    });
    try {
      await resolveComment({ commentId, userId });
      toast.success("Comment unresolved!");
    } catch (err: any) {
      toast.error(err?.message || "Failed to unresolve comment");
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment({ commentId, userId });
      const updatedComments = comments.filter(
        (comment) => comment.id !== commentId
      );
      setComments(updatedComments);
      if (socket) {
        socket.emit("message", {
          type: "deleteComment",
          updatedComments,
          workspaceId: projectId,
        });
      }
      toast.success("Comment deleted!");
    } catch (err: any) {
      toast.error(err?.message || "Failed to delete comment");
    }
  };

  const handleDeleteReply = async (commentId: string, replyId: string) => {
    try {
      await deleteCommentReply({ commentId, replyId, userId });

      const updatedComments = comments.map((comment) =>
        comment.id === commentId
          ? {
              ...comment,
              replies: comment.replies.filter((r: any) => r.id !== replyId),
            }
          : comment
      );

      setComments(updatedComments);

      if (socket) {
        socket.emit("message", {
          type: "deleteReply",
          updatedComments,
          workspaceId: projectId,
        });
      }
      toast.success("Reply deleted");
    } catch (err: any) {
      toast.error(err?.message || "Failed to delete reply");
    }
  };

  const handleEditReply = async (
    commentId: string,
    replyId: string,
    newReply: string
  ) => {
    // Immediately update local state for instant UI feedback
    setComments((prevComments) => {
      const updated = prevComments.map((comment) =>
        comment.id === commentId
          ? {
              ...comment,
              replies: comment.replies.map((r) =>
                r.id === replyId ? { ...r, replyData: newReply } : r
              ),
            }
          : comment
      );
      if (socket) {
        socket.emit("message", {
          type: "editReply",
          updatedComments: updated,
          workspaceId: projectId,
        });
      }
      return updated;
    });
    try {
      await updateCommentReply({
        commentId,
        replyId,
        userId: user.id,
        replyData: newReply,
      });
      toast.success("Reply updated!");
    } catch (err: any) {
      toast.error(err?.message || "Failed to update reply");
    }
  };

  const handleEditComment = async (commentId: string, newText: string) => {
    setEditCommentLoading(true);
    // Immediately update local state for instant UI feedback
    setComments((prevComments) => {
      const updated = prevComments.map((comment) =>
        comment.id === commentId ? { ...comment, text: newText } : comment
      );
      if (socket) {
        socket.emit("message", {
          type: "editComment",
          updatedComments: updated,
          workspaceId: projectId,
        });
      }
      return updated;
    });
    try {
      await updateComment({ commentId, userId: user.id, commentData: newText });
      toast.success("Comment updated!");
      setEditingCommentId(null);
    } catch (err: any) {
      toast.error(err?.message || "Failed to update comment");
    } finally {
      setEditCommentLoading(false);
    }
  };

  return (
    <div className="h-screen w-screen relative overflow-hidden">
      <div className="absolute inset-0 model-viewer">
        <MemoizedModelViewer
          modelPath={decodeURIComponent(modelPath)}
          isProjectPage
          userId={userId}
          user={user}
          workspaceId={projectId as string}
          projectId={projectId as string}
          isCommentsMode={isCommentsMode}
          onCommentSubmit={(
            backendCommentOrText: any,
            importance?: string,
            inputPosition?: { x: number; y: number } | null,
            position?: THREE.Vector3 | null
          ) => {
            // If called with backendComment (old style), just add it
            if (
              typeof backendCommentOrText === "object" &&
              backendCommentOrText._id
            ) {
              handleCommentCreated(backendCommentOrText);
            } else {
              // Called with (text, importance, inputPosition, position)
              handleCreateComment(
                backendCommentOrText,
                importance!,
                inputPosition!,
                position!
              );
            }
          }}
          onReplySubmit={handleReplySubmit}
          comments={comments}
          commentLoading={commentLoading}
          onEditReply={handleEditReply}
          onEditComment={handleEditComment}
        />
      </div>

      <div className="fixed top-6 left-6 z-10 logo">
        <Link href={"/"}>
          <div className="size-12">
            <Image
              src="/images/CSS Logo.png"
              alt="Logo"
              width={1024}
              height={780}
              className="w-full h-full object-contain"
            />
          </div>
        </Link>
      </div>

      <div className="fixed top-6 right-6 z-10 flex items-center gap-3">
        {/* Presence Avatars */}
        <TooltipProvider>
          <div className="flex -space-x-2">
            {connectedUsers.map((u) => (
              <Tooltip key={u.id || u._id || u.name}>
                <TooltipTrigger asChild>
                  <div>
                    <Avatar className="h-8 w-8 border-2 border-[#F5C754]">
                      <AvatarImage
                        src={u.image || "/icons/Gold user.svg"}
                        alt={u.name}
                      />
                      <AvatarFallback>{u.name?.[0] || "?"}</AvatarFallback>
                    </Avatar>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <span className="text-xs font-medium">{u.name}</span>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        </TooltipProvider>
        {/* Main user avatar */}
        <Avatar className="size-10 border-2 border-[#F5C754]">
          <AvatarImage src={user.image || "/icons/Gold user.svg"} />
          <AvatarFallback className="gold_icon_gradient">
            {user.name?.[0] || "CN"}
          </AvatarFallback>
        </Avatar>

        {/* Onboarding Trigger */}
        <OnboardingTrigger
          page="project"
          className="bg-[#F5C754] text-[#18191E] hover:bg-[#F5C754]/90"
          variant="default"
        >
          <HelpCircle className="w-4 h-4" />
        </OnboardingTrigger>
      </div>

      <div className="fixed bottom-6 right-6 z-[999999] flex flex-col items-end gap-4 comments-controls">
        {showCommentsList && (
          <div className="bg-gradient-to-br from-[#47474B] via-[#635A4F] to-[#48484D] rounded-lg  p-4 w-80 mb-2 comments-panel">
            <Tabs
              value={activeCommentsTab}
              onValueChange={setActiveCommentsTab}
            >
              <div className="flex justify-between items-center mb-4">
                <TabsList className="bg-[#FDE9CE] text-[#18191E] rounded-lg">
                  <TabsTrigger
                    value="unresolved"
                    className="bg-[#FDE9CE]/10 text-[#18191E]/40 data-[state=active]:bg-[#F5C754] data-[state=active]:text-[#18191E] "
                  >
                    Comments
                  </TabsTrigger>
                  <TabsTrigger
                    value="resolved"
                    className="bg-[#FDE9CE]/10 text-[#18191E]/40 data-[state=active]:bg-[#F5C754] data-[state=active]:text-[#18191E]"
                  >
                    Resolved
                  </TabsTrigger>
                </TabsList>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCommentsList(false)}
                  className="text-[#FDE9CE]"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <TabsContent value="unresolved">
                <div className="space-y-4 max-h-[400px] overflow-y-auto custom-scrollbar">
                  {unresolvedComments.length === 0 ? (
                    <div className="text-[#FDE9CE]/80 text-center py-4">
                      No comments yet. Click on the model to add a comment.
                    </div>
                  ) : (
                    unresolvedComments.map((comment) => {
                      const getImportanceIcon = () => {
                        switch (comment.importance) {
                          case "high":
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#FF5757]" />
                            );
                          case "medium":
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#FF9F45]" />
                            );
                          case "low":
                          default:
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#F5C754]" />
                            );
                        }
                      };

                      return (
                        <div
                          key={comment.id}
                          className="bg-gradient-to-r from-[#25262D] via-[#444A5B] to-[#25262C] rounded-lg p-3 hover:bg-[#363643]/80 transition cursor-pointer"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <Image
                              src={comment.author.image}
                              alt={comment.author.name}
                              width={24}
                              height={24}
                              className="rounded-full"
                            />
                            <span className="text-[#FDE9CE] text-sm">
                              {comment.author.name}
                            </span>
                            <div className="ml-1">{getImportanceIcon()}</div>
                            <span className="text-[#FDE9CE]/50 text-xs ml-auto">
                              {new Date(comment.createdAt).toLocaleDateString()}
                            </span>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <button className="ml-2 p-1 rounded hover:bg-zinc-700">
                                  <MoreVertical className="w-4 h-4 text-[#FDE9CE]" />
                                </button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="end"
                                className="bg-[#FDE9CE] z-[999999] text-[#18191E] rounded-lg shadow-lg"
                              >
                                {activeCommentsTab === "unresolved" && (
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleResolveComment(comment.id)
                                    }
                                    className="text-[#18191E] focus:bg-[#DABD99]"
                                  >
                                    Resolve
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteComment(comment.id)
                                  }
                                  className="text-[#18191E] focus:bg-[#DABD99]"
                                >
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          {editingCommentId === comment.id ? (
                            <div className="flex flex-col gap-2 mt-2">
                              <textarea
                                className="w-full bg-white/20 text-[#FDE9CE] rounded p-1 text-sm border border-[#f3e1ca]"
                                value={editingCommentText}
                                onChange={(e) =>
                                  setEditingCommentText(e.target.value)
                                }
                                rows={2}
                                disabled={editCommentLoading}
                              />
                              <div className="flex gap-2">
                                <button
                                  className="px-2 py-1 rounded bg-[#F5C754] text-[#18191E] text-xs"
                                  disabled={
                                    editCommentLoading ||
                                    !editingCommentText.trim()
                                  }
                                  onClick={() =>
                                    handleEditComment(
                                      comment.id,
                                      editingCommentText
                                    )
                                  }
                                >
                                  Save
                                </button>
                                <button
                                  className="px-2 py-1 rounded bg-[#47474B] text-[#FDE9CE] text-xs"
                                  onClick={() => setEditingCommentId(null)}
                                  disabled={editCommentLoading}
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-[#FDE9CE]/80 text-sm">
                              {comment.text}
                            </p>
                          )}

                          {comment.replies && comment.replies.length > 0 && (
                            <div className="mt-3 pt-2 border-t border-[#FDE9CE]/10">
                              <p className="text-[#FDE9CE]/60 text-xs mb-2">
                                {comment.replies.length}{" "}
                                {comment.replies.length === 1
                                  ? "reply"
                                  : "replies"}
                              </p>
                              {comment.replies
                                .slice(0, 2)
                                .map((reply, index) => (
                                  <div
                                    key={reply.id || index}
                                    className="flex items-start gap-2 mb-2"
                                  >
                                    <Avatar className="w-[18px] h-[18px] mt-1">
                                      <AvatarImage
                                        src={reply.author.image}
                                        alt={reply.author.name}
                                      />
                                      <AvatarFallback className="text-[8px]">
                                        {reply.author.name.charAt(0)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="flex items-center gap-1">
                                        <span className="text-[#FDE9CE] text-xs font-medium">
                                          {reply.author.name}
                                        </span>
                                        <span className="text-[#FDE9CE]/40 text-xs">
                                          {new Date(
                                            reply.createdAt
                                          ).toLocaleDateString()}
                                        </span>
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <button className="ml-2 p-1 rounded hover:bg-zinc-700">
                                              <MoreVertical className="w-4 h-4 text-[#FDE9CE]" />
                                            </button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent
                                            className="bg-[#FDE9CE] z-[999999] text-[#18191E] rounded-lg shadow-lg"
                                            align="end"
                                          >
                                            <DropdownMenuItem
                                              className="text-[#18191E] focus:bg-[#DABD99] "
                                              onClick={() =>
                                                handleDeleteReply(
                                                  comment.id,
                                                  reply.id
                                                )
                                              }
                                            >
                                              Delete
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                      <p className="text-[#FDE9CE]/70 text-xs">
                                        {reply.replyData}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              {comment.replies.length > 2 && (
                                <p className="text-[#F5C754] text-xs cursor-pointer hover:underline">
                                  View all replies
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              </TabsContent>
              <TabsContent value="resolved">
                <div className="space-y-4 max-h-[400px] overflow-y-auto custom-scrollbar">
                  {resolvedComments.length === 0 ? (
                    <div className="text-[#FDE9CE]/80 text-center py-4">
                      No resolved comments yet.
                    </div>
                  ) : (
                    resolvedComments.map((comment) => {
                      const getImportanceIcon = () => {
                        switch (comment.importance) {
                          case "high":
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#FF5757]" />
                            );
                          case "medium":
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#FF9F45]" />
                            );
                          case "low":
                          default:
                            return (
                              <div className="w-3 h-3 rounded-full bg-[#F5C754]" />
                            );
                        }
                      };

                      return (
                        <div
                          key={comment.id}
                          className="bg-gradient-to-r from-[#25262D] via-[#444A5B] to-[#25262C] rounded-lg p-3 hover:bg-[#363643]/80 transition cursor-pointer"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <Image
                              src={comment.author.image}
                              alt={comment.author.name}
                              width={24}
                              height={24}
                              className="rounded-full"
                            />
                            <span className="text-[#FDE9CE] text-sm">
                              {comment.author.name}
                            </span>
                            <div className="ml-1">{getImportanceIcon()}</div>
                            <span className="text-[#FDE9CE]/50 text-xs ml-auto">
                              {new Date(comment.createdAt).toLocaleDateString()}
                            </span>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <button className="ml-2 p-1 rounded hover:bg-zinc-700">
                                  <MoreVertical className="w-4 h-4 text-[#FDE9CE]" />
                                </button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="end"
                                className="bg-[#FDE9CE] z-[999999] text-[#18191E] rounded-lg shadow-lg"
                              >
                                {activeCommentsTab === "resolved" && (
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleUnresolveComment(comment.id)
                                    }
                                    className="text-[#18191E] focus:bg-[#DABD99]"
                                  >
                                    Unresolve
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteComment(comment.id)
                                  }
                                  className="text-[#18191E] focus:bg-[#DABD99]"
                                >
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          {editingCommentId === comment.id ? (
                            <div className="flex flex-col gap-2 mt-2">
                              <textarea
                                className="w-full bg-white/20 text-[#FDE9CE] rounded p-1 text-sm border border-[#f3e1ca]"
                                value={editingCommentText}
                                onChange={(e) =>
                                  setEditingCommentText(e.target.value)
                                }
                                rows={2}
                                disabled={editCommentLoading}
                              />
                              <div className="flex gap-2">
                                <button
                                  className="px-2 py-1 rounded bg-[#F5C754] text-[#18191E] text-xs"
                                  disabled={
                                    editCommentLoading ||
                                    !editingCommentText.trim()
                                  }
                                  onClick={() =>
                                    handleEditComment(
                                      comment.id,
                                      editingCommentText
                                    )
                                  }
                                >
                                  Save
                                </button>
                                <button
                                  className="px-2 py-1 rounded bg-[#47474B] text-[#FDE9CE] text-xs"
                                  onClick={() => setEditingCommentId(null)}
                                  disabled={editCommentLoading}
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-[#FDE9CE]/80 text-sm">
                              {comment.text}
                            </p>
                          )}

                          {comment.replies && comment.replies.length > 0 && (
                            <div className="mt-3 pt-2 border-t border-[#FDE9CE]/10">
                              <p className="text-[#FDE9CE]/60 text-xs mb-2">
                                {comment.replies.length}{" "}
                                {comment.replies.length === 1
                                  ? "reply"
                                  : "replies"}
                              </p>
                              {comment.replies
                                .slice(0, 2)
                                .map((reply, index) => (
                                  <div
                                    key={reply.id || index}
                                    className="flex items-start gap-2 mb-2"
                                  >
                                    <Avatar className="w-[18px] h-[18px] mt-1">
                                      <AvatarImage
                                        src={reply.author.image}
                                        alt={reply.author.name}
                                      />
                                      <AvatarFallback className="text-[8px]">
                                        {reply.author.name.charAt(0)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="flex items-center gap-1">
                                        <span className="text-[#FDE9CE] text-xs font-medium">
                                          {reply.author.name}
                                        </span>
                                        <span className="text-[#FDE9CE]/40 text-xs">
                                          {new Date(
                                            reply.createdAt
                                          ).toLocaleDateString()}
                                        </span>
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <button className="ml-2 p-1 rounded hover:bg-zinc-700">
                                              <MoreVertical className="w-4 h-4 text-[#FDE9CE]" />
                                            </button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent
                                            className="bg-[#FDE9CE] z-[999999] text-[#18191E] rounded-lg shadow-lg"
                                            align="end"
                                          >
                                            <DropdownMenuItem
                                              className="text-[#18191E] focus:bg-[#DABD99] "
                                              onClick={() =>
                                                handleDeleteReply(
                                                  comment.id,
                                                  reply.id
                                                )
                                              }
                                            >
                                              Delete
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                      <p className="text-[#FDE9CE]/70 text-xs">
                                        {reply.replyData}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              {comment.replies.length > 2 && (
                                <p className="text-[#F5C754] text-xs cursor-pointer hover:underline">
                                  View all replies
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
        <Button
          className={`gold_icon_gradient p-[1px] w-10 h-10 rounded-full comments-button ${
            isCommentsMode ? "bg-[#F5C754]" : ""
          }`}
          onClick={toggleCommentsMode}
        >
          <MessageCircle className="w-6 h-6" />
        </Button>
      </div>
    </div>
  );
}
