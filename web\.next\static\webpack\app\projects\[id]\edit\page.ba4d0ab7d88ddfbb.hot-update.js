"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // Check if there are unsaved changes\n    const hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Update the config state for unsaved changes tracking\n            setDbSyncedConfig(config);\n            setWorkingConfig(config);\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                    // Set this as the baseline for unsaved changes tracking\n                    setDbSyncedConfig(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1873,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1874,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1887,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1895,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1890,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1919,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1931,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1948,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1957,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1978,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2036,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1914,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2064,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2063,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2058,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2109,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2108,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2147,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2171,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2183,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2209,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2212,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2232,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2233,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2227,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2257,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2251,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2269,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2268,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2086,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2080,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2285,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"mqt6PNy/xN3BXo3wwjlB1Gn3HLU=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});