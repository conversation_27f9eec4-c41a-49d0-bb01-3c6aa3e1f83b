"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-endpoint@3.299.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-endpoint@3.299.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfigValueProvider: () => (/* binding */ createConfigValueProvider)\n/* harmony export */ });\nconst createConfigValueProvider = (configKey, canonicalEndpointParamKey, config) => {\n    const configProvider = async () => {\n        const configValue = config[configKey] ?? config[canonicalEndpointParamKey];\n        if (typeof configValue === \"function\") {\n            return configValue();\n        }\n        return configValue;\n    };\n    if (configKey === \"endpoint\" || canonicalEndpointParamKey === \"endpoint\") {\n        return async () => {\n            const endpoint = await configProvider();\n            if (endpoint && typeof endpoint === \"object\") {\n                if (\"url\" in endpoint) {\n                    return endpoint.url.href;\n                }\n                if (\"hostname\" in endpoint) {\n                    const { protocol, hostname, port, path } = endpoint;\n                    return `${protocol}//${hostname}${port ? \":\" + port : \"\"}${path}`;\n                }\n            }\n            return endpoint;\n        };\n    }\n    return configProvider;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfigValueProvider: () => (/* binding */ createConfigValueProvider)\n/* harmony export */ });\nconst createConfigValueProvider = (configKey, canonicalEndpointParamKey, config) => {\n    const configProvider = async () => {\n        const configValue = config[configKey] ?? config[canonicalEndpointParamKey];\n        if (typeof configValue === \"function\") {\n            return configValue();\n        }\n        return configValue;\n    };\n    if (configKey === \"endpoint\" || canonicalEndpointParamKey === \"endpoint\") {\n        return async () => {\n            const endpoint = await configProvider();\n            if (endpoint && typeof endpoint === \"object\") {\n                if (\"url\" in endpoint) {\n                    return endpoint.url.href;\n                }\n                if (\"hostname\" in endpoint) {\n                    const { protocol, hostname, port, path } = endpoint;\n                    return `${protocol}//${hostname}${port ? \":\" + port : \"\"}${path}`;\n                }\n            }\n            return endpoint;\n        };\n    }\n    return configProvider;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromInstructions: () => (/* binding */ getEndpointFromInstructions),\n/* harmony export */   resolveParams: () => (/* binding */ resolveParams)\n/* harmony export */ });\n/* harmony import */ var _service_customizations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../service-customizations */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js\");\n/* harmony import */ var _createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createConfigValueProvider */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\");\n\n\nconst getEndpointFromInstructions = async (commandInput, instructionsSupplier, clientConfig, context) => {\n    const endpointParams = await resolveParams(commandInput, instructionsSupplier, clientConfig);\n    if (typeof clientConfig.endpointProvider !== \"function\") {\n        throw new Error(\"config.endpointProvider is not set.\");\n    }\n    const endpoint = clientConfig.endpointProvider(endpointParams, context);\n    return endpoint;\n};\nconst resolveParams = async (commandInput, instructionsSupplier, clientConfig) => {\n    const endpointParams = {};\n    const instructions = instructionsSupplier?.getEndpointParameterInstructions?.() || {};\n    for (const [name, instruction] of Object.entries(instructions)) {\n        switch (instruction.type) {\n            case \"staticContextParams\":\n                endpointParams[name] = instruction.value;\n                break;\n            case \"contextParams\":\n                endpointParams[name] = commandInput[instruction.name];\n                break;\n            case \"clientContextParams\":\n            case \"builtInParams\":\n                endpointParams[name] = await (0,_createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__.createConfigValueProvider)(instruction.name, name, clientConfig)();\n                break;\n            default:\n                throw new Error(\"Unrecognized endpoint parameter instruction: \" + JSON.stringify(instruction));\n        }\n    }\n    if (Object.keys(instructions).length === 0) {\n        Object.assign(endpointParams, clientConfig);\n    }\n    if (String(clientConfig.serviceId).toLowerCase() === \"s3\") {\n        await (0,_service_customizations__WEBPACK_IMPORTED_MODULE_0__.resolveParamsForS3)(endpointParams);\n    }\n    return endpointParams;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromInstructions: () => (/* binding */ getEndpointFromInstructions),\n/* harmony export */   resolveParams: () => (/* binding */ resolveParams)\n/* harmony export */ });\n/* harmony import */ var _service_customizations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../service-customizations */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js\");\n/* harmony import */ var _createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createConfigValueProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\");\n\n\nconst getEndpointFromInstructions = async (commandInput, instructionsSupplier, clientConfig, context) => {\n    const endpointParams = await resolveParams(commandInput, instructionsSupplier, clientConfig);\n    if (typeof clientConfig.endpointProvider !== \"function\") {\n        throw new Error(\"config.endpointProvider is not set.\");\n    }\n    const endpoint = clientConfig.endpointProvider(endpointParams, context);\n    return endpoint;\n};\nconst resolveParams = async (commandInput, instructionsSupplier, clientConfig) => {\n    const endpointParams = {};\n    const instructions = instructionsSupplier?.getEndpointParameterInstructions?.() || {};\n    for (const [name, instruction] of Object.entries(instructions)) {\n        switch (instruction.type) {\n            case \"staticContextParams\":\n                endpointParams[name] = instruction.value;\n                break;\n            case \"contextParams\":\n                endpointParams[name] = commandInput[instruction.name];\n                break;\n            case \"clientContextParams\":\n            case \"builtInParams\":\n                endpointParams[name] = await (0,_createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__.createConfigValueProvider)(instruction.name, name, clientConfig)();\n                break;\n            default:\n                throw new Error(\"Unrecognized endpoint parameter instruction: \" + JSON.stringify(instruction));\n        }\n    }\n    if (Object.keys(instructions).length === 0) {\n        Object.assign(endpointParams, clientConfig);\n    }\n    if (String(clientConfig.serviceId).toLowerCase() === \"s3\") {\n        await (0,_service_customizations__WEBPACK_IMPORTED_MODULE_0__.resolveParamsForS3)(endpointParams);\n    }\n    return endpointParams;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEndpointFromInstructions */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toEndpointV1 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9hZGFwdG9ycy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEM7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9hZGFwdG9ycy9pbmRleC5qcz8yZTY3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2dldEVuZHBvaW50RnJvbUluc3RydWN0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdG9FbmRwb2ludFYxXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromInstructions: () => (/* reexport safe */ _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions),\n/* harmony export */   resolveParams: () => (/* reexport safe */ _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.resolveParams),\n/* harmony export */   toEndpointV1: () => (/* reexport safe */ _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__.toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEndpointFromInstructions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n/* harmony import */ var _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toEndpointV1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEM7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9hZGFwdG9ycy9pbmRleC5qcz82MDhkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2dldEVuZHBvaW50RnJvbUluc3RydWN0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdG9FbmRwb2ludFYxXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toEndpointV1: () => (/* binding */ toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/url-parser */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+url-parser@3.296.0/node_modules/@aws-sdk/url-parser/dist-es/index.js\");\n\nconst toEndpointV1 = (endpoint) => {\n    if (typeof endpoint === \"object\") {\n        if (\"url\" in endpoint) {\n            return (0,_aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint.url);\n        }\n        return endpoint;\n    }\n    return (0,_aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9hZGFwdG9ycy90b0VuZHBvaW50VjEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDeEM7QUFDUDtBQUNBO0FBQ0EsbUJBQW1CLDZEQUFRO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNkRBQVE7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvdG9FbmRwb2ludFYxLmpzPzZjOGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGFyc2VVcmwgfSBmcm9tIFwiQGF3cy1zZGsvdXJsLXBhcnNlclwiO1xuZXhwb3J0IGNvbnN0IHRvRW5kcG9pbnRWMSA9IChlbmRwb2ludCkgPT4ge1xuICAgIGlmICh0eXBlb2YgZW5kcG9pbnQgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgaWYgKFwidXJsXCIgaW4gZW5kcG9pbnQpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJzZVVybChlbmRwb2ludC51cmwpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBlbmRwb2ludDtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnNlVXJsKGVuZHBvaW50KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toEndpointV1: () => (/* binding */ toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/url-parser */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+url-parser@3.296.0/node_modules/@aws-sdk/url-parser/dist-es/index.js\");\n\nconst toEndpointV1 = (endpoint) => {\n    if (typeof endpoint === \"object\") {\n        if (\"url\" in endpoint) {\n            return (0,_aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint.url);\n        }\n        return endpoint;\n    }\n    return (0,_aws_sdk_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvdG9FbmRwb2ludFYxLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDO0FBQ3hDO0FBQ1A7QUFDQTtBQUNBLG1CQUFtQiw2REFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLDZEQUFRO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL2FkYXB0b3JzL3RvRW5kcG9pbnRWMS5qcz9mZjUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhcnNlVXJsIH0gZnJvbSBcIkBhd3Mtc2RrL3VybC1wYXJzZXJcIjtcbmV4cG9ydCBjb25zdCB0b0VuZHBvaW50VjEgPSAoZW5kcG9pbnQpID0+IHtcbiAgICBpZiAodHlwZW9mIGVuZHBvaW50ID09PSBcIm9iamVjdFwiKSB7XG4gICAgICAgIGlmIChcInVybFwiIGluIGVuZHBvaW50KSB7XG4gICAgICAgICAgICByZXR1cm4gcGFyc2VVcmwoZW5kcG9pbnQudXJsKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZW5kcG9pbnQ7XG4gICAgfVxuICAgIHJldHVybiBwYXJzZVVybChlbmRwb2ludCk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddleware: () => (/* binding */ endpointMiddleware)\n/* harmony export */ });\n/* harmony import */ var _adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptors/getEndpointFromInstructions */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n\nconst endpointMiddleware = ({ config, instructions, }) => {\n    return (next, context) => async (args) => {\n        const endpoint = await (0,_adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions)(args.input, {\n            getEndpointParameterInstructions() {\n                return instructions;\n            },\n        }, { ...config }, context);\n        context.endpointV2 = endpoint;\n        context.authSchemes = endpoint.properties?.authSchemes;\n        const authScheme = context.authSchemes?.[0];\n        if (authScheme) {\n            context[\"signing_region\"] = authScheme.signingRegion;\n            context[\"signing_service\"] = authScheme.signingName;\n        }\n        return next({\n            ...args,\n        });\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9lbmRwb2ludE1pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUY7QUFDOUUsOEJBQThCLHVCQUF1QjtBQUM1RDtBQUNBLCtCQUErQixrR0FBMkI7QUFDMUQ7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTLElBQUksV0FBVztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9lbmRwb2ludE1pZGRsZXdhcmUuanM/ZGIyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRFbmRwb2ludEZyb21JbnN0cnVjdGlvbnMgfSBmcm9tIFwiLi9hZGFwdG9ycy9nZXRFbmRwb2ludEZyb21JbnN0cnVjdGlvbnNcIjtcbmV4cG9ydCBjb25zdCBlbmRwb2ludE1pZGRsZXdhcmUgPSAoeyBjb25maWcsIGluc3RydWN0aW9ucywgfSkgPT4ge1xuICAgIHJldHVybiAobmV4dCwgY29udGV4dCkgPT4gYXN5bmMgKGFyZ3MpID0+IHtcbiAgICAgICAgY29uc3QgZW5kcG9pbnQgPSBhd2FpdCBnZXRFbmRwb2ludEZyb21JbnN0cnVjdGlvbnMoYXJncy5pbnB1dCwge1xuICAgICAgICAgICAgZ2V0RW5kcG9pbnRQYXJhbWV0ZXJJbnN0cnVjdGlvbnMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGluc3RydWN0aW9ucztcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0sIHsgLi4uY29uZmlnIH0sIGNvbnRleHQpO1xuICAgICAgICBjb250ZXh0LmVuZHBvaW50VjIgPSBlbmRwb2ludDtcbiAgICAgICAgY29udGV4dC5hdXRoU2NoZW1lcyA9IGVuZHBvaW50LnByb3BlcnRpZXM/LmF1dGhTY2hlbWVzO1xuICAgICAgICBjb25zdCBhdXRoU2NoZW1lID0gY29udGV4dC5hdXRoU2NoZW1lcz8uWzBdO1xuICAgICAgICBpZiAoYXV0aFNjaGVtZSkge1xuICAgICAgICAgICAgY29udGV4dFtcInNpZ25pbmdfcmVnaW9uXCJdID0gYXV0aFNjaGVtZS5zaWduaW5nUmVnaW9uO1xuICAgICAgICAgICAgY29udGV4dFtcInNpZ25pbmdfc2VydmljZVwiXSA9IGF1dGhTY2hlbWUuc2lnbmluZ05hbWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5leHQoe1xuICAgICAgICAgICAgLi4uYXJncyxcbiAgICAgICAgfSk7XG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddleware: () => (/* binding */ endpointMiddleware)\n/* harmony export */ });\n/* harmony import */ var _adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptors/getEndpointFromInstructions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n\nconst endpointMiddleware = ({ config, instructions, }) => {\n    return (next, context) => async (args) => {\n        const endpoint = await (0,_adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions)(args.input, {\n            getEndpointParameterInstructions() {\n                return instructions;\n            },\n        }, { ...config }, context);\n        context.endpointV2 = endpoint;\n        context.authSchemes = endpoint.properties?.authSchemes;\n        const authScheme = context.authSchemes?.[0];\n        if (authScheme) {\n            context[\"signing_region\"] = authScheme.signingRegion;\n            context[\"signing_service\"] = authScheme.signingName;\n        }\n        return next({\n            ...args,\n        });\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZW5kcG9pbnRNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFGO0FBQzlFLDhCQUE4Qix1QkFBdUI7QUFDNUQ7QUFDQSwrQkFBK0Isa0dBQTJCO0FBQzFEO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUyxJQUFJLFdBQVc7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZW5kcG9pbnRNaWRkbGV3YXJlLmpzPzYyMTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0RW5kcG9pbnRGcm9tSW5zdHJ1Y3Rpb25zIH0gZnJvbSBcIi4vYWRhcHRvcnMvZ2V0RW5kcG9pbnRGcm9tSW5zdHJ1Y3Rpb25zXCI7XG5leHBvcnQgY29uc3QgZW5kcG9pbnRNaWRkbGV3YXJlID0gKHsgY29uZmlnLCBpbnN0cnVjdGlvbnMsIH0pID0+IHtcbiAgICByZXR1cm4gKG5leHQsIGNvbnRleHQpID0+IGFzeW5jIChhcmdzKSA9PiB7XG4gICAgICAgIGNvbnN0IGVuZHBvaW50ID0gYXdhaXQgZ2V0RW5kcG9pbnRGcm9tSW5zdHJ1Y3Rpb25zKGFyZ3MuaW5wdXQsIHtcbiAgICAgICAgICAgIGdldEVuZHBvaW50UGFyYW1ldGVySW5zdHJ1Y3Rpb25zKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBpbnN0cnVjdGlvbnM7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9LCB7IC4uLmNvbmZpZyB9LCBjb250ZXh0KTtcbiAgICAgICAgY29udGV4dC5lbmRwb2ludFYyID0gZW5kcG9pbnQ7XG4gICAgICAgIGNvbnRleHQuYXV0aFNjaGVtZXMgPSBlbmRwb2ludC5wcm9wZXJ0aWVzPy5hdXRoU2NoZW1lcztcbiAgICAgICAgY29uc3QgYXV0aFNjaGVtZSA9IGNvbnRleHQuYXV0aFNjaGVtZXM/LlswXTtcbiAgICAgICAgaWYgKGF1dGhTY2hlbWUpIHtcbiAgICAgICAgICAgIGNvbnRleHRbXCJzaWduaW5nX3JlZ2lvblwiXSA9IGF1dGhTY2hlbWUuc2lnbmluZ1JlZ2lvbjtcbiAgICAgICAgICAgIGNvbnRleHRbXCJzaWduaW5nX3NlcnZpY2VcIl0gPSBhdXRoU2NoZW1lLnNpZ25pbmdOYW1lO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXh0KHtcbiAgICAgICAgICAgIC4uLmFyZ3MsXG4gICAgICAgIH0pO1xuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddlewareOptions: () => (/* binding */ endpointMiddlewareOptions),\n/* harmony export */   getEndpointPlugin: () => (/* binding */ getEndpointPlugin)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_serde__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-serde */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\");\n\n\nconst endpointMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"ENDPOINT_PARAMETERS\", \"ENDPOINT_V2\", \"ENDPOINT\"],\n    name: \"endpointV2Middleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: _aws_sdk_middleware_serde__WEBPACK_IMPORTED_MODULE_0__.serializerMiddlewareOption.name,\n};\nconst getEndpointPlugin = (config, instructions) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__.endpointMiddleware)({\n            config,\n            instructions,\n        }), endpointMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9nZXRFbmRwb2ludFBsdWdpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVFO0FBQ2I7QUFDbkQ7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGlGQUEwQjtBQUM1QztBQUNPO0FBQ1A7QUFDQSxrQ0FBa0MsdUVBQWtCO0FBQ3BEO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZ2V0RW5kcG9pbnRQbHVnaW4uanM/ODkxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbiB9IGZyb20gXCJAYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlXCI7XG5pbXBvcnQgeyBlbmRwb2ludE1pZGRsZXdhcmUgfSBmcm9tIFwiLi9lbmRwb2ludE1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBlbmRwb2ludE1pZGRsZXdhcmVPcHRpb25zID0ge1xuICAgIHN0ZXA6IFwic2VyaWFsaXplXCIsXG4gICAgdGFnczogW1wiRU5EUE9JTlRfUEFSQU1FVEVSU1wiLCBcIkVORFBPSU5UX1YyXCIsIFwiRU5EUE9JTlRcIl0sXG4gICAgbmFtZTogXCJlbmRwb2ludFYyTWlkZGxld2FyZVwiLFxuICAgIG92ZXJyaWRlOiB0cnVlLFxuICAgIHJlbGF0aW9uOiBcImJlZm9yZVwiLFxuICAgIHRvTWlkZGxld2FyZTogc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24ubmFtZSxcbn07XG5leHBvcnQgY29uc3QgZ2V0RW5kcG9pbnRQbHVnaW4gPSAoY29uZmlnLCBpbnN0cnVjdGlvbnMpID0+ICh7XG4gICAgYXBwbHlUb1N0YWNrOiAoY2xpZW50U3RhY2spID0+IHtcbiAgICAgICAgY2xpZW50U3RhY2suYWRkUmVsYXRpdmVUbyhlbmRwb2ludE1pZGRsZXdhcmUoe1xuICAgICAgICAgICAgY29uZmlnLFxuICAgICAgICAgICAgaW5zdHJ1Y3Rpb25zLFxuICAgICAgICB9KSwgZW5kcG9pbnRNaWRkbGV3YXJlT3B0aW9ucyk7XG4gICAgfSxcbn0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddlewareOptions: () => (/* binding */ endpointMiddlewareOptions),\n/* harmony export */   getEndpointPlugin: () => (/* binding */ getEndpointPlugin)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_serde__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\");\n\n\nconst endpointMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"ENDPOINT_PARAMETERS\", \"ENDPOINT_V2\", \"ENDPOINT\"],\n    name: \"endpointV2Middleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: _aws_sdk_middleware_serde__WEBPACK_IMPORTED_MODULE_0__.serializerMiddlewareOption.name,\n};\nconst getEndpointPlugin = (config, instructions) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__.endpointMiddleware)({\n            config,\n            instructions,\n        }), endpointMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZ2V0RW5kcG9pbnRQbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RTtBQUNiO0FBQ25EO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixpRkFBMEI7QUFDNUM7QUFDTztBQUNQO0FBQ0Esa0NBQWtDLHVFQUFrQjtBQUNwRDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL2dldEVuZHBvaW50UGx1Z2luLmpzPzdiMGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24gfSBmcm9tIFwiQGF3cy1zZGsvbWlkZGxld2FyZS1zZXJkZVwiO1xuaW1wb3J0IHsgZW5kcG9pbnRNaWRkbGV3YXJlIH0gZnJvbSBcIi4vZW5kcG9pbnRNaWRkbGV3YXJlXCI7XG5leHBvcnQgY29uc3QgZW5kcG9pbnRNaWRkbGV3YXJlT3B0aW9ucyA9IHtcbiAgICBzdGVwOiBcInNlcmlhbGl6ZVwiLFxuICAgIHRhZ3M6IFtcIkVORFBPSU5UX1BBUkFNRVRFUlNcIiwgXCJFTkRQT0lOVF9WMlwiLCBcIkVORFBPSU5UXCJdLFxuICAgIG5hbWU6IFwiZW5kcG9pbnRWMk1pZGRsZXdhcmVcIixcbiAgICBvdmVycmlkZTogdHJ1ZSxcbiAgICByZWxhdGlvbjogXCJiZWZvcmVcIixcbiAgICB0b01pZGRsZXdhcmU6IHNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uLm5hbWUsXG59O1xuZXhwb3J0IGNvbnN0IGdldEVuZHBvaW50UGx1Z2luID0gKGNvbmZpZywgaW5zdHJ1Y3Rpb25zKSA9PiAoe1xuICAgIGFwcGx5VG9TdGFjazogKGNsaWVudFN0YWNrKSA9PiB7XG4gICAgICAgIGNsaWVudFN0YWNrLmFkZFJlbGF0aXZlVG8oZW5kcG9pbnRNaWRkbGV3YXJlKHtcbiAgICAgICAgICAgIGNvbmZpZyxcbiAgICAgICAgICAgIGluc3RydWN0aW9ucyxcbiAgICAgICAgfSksIGVuZHBvaW50TWlkZGxld2FyZU9wdGlvbnMpO1xuICAgIH0sXG59KTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _adaptors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptors */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _adaptors__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _adaptors__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointPlugin */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveEndpointConfig */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkI7QUFDVTtBQUNEO0FBQ0k7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvaW5kZXguanM/ZmM0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9hZGFwdG9yc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZW5kcG9pbnRNaWRkbGV3YXJlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9nZXRFbmRwb2ludFBsdWdpblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmVzb2x2ZUVuZHBvaW50Q29uZmlnXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlc1wiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddleware: () => (/* reexport safe */ _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__.endpointMiddleware),\n/* harmony export */   endpointMiddlewareOptions: () => (/* reexport safe */ _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__.endpointMiddlewareOptions),\n/* harmony export */   getEndpointFromInstructions: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions),\n/* harmony export */   getEndpointPlugin: () => (/* reexport safe */ _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__.getEndpointPlugin),\n/* harmony export */   resolveEndpointConfig: () => (/* reexport safe */ _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__.resolveEndpointConfig),\n/* harmony export */   resolveParams: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.resolveParams),\n/* harmony export */   toEndpointV1: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _adaptors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptors */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/index.js\");\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/endpointMiddleware.js\");\n/* harmony import */ var _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointPlugin */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/getEndpointPlugin.js\");\n/* harmony import */ var _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveEndpointConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCO0FBQ1U7QUFDRDtBQUNJO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL2luZGV4LmpzPzVkODEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vYWRhcHRvcnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2VuZHBvaW50TWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZ2V0RW5kcG9pbnRQbHVnaW5cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmVFbmRwb2ludENvbmZpZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpointConfig: () => (/* binding */ resolveEndpointConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-middleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-middleware@3.296.0/node_modules/@aws-sdk/util-middleware/dist-es/index.js\");\n/* harmony import */ var _adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./adaptors/toEndpointV1 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\nconst resolveEndpointConfig = (input) => {\n    const tls = input.tls ?? true;\n    const { endpoint } = input;\n    const customEndpointProvider = endpoint != null ? async () => (0,_adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_1__.toEndpointV1)(await (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(endpoint)()) : undefined;\n    const isCustomEndpoint = !!endpoint;\n    return {\n        ...input,\n        endpoint: customEndpointProvider,\n        tls,\n        isCustomEndpoint,\n        useDualstackEndpoint: (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.useDualstackEndpoint ?? false),\n        useFipsEndpoint: (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.useFipsEndpoint ?? false),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9yZXNvbHZlRW5kcG9pbnRDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZEO0FBQ047QUFDaEQ7QUFDUDtBQUNBLFlBQVksV0FBVztBQUN2QixrRUFBa0Usb0VBQVksT0FBTywyRUFBaUI7QUFDdEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLDJFQUFpQjtBQUMvQyx5QkFBeUIsMkVBQWlCO0FBQzFDO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvcmVzb2x2ZUVuZHBvaW50Q29uZmlnLmpzPzc3ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9ybWFsaXplUHJvdmlkZXIgfSBmcm9tIFwiQGF3cy1zZGsvdXRpbC1taWRkbGV3YXJlXCI7XG5pbXBvcnQgeyB0b0VuZHBvaW50VjEgfSBmcm9tIFwiLi9hZGFwdG9ycy90b0VuZHBvaW50VjFcIjtcbmV4cG9ydCBjb25zdCByZXNvbHZlRW5kcG9pbnRDb25maWcgPSAoaW5wdXQpID0+IHtcbiAgICBjb25zdCB0bHMgPSBpbnB1dC50bHMgPz8gdHJ1ZTtcbiAgICBjb25zdCB7IGVuZHBvaW50IH0gPSBpbnB1dDtcbiAgICBjb25zdCBjdXN0b21FbmRwb2ludFByb3ZpZGVyID0gZW5kcG9pbnQgIT0gbnVsbCA/IGFzeW5jICgpID0+IHRvRW5kcG9pbnRWMShhd2FpdCBub3JtYWxpemVQcm92aWRlcihlbmRwb2ludCkoKSkgOiB1bmRlZmluZWQ7XG4gICAgY29uc3QgaXNDdXN0b21FbmRwb2ludCA9ICEhZW5kcG9pbnQ7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgLi4uaW5wdXQsXG4gICAgICAgIGVuZHBvaW50OiBjdXN0b21FbmRwb2ludFByb3ZpZGVyLFxuICAgICAgICB0bHMsXG4gICAgICAgIGlzQ3VzdG9tRW5kcG9pbnQsXG4gICAgICAgIHVzZUR1YWxzdGFja0VuZHBvaW50OiBub3JtYWxpemVQcm92aWRlcihpbnB1dC51c2VEdWFsc3RhY2tFbmRwb2ludCA/PyBmYWxzZSksXG4gICAgICAgIHVzZUZpcHNFbmRwb2ludDogbm9ybWFsaXplUHJvdmlkZXIoaW5wdXQudXNlRmlwc0VuZHBvaW50ID8/IGZhbHNlKSxcbiAgICB9O1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpointConfig: () => (/* binding */ resolveEndpointConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-middleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-middleware@3.296.0/node_modules/@aws-sdk/util-middleware/dist-es/index.js\");\n/* harmony import */ var _adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./adaptors/toEndpointV1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\nconst resolveEndpointConfig = (input) => {\n    const tls = input.tls ?? true;\n    const { endpoint } = input;\n    const customEndpointProvider = endpoint != null ? async () => (0,_adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_1__.toEndpointV1)(await (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(endpoint)()) : undefined;\n    const isCustomEndpoint = !!endpoint;\n    return {\n        ...input,\n        endpoint: customEndpointProvider,\n        tls,\n        isCustomEndpoint,\n        useDualstackEndpoint: (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.useDualstackEndpoint ?? false),\n        useFipsEndpoint: (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.useFipsEndpoint ?? false),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvcmVzb2x2ZUVuZHBvaW50Q29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2RDtBQUNOO0FBQ2hEO0FBQ1A7QUFDQSxZQUFZLFdBQVc7QUFDdkIsa0VBQWtFLG9FQUFZLE9BQU8sMkVBQWlCO0FBQ3RHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwyRUFBaUI7QUFDL0MseUJBQXlCLDJFQUFpQjtBQUMxQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL3Jlc29sdmVFbmRwb2ludENvbmZpZy5qcz8zYWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vcm1hbGl6ZVByb3ZpZGVyIH0gZnJvbSBcIkBhd3Mtc2RrL3V0aWwtbWlkZGxld2FyZVwiO1xuaW1wb3J0IHsgdG9FbmRwb2ludFYxIH0gZnJvbSBcIi4vYWRhcHRvcnMvdG9FbmRwb2ludFYxXCI7XG5leHBvcnQgY29uc3QgcmVzb2x2ZUVuZHBvaW50Q29uZmlnID0gKGlucHV0KSA9PiB7XG4gICAgY29uc3QgdGxzID0gaW5wdXQudGxzID8/IHRydWU7XG4gICAgY29uc3QgeyBlbmRwb2ludCB9ID0gaW5wdXQ7XG4gICAgY29uc3QgY3VzdG9tRW5kcG9pbnRQcm92aWRlciA9IGVuZHBvaW50ICE9IG51bGwgPyBhc3luYyAoKSA9PiB0b0VuZHBvaW50VjEoYXdhaXQgbm9ybWFsaXplUHJvdmlkZXIoZW5kcG9pbnQpKCkpIDogdW5kZWZpbmVkO1xuICAgIGNvbnN0IGlzQ3VzdG9tRW5kcG9pbnQgPSAhIWVuZHBvaW50O1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmlucHV0LFxuICAgICAgICBlbmRwb2ludDogY3VzdG9tRW5kcG9pbnRQcm92aWRlcixcbiAgICAgICAgdGxzLFxuICAgICAgICBpc0N1c3RvbUVuZHBvaW50LFxuICAgICAgICB1c2VEdWFsc3RhY2tFbmRwb2ludDogbm9ybWFsaXplUHJvdmlkZXIoaW5wdXQudXNlRHVhbHN0YWNrRW5kcG9pbnQgPz8gZmFsc2UpLFxuICAgICAgICB1c2VGaXBzRW5kcG9pbnQ6IG5vcm1hbGl6ZVByb3ZpZGVyKGlucHV0LnVzZUZpcHNFbmRwb2ludCA/PyBmYWxzZSksXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/resolveEndpointConfig.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _s3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./s3 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _s3__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _s3__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9zZXJ2aWNlLWN1c3RvbWl6YXRpb25zL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL3NlcnZpY2UtY3VzdG9taXphdGlvbnMvaW5kZXguanM/N2JmYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9zM1wiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOT_PATTERN: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.DOT_PATTERN),\n/* harmony export */   S3_HOSTNAME_PATTERN: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.S3_HOSTNAME_PATTERN),\n/* harmony export */   isArnBucketName: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.isArnBucketName),\n/* harmony export */   isDnsCompatibleBucketName: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.isDnsCompatibleBucketName),\n/* harmony export */   resolveParamsForS3: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.resolveParamsForS3)\n/* harmony export */ });\n/* harmony import */ var _s3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./s3 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvc2VydmljZS1jdXN0b21pemF0aW9ucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvc2VydmljZS1jdXN0b21pemF0aW9ucy9pbmRleC5qcz9lNTJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3MzXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOT_PATTERN: () => (/* binding */ DOT_PATTERN),\n/* harmony export */   S3_HOSTNAME_PATTERN: () => (/* binding */ S3_HOSTNAME_PATTERN),\n/* harmony export */   isArnBucketName: () => (/* binding */ isArnBucketName),\n/* harmony export */   isDnsCompatibleBucketName: () => (/* binding */ isDnsCompatibleBucketName),\n/* harmony export */   resolveParamsForS3: () => (/* binding */ resolveParamsForS3)\n/* harmony export */ });\nconst resolveParamsForS3 = async (endpointParams) => {\n    const bucket = endpointParams?.Bucket || \"\";\n    if (typeof endpointParams.Bucket === \"string\") {\n        endpointParams.Bucket = bucket.replace(/#/g, encodeURIComponent(\"#\")).replace(/\\?/g, encodeURIComponent(\"?\"));\n    }\n    if (isArnBucketName(bucket)) {\n        if (endpointParams.ForcePathStyle === true) {\n            throw new Error(\"Path-style addressing cannot be used with ARN buckets\");\n        }\n    }\n    else if (!isDnsCompatibleBucketName(bucket) ||\n        (bucket.indexOf(\".\") !== -1 && !String(endpointParams.Endpoint).startsWith(\"http:\")) ||\n        bucket.toLowerCase() !== bucket ||\n        bucket.length < 3) {\n        endpointParams.ForcePathStyle = true;\n    }\n    if (endpointParams.DisableMultiRegionAccessPoints) {\n        endpointParams.disableMultiRegionAccessPoints = true;\n        endpointParams.DisableMRAP = true;\n    }\n    return endpointParams;\n};\nconst DOMAIN_PATTERN = /^[a-z0-9][a-z0-9\\.\\-]{1,61}[a-z0-9]$/;\nconst IP_ADDRESS_PATTERN = /(\\d+\\.){3}\\d+/;\nconst DOTS_PATTERN = /\\.\\./;\nconst DOT_PATTERN = /\\./;\nconst S3_HOSTNAME_PATTERN = /^(.+\\.)?s3(-fips)?(\\.dualstack)?[.-]([a-z0-9-]+)\\./;\nconst isDnsCompatibleBucketName = (bucketName) => DOMAIN_PATTERN.test(bucketName) && !IP_ADDRESS_PATTERN.test(bucketName) && !DOTS_PATTERN.test(bucketName);\nconst isArnBucketName = (bucketName) => {\n    const [arn, partition, service, region, account, typeOrId] = bucketName.split(\":\");\n    const isArn = arn === \"arn\" && bucketName.split(\":\").length >= 6;\n    const isValidArn = [arn, partition, service, account, typeOrId].filter(Boolean).length === 5;\n    if (isArn && !isValidArn) {\n        throw new Error(`Invalid ARN: ${bucketName} was an invalid ARN.`);\n    }\n    return arn === \"arn\" && !!partition && !!service && !!account && !!typeOrId;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOT_PATTERN: () => (/* binding */ DOT_PATTERN),\n/* harmony export */   S3_HOSTNAME_PATTERN: () => (/* binding */ S3_HOSTNAME_PATTERN),\n/* harmony export */   isArnBucketName: () => (/* binding */ isArnBucketName),\n/* harmony export */   isDnsCompatibleBucketName: () => (/* binding */ isDnsCompatibleBucketName),\n/* harmony export */   resolveParamsForS3: () => (/* binding */ resolveParamsForS3)\n/* harmony export */ });\nconst resolveParamsForS3 = async (endpointParams) => {\n    const bucket = endpointParams?.Bucket || \"\";\n    if (typeof endpointParams.Bucket === \"string\") {\n        endpointParams.Bucket = bucket.replace(/#/g, encodeURIComponent(\"#\")).replace(/\\?/g, encodeURIComponent(\"?\"));\n    }\n    if (isArnBucketName(bucket)) {\n        if (endpointParams.ForcePathStyle === true) {\n            throw new Error(\"Path-style addressing cannot be used with ARN buckets\");\n        }\n    }\n    else if (!isDnsCompatibleBucketName(bucket) ||\n        (bucket.indexOf(\".\") !== -1 && !String(endpointParams.Endpoint).startsWith(\"http:\")) ||\n        bucket.toLowerCase() !== bucket ||\n        bucket.length < 3) {\n        endpointParams.ForcePathStyle = true;\n    }\n    if (endpointParams.DisableMultiRegionAccessPoints) {\n        endpointParams.disableMultiRegionAccessPoints = true;\n        endpointParams.DisableMRAP = true;\n    }\n    return endpointParams;\n};\nconst DOMAIN_PATTERN = /^[a-z0-9][a-z0-9\\.\\-]{1,61}[a-z0-9]$/;\nconst IP_ADDRESS_PATTERN = /(\\d+\\.){3}\\d+/;\nconst DOTS_PATTERN = /\\.\\./;\nconst DOT_PATTERN = /\\./;\nconst S3_HOSTNAME_PATTERN = /^(.+\\.)?s3(-fips)?(\\.dualstack)?[.-]([a-z0-9-]+)\\./;\nconst isDnsCompatibleBucketName = (bucketName) => DOMAIN_PATTERN.test(bucketName) && !IP_ADDRESS_PATTERN.test(bucketName) && !DOTS_PATTERN.test(bucketName);\nconst isArnBucketName = (bucketName) => {\n    const [arn, partition, service, region, account, typeOrId] = bucketName.split(\":\");\n    const isArn = arn === \"arn\" && bucketName.split(\":\").length >= 6;\n    const isValidArn = [arn, partition, service, account, typeOrId].filter(Boolean).length === 5;\n    if (isArn && !isValidArn) {\n        throw new Error(`Invalid ARN: ${bucketName} was an invalid ARN.`);\n    }\n    return arn === \"arn\" && !!partition && !!service && !!account && !!typeOrId;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/service-customizations/s3.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWVuZHBvaW50QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvdHlwZXMuanM/YjMzMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1lbmRwb2ludEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZW5kcG9pbnRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL3R5cGVzLmpzPzdmN2YiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/types.js\n");

/***/ })

};
;