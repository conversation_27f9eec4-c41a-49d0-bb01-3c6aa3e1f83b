"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // Check if there are unsaved changes\n    const hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1867,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1868,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1881,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1889,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1884,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1913,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1925,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1942,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1951,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1972,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2030,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1908,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2058,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2057,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2052,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2103,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2102,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2165,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2177,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2203,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2206,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2226,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2227,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2221,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2250,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2251,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2245,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2263,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2080,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2074,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2279,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"mqt6PNy/xN3BXo3wwjlB1Gn3HLU=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvRWRpdG9yRXhwZXJpZW5jZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVUyQjtBQUN1QztBQUNTO0FBQzNCO0FBRWpCO0FBQ2E7QUFDVTtBQUN4QjtBQUNZO0FBQ007QUFDTjtBQUNKO0FBQ3NCO0FBQ3BCO0FBQ1I7QUFDa0I7QUFFaUI7QUFFVTtBQUlsQztBQUMwQjtBQUNuQjtBQUNVO0FBQ2xCO0FBQ3NCO0FBQ1Y7QUFJZjtBQUNSO0FBQ2dDO0FBRS9ELE1BQU0yQyxrQkFBa0I7UUFBQyxFQUFFQyxlQUFlLEVBQUU7O0lBQzFDLE1BQU0sRUFBRUMsRUFBRSxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRSxHQUFHdkMsc0RBQVFBO0lBRXRDSSxnREFBU0EsQ0FBQztRQUNSLElBQUksT0FBT2dDLG9CQUFvQixZQUFZO1lBQ3pDQSxnQkFBZ0I7Z0JBQ2QsSUFBSTtvQkFDRixtQkFBbUI7b0JBQ25CQyxHQUFHRyxNQUFNLENBQUNGLE9BQU9DO29CQUVqQiwwQkFBMEI7b0JBQzFCLE1BQU1FLFVBQVVKLEdBQUdLLFVBQVUsQ0FBQ0MsU0FBUyxDQUFDO29CQUV4QyxnQ0FBZ0M7b0JBQ2hDLE1BQU1DLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztvQkFDcEMsTUFBTUMsWUFBWSxJQUFJQyxPQUNuQkMsV0FBVyxHQUNYQyxPQUFPLENBQUMsTUFBTSxLQUNkQyxTQUFTLENBQUMsR0FBRztvQkFDaEJQLEtBQUtRLFFBQVEsR0FBRyxlQUF5QixPQUFWTCxXQUFVO29CQUN6Q0gsS0FBS1MsSUFBSSxHQUFHWjtvQkFDWkksU0FBU1MsSUFBSSxDQUFDQyxXQUFXLENBQUNYO29CQUMxQkEsS0FBS1ksS0FBSztvQkFFVixXQUFXO29CQUNYQyxXQUFXO3dCQUNUWixTQUFTUyxJQUFJLENBQUNJLFdBQVcsQ0FBQ2Q7b0JBQzVCLEdBQUc7b0JBRUhlLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixPQUFPO2dCQUNULEVBQUUsT0FBT0MsT0FBTztvQkFDZEYsUUFBUUUsS0FBSyxDQUFDLCtCQUErQkE7b0JBQzdDLE9BQU87Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDeEI7UUFBSUM7UUFBT0M7UUFBUUg7S0FBZ0I7SUFFdkMsT0FBTztBQUNUO0dBeENNRDs7UUFDMEJuQyxrREFBUUE7OztLQURsQ21DO0FBMENOLGlEQUFpRDtBQUNqRCxNQUFNMkIsdUJBQXVCO1FBQUMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU87SUFDbEQscUJBQU8sOERBQUNuRCx5REFBYUE7UUFBQ2tELFVBQVVBO1FBQVcsR0FBR0MsS0FBSzs7Ozs7O0FBQ3JEO01BRk1GO0FBSU4sZ0RBQWdEO0FBQ2hELE1BQU1HLG1CQUFtQjtRQUFDLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFOztJQUNyRCxNQUFNLEVBQUU5QixFQUFFLEVBQUUsR0FBR3JDLHNEQUFRQTtJQUV2QkksZ0RBQVNBLENBQUM7UUFDUixJQUFJK0QsaUJBQWlCO1lBQ25CQSxnQkFBZ0I5QjtRQUNsQjtJQUNGLEdBQUc7UUFBQ0E7UUFBSThCO0tBQWdCO0lBRXhCLE9BQU87QUFDVDtJQVZNRjs7UUFDV2pFLGtEQUFRQTs7O01BRG5CaUU7QUFZUyxTQUFTRyxpQkFBaUIsS0FBMkI7UUFBM0IsRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLElBQUksRUFBRSxHQUEzQjs7SUFDdkMsTUFBTSxFQUFFQyxVQUFVLEVBQUUsR0FBR3ZDLDZEQUFlQTtJQUN0QyxNQUFNLEVBQ0p3QyxhQUFhLEVBQ2JDLGNBQWMsRUFDZEMsZ0JBQWdCLEVBQ2hCQyxpQkFBaUIsRUFDakJDLGtCQUFrQixFQUNuQixHQUFHM0MsNERBQWNBO0lBRWxCLE1BQU00QyxZQUFZekUsNkNBQU1BLENBQUM7SUFDekIsTUFBTTBFLG1CQUFtQjFFLDZDQUFNQSxDQUFDO0lBQ2hDLE1BQU0sQ0FBQzJFLGVBQWVDLGlCQUFpQixHQUFHM0UsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNEUsbUJBQW1CQyxxQkFBcUIsR0FBRzdFLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzhFLGVBQWVDLGlCQUFpQixHQUFHL0UsK0NBQVFBLENBQUMrRCxZQUFZO0lBQy9ELE1BQU0sQ0FBQ2lCLFlBQVlDLGNBQWMsR0FBR2pGLCtDQUFRQSxDQUFDO1FBQzNDa0YsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsWUFBWTtJQUNkO0lBQ0EsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUd2RiwrQ0FBUUEsQ0FBQztRQUNuQztZQUNFd0YsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBRTtZQUN2QkMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxlQUFlO1FBQ2pCO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUc7Z0JBQUc7YUFBRTtZQUNuQkMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxVQUFVO2dCQUFDLENBQUM7Z0JBQUc7Z0JBQUcsQ0FBQzthQUFFO1lBQ3JCQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUc7Z0JBQUcsQ0FBQzthQUFFO1lBQ3BCQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsTUFBTTtRQUNSO0tBQ0Q7SUFDRCxNQUFNRyxZQUFZakcsNkNBQU1BLENBQUMsQ0FBQztJQUMxQixNQUFNLENBQUNrRyxpQkFBaUJDLG1CQUFtQixHQUFHbEcsK0NBQVFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUNtRyxlQUFlQyxpQkFBaUIsR0FBR3BHLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FHLHNCQUFzQkMsd0JBQXdCLEdBQUd0RywrQ0FBUUEsQ0FBQyxDQUFDO0lBQ2xFLE1BQU0sQ0FBQ3VHLGNBQWNDLGdCQUFnQixHQUFHeEcsK0NBQVFBLENBQUMsRUFBRTtJQUNuRCxNQUFNLENBQUN5RyxlQUFlQyxpQkFBaUIsR0FBRzFHLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0yRyxvQkFBb0I1Ryw2Q0FBTUEsQ0FBQyxJQUFJRyx5Q0FBVztJQUVoRG1ELFFBQVFDLEdBQUcsQ0FBQztJQUVaLE1BQU0sQ0FBQ3VELGVBQWVDLGlCQUFpQixHQUFHOUcsK0NBQVFBLENBQUM7SUFFbkQsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQytHLGdCQUFnQkMsa0JBQWtCLEdBQUdoSCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNpSCxnQkFBZ0JDLGtCQUFrQixHQUFHbEgsK0NBQVFBLENBQUM7SUFFckQsdUJBQXVCO0lBQ3ZCLE1BQU0sQ0FBQ21ILFVBQVVDLFlBQVksR0FBR3BILCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3FILFdBQVdDLGFBQWEsR0FBR3RILCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3VILFlBQVlDLGNBQWMsR0FBR3hILCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lILFdBQVdDLGFBQWEsR0FBRzFILCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzJILFNBQVNDLFdBQVcsR0FBRzVILCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzZILGlCQUFpQkMsbUJBQW1CLEdBQUc5SCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMrSCxrQkFBa0JDLG9CQUFvQixHQUFHaEksK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDaUksWUFBWUMsY0FBYyxHQUFHbEksK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDbUksY0FBY0MsZ0JBQWdCLEdBQUdwSSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNxSSxTQUFTQyxXQUFXLEdBQUd0SSwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN1SSxhQUFhQyxlQUFlLEdBQUd4SSwrQ0FBUUEsQ0FBQztJQUUvQyxNQUFNLENBQUN5SSx1QkFBdUJDLHlCQUF5QixHQUFHMUksK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDMkksd0JBQXdCQywwQkFBMEIsR0FBRzVJLCtDQUFRQSxDQUFDO1FBQ25FNkksV0FBVztZQUNUQyxTQUFTO1lBQ1RDLFlBQVk7UUFDZDtRQUNBQyxPQUFPO1lBQ0xGLFNBQVM7WUFDVG5ELFdBQVc7WUFDWHNELFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLEtBQUs7WUFDSEwsU0FBUztZQUNUTSxlQUFlO1lBQ2ZDLFVBQVU7WUFDVk4sWUFBWTtRQUNkO1FBQ0FPLFVBQVU7WUFDUlIsU0FBUztZQUNUUyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1DLGtCQUFrQjFKLDZDQUFNQSxDQUFDO1FBQzdCMkUsZUFBZTtRQUNmWSxRQUFRLEVBQUU7UUFDVm1DLFdBQVc7UUFDWEUsU0FBUztRQUNUZ0Isd0JBQXdCO1FBQ3hCeEIsVUFBVTtRQUNWRSxXQUFXO1FBQ1hFLFlBQVk7UUFDWk0saUJBQWlCO1FBQ2pCRSxrQkFBa0I7UUFDbEJJLGNBQWM7UUFDZEUsU0FBUztRQUNUcUIsZ0JBQWdCO1FBQ2hCQyxjQUFjO0lBQ2hCO0lBRUEsd0RBQXdEO0lBQ3hELE1BQU1DLHVCQUF1QjdKLDZDQUFNQSxDQUFDO1FBQ2xDOEosU0FBUyxFQUFFO0lBQ2I7SUFFQSw4Q0FBOEM7SUFDOUMvSixnREFBU0EsQ0FBQztRQUNSMkosZ0JBQWdCSyxPQUFPLEdBQUc7WUFDeEJwRjtZQUNBWSxRQUFRO21CQUFJQTthQUFPO1lBQ25CbUM7WUFDQUU7WUFDQWdCLHdCQUF3QjtnQkFBRSxHQUFHQSxzQkFBc0I7WUFBQztZQUNwRHhCO1lBQ0FFO1lBQ0FFO1lBQ0FNO1lBQ0FFO1lBQ0FJO1lBQ0FFO1lBQ0FxQixnQkFBZ0IsSUFBSXhKLDJDQUFhLENBQUMsR0FBRyxHQUFHO1lBQ3hDeUosY0FBYyxJQUFJekosMkNBQWEsQ0FBQyxHQUFHLEdBQUc7UUFDeEM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNOEosbUJBQW1CakssNkNBQU1BO0lBQy9CLE1BQU1rSyxXQUFXbEssNkNBQU1BO0lBQ3ZCLE1BQU0sQ0FBQzBELFVBQVV5RyxZQUFZLEdBQUdsSywrQ0FBUUEsQ0FBQztJQUV6Qyw4QkFBOEI7SUFDOUIsTUFBTSxDQUFDbUssU0FBU0MsV0FBVyxHQUFHcEssK0NBQVFBLENBQUMsRUFBRTtJQUN6QyxNQUFNLENBQUNxSyxjQUFjQyxnQkFBZ0IsR0FBR3RLLCtDQUFRQSxDQUFDLENBQUM7SUFDbEQsTUFBTXVLLHNCQUFzQnhLLDZDQUFNQSxDQUFDO0lBQ25DLE1BQU15Syx5QkFBeUJ6Syw2Q0FBTUEsQ0FBQztJQUN0QyxNQUFNMEssMEJBQTBCMUssNkNBQU1BLENBQUM7SUFFdkMsTUFBTSxDQUFDMkssZ0JBQWdCQyxrQkFBa0IsR0FBRzNLLCtDQUFRQSxDQUFDO0lBRXJELDBDQUEwQztJQUMxQyxNQUFNNEssZUFBZS9LLGtEQUFXQSxDQUFDO1FBQy9Cd0QsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUIrRztRQUNyQ2hILFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUI2RyxRQUFRVSxNQUFNO1FBQzdDeEgsUUFBUUMsR0FBRyxDQUFDLGFBQWErRyxlQUFlO1FBQ3hDaEgsUUFBUUMsR0FBRyxDQUFDLGFBQWErRyxlQUFlRixRQUFRVSxNQUFNLEdBQUc7UUFDekR4SCxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDaUgsb0JBQW9CVCxPQUFPO1FBQ3ZFekcsUUFBUUMsR0FBRyxDQUNULG1DQUNBa0gsdUJBQXVCVixPQUFPO1FBRWhDekcsUUFBUUMsR0FBRyxDQUNULG9DQUNBbUgsd0JBQXdCWCxPQUFPO1FBRWpDekcsUUFBUUMsR0FBRyxDQUNULG9CQUNBNkcsUUFBUVcsR0FBRyxDQUFDLENBQUNDLEdBQUdDLElBQU0sR0FBT0EsT0FBSkEsR0FBMkMsT0FBdkNBLE1BQU1YLGVBQWUsZUFBZTtRQUVuRWhILFFBQVFDLEdBQUcsQ0FBQztJQUNkLEdBQUc7UUFBQytHO1FBQWNGO0tBQVE7SUFFMUIsOENBQThDO0lBQzlDckssZ0RBQVNBLENBQUM7UUFDUm1MLE9BQU9MLFlBQVksR0FBR0E7UUFDdEIsT0FBTztZQUNMLE9BQU9LLE9BQU9MLFlBQVk7UUFDNUI7SUFDRixHQUFHO1FBQUNBO0tBQWE7SUFFakIscUNBQXFDO0lBQ3JDLE1BQU1NLGdCQUFnQnJMLGtEQUFXQSxDQUFDO1FBQ2hDLE9BQU9zTCxLQUFLQyxLQUFLLENBQ2ZELEtBQUtFLFNBQVMsQ0FBQztZQUNiM0c7WUFDQUk7WUFDQVE7WUFDQW1DO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0FFO1lBQ0F4QjtZQUNBRTtZQUNBRTtZQUNBdEIsaUJBQWlCQSxnQkFBZ0I2RSxHQUFHLENBQUMsQ0FBQ1EsTUFBUUEsSUFBSUMsSUFBSTtZQUN0RCxtREFBbUQ7WUFDbkRDLGNBQWNqRixhQUFhdUUsR0FBRyxDQUFDLENBQUNRLE1BQVM7b0JBQ3ZDQyxNQUFNRCxJQUFJQyxJQUFJO29CQUNkMUYsTUFBTXlGLElBQUl6RixJQUFJO29CQUNkSCxVQUFVNEYsSUFBSTVGLFFBQVEsQ0FBQytGLE9BQU87b0JBQzlCQyxVQUFVSixJQUFJSSxRQUFRLENBQUNELE9BQU87b0JBQzlCRSxPQUFPTCxJQUFJSyxLQUFLLENBQUNGLE9BQU87b0JBQ3hCRyxVQUFVTixJQUFJTSxRQUFRLEdBQ2xCO3dCQUNFbkcsTUFBTTZGLElBQUlNLFFBQVEsQ0FBQ25HLElBQUk7d0JBQ3ZCRyxPQUFPMEYsSUFBSU0sUUFBUSxDQUFDaEcsS0FBSyxHQUNyQjBGLElBQUlNLFFBQVEsQ0FBQ2hHLEtBQUssQ0FBQ2lHLFlBQVksS0FDL0I7d0JBQ0pDLFdBQVdSLElBQUlNLFFBQVEsQ0FBQ0UsU0FBUzt3QkFDakNDLFdBQVdULElBQUlNLFFBQVEsQ0FBQ0csU0FBUzt3QkFDakNDLFdBQVdWLElBQUlNLFFBQVEsQ0FBQ0ksU0FBUzt3QkFDakNDLG9CQUFvQlgsSUFBSU0sUUFBUSxDQUFDSyxrQkFBa0I7d0JBQ25ENUUsV0FBV2lFLElBQUlNLFFBQVEsQ0FBQ3ZFLFNBQVM7d0JBQ2pDNkUsYUFBYVosSUFBSU0sUUFBUSxDQUFDTSxXQUFXO3dCQUNyQ0MsU0FBU2IsSUFBSU0sUUFBUSxDQUFDTyxPQUFPO3dCQUM3QkMsaUJBQWlCZCxJQUFJTSxRQUFRLENBQUNRLGVBQWU7b0JBQy9DLElBQ0E7Z0JBQ047UUFDRjtJQUVKLEdBQUc7UUFDRDFIO1FBQ0FJO1FBQ0FRO1FBQ0FtQztRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBeEI7UUFDQUU7UUFDQUU7UUFDQXRCO1FBQ0FNO0tBQ0Q7SUFFRCwwRUFBMEU7SUFDMUUsTUFBTThGLHNCQUFzQnhNLGtEQUFXQSxDQUFDO1FBQ3RDLE9BQU87WUFDTHVGLFdBQVdtQixhQUFhdUUsR0FBRyxDQUFDLENBQUNRLE1BQVM7b0JBQ3BDQyxNQUFNRCxJQUFJQyxJQUFJO29CQUNkMUYsTUFBTXlGLElBQUl6RixJQUFJO29CQUNkK0YsVUFBVU4sSUFBSU0sUUFBUSxHQUNsQjt3QkFDRW5HLE1BQU02RixJQUFJTSxRQUFRLENBQUNuRyxJQUFJO3dCQUN2QkcsT0FBTzBGLElBQUlNLFFBQVEsQ0FBQ2hHLEtBQUssR0FDckIwRixJQUFJTSxRQUFRLENBQUNoRyxLQUFLLENBQUNpRyxZQUFZLEtBQy9CO3dCQUNKQyxXQUFXUixJQUFJTSxRQUFRLENBQUNFLFNBQVM7d0JBQ2pDQyxXQUFXVCxJQUFJTSxRQUFRLENBQUNHLFNBQVM7d0JBQ2pDQyxXQUFXVixJQUFJTSxRQUFRLENBQUNJLFNBQVM7d0JBQ2pDQyxvQkFBb0JYLElBQUlNLFFBQVEsQ0FBQ0ssa0JBQWtCO3dCQUNuRDVFLFdBQVdpRSxJQUFJTSxRQUFRLENBQUN2RSxTQUFTO3dCQUNqQzZFLGFBQWFaLElBQUlNLFFBQVEsQ0FBQ00sV0FBVzt3QkFDckNDLFNBQVNiLElBQUlNLFFBQVEsQ0FBQ08sT0FBTzt3QkFDN0JDLGlCQUFpQmQsSUFBSU0sUUFBUSxDQUFDUSxlQUFlO29CQUMvQyxJQUNBO2dCQUNOO1lBQ0FFLGdCQUFnQjtnQkFDZHhELFNBQVNMO2dCQUNUOEQsVUFBVTVEO1lBQ1o7WUFDQXJELFFBQVFBLE9BQU93RixHQUFHLENBQUMsQ0FBQzBCO29CQUdoQnhHO3VCQUgyQjtvQkFDN0IsR0FBR3dHLEtBQUs7b0JBQ1I5RyxVQUNFTSxFQUFBQSw4QkFBQUEsVUFBVThELE9BQU8sQ0FBQzBDLE1BQU1oSCxFQUFFLENBQUMsY0FBM0JRLGtEQUFBQSw0QkFBNkJOLFFBQVEsQ0FBQytGLE9BQU8sT0FBTWUsTUFBTTlHLFFBQVE7Z0JBQ3JFOztZQUNBK0csYUFBYTtnQkFDWEMsUUFBUWpGO2dCQUNSOUIsV0FBV3dDO2dCQUNYd0UsTUFBTXRFO2dCQUNOcUQsVUFBVW5EO2dCQUNWVjtnQkFDQUY7Z0JBQ0FNO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFDRDFCO1FBQ0FrQztRQUNBRTtRQUNBckQ7UUFDQW1DO1FBQ0FVO1FBQ0FFO1FBQ0FFO1FBQ0FWO1FBQ0FGO1FBQ0FNO0tBQ0Q7SUFFRCxxQ0FBcUM7SUFDckMsTUFBTTJFLG9CQUFvQi9NLGtEQUFXQSxDQUFDO1FBQ3BDLElBQUksQ0FBQ3VFLGdCQUFnQixPQUFPO1FBRTVCLE1BQU15SSxnQkFBZ0JSO1FBQ3RCLE1BQU1TLGNBQWM7WUFDbEIxSCxXQUFXaEIsZUFBZWdCLFNBQVMsSUFBSSxFQUFFO1lBQ3pDa0gsZ0JBQWdCbEksZUFBZWtJLGNBQWMsSUFBSTtnQkFDL0N4RCxTQUFTO2dCQUNUeUQsVUFBVSxDQUFDO1lBQ2I7WUFDQWpILFFBQVFsQixlQUFla0IsTUFBTSxJQUFJLEVBQUU7WUFDbkNtSCxhQUFhckksZUFBZXFJLFdBQVcsSUFBSSxDQUFDO1FBQzlDO1FBRUEsT0FBT3RCLEtBQUtFLFNBQVMsQ0FBQ3dCLG1CQUFtQjFCLEtBQUtFLFNBQVMsQ0FBQ3lCO0lBQzFELEdBQUc7UUFBQzFJO1FBQWdCaUk7S0FBb0I7SUFFeEMsaUNBQWlDO0lBQ2pDLE1BQU1VLGdCQUFnQmxOLGtEQUFXQSxDQUFDO1FBQ2hDLElBQUksQ0FBQ3VFLGdCQUFnQjtZQUNuQjFDLDBDQUFLQSxDQUFDNkIsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLE1BQU15SixnQkFBZ0IvQixPQUFPZ0MsT0FBTyxDQUNsQztRQUdGLElBQUlELGVBQWU7WUFDakJFLGdCQUFnQjlJO1lBQ2hCMUMsMENBQUtBLENBQUN5TCxPQUFPLENBQUM7UUFDaEI7SUFDRixHQUFHO1FBQUMvSTtLQUFlO0lBRW5CLHFEQUFxRDtJQUNyRHRFLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXFLLFFBQVFVLE1BQU0sS0FBSyxHQUFHO1lBQ3hCeEgsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTThKLGVBQWVsQztZQUNyQmQsV0FBVztnQkFBQ2dEO2FBQWE7WUFDekI5QyxnQkFBZ0I7UUFDbEI7SUFDRixHQUFHO1FBQUNILFFBQVFVLE1BQU07UUFBRUs7S0FBYztJQUVsQyxpQ0FBaUM7SUFDakMsTUFBTW1DLGVBQWV4TixrREFBV0EsQ0FBQztRQUMvQixJQUFJMEssb0JBQW9CVCxPQUFPLElBQUlVLHVCQUF1QlYsT0FBTyxFQUFFO1lBQ2pFekcsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLE1BQU1nSyxXQUFXcEM7UUFFakIsNkRBQTZEO1FBQzdELElBQUlmLFFBQVFVLE1BQU0sR0FBRyxLQUFLUixnQkFBZ0IsR0FBRztZQUMzQyxNQUFNa0QsZUFBZXBELE9BQU8sQ0FBQ0UsYUFBYTtZQUMxQyxJQUFJYyxLQUFLRSxTQUFTLENBQUNrQyxrQkFBa0JwQyxLQUFLRSxTQUFTLENBQUNpQyxXQUFXO2dCQUM3RGpLLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1FBQ0Y7UUFFQSxNQUFNa0ssYUFBYXJELFFBQVFzRCxLQUFLLENBQUMsR0FBR3BELGVBQWU7UUFDbkRELFdBQVc7ZUFBSW9EO1lBQVlGO1NBQVM7UUFDcEMsTUFBTUksV0FBV3JELGVBQWU7UUFDaENDLGdCQUFnQm9EO1FBQ2hCckssUUFBUUMsR0FBRyxDQUNULGlEQUNBb0ssVUFDQSxtQkFDQUYsV0FBVzNDLE1BQU0sR0FBRyxHQUNwQixhQUNBNkMsV0FBV0YsV0FBVzNDLE1BQU07SUFFaEMsR0FBRztRQUFDVjtRQUFTRTtRQUFjYTtLQUFjO0lBRXpDLE1BQU15Qyw4QkFBOEI1Tiw2Q0FBTUEsQ0FBQztJQUUzQyxvRkFBb0Y7SUFDcEYsTUFBTSxDQUFDZ0wsR0FBRzZDLGVBQWUsR0FBRzVOLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU02TixjQUFjaE8sa0RBQVdBLENBQUMsSUFBTStOLGVBQWUsQ0FBQ0UsSUFBTUEsSUFBSSxJQUFJLEVBQUU7SUFDdEUsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHaE8sK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDaU8saUJBQWlCQyxtQkFBbUIsR0FBR2xPLCtDQUFRQSxDQUFDO0lBRXZELE1BQU1tTyxnQkFBZ0J0TyxrREFBV0EsQ0FDL0IsQ0FBQ3VPO1FBQ0N6SixpQkFBaUJ5SixPQUFPMUosYUFBYTtRQUNyQ0ssaUJBQWlCcUosT0FBT3RKLGFBQWE7UUFDckNTLFVBQVU2SSxPQUFPOUksTUFBTTtRQUN2Qm9DLGFBQWEwRyxPQUFPM0csU0FBUztRQUM3QkcsV0FBV3dHLE9BQU96RyxPQUFPO1FBQ3pCRyxtQkFBbUJzRyxPQUFPdkcsZUFBZTtRQUN6Q0csb0JBQW9Cb0csT0FBT3JHLGdCQUFnQjtRQUMzQ0csY0FBY2tHLE9BQU9uRyxVQUFVO1FBQy9CRyxnQkFBZ0JnRyxPQUFPakcsWUFBWTtRQUNuQ0csV0FBVzhGLE9BQU8vRixPQUFPO1FBQ3pCRyxlQUFlNEYsT0FBTzdGLFdBQVc7UUFDakNHLHlCQUF5QjBGLE9BQU8zRixxQkFBcUI7UUFDckRHLDBCQUEwQndGLE9BQU96RixzQkFBc0I7UUFDdkR2QixZQUFZZ0gsT0FBT2pILFFBQVE7UUFDM0JHLGFBQWE4RyxPQUFPL0csU0FBUztRQUM3QkcsY0FBYzRHLE9BQU83RyxVQUFVO1FBRS9CLG1EQUFtRDtRQUNuRCxJQUFJNkcsT0FBTzVDLFlBQVksSUFBSWpGLGFBQWFzRSxNQUFNLEdBQUcsR0FBRztZQUNsRHVELE9BQU81QyxZQUFZLENBQUM2QyxPQUFPLENBQUMsQ0FBQ0M7Z0JBQzNCLE1BQU1oRCxNQUFNL0UsYUFBYWdJLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFakQsSUFBSSxLQUFLK0MsV0FBVy9DLElBQUk7Z0JBQy9ELElBQUlELEtBQUs7b0JBQ1Asb0JBQW9CO29CQUNwQkEsSUFBSTVGLFFBQVEsQ0FBQytJLFNBQVMsQ0FBQ0gsV0FBVzVJLFFBQVE7b0JBQzFDNEYsSUFBSUksUUFBUSxDQUFDK0MsU0FBUyxDQUFDSCxXQUFXNUMsUUFBUTtvQkFDMUNKLElBQUlLLEtBQUssQ0FBQzhDLFNBQVMsQ0FBQ0gsV0FBVzNDLEtBQUs7b0JBRXBDLDhCQUE4QjtvQkFDOUIsSUFBSTJDLFdBQVcxQyxRQUFRLElBQUlOLElBQUlNLFFBQVEsRUFBRTt3QkFDdkMsTUFBTUEsV0FBV04sSUFBSU0sUUFBUTt3QkFDN0IsSUFBSUEsU0FBU2hHLEtBQUssRUFBRTs0QkFDbEJnRyxTQUFTaEcsS0FBSyxDQUFDOEksTUFBTSxDQUFDQyxTQUFTTCxXQUFXMUMsUUFBUSxDQUFDaEcsS0FBSyxFQUFFO3dCQUM1RDt3QkFDQSxJQUFJMEksV0FBVzFDLFFBQVEsQ0FBQ0UsU0FBUyxLQUFLOEMsV0FBVzs0QkFDL0NoRCxTQUFTRSxTQUFTLEdBQUd3QyxXQUFXMUMsUUFBUSxDQUFDRSxTQUFTO3dCQUNwRDt3QkFDQSxJQUFJd0MsV0FBVzFDLFFBQVEsQ0FBQ0csU0FBUyxLQUFLNkMsV0FBVzs0QkFDL0NoRCxTQUFTRyxTQUFTLEdBQUd1QyxXQUFXMUMsUUFBUSxDQUFDRyxTQUFTO3dCQUNwRDt3QkFDQSxJQUFJdUMsV0FBVzFDLFFBQVEsQ0FBQ0ksU0FBUyxLQUFLNEMsV0FBVzs0QkFDL0NoRCxTQUFTSSxTQUFTLEdBQUdzQyxXQUFXMUMsUUFBUSxDQUFDSSxTQUFTO3dCQUNwRDt3QkFDQSxJQUFJc0MsV0FBVzFDLFFBQVEsQ0FBQ0ssa0JBQWtCLEtBQUsyQyxXQUFXOzRCQUN4RGhELFNBQVNLLGtCQUFrQixHQUN6QnFDLFdBQVcxQyxRQUFRLENBQUNLLGtCQUFrQjt3QkFDMUM7d0JBQ0EsSUFBSXFDLFdBQVcxQyxRQUFRLENBQUN2RSxTQUFTLEtBQUt1SCxXQUFXOzRCQUMvQ2hELFNBQVN2RSxTQUFTLEdBQUdpSCxXQUFXMUMsUUFBUSxDQUFDdkUsU0FBUzt3QkFDcEQ7d0JBQ0EsSUFBSWlILFdBQVcxQyxRQUFRLENBQUNNLFdBQVcsS0FBSzBDLFdBQVc7NEJBQ2pEaEQsU0FBU00sV0FBVyxHQUFHb0MsV0FBVzFDLFFBQVEsQ0FBQ00sV0FBVzt3QkFDeEQ7d0JBQ0EsSUFBSW9DLFdBQVcxQyxRQUFRLENBQUNPLE9BQU8sS0FBS3lDLFdBQVc7NEJBQzdDaEQsU0FBU08sT0FBTyxHQUFHbUMsV0FBVzFDLFFBQVEsQ0FBQ08sT0FBTzt3QkFDaEQ7d0JBQ0EsSUFBSW1DLFdBQVcxQyxRQUFRLENBQUNRLGVBQWUsS0FBS3dDLFdBQVc7NEJBQ3JEaEQsU0FBU1EsZUFBZSxHQUFHa0MsV0FBVzFDLFFBQVEsQ0FBQ1EsZUFBZTt3QkFDaEU7d0JBQ0FSLFNBQVNpRCxXQUFXLEdBQUc7b0JBQ3pCO2dCQUNGO1lBQ0Y7WUFDQSxrQ0FBa0M7WUFDbENoQjtZQUNBLDJFQUEyRTtZQUMzRSxJQUFJLENBQUN0RCxvQkFBb0JULE9BQU8sRUFBRTtnQkFDaENvRSxtQkFBbUIsQ0FBQ1ksSUFBTUEsSUFBSTtnQkFDOUJkLG9CQUFvQixDQUFDYyxJQUFNQSxJQUFJO1lBQ2pDO1FBQ0Y7UUFFQSx1RUFBdUU7UUFDdkVuQiw0QkFBNEI3RCxPQUFPLEdBQUdzRSxPQUFPbkksZUFBZSxJQUFJLEVBQUU7UUFDbEU1QyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DOEs7SUFDbEQsR0FDQTtRQUFDN0g7UUFBY3NIO1FBQWFLO1FBQW9CRjtLQUFvQjtJQUd0RSxnRUFBZ0U7SUFDaEVsTyxnREFBU0EsQ0FBQztRQUNSLElBQUk2Tiw0QkFBNEI3RCxPQUFPLElBQUl2RCxhQUFhc0UsTUFBTSxHQUFHLEdBQUc7WUFDbEUsTUFBTWtFLFFBQVFwQiw0QkFBNEI3RCxPQUFPO1lBQ2pELE1BQU1rRixjQUFjekksYUFBYTBJLE1BQU0sQ0FBQyxDQUFDM0QsTUFDdkN5RCxNQUFNRyxRQUFRLENBQUM1RCxJQUFJQyxJQUFJO1lBRXpCckYsbUJBQW1COEk7WUFDbkJyQiw0QkFBNEI3RCxPQUFPLEdBQUc7WUFDdEN6RyxRQUFRQyxHQUFHLENBQUMsNENBQTRDMEw7UUFDMUQ7SUFDRixHQUFHO1FBQUN6STtLQUFhO0lBRWpCLGNBQWM7SUFDZCxNQUFNNEksYUFBYXRQLGtEQUFXQSxDQUFDO1FBQzdCLGdDQUFnQztRQUNoQyxJQUFJNEssd0JBQXdCWCxPQUFPLEVBQUU7WUFDbkN6RyxRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUFELFFBQVFDLEdBQUcsQ0FDVCxzQ0FDQStHLGNBQ0EsbUJBQ0FGLFFBQVFVLE1BQU07UUFHaEIsSUFBSVIsZUFBZSxHQUFHO1lBQ3BCaEgsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQytHLGVBQWU7WUFFckUsd0NBQXdDO1lBQ3hDSSx3QkFBd0JYLE9BQU8sR0FBRztZQUNsQ1Msb0JBQW9CVCxPQUFPLEdBQUc7WUFDOUJVLHVCQUF1QlYsT0FBTyxHQUFHO1lBRWpDLE1BQU1zRixhQUFhakYsT0FBTyxDQUFDRSxlQUFlLEVBQUU7WUFDNUM4RCxjQUFjaUI7WUFDZDlFLGdCQUFnQkQsZUFBZTtZQUUvQix3Q0FBd0M7WUFDeENsSCxXQUFXO2dCQUNUb0gsb0JBQW9CVCxPQUFPLEdBQUc7Z0JBQzlCVSx1QkFBdUJWLE9BQU8sR0FBRztnQkFDakNXLHdCQUF3QlgsT0FBTyxHQUFHO2dCQUNsQ3pHLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEdBQUc7UUFDTCxPQUFPO1lBQ0xELFFBQVFDLEdBQUcsQ0FBQztRQUNkO0lBQ0YsR0FBRztRQUFDK0c7UUFBY0Y7UUFBU2dFO0tBQWM7SUFFekMsY0FBYztJQUNkLE1BQU1rQixhQUFheFAsa0RBQVdBLENBQUM7UUFDN0IsZ0NBQWdDO1FBQ2hDLElBQUk0Syx3QkFBd0JYLE9BQU8sRUFBRTtZQUNuQ3pHLFFBQVFDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQXNIO1FBQ0F2SCxRQUFRQyxHQUFHLENBQ1Qsc0NBQ0ErRyxjQUNBLG1CQUNBRixRQUFRVSxNQUFNO1FBR2hCLElBQUlSLGVBQWVGLFFBQVFVLE1BQU0sR0FBRyxHQUFHO1lBQ3JDeEgsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQytHLGVBQWU7WUFFckUsd0NBQXdDO1lBQ3hDSSx3QkFBd0JYLE9BQU8sR0FBRztZQUNsQ1Msb0JBQW9CVCxPQUFPLEdBQUc7WUFDOUJVLHVCQUF1QlYsT0FBTyxHQUFHO1lBRWpDLE1BQU13RixhQUFhbkYsT0FBTyxDQUFDRSxlQUFlLEVBQUU7WUFDNUNoSCxRQUFRQyxHQUFHLENBQUMsNkJBQTZCZ007WUFDekNuQixjQUFjbUI7WUFDZGhGLGdCQUFnQkQsZUFBZTtZQUUvQix3Q0FBd0M7WUFDeENsSCxXQUFXO2dCQUNUb0gsb0JBQW9CVCxPQUFPLEdBQUc7Z0JBQzlCVSx1QkFBdUJWLE9BQU8sR0FBRztnQkFDakNXLHdCQUF3QlgsT0FBTyxHQUFHO2dCQUNsQ3pHLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEdBQUc7UUFDTCxPQUFPO1lBQ0xELFFBQVFDLEdBQUcsQ0FBQztRQUNkO0lBQ0YsR0FBRztRQUFDK0c7UUFBY0Y7UUFBU2dFO1FBQWV2RDtLQUFhO0lBRXZELGtEQUFrRDtJQUNsRDlLLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlQLGVBQWU7WUFDbkIsSUFBSXRKLGdCQUFnQjRFLE1BQU0sR0FBRyxLQUFLdkYsT0FBT3VGLE1BQU0sR0FBRyxHQUFHO2dCQUNuRHdDLGFBQWE7b0JBQ1hwSDtvQkFDQVg7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsNkNBQTZDO1FBQzdDLE1BQU1rSyxZQUFZck0sV0FBV29NLGNBQWM7UUFDM0MsT0FBTyxJQUFNRSxhQUFhRDtJQUM1QixHQUFHO1FBQUN2SjtRQUFpQlg7UUFBUStIO0tBQWE7SUFFMUMsTUFBTXFDLG9CQUFvQjdQLGtEQUFXQSxDQUNuQyxDQUFDOFA7UUFDQ0EsRUFBRUMsZUFBZTtRQUVqQiw4RkFBOEY7UUFDOUYsSUFBSTdJLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUExRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CcU0sRUFBRUUsTUFBTSxDQUFDaEssSUFBSSxFQUFFOEosRUFBRUUsTUFBTTtRQUV0RCxJQUFJRixFQUFFRyxRQUFRLEVBQUU7WUFDZDVKLG1CQUFtQixDQUFDNko7Z0JBQ2xCLGlDQUFpQztnQkFDakMsTUFBTUMsWUFBWUMsTUFBTUMsT0FBTyxDQUFDSCxRQUFRQSxPQUFPO29CQUFDQTtpQkFBSyxDQUFDZCxNQUFNLENBQUNrQjtnQkFFN0QsSUFBSUgsVUFBVWQsUUFBUSxDQUFDUyxFQUFFRSxNQUFNLEdBQUc7b0JBQ2hDLE9BQU9HLFVBQVVmLE1BQU0sQ0FBQyxDQUFDM0QsTUFBUUEsUUFBUXFFLEVBQUVFLE1BQU07Z0JBQ25EO2dCQUNBLE9BQU87dUJBQUlHO29CQUFXTCxFQUFFRSxNQUFNO2lCQUFDO1lBQ2pDO1FBQ0YsT0FBTztZQUNMM0osbUJBQW1CO2dCQUFDeUosRUFBRUUsTUFBTTthQUFDO1FBQy9CO0lBQ0YsR0FDQTtRQUFDOUk7UUFBZ0JiO0tBQW1CO0lBR3RDLDRDQUE0QztJQUM1Q3BHLGdEQUFTQSxDQUFDO1FBQ1J1RCxRQUFRQyxHQUFHLENBQUMsNENBQTRDMkM7UUFDeEQ1QyxRQUFRQyxHQUFHLENBQ1QsMkNBQ0EyQyw0QkFBQUEsc0NBQUFBLGdCQUFpQjRFLE1BQU07SUFFM0IsR0FBRztRQUFDNUU7S0FBZ0I7SUFFcEIseUNBQXlDO0lBQ3pDbkcsZ0RBQVNBLENBQUM7UUFDUnVELFFBQVFDLEdBQUcsQ0FBQyx5Q0FBeUNpRDtRQUNyRGxELFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NpRCx5QkFBQUEsbUNBQUFBLGFBQWNzRSxNQUFNO1FBQ3hFeEgsUUFBUUMsR0FBRyxDQUNULHlDQUNBaUQseUJBQUFBLG1DQUFBQSxhQUFjdUUsR0FBRyxDQUFDLENBQUNRLE1BQVM7Z0JBQzFCekYsSUFBSSxFQUFFeUYsZ0JBQUFBLDBCQUFBQSxJQUFLekYsSUFBSTtnQkFDZkosSUFBSSxFQUFFNkYsZ0JBQUFBLDBCQUFBQSxJQUFLN0YsSUFBSTtnQkFDZjJLLFFBQVEsRUFBRTlFLGdCQUFBQSwwQkFBQUEsSUFBSzhFLFFBQVE7WUFDekI7SUFFSixHQUFHO1FBQUM3SjtLQUFhO0lBRWpCLE1BQU04SixzQkFBc0J4USxrREFBV0EsQ0FBQztRQUN0QywwRUFBMEU7UUFDMUUscUZBQXFGO1FBQ3JGLHVFQUF1RTtRQUN2RSxJQUFJa0gsZ0JBQWdCO1lBQ2xCLDRFQUE0RTtZQUM1RTtRQUNGO1FBQ0FiLG1CQUFtQixFQUFFO1FBQ3JCRSxpQkFBaUI7SUFDakIsdUdBQXVHO0lBQ3pHLEdBQUc7UUFBQ1c7UUFBZ0JDO0tBQWtCO0lBRXRDLHVDQUF1QztJQUN2Q2xILGdEQUFTQSxDQUFDO1FBQ1IsZ0VBQWdFO1FBQ2hFLE9BQU87WUFDTCxzQkFBc0I7WUFDdEIsSUFBSTZHLGtCQUFrQm1ELE9BQU8sSUFBSW5ELGtCQUFrQm1ELE9BQU8sQ0FBQ3dHLE1BQU0sRUFBRTtnQkFDakUzSixrQkFBa0JtRCxPQUFPLENBQUN3RyxNQUFNLENBQUNDLE1BQU0sQ0FBQzVKLGtCQUFrQm1ELE9BQU87WUFDbkU7UUFDRjtJQUNGLEdBQUcsRUFBRTtJQUVMaEssZ0RBQVNBLENBQUM7UUFDUiw0Q0FBNEM7UUFDNUMsTUFBTTBRLGVBQWVQLE1BQU1DLE9BQU8sQ0FBQ2pLLG1CQUMvQkEsa0JBQ0E7WUFBQ0E7U0FBZ0IsQ0FBQ2dKLE1BQU0sQ0FBQ2tCO1FBRTdCLE1BQU94SixrQkFBa0JtRCxPQUFPLENBQUNsRyxRQUFRLENBQUNpSCxNQUFNLEdBQUcsRUFBRztZQUNwRGxFLGtCQUFrQm1ELE9BQU8sQ0FBQ3lHLE1BQU0sQ0FBQzVKLGtCQUFrQm1ELE9BQU8sQ0FBQ2xHLFFBQVEsQ0FBQyxFQUFFO1FBQ3hFO1FBRUEsSUFBSTRNLGFBQWEzRixNQUFNLEdBQUcsR0FBRztZQUMzQixNQUFNNEYsU0FBUyxJQUFJdlEsMkNBQWE7WUFDaENzUSxhQUFhbkMsT0FBTyxDQUFDLENBQUMvQztnQkFDcEJtRixPQUFPQyxHQUFHLENBQUNwRixJQUFJNUYsUUFBUTtZQUN6QjtZQUNBK0ssT0FBT0UsWUFBWSxDQUFDSCxhQUFhM0YsTUFBTTtZQUV2Q2xFLGtCQUFrQm1ELE9BQU8sQ0FBQ3BFLFFBQVEsQ0FBQ2tMLElBQUksQ0FBQ0g7WUFFeENELGFBQWFuQyxPQUFPLENBQUMsQ0FBQy9DO2dCQUNwQixNQUFNdUYsUUFBUXZGLElBQUl1RixLQUFLO2dCQUN2QixxRUFBcUU7Z0JBQ3JFQSxNQUFNQyxRQUFRLENBQUMsQ0FBQ0M7b0JBQ2QsSUFBSUEsTUFBTUMsTUFBTSxFQUFFO3dCQUNoQkQsTUFBTUUsT0FBTyxHQUFHO29CQUNsQjtnQkFDRjtnQkFDQUosTUFBTW5MLFFBQVEsQ0FBQ3dMLEdBQUcsQ0FBQ1Q7Z0JBQ25COUosa0JBQWtCbUQsT0FBTyxDQUFDNEcsR0FBRyxDQUFDRztZQUNoQztRQUNGO0lBQ0YsR0FBRztRQUFDNUs7S0FBZ0I7SUFFcEIsTUFBTWtMLHlCQUF5QnRSLGtEQUFXQSxDQUFDO1FBQ3pDLDRDQUE0QztRQUM1QyxNQUFNMlEsZUFBZVAsTUFBTUMsT0FBTyxDQUFDakssbUJBQy9CQSxrQkFDQTtZQUFDQTtTQUFnQixDQUFDZ0osTUFBTSxDQUFDa0I7UUFFN0IsSUFBSUssYUFBYTNGLE1BQU0sR0FBRyxLQUFLbEUsa0JBQWtCbUQsT0FBTyxFQUFFO1lBQ3hEMEcsYUFBYW5DLE9BQU8sQ0FBQyxDQUFDL0MsS0FBSzhGO2dCQUN6QixJQUFJekssa0JBQWtCbUQsT0FBTyxDQUFDbEcsUUFBUSxDQUFDd04sTUFBTSxFQUFFO29CQUM3QyxNQUFNQyxXQUFXLElBQUluUiwyQ0FBYTtvQkFDbEN5RyxrQkFBa0JtRCxPQUFPLENBQUNsRyxRQUFRLENBQUN3TixNQUFNLENBQUNFLGdCQUFnQixDQUFDRDtvQkFDM0QvRixJQUFJNUYsUUFBUSxDQUFDa0wsSUFBSSxDQUFDUztvQkFFbEIvRixJQUFJSSxRQUFRLENBQUNrRixJQUFJLENBQUNqSyxrQkFBa0JtRCxPQUFPLENBQUM0QixRQUFRO29CQUNwREosSUFBSUssS0FBSyxDQUFDaUYsSUFBSSxDQUFDakssa0JBQWtCbUQsT0FBTyxDQUFDNkIsS0FBSztnQkFDaEQ7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDMUY7S0FBZ0I7SUFFcEIsTUFBTXNMLG9CQUFvQjFSLGtEQUFXQSxDQUFDLENBQUNtQztZQUdBQTtRQUZyQ3FCLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0N0QjtRQUNwRHFCLFFBQVFDLEdBQUcsQ0FBQyxlQUFldEIsTUFBTXlELElBQUk7UUFDckNwQyxRQUFRQyxHQUFHLENBQUMsMEJBQXlCdEIsa0JBQUFBLE1BQU00QixRQUFRLGNBQWQ1QixzQ0FBQUEsZ0JBQWdCNkksTUFBTTtRQUUzRHJFLGdCQUFnQixFQUFFO1FBQ2xCTixtQkFBbUIsRUFBRTtRQUVyQixNQUFNc0wsUUFBUXhRLGtGQUFtQkEsQ0FBQ2dCO1FBQ2xDaUQsY0FBY3VNO1FBQ2R2USxnRkFBaUJBLENBQUNlO1FBRWxCLE1BQU02SCxVQUFVLEVBQUU7UUFDbEIsTUFBTTRILGlCQUFpQixFQUFFO1FBRXpCelAsTUFBTThPLFFBQVEsQ0FBQyxDQUFDakI7WUFDZHhNLFFBQVFDLEdBQUcsQ0FDVCxzQkFDQXVNLE9BQU9oSyxJQUFJLEVBQ1hnSyxPQUFPcEssSUFBSSxFQUNYLFdBQ0FvSyxPQUFPbUIsTUFBTSxFQUNiLGFBQ0FuQixPQUFPTyxRQUFRO1lBR2pCLElBQUlQLE9BQU9tQixNQUFNLEVBQUU7Z0JBQ2pCLElBQUksQ0FBQ25CLE9BQU9oSyxJQUFJLEVBQUU7b0JBQ2hCZ0ssT0FBT2hLLElBQUksR0FBRyxRQUFrQixPQUFWZ0ssT0FBT3JLLEVBQUU7Z0JBQ2pDO2dCQUVBbkMsUUFBUUMsR0FBRyxDQUNULDJCQUNBdU0sT0FBT2hLLElBQUksRUFDWCxhQUNBZ0ssT0FBT08sUUFBUTtnQkFFakJ2RyxRQUFRNkgsSUFBSSxDQUFDN0I7Z0JBRWI0QixlQUFlQyxJQUFJLENBQUM7b0JBQ2xCbE0sSUFBSXFLLE9BQU90RSxJQUFJO29CQUNmMUYsTUFBTWdLLE9BQU9oSyxJQUFJO29CQUNqQkgsVUFBVW1LLE9BQU9uSyxRQUFRLENBQUNtTCxLQUFLO29CQUMvQm5GLFVBQVVtRSxPQUFPbkUsUUFBUSxDQUFDbUYsS0FBSztvQkFDL0JsRixPQUFPa0UsT0FBT2xFLEtBQUssQ0FBQ2tGLEtBQUs7b0JBQ3pCakYsVUFBVWlFLE9BQU9qRSxRQUFRLEdBQ3JCO3dCQUNFaEcsT0FBT2lLLE9BQU9qRSxRQUFRLENBQUNoRyxLQUFLLEdBQ3hCaUssT0FBT2pFLFFBQVEsQ0FBQ2hHLEtBQUssQ0FBQytMLE1BQU0sS0FDNUI7d0JBQ0o3RixXQUFXK0QsT0FBT2pFLFFBQVEsQ0FBQ0UsU0FBUzt3QkFDcENDLFdBQVc4RCxPQUFPakUsUUFBUSxDQUFDRyxTQUFTO3dCQUNwQzFFLFdBQVd3SSxPQUFPakUsUUFBUSxDQUFDdkUsU0FBUzt3QkFDcEM2RSxhQUFhMkQsT0FBT2pFLFFBQVEsQ0FBQ00sV0FBVzt3QkFDeENDLFNBQVMwRCxPQUFPakUsUUFBUSxDQUFDTyxPQUFPO29CQUNsQyxJQUNBO2dCQUNOO1lBQ0Y7UUFDRjtRQUVBOUksUUFBUUMsR0FBRyxDQUNULFNBQ0F1RyxRQUFRZ0IsTUFBTSxFQUNkLGlCQUNBaEIsUUFBUWlCLEdBQUcsQ0FBQyxDQUFDUSxNQUFTO2dCQUFFekYsTUFBTXlGLElBQUl6RixJQUFJO2dCQUFFdUssVUFBVTlFLElBQUk4RSxRQUFRO1lBQUM7UUFHakV4RyxxQkFBcUJFLE9BQU8sQ0FBQ0QsT0FBTyxHQUFHNEg7UUFDdkNqTCxnQkFBZ0JxRDtJQUNsQixHQUFHLEVBQUU7SUFFTCwwQ0FBMEM7SUFDMUMvSixnREFBU0EsQ0FBQztRQUNSLElBQUk0RSxrQkFBa0JFLG1CQUFtQjtZQUN2Qyw2Q0FBNkM7WUFDN0M0QixnQkFBZ0IsRUFBRTtZQUNsQk4sbUJBQW1CLEVBQUU7WUFDckJyQixxQkFBcUJIO1FBQ3ZCO0lBQ0YsR0FBRztRQUFDQTtRQUFlRTtLQUFrQjtJQUVyQyxNQUFNZ04sbUJBQW1CL1Isa0RBQVdBLENBQUMsQ0FBQ2dTLFNBQVNDO1FBQzdDeEwsd0JBQXdCLENBQUN5SixPQUFVO2dCQUNqQyxHQUFHQSxJQUFJO2dCQUNQLENBQUM4QixRQUFRLEVBQUU7dUJBQUs5QixJQUFJLENBQUM4QixRQUFRLElBQUksRUFBRTtvQkFBR0M7aUJBQVk7WUFDcEQ7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQyxnQkFBZ0JsUyxrREFBV0EsQ0FBQyxDQUFDZ1M7UUFDakN2TCx3QkFBd0IsQ0FBQ3lKO1lBQ3ZCLE1BQU01RixVQUFVNEYsSUFBSSxDQUFDOEIsUUFBUSxJQUFJLEVBQUU7WUFDbkMsSUFBSTFILFFBQVFVLE1BQU0sSUFBSSxHQUFHLE9BQU9rRjtZQUVoQyxNQUFNdkMsYUFBYXJELFFBQVFzRCxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQ3JDLE1BQU11RSxlQUFleEUsVUFBVSxDQUFDQSxXQUFXM0MsTUFBTSxHQUFHLEVBQUU7WUFFdEQsSUFBSTdFLFVBQVU4RCxPQUFPLENBQUMrSCxRQUFRLEVBQUU7Z0JBQzlCN0wsVUFBVThELE9BQU8sQ0FBQytILFFBQVEsQ0FBQ25NLFFBQVEsQ0FBQ2tMLElBQUksQ0FBQ29CO1lBQzNDO1lBRUEsT0FBTztnQkFDTCxHQUFHakMsSUFBSTtnQkFDUCxDQUFDOEIsUUFBUSxFQUFFckU7WUFDYjtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwxTixnREFBU0EsQ0FBQztRQUNSLE1BQU1tUyxnQkFBZ0IsQ0FBQ0M7WUFDckIsc0NBQXNDO1lBQ3RDLElBQUlBLE1BQU1DLE9BQU8sRUFBRTtnQkFDakIsSUFBSUQsTUFBTUUsR0FBRyxLQUFLLE9BQU8sQ0FBQ0YsTUFBTXBDLFFBQVEsRUFBRTtvQkFDeENvQyxNQUFNRyxjQUFjO29CQUNwQmxEO29CQUNBO2dCQUNGO2dCQUNBLElBQUkrQyxNQUFNRSxHQUFHLEtBQUssT0FBUUYsTUFBTUUsR0FBRyxLQUFLLE9BQU9GLE1BQU1wQyxRQUFRLEVBQUc7b0JBQzlEb0MsTUFBTUcsY0FBYztvQkFDcEJoRDtvQkFDQTtnQkFDRjtZQUNGO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUk2QyxNQUFNRSxHQUFHLEtBQUssWUFBWXJMLGdCQUFnQjtnQkFDNUNDLGtCQUFrQjtnQkFDbEI7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQixJQUFJa0wsTUFBTUMsT0FBTyxJQUFJRCxNQUFNRSxHQUFHLEtBQUssT0FBT2pNLGVBQWU7Z0JBQ3ZENEwsY0FBYzVMO1lBQ2hCO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUlGLGdCQUFnQjRFLE1BQU0sR0FBRyxHQUFHO2dCQUM5QixPQUFRcUgsTUFBTUUsR0FBRztvQkFDZixLQUFLO3dCQUNIMUwsaUJBQWlCO3dCQUNqQjtvQkFDRixLQUFLO3dCQUNIQSxpQkFBaUI7d0JBQ2pCO29CQUNGLEtBQUs7d0JBQ0hBLGlCQUFpQjt3QkFDakI7b0JBQ0YsS0FBSzt3QkFDSFIsbUJBQW1CLEVBQUU7d0JBQ3JCO2dCQUNKO1lBQ0Y7WUFFQSxvQkFBb0I7WUFDcEIsSUFBSWdNLE1BQU1DLE9BQU8sSUFBSUQsTUFBTUUsR0FBRyxLQUFLLEtBQUs7Z0JBQ3RDRixNQUFNRyxjQUFjO2dCQUNwQixJQUFJOUwsYUFBYXNFLE1BQU0sR0FBRyxHQUFHO29CQUMzQjNFLG1CQUFtQjsyQkFBSUs7cUJBQWE7Z0JBQ3RDO1lBQ0Y7UUFDRjtRQUVBMEUsT0FBT3FILGdCQUFnQixDQUFDLFdBQVdMO1FBQ25DLE9BQU8sSUFBTWhILE9BQU9zSCxtQkFBbUIsQ0FBQyxXQUFXTjtJQUNyRCxHQUFHO1FBQ0Q5TDtRQUNBNEw7UUFDQXhMO1FBQ0FOLGdCQUFnQjRFLE1BQU07UUFDdEJzRTtRQUNBRTtRQUNBdEk7UUFDQUM7S0FDRDtJQUVEbEgsZ0RBQVNBLENBQUM7UUFDUixPQUFPO1lBQ0wsSUFBSWdGLGVBQWU7Z0JBQ2pCME4sSUFBSUMsZUFBZSxDQUFDM047WUFDdEI7WUFDQSxJQUFJbUQsWUFBWTtnQkFDZHVLLElBQUlDLGVBQWUsQ0FBQ3hLO1lBQ3RCO1FBQ0Y7SUFDRixHQUFHO1FBQUNuRDtRQUFlbUQ7S0FBVztJQUU5QixNQUFNeUssbUJBQW1CN1Msa0RBQVdBLENBQUMsQ0FBQzhTO1FBQ3BDN1IsNkVBQWFBLENBQUNrSixpQkFBaUJGLE9BQU8sRUFBRTZJO0lBQzFDLEdBQUcsRUFBRTtJQUVMLDBEQUEwRDtJQUMxRCxNQUFNQyxXQUFXN1MsNkNBQU1BLENBQUM7SUFFeEIsTUFBTSxDQUFDOFMsaUJBQWlCQyxtQkFBbUIsR0FBRzlTLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQytTLG9CQUFvQkMsc0JBQXNCLEdBQUdoVCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNaVQsYUFBYWxULDZDQUFNQTtJQUV6Qix5QkFBeUI7SUFDekIsTUFBTW1ULGdCQUFnQnJULGtEQUFXQSxDQUMvQixDQUFDNEM7UUFDQyxJQUFJLENBQUNvUSxtQkFBbUIsQ0FBQzdJLGlCQUFpQkYsT0FBTyxFQUFFO1lBQ2pEO1FBQ0Y7UUFFQSx3Q0FBd0M7UUFDeEMsTUFBTXFKLFlBQVlqUywrRUFBb0JBLENBQUNxTixJQUFJLENBQ3pDLENBQUM2RSxJQUFNQSxFQUFFNU4sRUFBRSxLQUFLcU47UUFFbEIsSUFBSSxDQUFDTSxXQUFXO1FBRWhCLElBQUlKLHVCQUF1QixHQUFHO1lBQzVCQyxzQkFBc0J2UTtRQUN4QjtRQUVBLE1BQU00USxVQUFVNVEsWUFBWXNRO1FBQzVCLE1BQU1PLFdBQVcsVUFBV0gsVUFBVUksUUFBUSxHQUFJSixVQUFVSSxRQUFRO1FBRXBFLHVFQUF1RTtRQUN2RSxNQUFNQyxTQUFTLElBQUl0VCwyQ0FBYSxDQUFDLEdBQUcsR0FBRztRQUN2QyxJQUFJK0YsZ0JBQWdCNEUsTUFBTSxHQUFHLEdBQUc7WUFDOUIsdUNBQXVDO1lBQ3ZDLE1BQU0yRixlQUFlUCxNQUFNQyxPQUFPLENBQUNqSyxtQkFDL0JBLGtCQUNBO2dCQUFDQTthQUFnQixDQUFDZ0osTUFBTSxDQUFDa0I7WUFFN0IsSUFBSUssYUFBYTNGLE1BQU0sR0FBRyxHQUFHO2dCQUMzQixNQUFNNEYsU0FBUyxJQUFJdlEsMkNBQWE7Z0JBQ2hDc1EsYUFBYW5DLE9BQU8sQ0FBQyxDQUFDL0M7b0JBQ3BCbUYsT0FBT0MsR0FBRyxDQUFDcEYsSUFBSTVGLFFBQVE7Z0JBQ3pCO2dCQUNBK0ssT0FBT0UsWUFBWSxDQUFDSCxhQUFhM0YsTUFBTTtnQkFDdkMySSxPQUFPNUMsSUFBSSxDQUFDSDtZQUNkO1FBQ0Y7UUFFQSw4Q0FBOEM7UUFDOUMwQyxVQUFVTSxPQUFPLENBQ2Z6SixpQkFBaUJGLE9BQU8sQ0FBQytGLE1BQU0sRUFDL0I3RixpQkFBaUJGLE9BQU8sRUFDeEJ3SixVQUNBRTtRQUdGUCxXQUFXbkosT0FBTyxHQUFHNEosc0JBQXNCUjtJQUM3QyxHQUNBO1FBQUNMO1FBQWlCN0k7UUFBa0IrSTtRQUFvQjlNO0tBQWdCO0lBRzFFLHVCQUF1QjtJQUN2Qm5HLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSStTLGlCQUFpQjtZQUNuQkcsc0JBQXNCO1lBQ3RCQyxXQUFXbkosT0FBTyxHQUFHNEosc0JBQXNCUjtZQUUzQywwQ0FBMEM7WUFDMUMsSUFBSWxKLGlCQUFpQkYsT0FBTyxFQUFFO2dCQUM1QkUsaUJBQWlCRixPQUFPLENBQUNoQixPQUFPLEdBQUc7WUFDckM7UUFDRixPQUFPO1lBQ0wsZ0RBQWdEO1lBQ2hELElBQUlrQixpQkFBaUJGLE9BQU8sSUFBSTdELGdCQUFnQjRFLE1BQU0sS0FBSyxHQUFHO2dCQUM1RGIsaUJBQWlCRixPQUFPLENBQUNoQixPQUFPLEdBQUc7WUFDckM7UUFDRjtRQUVBLE9BQU87WUFDTCxJQUFJbUssV0FBV25KLE9BQU8sRUFBRTtnQkFDdEI2SixxQkFBcUJWLFdBQVduSixPQUFPO1lBQ3pDO1FBQ0Y7SUFDRixHQUFHO1FBQUMrSTtRQUFpQks7UUFBZWpOO0tBQWdCO0lBRXBELDBDQUEwQztJQUMxQyxNQUFNMk4sc0JBQXNCL1Qsa0RBQVdBLENBQUMsQ0FBQ2dVO1FBQ3ZDZixtQkFBbUJlO0lBQ3JCLEdBQUcsRUFBRTtJQUVMLE1BQU1DLHVCQUF1QjtRQUMzQixPQUFPck07SUFDVDtJQUVBLGlCQUFpQjtJQUNqQixNQUFNc00sY0FBY2xVLGtEQUFXQSxDQUFDO1FBQzlCLGlEQUFpRDtRQUNqRCxNQUFNbVUsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1FBQ3hDLElBQUlGLFlBQVk7WUFDZCxNQUFNRyxlQUFlbEosT0FBT2dDLE9BQU8sQ0FDakM7WUFFRixJQUFJLENBQUNrSCxjQUFjO2dCQUNqQjtZQUNGO1lBQ0EsMENBQTBDO1lBQzFDRixhQUFhRyxVQUFVLENBQUM7UUFDMUI7UUFFQS9RLFFBQVFDLEdBQUcsQ0FBQztRQUNaa0QsZ0JBQWdCLEVBQUU7UUFDbEJOLG1CQUFtQixFQUFFO1FBRXJCLHdDQUF3QztRQUN4Q1gsVUFBVTtZQUNSO2dCQUNFQyxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxVQUFVO29CQUFDO29CQUFLO29CQUFLO2lCQUFFO2dCQUN2QkMsV0FBVztnQkFDWEMsT0FBTztnQkFDUEMsTUFBTTtnQkFDTkMsWUFBWTtnQkFDWkMsZUFBZTtZQUNqQjtZQUNBO2dCQUNFUCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxVQUFVO29CQUFDO29CQUFHO29CQUFHO2lCQUFFO2dCQUNuQkMsV0FBVztnQkFDWEMsT0FBTztnQkFDUEMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VMLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLFVBQVU7b0JBQUMsQ0FBQztvQkFBRztvQkFBRyxDQUFDO2lCQUFFO2dCQUNyQkMsV0FBVztnQkFDWEMsT0FBTztnQkFDUEMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VMLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLFVBQVU7b0JBQUM7b0JBQUc7b0JBQUcsQ0FBQztpQkFBRTtnQkFDcEJDLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtTQUNEO1FBRUQsNkJBQTZCO1FBQzdCNkIsYUFBYTtRQUNiRSxXQUFXO1FBQ1hFLG1CQUFtQjtRQUNuQkUsb0JBQW9CO1FBQ3BCRSxjQUFjO1FBQ2RFLGdCQUFnQjtRQUNoQkUsV0FBVztRQUNYRSxlQUFlO1FBRWYsaUNBQWlDO1FBQ2pDRSx5QkFBeUI7UUFDekJFLDBCQUEwQjtZQUN4QkMsV0FBVztnQkFDVEMsU0FBUztnQkFDVEMsWUFBWTtZQUNkO1lBQ0FDLE9BQU87Z0JBQ0xGLFNBQVM7Z0JBQ1RuRCxXQUFXO2dCQUNYc0QsV0FBVztnQkFDWEMsUUFBUTtZQUNWO1lBQ0FDLEtBQUs7Z0JBQ0hMLFNBQVM7Z0JBQ1RNLGVBQWU7Z0JBQ2ZDLFVBQVU7Z0JBQ1ZOLFlBQVk7WUFDZDtZQUNBTyxVQUFVO2dCQUNSUixTQUFTO2dCQUNUUyxVQUFVO2dCQUNWQyxRQUFRO1lBQ1Y7UUFDRjtRQUVBLHVCQUF1QjtRQUN2QnBDLFlBQVk7UUFDWkUsYUFBYTtRQUNiRSxjQUFjO1FBRWQsd0JBQXdCO1FBQ3hCLElBQUl3QyxpQkFBaUJGLE9BQU8sRUFBRTtZQUM1QkUsaUJBQWlCRixPQUFPLENBQUMrRixNQUFNLENBQUNuSyxRQUFRLENBQUMyTyxHQUFHLENBQUMsR0FBRyxHQUFHO1lBQ25EckssaUJBQWlCRixPQUFPLENBQUMwSixNQUFNLENBQUNhLEdBQUcsQ0FBQyxHQUFHLEdBQUc7WUFDMUNySyxpQkFBaUJGLE9BQU8sQ0FBQ3dLLE1BQU07UUFDakM7UUFFQSx3QkFBd0I7UUFDeEI1TixpQkFBaUI7UUFDakJOLGlCQUFpQjtRQUVqQix5QkFBeUI7UUFDekJtTyxvQkFBb0I7UUFDcEJDLHVCQUF1QjtRQUN2QkMsc0JBQXNCO1FBRXRCLGdCQUFnQjtRQUNoQnJLLFdBQVcsRUFBRTtRQUNiRSxnQkFBZ0IsQ0FBQztRQUNqQmhFLHdCQUF3QixDQUFDO1FBRXpCLG9CQUFvQjtRQUNwQnJCLGNBQWM7WUFDWkMsVUFBVTtZQUNWQyxXQUFXO1lBQ1hDLFdBQVc7WUFDWEMsWUFBWTtRQUNkO1FBRUEsd0JBQXdCO1FBQ3hCeUIsaUJBQWlCO1FBRWpCLHlCQUF5QjtRQUN6QmdNLG1CQUFtQjtRQUNuQkUsc0JBQXNCO1FBQ3RCLElBQUlDLFdBQVduSixPQUFPLEVBQUU7WUFDdEI2SixxQkFBcUJWLFdBQVduSixPQUFPO1FBQ3pDO1FBRUEsK0NBQStDO1FBQy9DLElBQUloRixlQUFlO1lBQ2pCME4sSUFBSUMsZUFBZSxDQUFDM047UUFDdEI7UUFDQSxJQUFJbUQsWUFBWTtZQUNkdUssSUFBSUMsZUFBZSxDQUFDeEs7UUFDdEI7UUFFQSw4REFBOEQ7UUFDOUR0RCxpQkFBaUI7UUFDakIsbURBQW1EO1FBQ25EeEIsV0FBVztZQUNUd0IsaUJBQWlCO1FBQ25CLEdBQUc7SUFDTCxHQUFHLEVBQUU7SUFFTCxNQUFNK1AsZ0JBQWdCM1UsNkNBQU1BLENBQUM7SUFFN0IsTUFBTTRVLG9CQUFvQjlVLGtEQUFXQSxDQUFDO1FBQ3BDLElBQUk2VSxjQUFjNUssT0FBTyxFQUFFO1lBQ3pCekcsUUFBUUMsR0FBRyxDQUFDO1lBQ1pvUixjQUFjNUssT0FBTztRQUN2QixPQUFPO1lBQ0x6RyxRQUFRRSxLQUFLLENBQUM7UUFDaEI7SUFDRixHQUFHLEVBQUU7SUFFTCw4Q0FBOEM7SUFDOUN6RCxnREFBU0EsQ0FBQztRQUNSLE1BQU04VSxpQkFBaUI7WUFDckIsTUFBTVosYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLElBQUlGLFlBQVk7Z0JBQ2QsSUFBSTtvQkFDRixNQUFNYSxjQUFjMUosS0FBS0MsS0FBSyxDQUFDNEk7b0JBQy9CM1EsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnVSO29CQUVwQyxvREFBb0Q7b0JBQ3BELElBQUksQ0FBQ2pRLG1CQUFtQjt3QkFDdEJ2QixRQUFRQyxHQUFHLENBQ1QsdUNBQ0F1UixZQUFZQyxLQUFLO3dCQUVuQm5RLGlCQUFpQixhQUFhLG9DQUFvQzt3QkFFbEUsaURBQWlEO3dCQUNqRFksVUFBVXNQLFlBQVl2UCxNQUFNO3dCQUM1Qm9DLGFBQWFtTixZQUFZcEksV0FBVyxDQUFDQyxNQUFNO3dCQUMzQzlFLFdBQVdpTixZQUFZcEksV0FBVyxDQUFDOUUsT0FBTzt3QkFDMUNHLG1CQUFtQitNLFlBQVlwSSxXQUFXLENBQUM1RSxlQUFlO3dCQUMxREcsb0JBQW9CNk0sWUFBWXBJLFdBQVcsQ0FBQzFFLGdCQUFnQjt3QkFDNURLLGdCQUFnQnlNLFlBQVlwSSxXQUFXLENBQUM5RyxTQUFTO3dCQUNqRDJDLFdBQVd1TSxZQUFZcEksV0FBVyxDQUFDRSxJQUFJO3dCQUN2Q25FLGVBQWVxTSxZQUFZcEksV0FBVyxDQUFDZixRQUFRO3dCQUMvQ2hELHlCQUF5Qm1NLFlBQVl2SSxjQUFjLENBQUN4RCxPQUFPO3dCQUMzREYsMEJBQTBCaU0sWUFBWXZJLGNBQWMsQ0FBQ0MsUUFBUTt3QkFDN0RuRixZQUFZeU4sWUFBWUUsYUFBYSxDQUFDNU4sUUFBUTt3QkFDOUNHLGFBQWF1TixZQUFZRSxhQUFhLENBQUMxTixTQUFTO3dCQUNoREcsY0FBY3FOLFlBQVlFLGFBQWEsQ0FBQ3hOLFVBQVU7d0JBRWxELHdFQUF3RTt3QkFDeEVwRSxXQUFXOzRCQUNULElBQUk2RyxpQkFBaUJGLE9BQU8sSUFBSStLLFlBQVk1UyxNQUFNLEVBQUU7Z0NBQ2xEK0gsaUJBQWlCRixPQUFPLENBQUMrRixNQUFNLENBQUNuSyxRQUFRLENBQUMrSSxTQUFTLENBQ2hEb0csWUFBWTVTLE1BQU0sQ0FBQ3lELFFBQVE7Z0NBRTdCc0UsaUJBQWlCRixPQUFPLENBQUMwSixNQUFNLENBQUMvRSxTQUFTLENBQ3ZDb0csWUFBWTVTLE1BQU0sQ0FBQ3VSLE1BQU07Z0NBRTNCeEosaUJBQWlCRixPQUFPLENBQUN3SyxNQUFNOzRCQUNqQzt3QkFDRixHQUFHO29CQUNMLE9BQU8sSUFBSTlQLFVBQVVzRixPQUFPLEVBQUU7d0JBQzVCLHdEQUF3RDt3QkFDeER6RyxRQUFRQyxHQUFHLENBQUM7d0JBQ1o0SixnQkFBZ0IySDtvQkFDbEI7b0JBRUEseUNBQXlDO29CQUN6QyxNQUFNRyxtQkFBbUI7d0JBQ3ZCLElBQUksQ0FBQ0gsWUFBWXpQLFNBQVMsSUFBSSxDQUFDbUIsYUFBYXNFLE1BQU0sRUFBRTs0QkFDbER4SCxRQUFRQyxHQUFHLENBQUM7NEJBQ1o7d0JBQ0Y7d0JBRUFELFFBQVFDLEdBQUcsQ0FDVCxnREFDQWlEO3dCQUVGbEQsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQnVSLFlBQVl6UCxTQUFTO3dCQUVyRCxNQUFNNlAsb0JBQW9CLElBQUlDLElBQzVCTCxZQUFZelAsU0FBUyxDQUFDMEYsR0FBRyxDQUFDLENBQUNxSyxJQUFNO2dDQUFDQSxFQUFFdFAsSUFBSTtnQ0FBRXNQLEVBQUV2SixRQUFROzZCQUFDO3dCQUd2RHJGLGFBQWE4SCxPQUFPLENBQUMsQ0FBQy9DOzRCQUNwQixJQUFJQSxJQUFJTSxRQUFRLElBQUlxSixrQkFBa0JHLEdBQUcsQ0FBQzlKLElBQUl6RixJQUFJLEdBQUc7Z0NBQ25ELE1BQU13UCxnQkFBZ0JKLGtCQUFrQkssR0FBRyxDQUFDaEssSUFBSXpGLElBQUk7Z0NBQ3BEeEMsUUFBUUMsR0FBRyxDQUNULGtDQUNBZ0ksSUFBSXpGLElBQUksRUFDUndQO2dDQUVGdFUsdUZBQXFCQSxDQUFDdUssS0FBSytKOzRCQUM3QixPQUFPO2dDQUNMaFMsUUFBUUMsR0FBRyxDQUNULDZDQUNBZ0ksSUFBSXpGLElBQUk7NEJBRVo7d0JBQ0Y7b0JBQ0Y7b0JBRUEsZ0RBQWdEO29CQUNoRCxNQUFNMFAsZ0JBQWdCQyxZQUFZO3dCQUNoQyxJQUFJalAsYUFBYXNFLE1BQU0sR0FBRyxHQUFHOzRCQUMzQjRLLGNBQWNGOzRCQUNkLHFFQUFxRTs0QkFDckVwUyxXQUFXNlIsa0JBQWtCO3dCQUMvQjtvQkFDRixHQUFHO29CQUVILDRFQUE0RTtvQkFDNUU3UixXQUFXO3dCQUNUc1MsY0FBY0Y7b0JBQ2hCLEdBQUc7b0JBRUgsd0VBQXdFO29CQUN4RXBTLFdBQVc7d0JBQ1QsSUFBSTZHLGlCQUFpQkYsT0FBTyxJQUFJK0ssWUFBWTVTLE1BQU0sRUFBRTs0QkFDbEQrSCxpQkFBaUJGLE9BQU8sQ0FBQytGLE1BQU0sQ0FBQ25LLFFBQVEsQ0FBQytJLFNBQVMsQ0FDaERvRyxZQUFZNVMsTUFBTSxDQUFDeUQsUUFBUTs0QkFFN0JzRSxpQkFBaUJGLE9BQU8sQ0FBQzBKLE1BQU0sQ0FBQy9FLFNBQVMsQ0FDdkNvRyxZQUFZNVMsTUFBTSxDQUFDdVIsTUFBTTs0QkFFM0J4SixpQkFBaUJGLE9BQU8sQ0FBQ3dLLE1BQU07d0JBQ2pDO29CQUNGLEdBQUc7Z0JBQ0wsRUFBRSxPQUFPL1EsT0FBTztvQkFDZEYsUUFBUUUsS0FBSyxDQUFDLDhCQUE4QkE7Z0JBQzlDO1lBQ0Y7UUFDRjtRQUVBLHdDQUF3QztRQUN4Q3FSO1FBRUEsdURBQXVEO1FBQ3ZELE1BQU1jLGtCQUFrQkYsWUFBWTtZQUNsQyxJQUFJdkwsU0FBU0gsT0FBTyxJQUFJRSxpQkFBaUJGLE9BQU8sRUFBRTtnQkFDaEQyTCxjQUFjQztnQkFDZGQ7WUFDRjtRQUNGLEdBQUc7UUFFSCxPQUFPLElBQU1hLGNBQWNDO0lBQzdCLEdBQUc7UUFBQ25QO1FBQWM3QjtLQUFjO0lBRWhDLE1BQU0sQ0FBQ2lSLGFBQWFDLGVBQWUsR0FBRzVWLCtDQUFRQSxDQUFDO0lBRS9DRixnREFBU0EsQ0FBQztRQUNSLElBQUl5RyxhQUFhc0UsTUFBTSxHQUFHLEtBQUs4SyxlQUFlQSxZQUFZdlEsU0FBUyxFQUFFO1lBQ25FLE1BQU15USxpQkFBaUI7Z0JBQ3JCeFMsUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q2lEO2dCQUNwRGxELFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NxUyxZQUFZdlEsU0FBUztnQkFDbkVtQixhQUFhOEgsT0FBTyxDQUFDLENBQUMvQztvQkFDcEIsTUFBTXdLLGlCQUFpQkgsWUFBWXZRLFNBQVMsQ0FBQ21KLElBQUksQ0FDL0MsQ0FBQzRHLElBQU1BLEVBQUV0UCxJQUFJLEtBQUt5RixJQUFJekYsSUFBSSxJQUFJc1AsRUFBRTNQLEVBQUUsS0FBSzhGLElBQUlDLElBQUk7b0JBRWpELElBQUl1SyxrQkFBa0JBLGVBQWVsSyxRQUFRLEVBQUU7d0JBQzdDdkksUUFBUUMsR0FBRyxDQUNULHFDQUNBZ0ksSUFBSXpGLElBQUksRUFDUmlRLGVBQWVsSyxRQUFRO3dCQUV6QjdLLHVGQUFxQkEsQ0FBQ3VLLEtBQUt3SyxlQUFlbEssUUFBUTtvQkFDcEQsT0FBTzt3QkFDTHZJLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NnSSxJQUFJekYsSUFBSTtvQkFDOUQ7Z0JBQ0Y7WUFDRjtZQUNBLHFFQUFxRTtZQUNyRSxNQUFNMkosWUFBWXJNLFdBQVcwUyxnQkFBZ0I7WUFDN0MsT0FBTyxJQUFNcEcsYUFBYUQ7UUFDNUI7SUFDRixHQUFHO1FBQUNqSjtRQUFjb1A7S0FBWTtJQUU5QixNQUFNekksa0JBQWtCLENBQUNrQjtRQUN2QixJQUFJO1lBQ0YvSyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDOEs7WUFFNUMsOEJBQThCO1lBQzlCNUosVUFBVXNGLE9BQU8sR0FBR3NFO1lBQ3BCM0osaUJBQWlCcUYsT0FBTyxHQUFHc0UsUUFBUSxvREFBb0Q7WUFDdkZ3SCxlQUFleEgsU0FBUyw4QkFBOEI7WUFFdEQsaURBQWlEO1lBQ2pENUgsZ0JBQWdCLEVBQUU7WUFDbEJOLG1CQUFtQixFQUFFO1lBQ3JCckIscUJBQXFCO1lBRXJCLHlCQUF5QjtZQUN6QkYsaUJBQWlCO1lBQ2pCWSxVQUFVNkksT0FBTzlJLE1BQU07WUFDdkJvQyxhQUFhMEcsT0FBTzNCLFdBQVcsQ0FBQ0MsTUFBTTtZQUN0QzlFLFdBQVd3RyxPQUFPM0IsV0FBVyxDQUFDOUUsT0FBTztZQUNyQ0csbUJBQW1Cc0csT0FBTzNCLFdBQVcsQ0FBQzVFLGVBQWU7WUFDckRHLG9CQUFvQm9HLE9BQU8zQixXQUFXLENBQUMxRSxnQkFBZ0I7WUFDdkRLLGdCQUFnQmdHLE9BQU8zQixXQUFXLENBQUM5RyxTQUFTO1lBQzVDMkMsV0FBVzhGLE9BQU8zQixXQUFXLENBQUNFLElBQUk7WUFDbENuRSxlQUFlNEYsT0FBTzNCLFdBQVcsQ0FBQ2YsUUFBUTtZQUMxQ2hELHlCQUF5QjBGLE9BQU85QixjQUFjLENBQUN4RCxPQUFPO1lBQ3RERiwwQkFBMEJ3RixPQUFPOUIsY0FBYyxDQUFDQyxRQUFRO1lBQ3hEbkYsWUFBWWdILE9BQU8yRyxhQUFhLENBQUM1TixRQUFRO1lBQ3pDRyxhQUFhOEcsT0FBTzJHLGFBQWEsQ0FBQzFOLFNBQVM7WUFDM0NHLGNBQWM0RyxPQUFPMkcsYUFBYSxDQUFDeE4sVUFBVTtZQUU3QywrREFBK0Q7WUFDL0RwRSxXQUFXO2dCQUNULElBQUk2RyxpQkFBaUJGLE9BQU8sSUFBSXNFLE9BQU9uTSxNQUFNLEVBQUU7b0JBQzdDK0gsaUJBQWlCRixPQUFPLENBQUMrRixNQUFNLENBQUNuSyxRQUFRLENBQUMrSSxTQUFTLENBQ2hETCxPQUFPbk0sTUFBTSxDQUFDeUQsUUFBUTtvQkFFeEJzRSxpQkFBaUJGLE9BQU8sQ0FBQzBKLE1BQU0sQ0FBQy9FLFNBQVMsQ0FBQ0wsT0FBT25NLE1BQU0sQ0FBQ3VSLE1BQU07b0JBQzlEeEosaUJBQWlCRixPQUFPLENBQUN3SyxNQUFNO2dCQUNqQztZQUNGLEdBQUc7WUFFSCxpREFBaUQ7WUFDakQsTUFBTVUsbUJBQW1CO2dCQUN2QixJQUFJLENBQUM1RyxPQUFPaEosU0FBUyxJQUFJLENBQUNtQixhQUFhc0UsTUFBTSxFQUFFO29CQUM3Q3hILFFBQVFDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRjtnQkFDQUQsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2lEO2dCQUNoRCxNQUFNME8sb0JBQW9CLElBQUlDLElBQzVCOUcsT0FBT2hKLFNBQVMsQ0FBQzBGLEdBQUcsQ0FBQyxDQUFDcUssSUFBTTt3QkFBQ0EsRUFBRXRQLElBQUk7d0JBQUVzUCxFQUFFdkosUUFBUTtxQkFBQztnQkFFbERyRixhQUFhOEgsT0FBTyxDQUFDLENBQUMvQztvQkFDcEIsSUFBSUEsSUFBSU0sUUFBUSxJQUFJcUosa0JBQWtCRyxHQUFHLENBQUM5SixJQUFJekYsSUFBSSxHQUFHO3dCQUNuRCxNQUFNd1AsZ0JBQWdCSixrQkFBa0JLLEdBQUcsQ0FBQ2hLLElBQUl6RixJQUFJO3dCQUNwRHhDLFFBQVFDLEdBQUcsQ0FDVCxrQ0FDQWdJLElBQUl6RixJQUFJLEVBQ1J3UDt3QkFFRnRVLHVGQUFxQkEsQ0FBQ3VLLEtBQUsrSjtvQkFDN0IsT0FBTzt3QkFDTGhTLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkNnSSxJQUFJekYsSUFBSTtvQkFDbkU7Z0JBQ0Y7WUFDRjtZQUNBLElBQUlVLGFBQWFzRSxNQUFNLEdBQUcsR0FBRztnQkFDM0IxSCxXQUFXNlIsa0JBQWtCO1lBQy9CLE9BQU87Z0JBQ0wsTUFBTU8sZ0JBQWdCQyxZQUFZO29CQUNoQyxJQUFJalAsYUFBYXNFLE1BQU0sR0FBRyxHQUFHO3dCQUMzQjRLLGNBQWNGO3dCQUNkcFMsV0FBVzZSLGtCQUFrQjtvQkFDL0I7Z0JBQ0YsR0FBRztnQkFDSDdSLFdBQVc7b0JBQ1RzUyxjQUFjRjtnQkFDaEIsR0FBRztZQUNMO1FBQ0YsRUFBRSxPQUFPaFMsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsc0NBQXNDQTtRQUN0RDtJQUNGO0lBRUEsTUFBTXdTLGtCQUFrQixPQUFPQyxXQUFXQyxhQUFhQztRQUNyRCw4RkFBOEY7UUFDOUYsSUFBSXJCO1FBQ0osSUFBSXBRLGlCQUFpQnFGLE9BQU8sRUFBRTtZQUM1QixvRkFBb0Y7WUFDcEYrSyxjQUFjO2dCQUNaLEdBQUdwUSxpQkFBaUJxRixPQUFPO2dCQUMzQmpFLE1BQU1tUTtnQkFDTnZULFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNuQztRQUNGLE9BQU87Z0JBK0JTcUgsMkJBQ0ZBO1lBL0JaLGtDQUFrQztZQUNsQzZLLGNBQWM7Z0JBQ1poUCxNQUFNbVE7Z0JBQ052VCxXQUFXLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ2pDbVMsT0FBT3BRO2dCQUNQWSxRQUFRQSxPQUFPd0YsR0FBRyxDQUFDLENBQUMwQjt3QkFHaEJ4RzsyQkFIMkI7d0JBQzdCLEdBQUd3RyxLQUFLO3dCQUNSOUcsVUFDRU0sRUFBQUEsOEJBQUFBLFVBQVU4RCxPQUFPLENBQUMwQyxNQUFNaEgsRUFBRSxDQUFDLGNBQTNCUSxrREFBQUEsNEJBQTZCTixRQUFRLENBQUMrRixPQUFPLE9BQU1lLE1BQU05RyxRQUFRO29CQUNyRTs7Z0JBQ0ErRyxhQUFhO29CQUNYQyxRQUFRakY7b0JBQ1I5QixXQUFXd0M7b0JBQ1h3RSxNQUFNdEU7b0JBQ05xRCxVQUFVbkQ7b0JBQ1ZWO29CQUNBRjtvQkFDQU07Z0JBQ0Y7Z0JBQ0FxRSxnQkFBZ0I7b0JBQ2R4RCxTQUFTTDtvQkFDVDhELFVBQVU1RDtnQkFDWjtnQkFDQW9NLGVBQWU7b0JBQ2I1TjtvQkFDQUU7b0JBQ0FFO29CQUNBUTtnQkFDRjtnQkFDQTlGLFFBQVE7b0JBQ055RCxRQUFRLEdBQUVzRSw0QkFBQUEsaUJBQWlCRixPQUFPLGNBQXhCRSxnREFBQUEsMEJBQTBCNkYsTUFBTSxDQUFDbkssUUFBUSxDQUFDK0YsT0FBTztvQkFDM0QrSCxNQUFNLEdBQUV4Siw2QkFBQUEsaUJBQWlCRixPQUFPLGNBQXhCRSxpREFBQUEsMkJBQTBCd0osTUFBTSxDQUFDL0gsT0FBTztnQkFDbEQ7Z0JBQ0FyRyxXQUFXbUIsYUFBYXVFLEdBQUcsQ0FBQyxDQUFDUTtvQkFDM0IsTUFBTU0sV0FBV04sSUFBSU0sUUFBUTtvQkFDN0IsSUFBSSxDQUFDQSxVQUNILE9BQU87d0JBQUVwRyxJQUFJOEYsSUFBSUMsSUFBSTt3QkFBRTFGLE1BQU15RixJQUFJekYsSUFBSTt3QkFBRStGLFVBQVU7b0JBQUs7b0JBQ3hELE9BQU87d0JBQ0xwRyxJQUFJOEYsSUFBSUMsSUFBSTt3QkFDWjFGLE1BQU15RixJQUFJekYsSUFBSTt3QkFDZCtGLFVBQVU7NEJBQ1JuRyxNQUFNbUcsU0FBU25HLElBQUk7NEJBQ25CRyxPQUFPZ0csU0FBU2hHLEtBQUssR0FBR2dHLFNBQVNoRyxLQUFLLENBQUNpRyxZQUFZLEtBQUs7NEJBQ3hEQyxXQUFXRixTQUFTRSxTQUFTOzRCQUM3QkMsV0FBV0gsU0FBU0csU0FBUzs0QkFDN0IxRSxXQUFXdUUsU0FBU3ZFLFNBQVM7NEJBQzdCNkUsYUFBYU4sU0FBU00sV0FBVzs0QkFDakNDLFNBQVNQLFNBQVNPLE9BQU87NEJBQ3pCQyxpQkFBaUJSLFNBQVNRLGVBQWU7NEJBQ3pDLEdBQUlSLFNBQVNuRyxJQUFJLEtBQUssNEJBQTRCO2dDQUNoRDBRLG9CQUFvQnZLLFNBQVN1SyxrQkFBa0I7Z0NBQy9DQyxZQUFZeEssU0FBU3dLLFVBQVU7Z0NBQy9CQyxLQUFLekssU0FBU3lLLEdBQUc7Z0NBQ2pCQyxpQkFBaUIxSyxTQUFTMEssZUFBZTs0QkFDM0MsQ0FBQzt3QkFDSDtvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJSixhQUFhLFNBQVM7WUFDeEIsdUJBQXVCO1lBQ3ZCakMsYUFBYXNDLE9BQU8sQ0FBQyxxQkFBcUJwTCxLQUFLRSxTQUFTLENBQUN3SjtRQUMzRCxPQUFPLElBQUlxQixhQUFhLE1BQU07WUFDNUIscUJBQXFCO1lBQ3JCLElBQUk7Z0JBQ0YsTUFBTU0sVUFBVSxNQUFNaFYsNEVBQWFBLENBQUM7b0JBQ2xDaVYsUUFBUXhTLEtBQUt1QixFQUFFO29CQUNma1IsYUFBYTFTLFFBQVEyUyxHQUFHO29CQUN4QjlRLE1BQU1tUTtvQkFDTjVILFFBQVF5RztvQkFDUm9CLGFBQWFBO2dCQUNmO2dCQUNBNVMsUUFBUUMsR0FBRyxDQUFDLFdBQVdrVDtnQkFDdkIsSUFBSUEsU0FBUztvQkFDWHRTLFdBQVdGLFFBQVEyUyxHQUFHLEVBQUVIO2dCQUMxQjtnQkFDQTlVLDBDQUFLQSxDQUFDeUwsT0FBTyxDQUFDO1lBQ2hCLEVBQUUsT0FBT3lKLEtBQUs7Z0JBQ1psViwwQ0FBS0EsQ0FBQzZCLEtBQUssQ0FBQztZQUNkO1FBQ0YsT0FBTztZQUNMLGlCQUFpQjtZQUNqQixNQUFNc1QsT0FBTyxJQUFJQyxLQUFLO2dCQUFDM0wsS0FBS0UsU0FBUyxDQUFDd0osYUFBYSxNQUFNO2FBQUcsRUFBRTtnQkFDNURwUCxNQUFNO1lBQ1I7WUFDQSxNQUFNc1IsTUFBTXZFLElBQUl3RSxlQUFlLENBQUNIO1lBQ2hDLE1BQU16RCxJQUFJN1EsU0FBU0MsYUFBYSxDQUFDO1lBQ2pDNFEsRUFBRXJRLElBQUksR0FBR2dVO1lBQ1QzRCxFQUFFdFEsUUFBUSxHQUFHLEdBQWtDLE9BQS9Ca1QsVUFBVXBULE9BQU8sQ0FBQyxRQUFRLE1BQUs7WUFDL0NMLFNBQVNTLElBQUksQ0FBQ0MsV0FBVyxDQUFDbVE7WUFDMUJBLEVBQUVsUSxLQUFLO1lBQ1BYLFNBQVNTLElBQUksQ0FBQ0ksV0FBVyxDQUFDZ1E7WUFDMUJaLElBQUlDLGVBQWUsQ0FBQ3NFO1FBQ3RCO0lBQ0Y7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTSxDQUFDRSxlQUFlQyxpQkFBaUIsR0FBR2xYLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ21YLGVBQWVDLGlCQUFpQixHQUFHcFgsK0NBQVFBLENBQUM7UUFBQztRQUFHO1FBQU07S0FBRTtJQUMvRCxNQUFNLENBQUNxWCxZQUFZQyxjQUFjLEdBQUd0WCwrQ0FBUUEsQ0FBQztRQUFDO1FBQUs7UUFBSztLQUFJO0lBQzVELE1BQU0sQ0FBQ3VYLGVBQWVDLGlCQUFpQixHQUFHeFgsK0NBQVFBLENBQUM7UUFBQyxDQUFDeVgsS0FBS0MsRUFBRSxHQUFHO1FBQUc7UUFBRztLQUFFO0lBQ3ZFLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHNVgsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDNlgsY0FBY0MsZ0JBQWdCLEdBQUc5WCwrQ0FBUUEsQ0FDOUM7SUFHRix1QkFBdUI7SUFDdkIsTUFBTSxDQUFDK1gsa0JBQWtCeEQsb0JBQW9CLEdBQUd2VSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNnWSxxQkFBcUJ4RCx1QkFBdUIsR0FBR3hVLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ2lZLG9CQUFvQnhELHNCQUFzQixHQUFHelUsK0NBQVFBLENBQUM7SUFFN0QsTUFBTWtZLG9CQUFvQnJZLGtEQUFXQSxDQUFDLENBQUNzWTtRQUNyQ2pCLGlCQUFpQmlCO1FBQ2pCLGlGQUFpRjtRQUNqRmxFLGFBQWFzQyxPQUFPLENBQUMsaUJBQWlCNEI7SUFDeEMsR0FBRyxFQUFFO0lBRUwsTUFBTUMsbUJBQW1Cdlksa0RBQVdBLENBQUMsQ0FBQ3dZO1FBQ3BDUCxnQkFBZ0JPO1FBQ2hCcEUsYUFBYXNDLE9BQU8sQ0FBQyxpQkFBaUI4QjtJQUN4QyxHQUFHLEVBQUU7SUFFTCxxQ0FBcUM7SUFDckN2WSxnREFBU0EsQ0FBQztRQUNSLE1BQU13WSxxQkFBcUJyRSxhQUFhQyxPQUFPLENBQUM7UUFDaEQsSUFBSW9FLG9CQUFvQjtZQUN0QnBCLGlCQUFpQm9CO1FBQ25CO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsdUJBQXVCMVksa0RBQVdBLENBQ3RDLENBQUNxUztRQUNDcUcscUJBQ0VyRyxPQUNBbEwsbUJBQ0FvUSxrQkFDQUk7SUFFSixHQUNBO1FBQUNKO1FBQWtCSTtRQUFrQnhRO0tBQWtCO0lBR3pELHlEQUF5RDtJQUN6RCxNQUFNd1IsMEJBQTBCM1ksa0RBQVdBLENBQUMsQ0FBQzRZO1FBQzNDLE1BQU1DLGVBQWU7WUFDbkJDLFlBQVk7Z0JBQUM7Z0JBQUc7Z0JBQU07YUFBRTtZQUN4QkMsVUFBVTtnQkFBQztnQkFBRztnQkFBTTthQUFFO1lBQ3RCQyxZQUFZO2dCQUFDO2dCQUFHO2dCQUFLO2FBQUU7WUFDdkJDLFlBQVk7Z0JBQUM7Z0JBQUc7Z0JBQUs7YUFBRTtZQUN2QkMsT0FBTztnQkFBQztnQkFBRztnQkFBTTthQUFFO1lBQ25CQyxPQUFPO2dCQUFDO2dCQUFHO2dCQUFLO2FBQUU7WUFDbEJDLE9BQU87Z0JBQUM7Z0JBQUc7Z0JBQU07YUFBRTtRQUNyQjtRQUNBLE9BQU9QLFlBQVksQ0FBQ0QsUUFBUSxJQUFJO1lBQUM7WUFBRztZQUFNO1NBQUU7SUFDOUMsR0FBRyxFQUFFO0lBRUwsTUFBTVMsMEJBQTBCclosa0RBQVdBLENBQUMsQ0FBQzRZO1FBQzNDLE1BQU1DLGVBQWU7WUFDbkJDLFlBQVk7Z0JBQUMsQ0FBQ2xCLEtBQUtDLEVBQUUsR0FBRztnQkFBRztnQkFBRzthQUFFO1lBQ2hDa0IsVUFBVTtnQkFBQyxDQUFDbkIsS0FBS0MsRUFBRSxHQUFHO2dCQUFHO2dCQUFHO2FBQUU7WUFDOUJtQixZQUFZO2dCQUFDLENBQUNwQixLQUFLQyxFQUFFLEdBQUc7Z0JBQUc7Z0JBQUc7YUFBRTtZQUNoQ29CLFlBQVk7Z0JBQUMsQ0FBQ3JCLEtBQUtDLEVBQUUsR0FBRztnQkFBRztnQkFBRzthQUFFO1lBQ2hDcUIsT0FBTztnQkFBQyxDQUFDdEIsS0FBS0MsRUFBRSxHQUFHO2dCQUFHO2dCQUFHO2FBQUU7WUFDM0JzQixPQUFPO2dCQUFDLENBQUN2QixLQUFLQyxFQUFFLEdBQUc7Z0JBQUc7Z0JBQUc7YUFBRTtZQUMzQnVCLE9BQU87Z0JBQUMsQ0FBQ3hCLEtBQUtDLEVBQUUsR0FBRztnQkFBRztnQkFBRzthQUFFO1FBQzdCO1FBQ0EsT0FBT2dCLFlBQVksQ0FBQ0QsUUFBUSxJQUFJO1lBQUMsQ0FBQ2hCLEtBQUtDLEVBQUUsR0FBRztZQUFHO1lBQUc7U0FBRTtJQUN0RCxHQUFHLEVBQUU7SUFFTCxNQUFNeUIsdUJBQXVCdFosa0RBQVdBLENBQUMsQ0FBQzRZO1FBQ3hDLE1BQU1DLGVBQWU7WUFDbkJDLFlBQVk7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUMzQkMsVUFBVTtnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQ3pCQyxZQUFZO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDM0JDLFlBQVk7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUMzQkMsT0FBTztnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQ3RCQyxPQUFPO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDdEJDLE9BQU87Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtRQUN4QjtRQUNBLE9BQU9QLFlBQVksQ0FBQ0QsUUFBUSxJQUFJO1lBQUM7WUFBSztZQUFLO1NBQUk7SUFDakQsR0FBRyxFQUFFO0lBRUwsOERBQThEO0lBQzlELE1BQU1XLHdCQUF3QnZaLGtEQUFXQSxDQUFDLENBQUN3WixXQUFXbEIsTUFBTU07UUFDMUQsSUFBSSxDQUFDTixRQUFRQSxLQUFLdE4sTUFBTSxLQUFLLEdBQUcsT0FBT3dPO1FBRXZDLE1BQU1DLGFBQWFuQixLQUFLdE4sTUFBTTtRQUM5QixJQUFJLENBQUMwTyxRQUFRQyxRQUFRQyxPQUFPLEdBQUdKO1FBRS9CLGtEQUFrRDtRQUNsRCxJQUFJSyxrQkFBa0I7UUFFdEIsSUFBSUosYUFBYSxJQUFJO1lBQ25CLGlEQUFpRDtZQUNqREksa0JBQWtCLE1BQU0sQ0FBQ0osYUFBYSxFQUFDLElBQUs7UUFDOUMsT0FBTyxJQUFJQSxhQUFhLEdBQUc7WUFDekIsOENBQThDO1lBQzlDSSxrQkFBa0IsTUFBTSxDQUFDSixhQUFhLEtBQUs7UUFDN0MsT0FBTyxJQUFJQSxhQUFhLEdBQUc7WUFDekIsc0VBQXNFO1lBQ3RFSSxrQkFBa0IsTUFBTUosYUFBYTtRQUN2QztRQUVBLHdEQUF3RDtRQUN4REMsU0FBU0EsU0FBU0c7UUFFbEIsc0RBQXNEO1FBQ3RELElBQUlKLGFBQWEsR0FBRztZQUNsQkUsU0FBU0EsU0FBUy9CLEtBQUtrQyxHQUFHLENBQUMsS0FBSyxNQUFNLENBQUNMLGFBQWEsS0FBSztRQUMzRDtRQUVBLDhFQUE4RTtRQUM5RSxNQUFNTSxXQUFXO1lBQ2ZqQixZQUFZO2dCQUFDO2dCQUFNO2dCQUFLO2FBQUk7WUFDNUJDLFVBQVU7Z0JBQUM7Z0JBQU07Z0JBQUs7YUFBSTtZQUMxQkMsWUFBWTtnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQzNCQyxZQUFZO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDM0JDLE9BQU87Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUN0QkMsT0FBTztnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQ3RCQyxPQUFPO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7UUFDeEI7UUFFQSxNQUFNWSxTQUFTRCxRQUFRLENBQUNuQixRQUFRLElBQUk7WUFBQztZQUFLO1lBQUs7U0FBSTtRQUNuRGMsU0FBUzlCLEtBQUtrQyxHQUFHLENBQUNKLFFBQVFNLE1BQU0sQ0FBQyxFQUFFO1FBQ25DTCxTQUFTL0IsS0FBS2tDLEdBQUcsQ0FBQ0gsUUFBUUssTUFBTSxDQUFDLEVBQUU7UUFDbkNKLFNBQVNoQyxLQUFLa0MsR0FBRyxDQUFDRixRQUFRSSxNQUFNLENBQUMsRUFBRTtRQUVuQyxPQUFPO1lBQUNOO1lBQVFDO1lBQVFDO1NBQU87SUFDakMsR0FBRyxFQUFFO0lBRUwsaUNBQWlDO0lBQ2pDM1osZ0RBQVNBLENBQUM7UUFDUixJQUFJNEUsaUJBQWlCdVMsZUFBZTtZQUNsQyxNQUFNb0MsWUFBWUYscUJBQXFCelU7WUFDdkMsTUFBTW9WLGVBQWVWLHNCQUNuQkMsV0FDQXBDLGVBQ0F2UztZQUVGNFMsY0FBY3dDO1FBQ2hCO0lBQ0YsR0FBRztRQUNEN0M7UUFDQXZTO1FBQ0F5VTtRQUNBQztLQUNEO0lBRUQsb0RBQW9EO0lBQ3BEdFosZ0RBQVNBLENBQUM7UUFDUixJQUFJNEUsZUFBZTtZQUNqQjBTLGlCQUFpQm9CLHdCQUF3QjlUO1lBQ3pDOFMsaUJBQWlCMEIsd0JBQXdCeFU7WUFDekMsMERBQTBEO1lBQzFELElBQUksQ0FBQ3VTLGVBQWU7Z0JBQ2xCSyxjQUFjNkIscUJBQXFCelU7WUFDckM7UUFDRjtJQUNGLEdBQUc7UUFDREE7UUFDQThUO1FBQ0FVO1FBQ0FDO1FBQ0FsQztLQUNEO0lBRUQsTUFBTSxDQUFDOEMsY0FBY0MsZ0JBQWdCLEdBQUdoYSwrQ0FBUUEsQ0FBQztJQUVqRCwyQ0FBMkM7SUFDM0MsTUFBTSxDQUFDaWEsVUFBVUMsWUFBWSxHQUFHbGEsK0NBQVFBLENBQUM7SUFFekNGLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXFhLGNBQWM7WUFDbEJELFlBQVlqUCxPQUFPbVAsVUFBVSxHQUFHO1FBQ2xDO1FBRUFEO1FBQ0FsUCxPQUFPcUgsZ0JBQWdCLENBQUMsVUFBVTZIO1FBRWxDLE9BQU8sSUFBTWxQLE9BQU9zSCxtQkFBbUIsQ0FBQyxVQUFVNEg7SUFDcEQsR0FBRyxFQUFFO0lBRUwsa0ZBQWtGO0lBQ2xGLE1BQU0sQ0FBQ0UsbUJBQW1CQyxxQkFBcUIsR0FBR3RhLCtDQUFRQSxDQUFDO1FBQ3pELHNFQUFzRTtRQUN0RSxJQUFJLElBQTZCLEVBQUU7WUFDakMsT0FBT2lMLE9BQU9tUCxVQUFVLEdBQUc7UUFDN0I7UUFDQSxPQUFPLE9BQU8sMkNBQTJDO0lBQzNEO0lBRUEsb0VBQW9FO0lBQ3BFdGEsZ0RBQVNBLENBQUM7UUFDUixJQUFJbWEsWUFBWXBULGVBQWU7WUFDN0J5VCxxQkFBcUI7UUFDdkI7SUFDRixHQUFHO1FBQUNMO1FBQVVwVDtLQUFjO0lBRTVCLHNFQUFzRTtJQUN0RS9HLGdEQUFTQSxDQUFDO1FBQ1IsaUZBQWlGO1FBQ2pGd2EscUJBQXFCTDtJQUN2QixHQUFHO1FBQUNBO0tBQVM7SUFFYixNQUFNTSxvQkFBb0I7UUFDeEIsSUFBSU4sVUFBVTtZQUNaLE9BQU87Z0JBQUMsQ0FBQztnQkFBTTtnQkFBTTthQUFJO1FBQzNCO1FBQ0EsT0FBTztZQUFDLENBQUM7WUFBTTtZQUFNO1NBQUk7SUFDM0I7SUFFQW5hLGdEQUFTQSxDQUFDO1FBQ1IsZ0VBQWdFO1FBQ2hFLE1BQU0wYSw4QkFBOEI7WUFDbEMsSUFBSTtnQkFDRixNQUFNQyxpQkFBaUIsTUFBTWhaLGdGQUFpQkEsQ0FBQztvQkFDN0NnVixRQUFReFMsS0FBS3VCLEVBQUU7b0JBQ2ZrUixhQUFhMVMsUUFBUTJTLEdBQUc7Z0JBQzFCO2dCQUNBLElBQUk4RCxrQkFBa0JBLGVBQWVyTSxNQUFNLEVBQUU7b0JBQzNDbEIsZ0JBQWdCdU4sZUFBZXJNLE1BQU07Z0JBQ3ZDO1lBQ0YsRUFBRSxPQUFPd0ksS0FBSztnQkFDWnZULFFBQVFFLEtBQUssQ0FBQywwQ0FBMENxVDtZQUMxRDtRQUNGO1FBQ0E0RDtJQUNBLG9CQUFvQjtJQUNwQix1REFBdUQ7SUFDekQsR0FBRyxFQUFFO0lBRUwscUJBQ0U7OzBCQUNFLDhEQUFDdmEsMERBQWNBOzs7OzswQkFDZiw4REFBQ0ksK0NBQU1BO2dCQUNMcWEsU0FBUzNHO2dCQUNUNEcsY0FBY2hHO2dCQUNkaUcsYUFBYTdFO2dCQUNiOEUsYUFBYTNOO2dCQUNiNk0sY0FBY0E7Z0JBQ2RlLG9CQUFvQixJQUFNZCxnQkFBZ0IsQ0FBQ0Q7Z0JBQzNDL1YsU0FBU0E7Z0JBQ1RDLE1BQU1BO2dCQUNOOFcsUUFBUTVMO2dCQUNSNkwsUUFBUTNMOzs7Ozs7MEJBR1YsOERBQUNqTyxzRUFBbUJBOzs7OzswQkFHcEIsOERBQUM2WjtnQkFDQ0MsV0FBVyx3RkFFVixPQURDbkIsZUFBZSxrQ0FBa0M7MEJBR25ELDRFQUFDblosaURBQU9BO29CQUNONkYsZUFBZUE7b0JBQ2ZDLGtCQUFrQkE7b0JBQ2xCeUksWUFBWUE7b0JBQ1pFLFlBQVlBO29CQUNaeEksZUFBZUE7b0JBQ2ZDLGtCQUFrQkE7b0JBQ2xCcVUsYUFBWTtvQkFDWjVVLGNBQWNBO29CQUNkTCxvQkFBb0JBO29CQUNwQkQsaUJBQWlCQTtvQkFDakIyTSxVQUFVQTtvQkFDVnFILFVBQVVBO29CQUNWSyxzQkFBc0JBOzs7Ozs7Ozs7OztZQUt6QnpULCtCQUNDLDhEQUFDb1U7Z0JBQ0NHLEtBQUt4STtnQkFDTHNJLFdBQVU7O29CQUVUclUsa0JBQWtCLDZCQUNqQiw4REFBQ2hHLDREQUFjQTt3QkFDYm9GLGlCQUFpQkE7d0JBQ2pCUSxlQUFlQTt3QkFDZkMsa0JBQWtCQTt3QkFDbEJ5SSxZQUFZQTt3QkFDWkUsWUFBWUE7d0JBQ1poQyxjQUFjQTt3QkFDZDFOLFlBQVlBLGtEQUFVQTs7Ozs7O29CQUl6QmtILGtCQUFrQiw0QkFDakIsOERBQUN0Ryx5REFBYUE7d0JBQ1owRixpQkFBaUJBO3dCQUNqQjhSLGtCQUFrQkE7d0JBQ2xCeEQscUJBQXFCQTt3QkFDckJ5RCxxQkFBcUJBO3dCQUNyQnhELHdCQUF3QkE7d0JBQ3hCeUQsb0JBQW9CQTt3QkFDcEJ4RCx1QkFBdUJBO3dCQUN2QmhSLFVBQVVBO3dCQUNWb0ssYUFBYUE7d0JBQ2JLLG9CQUFvQkE7d0JBQ3BCdk8sWUFBWUEsa0RBQVVBO3dCQUN0QjBOLGNBQWNBOzs7Ozs7b0JBSWpCeEcsa0JBQWtCLGtDQUNqQiw4REFBQ25HLHNFQUFtQkE7d0JBQ2xCK0gsdUJBQXVCQTt3QkFDdkJDLDBCQUEwQkE7d0JBQzFCQyx3QkFBd0JBO3dCQUN4QkMsMkJBQTJCQTs7Ozs7O29CQUk5Qi9CLGtCQUFrQiwrQkFDakIsOERBQUN6RywrREFBZ0JBO3dCQUNmcUgsV0FBV0E7d0JBQ1hDLGNBQWNBO3dCQUNkQyxTQUFTQTt3QkFDVEMsWUFBWUE7d0JBQ1pDLGlCQUFpQkE7d0JBQ2pCQyxvQkFBb0JBO3dCQUNwQkcsWUFBWUE7d0JBQ1pDLGVBQWVBO3dCQUNmQyxjQUFjQTt3QkFDZEMsaUJBQWlCQTt3QkFDakJzQyxnQkFBZ0JBO3dCQUNoQkMsbUJBQW1CQTt3QkFDbkJ0QyxTQUFTQTt3QkFDVEMsWUFBWUE7d0JBQ1pDLGFBQWFBO3dCQUNiQyxnQkFBZ0JBOzs7Ozs7b0JBSW5CM0Isa0JBQWtCLDBCQUNqQiw4REFBQ3ZHLG1EQUFVQTt3QkFDVGdGLFFBQVFBO3dCQUNSQyxXQUFXQTt3QkFDWFMsV0FBV0E7d0JBQ1htQixVQUFVQTt3QkFDVkMsYUFBYUE7d0JBQ2JDLFdBQVdBO3dCQUNYQyxjQUFjQTt3QkFDZFMsa0JBQWtCQTt3QkFDbEJDLHFCQUFxQkE7d0JBQ3JCcVQsZUFBZTs0QkFDYix3Q0FBd0M7NEJBQ3hDLE1BQU1DLGdCQUFnQjtnQ0FDcEI7b0NBQ0U5VixJQUFJO29DQUNKQyxNQUFNO29DQUNOQyxVQUFVO3dDQUFDO3dDQUFLO3dDQUFLO3FDQUFFO29DQUN2QkMsV0FBVztvQ0FDWEMsT0FBTztvQ0FDUEMsTUFBTTtvQ0FDTkMsWUFBWTtvQ0FDWkMsZUFBZTtnQ0FDakI7Z0NBQ0E7b0NBQ0VQLElBQUk7b0NBQ0pDLE1BQU07b0NBQ05DLFVBQVU7d0NBQUM7d0NBQUc7d0NBQUc7cUNBQUU7b0NBQ25CQyxXQUFXO29DQUNYQyxPQUFPO29DQUNQQyxNQUFNO2dDQUNSO2dDQUNBO29DQUNFTCxJQUFJO29DQUNKQyxNQUFNO29DQUNOQyxVQUFVO3dDQUFDLENBQUM7d0NBQUc7d0NBQUcsQ0FBQztxQ0FBRTtvQ0FDckJDLFdBQVc7b0NBQ1hDLE9BQU87b0NBQ1BDLE1BQU07Z0NBQ1I7Z0NBQ0E7b0NBQ0VMLElBQUk7b0NBQ0pDLE1BQU07b0NBQ05DLFVBQVU7d0NBQUM7d0NBQUc7d0NBQUcsQ0FBQztxQ0FBRTtvQ0FDcEJDLFdBQVc7b0NBQ1hDLE9BQU87b0NBQ1BDLE1BQU07Z0NBQ1I7NkJBQ0Q7NEJBQ0ROLFVBQVUrVjs0QkFDVix1QkFBdUI7NEJBQ3ZCbFYsaUJBQWlCOzRCQUNqQiwrQkFBK0I7NEJBQy9CRSx3QkFBd0IsQ0FBQzt3QkFDM0I7Ozs7OztvQkFJSE8sa0JBQWtCLDZCQUNqQiw4REFBQzFGLDREQUFjQTt3QkFDYm9hLGVBQWVyRDt3QkFDZnNELHVCQUF1QnBFO3dCQUN2QnFFLG9CQUFvQm5FO3dCQUNwQm9FLHVCQUF1QmxFO3dCQUN2QkwsZUFBZUE7d0JBQ2ZFLFlBQVlBO3dCQUNaRSxlQUFlQTt3QkFDZkksWUFBWUE7d0JBQ1pnRSxvQkFBb0IvRDt3QkFDcEJnRSxjQUFjeEQ7d0JBQ2RQLGNBQWNBO3dCQUNkOVEsZ0JBQWdCQTt3QkFDaEJDLG1CQUFtQkE7d0JBQ25CNlUsV0FBVzVVO3dCQUNYNlUsbUJBQW1CNVU7Ozs7Ozs7Ozs7OzswQkFPM0IsOERBQUMrVDtnQkFDQ0MsV0FBVyw0REFFVixPQURDbkIsZUFBZSxrQ0FBa0M7MEJBR25ELDRFQUFDa0I7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN6YSwrQ0FBUUE7d0JBQ1A4RixjQUFjQTt3QkFDZE4saUJBQWlCQTt3QkFDakI4VixnQkFBZ0I3Vjt3QkFDaEI4VixjQUFjdEo7d0JBQ2R1SixpQkFBaUJySTt3QkFDakJnSCxhQUFhN0U7d0JBQ2J4TyxZQUFZQTt3QkFDWkMsZUFBZUE7d0JBQ2YwVSxXQUFXN0I7d0JBQ1g4QixjQUFjN0I7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBCLDhEQUFDVztnQkFDQ0MsV0FBVTtnQkFDVmtCLE9BQU87b0JBQ0xDLFlBQVk7Z0JBQ2Q7MEJBRUEsNEVBQUM1Yyx1REFBTUE7b0JBQ0x5YixXQUFVO29CQUNWalosUUFBUTt3QkFDTnlELFVBQVU2VTt3QkFDVitCLEtBQUs7d0JBQ0xDLE1BQU07d0JBQ05DLEtBQUs7b0JBQ1A7b0JBQ0FDLE9BQU87b0JBQ1ByQixLQUFLblI7b0JBQ0xsSSxJQUFJO3dCQUNGMmEsV0FBVzt3QkFDWEMsdUJBQXVCO29CQUN6QjtvQkFDQUMsaUJBQWlCdk07b0JBQ2pCd00sV0FBVzt3QkFDVEMsZ0JBQWdCLENBQUNuTixJQUFPO2dDQUFFb04sU0FBU3BOLEVBQUVxTixPQUFPO2dDQUFFQyxTQUFTdE4sRUFBRXVOLE9BQU87NEJBQUM7b0JBQ25FO29CQUNBQyxXQUFVOzhCQUVWLDRFQUFDdmQsMkNBQVFBO3dCQUNQd2Qsd0JBQ0UsOERBQUNoZSxvREFBSUE7c0NBQ0gsNEVBQUNDLHNEQUFNQTs7Ozs7Ozs7Ozs7MENBTVgsOERBQUN3QztnQ0FDQ0MsaUJBQWlCLENBQUN1YjtvQ0FDaEIzSSxjQUFjNUssT0FBTyxHQUFHdVQ7Z0NBQzFCOzs7Ozs7MENBR0YsOERBQUMxWjtnQ0FDQ0UsaUJBQWlCLENBQUM5QjtvQ0FDaEJtSSxZQUFZbkk7Z0NBQ2Q7Ozs7OzswQ0FHRiw4REFBQ3BCLHFEQUFXQTtnQ0FDVjJFLFFBQVFBO2dDQUNSVSxXQUFXQTtnQ0FDWEcsZUFBZUE7Z0NBQ2ZtWCxlQUFlbFg7Z0NBQ2ZtWCxjQUFjM0w7Z0NBQ2Q3SixrQkFBa0JBO2dDQUNsQnlWLG9CQUFvQjs7Ozs7OzBDQUV0Qiw4REFBQ3JkLHFEQUFXQTtnQ0FDVnVNLFFBQVFvSDtnQ0FDUnVJLFlBQVl4VTtnQ0FDWkYsU0FBU0E7Z0NBQ1RNLFlBQVlBO2dDQUNadEMsV0FBV3dDO2dDQUNYd0UsTUFBTXRFO2dDQUNORSxhQUFhQTs7Ozs7OzBDQUdmLDhEQUFDaEgsZ0VBQWdCQTtnQ0FBQzBFLGlCQUFpQkE7MENBQ2pDLDRFQUFDNUUsb0RBQVVBO29DQUNUcUQsZUFBZUE7b0NBQ2YyQyxXQUFXQTtvQ0FDWGtLLG1CQUFtQkE7b0NBQ25CN0IsbUJBQW1CQTtvQ0FDbkI1SyxlQUFlQTtvQ0FDZnlDLFlBQVlBO29DQUNaMFAsZUFBZUE7b0NBQ2ZwRSxpQkFBaUJBO29DQUNqQnNFLGVBQWVBO29DQUNmRSxZQUFZQTtvQ0FDWkUsZUFBZUE7b0NBQ2ZJLFlBQVlBO29DQUNaVSxNQUFNUjtvQ0FDTjlRLGdCQUFnQkE7b0NBQ2hCMFcsa0JBQWtCbEY7b0NBQ2xCdFIsZ0JBQWdCQTtvQ0FDaEJWLGNBQWNBO29DQUNkd1Isa0JBQWtCQTtvQ0FDbEJDLHFCQUFxQkE7b0NBQ3JCQyxvQkFBb0JBOzs7Ozs7Ozs7OzswQ0FJeEIsOERBQUMxWSw2REFBYUE7Z0NBQ1o2YixLQUFLcFI7Z0NBQ0x3SixRQUFRO29DQUFDO29DQUFHO29DQUFLO2lDQUFFO2dDQUNuQmtLLFdBQVc7Z0NBQ1hDLGVBQWU7Z0NBQ2Ysb0JBQW9CO2dDQUNwQixpQ0FBaUM7Z0NBQ2pDLG9CQUFvQjtnQ0FDcEJDLGFBQWE7Ozs7Ozs0QkFHZDNYLGdCQUFnQjRFLE1BQU0sR0FBRyxLQUFLaEUsa0JBQWtCLDZCQUMvQyw4REFBQ3JILGlFQUFpQkE7Z0NBQ2hCcVEsUUFDRTVKLGdCQUFnQjRFLE1BQU0sS0FBSyxJQUN2QjVFLGVBQWUsQ0FBQyxFQUFFLEdBQ2xCVSxrQkFBa0JtRCxPQUFPO2dDQUUvQitULE1BQU1wWDtnQ0FDTnFYLGFBQWE7b0NBQ1gsSUFBSTlULGlCQUFpQkYsT0FBTyxFQUMxQkUsaUJBQWlCRixPQUFPLENBQUNoQixPQUFPLEdBQUc7Z0NBQ3ZDO2dDQUNBaVYsV0FBVztvQ0FDVCxJQUFJL1QsaUJBQWlCRixPQUFPLEVBQzFCRSxpQkFBaUJGLE9BQU8sQ0FBQ2hCLE9BQU8sR0FBRztvQ0FDckMsSUFBSTdDLGdCQUFnQjRFLE1BQU0sR0FBRyxHQUFHc0c7b0NBQ2hDdEQsZUFBZSwwQ0FBMEM7b0NBQ3pEbE8sc0RBQVVBLElBQUkseUNBQXlDO2dDQUN6RDtnQ0FDQXFlLGdCQUFnQjtvQ0FDZCxJQUFJL1gsZ0JBQWdCNEUsTUFBTSxHQUFHLEdBQUdzRztvQ0FDaEN0RCxlQUFlLDBDQUEwQztvQ0FDekRsTyxzREFBVUEsSUFBSSx5Q0FBeUM7Z0NBQ3pEOzs7Ozs7NEJBR0g0SCxlQUFlLHdCQUNkLDhEQUFDMFc7Z0NBQVdDLE1BQU07b0NBQUM7b0NBQUk7b0NBQUk7b0NBQVc7aUNBQVU7Z0NBQUVDLGFBQWE7Ozs7Ozs0QkFFaEU1VyxlQUFlLHlCQUNkLDhEQUFDNlc7Z0NBQ0NDLGNBQVksQ0FBQzVHLEtBQUtDLEVBQUUsR0FBRztnQ0FDdkJ5RyxhQUFhO2dDQUNielksVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUM0WTt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTs7Ozs7O2tEQUMvQiw4REFBQ0s7d0NBQ0MzWSxPQUFNO3dDQUNOeUIsV0FBV0E7d0NBQ1g2RSxhQUFhO3dDQUNiQyxTQUFTOzs7Ozs7Ozs7Ozs7NEJBSWQ1RSxlQUFlLDhCQUNkLDhEQUFDNlc7Z0NBQ0NELGFBQWE7Z0NBQ2J6UyxVQUFVO29DQUFDLENBQUMrTCxLQUFLQyxFQUFFLEdBQUc7b0NBQUc7b0NBQUc7aUNBQUU7Z0NBQzlCaFMsVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUM0WTt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSTt5Q0FBRzs7Ozs7O2tEQUM3Qiw4REFBQzVlLHFFQUFxQkE7d0NBQ3BCcU4sTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTt3Q0FDaEI2UixZQUFZO3dDQUNaQyxTQUFTO3dDQUNUQyxhQUFhO3dDQUNiNVMsV0FBVzt3Q0FDWDZTLFlBQVk7d0NBQ1pDLG1CQUFtQjt3Q0FDbkJDLG1CQUFtQjt3Q0FDbkJqWixPQUFNO3dDQUNObUcsV0FBVzt3Q0FDWCtTLFFBQVE7d0NBQ1I1UyxhQUFhO3dDQUNiQyxTQUFTOzs7Ozs7Ozs7Ozs7NEJBSWQ1RSxlQUFlLHdCQUNkLDhEQUFDNlc7Z0NBQ0NELGFBQWE7Z0NBQ2J6UyxVQUFVO29DQUFDLENBQUMrTCxLQUFLQyxFQUFFLEdBQUc7b0NBQUc7b0NBQUc7aUNBQUU7Z0NBQzlCaFMsVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUM0WTt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTs7Ozs7O2tEQUMvQiw4REFBQ2E7d0NBQ0NsUCxRQUNFLElBQUkzUCxrREFBb0IsQ0FBQzs0Q0FDdkJpTSxTQUFTOzRDQUNUdkcsT0FBTyxJQUFJMUYseUNBQVcsQ0FBQzs0Q0FDdkJnTSxhQUFhO3dDQUNmOzs7Ozs7Ozs7Ozs7MENBS1IsOERBQUNoTiwyREFBV0E7Z0NBQUNnZ0IsV0FBVTtnQ0FBZUMsUUFBUTtvQ0FBQztvQ0FBSTtpQ0FBRzswQ0FDcEQsNEVBQUNoZ0IsNkRBQWFBO29DQUNaaWdCLFlBQVc7b0NBQ1hDLFlBQVk7d0NBQUM7d0NBQU87d0NBQVM7cUNBQU87Ozs7Ozs7Ozs7OzBDQUl4Qyw4REFBQ047Z0NBQVVsUCxRQUFRbEosa0JBQWtCbUQsT0FBTzs7Ozs7OzBDQUU1Qyw4REFBQ3hJLDBFQUFxQkE7Z0NBQ3BCd0gsU0FBU0w7Z0NBQ1Q4RCxVQUFVNUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNakIrQixnQ0FBa0IsOERBQUNsSyxtREFBVUE7Z0JBQUNnUixPQUFPeE07Ozs7Ozs7O0FBRzVDO0lBN25Fd0JsQjs7UUFDQ25DLHlEQUFlQTtRQU9sQ0Msd0RBQWNBOzs7TUFSSWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvRWRpdG9yRXhwZXJpZW5jZS5qc3g/ZDliNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7XHJcbiAgR2l6bW9IZWxwZXIsXHJcbiAgR2l6bW9WaWV3cG9ydCxcclxuICBIdG1sLFxyXG4gIExvYWRlcixcclxuICBNZXNoUmVmbGVjdG9yTWF0ZXJpYWwsXHJcbiAgT3JiaXRDb250cm9scyxcclxuICBUcmFuc2Zvcm1Db250cm9scyxcclxufSBmcm9tIFwiQHJlYWN0LXRocmVlL2RyZWlcIjtcclxuaW1wb3J0IHsgQ2FudmFzLCB1c2VUaHJlZSwgaW52YWxpZGF0ZSB9IGZyb20gXCJAcmVhY3QtdGhyZWUvZmliZXJcIjtcclxuaW1wb3J0IHsgU3VzcGVuc2UsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgVG9hc3RDb250YWluZXIgfSBmcm9tIFwicmVhY3QtdG9hc3RpZnlcIjtcclxuXHJcbmltcG9ydCAqIGFzIFRIUkVFIGZyb20gXCJ0aHJlZVwiO1xyXG5pbXBvcnQgeyBFbnZpcm9ubWVudCB9IGZyb20gXCIuL0Vudmlyb25tZW50XCI7XHJcbmltcG9ydCB7IEVudmlyb25tZW50UGFuZWwgfSBmcm9tIFwiLi9FbnZpcm9ubWVudFBhbmVsXCI7XHJcbmltcG9ydCBIZWFkZXIgZnJvbSBcIi4vSGVhZGVyXCI7XHJcbmltcG9ydCB7IExpZ2h0UGFuZWwgfSBmcm9tIFwiLi9MaWdodFBhbmVsXCI7XHJcbmltcG9ydCB7IE1hdGVyaWFsUGFuZWwgfSBmcm9tIFwiLi9NYXRlcmlhbFBhbmVsXCI7XHJcbmltcG9ydCB7IE1vZGVsU3RhdHMgfSBmcm9tIFwiLi9Nb2RlbFN0YXRzXCI7XHJcbmltcG9ydCB7IE91dGxpbmVyIH0gZnJvbSBcIi4vT3V0bGluZXJcIjtcclxuaW1wb3J0IHsgUG9zdFByb2Nlc3NpbmdQYW5lbCB9IGZyb20gXCIuL1Bvc3RQcm9jZXNzaW5nUGFuZWxcIjtcclxuaW1wb3J0IFNjZW5lTGlnaHRzIGZyb20gXCIuL1NjZW5lTGlnaHRzXCI7XHJcbmltcG9ydCBUb29sYmFyIGZyb20gXCIuL1Rvb2xiYXJcIjtcclxuaW1wb3J0IHsgVHJhbnNmb3JtUGFuZWwgfSBmcm9tIFwiLi9UcmFuc2Zvcm1QYW5lbFwiO1xyXG5cclxuaW1wb3J0IHsgc2V0Q2FtZXJhVmlldyB9IGZyb20gXCIuLi8uLi91dGlscy9BcnRpc3RUb29sL2NhbWVyYVV0aWxzXCI7XHJcblxyXG5pbXBvcnQgeyBhcHBseU1hdGVyaWFsVG9PYmplY3QgfSBmcm9tIFwiLi4vLi4vdXRpbHMvQXJ0aXN0VG9vbC9tYXRlcmlhbFV0aWxzXCI7XHJcbmltcG9ydCB7XHJcbiAgY2FsY3VsYXRlTW9kZWxTdGF0cyxcclxuICBzZXRTaGFkb3dzT25Nb2RlbCxcclxufSBmcm9tIFwiLi4vLi4vdXRpbHMvQXJ0aXN0VG9vbC9zY2VuZVV0aWxzXCI7XHJcbmltcG9ydCB7IHByZWRlZmluZWRBbmltYXRpb25zIH0gZnJvbSBcIi4vYW5pbWF0aW9ucy9DYW1lcmFBbmltYXRpb25zXCI7XHJcbmltcG9ydCB7IEVuZ3JhdmluZ1BhbmVsIH0gZnJvbSBcIi4vRW5ncmF2aW5nUGFuZWxcIjtcclxuaW1wb3J0IHsgSW5pdGlhbE92ZXJsYXlQYW5lbCB9IGZyb20gXCIuL0luaXRpYWxPdmVybGF5UGFuZWxcIjtcclxuaW1wb3J0IHsgTW9kZWxTdGFnZSB9IGZyb20gXCIuL01vZGVsU3RhZ2VcIjtcclxuaW1wb3J0IHsgUG9zdFByb2Nlc3NpbmdFZmZlY3RzIH0gZnJvbSBcIi4vUG9zdFByb2Nlc3NpbmdFZmZlY3RzXCI7XHJcbmltcG9ydCB7IFNlbGVjdGlvbk91dGxpbmUgfSBmcm9tIFwiLi9TZWxlY3Rpb25PdXRsaW5lXCI7XHJcbmltcG9ydCB7XHJcbiAgY3JlYXRlVmVyc2lvbixcclxuICBnZXRDdXJyZW50VmVyc2lvbixcclxufSBmcm9tIFwiQC9saWIvYWN0aW9ucy92ZXJzaW9uLmFjdGlvbnNcIjtcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwic29ubmVyXCI7XHJcbmltcG9ydCB7IHVzZVZlcnNpb25TdGF0ZSwgdXNlQ29uZmlnU3RhdGUgfSBmcm9tIFwiQC9saWIvc3RhdGVzXCI7XHJcblxyXG5jb25zdCBTY3JlZW5zaG90VGFrZXIgPSAoeyBvblNjcmVlbnNob3RSZWYgfSkgPT4ge1xyXG4gIGNvbnN0IHsgZ2wsIHNjZW5lLCBjYW1lcmEgfSA9IHVzZVRocmVlKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAodHlwZW9mIG9uU2NyZWVuc2hvdFJlZiA9PT0gXCJmdW5jdGlvblwiKSB7XHJcbiAgICAgIG9uU2NyZWVuc2hvdFJlZigoKSA9PiB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIC8vIFJlbmRlciB0aGUgc2NlbmVcclxuICAgICAgICAgIGdsLnJlbmRlcihzY2VuZSwgY2FtZXJhKTtcclxuXHJcbiAgICAgICAgICAvLyBHZXQgdGhlIGNhbnZhcyBkYXRhIFVSTFxyXG4gICAgICAgICAgY29uc3QgZGF0YVVSTCA9IGdsLmRvbUVsZW1lbnQudG9EYXRhVVJMKFwiaW1hZ2UvcG5nXCIpO1xyXG5cclxuICAgICAgICAgIC8vIENyZWF0ZSBhbmQgdHJpZ2dlciBhIGRvd25sb2FkXHJcbiAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImFcIik7XHJcbiAgICAgICAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpXHJcbiAgICAgICAgICAgIC50b0lTT1N0cmluZygpXHJcbiAgICAgICAgICAgIC5yZXBsYWNlKC86L2csIFwiLVwiKVxyXG4gICAgICAgICAgICAuc3Vic3RyaW5nKDAsIDE5KTtcclxuICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSBgYWdhcGUtc2NlbmUtJHt0aW1lc3RhbXB9LnBuZ2A7XHJcbiAgICAgICAgICBsaW5rLmhyZWYgPSBkYXRhVVJMO1xyXG4gICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcclxuICAgICAgICAgIGxpbmsuY2xpY2soKTtcclxuXHJcbiAgICAgICAgICAvLyBDbGVhbiB1cFxyXG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XHJcbiAgICAgICAgICB9LCAxMDApO1xyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwiU2NyZWVuc2hvdCBjYXB0dXJlZCBzdWNjZXNzZnVsbHlcIik7XHJcbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNhcHR1cmluZyBzY3JlZW5zaG90OlwiLCBlcnJvcik7XHJcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9LCBbZ2wsIHNjZW5lLCBjYW1lcmEsIG9uU2NyZWVuc2hvdFJlZl0pO1xyXG5cclxuICByZXR1cm4gbnVsbDtcclxufTtcclxuXHJcbi8vIENvbXBvbmVudCB0byBwcm92aWRlIHJlbmRlcmVyIHRvIE1hdGVyaWFsUGFuZWxcclxuY29uc3QgTWF0ZXJpYWxQYW5lbFdyYXBwZXIgPSAoeyByZW5kZXJlciwgLi4ucHJvcHMgfSkgPT4ge1xyXG4gIHJldHVybiA8TWF0ZXJpYWxQYW5lbCByZW5kZXJlcj17cmVuZGVyZXJ9IHsuLi5wcm9wc30gLz47XHJcbn07XHJcblxyXG4vLyBDb21wb25lbnQgdG8gZ2V0IHJlbmRlcmVyIGZyb20gQ2FudmFzIGNvbnRleHRcclxuY29uc3QgUmVuZGVyZXJQcm92aWRlciA9ICh7IGNoaWxkcmVuLCBvblJlbmRlcmVyUmVhZHkgfSkgPT4ge1xyXG4gIGNvbnN0IHsgZ2wgfSA9IHVzZVRocmVlKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAob25SZW5kZXJlclJlYWR5KSB7XHJcbiAgICAgIG9uUmVuZGVyZXJSZWFkeShnbCk7XHJcbiAgICB9XHJcbiAgfSwgW2dsLCBvblJlbmRlcmVyUmVhZHldKTtcclxuXHJcbiAgcmV0dXJuIG51bGw7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFZGl0b3JFeHBlcmllbmNlKHsgbW9kZWxVcmwsIHByb2plY3QsIHVzZXIgfSkge1xyXG4gIGNvbnN0IHsgYWRkVmVyc2lvbiB9ID0gdXNlVmVyc2lvblN0YXRlKCk7XHJcbiAgY29uc3Qge1xyXG4gICAgd29ya2luZ0NvbmZpZyxcclxuICAgIGRiU3luY2VkQ29uZmlnLFxyXG4gICAgc2V0V29ya2luZ0NvbmZpZyxcclxuICAgIHNldERiU3luY2VkQ29uZmlnLFxyXG4gICAgcmVzZXRXb3JraW5nQ29uZmlnLFxyXG4gIH0gPSB1c2VDb25maWdTdGF0ZSgpO1xyXG5cclxuICBjb25zdCBjb25maWdSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgY29uc3Qgd29ya2luZ0NvbmZpZ1JlZiA9IHVzZVJlZihudWxsKTtcclxuICBjb25zdCBbc2VsZWN0ZWRNb2RlbCwgc2V0U2VsZWN0ZWRNb2RlbF0gPSB1c2VTdGF0ZShcInVwbG9hZGVkXCIpO1xyXG4gIGNvbnN0IFtsYXN0U2VsZWN0ZWRNb2RlbCwgc2V0TGFzdFNlbGVjdGVkTW9kZWxdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW3VwbG9hZGVkTW9kZWwsIHNldFVwbG9hZGVkTW9kZWxdID0gdXNlU3RhdGUobW9kZWxVcmwgfHwgbnVsbCk7XHJcbiAgY29uc3QgW21vZGVsU3RhdHMsIHNldE1vZGVsU3RhdHNdID0gdXNlU3RhdGUoe1xyXG4gICAgdmVydGljZXM6IDAsXHJcbiAgICB0cmlhbmdsZXM6IDAsXHJcbiAgICBtYXRlcmlhbHM6IDAsXHJcbiAgICBhbmltYXRpb25zOiAwLFxyXG4gIH0pO1xyXG4gIGNvbnN0IFtsaWdodHMsIHNldExpZ2h0c10gPSB1c2VTdGF0ZShbXHJcbiAgICB7XHJcbiAgICAgIGlkOiBcImRlZmF1bHREaXJlY3Rpb25hbFwiLFxyXG4gICAgICB0eXBlOiBcImRpcmVjdGlvbmFsXCIsXHJcbiAgICAgIHBvc2l0aW9uOiBbMi41LCA1LjUsIDVdLFxyXG4gICAgICBpbnRlbnNpdHk6IDEuMCxcclxuICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICBuYW1lOiBcIk1haW4gRGlyZWN0aW9uYWwgTGlnaHRcIixcclxuICAgICAgY2FzdFNoYWRvdzogdHJ1ZSxcclxuICAgICAgaGVscGVyVmlzaWJsZTogZmFsc2UsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpZDogXCJrZXlMaWdodFwiLFxyXG4gICAgICB0eXBlOiBcInBvaW50XCIsXHJcbiAgICAgIHBvc2l0aW9uOiBbNSwgNSwgNV0sXHJcbiAgICAgIGludGVuc2l0eTogOC4wLFxyXG4gICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgIG5hbWU6IFwiS2V5IExpZ2h0XCIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpZDogXCJmaWxsTGlnaHRcIixcclxuICAgICAgdHlwZTogXCJwb2ludFwiLFxyXG4gICAgICBwb3NpdGlvbjogWy01LCA1LCAtNV0sXHJcbiAgICAgIGludGVuc2l0eTogNC4wLFxyXG4gICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgIG5hbWU6IFwiRmlsbCBMaWdodFwiLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaWQ6IFwiYmFja0xpZ2h0XCIsXHJcbiAgICAgIHR5cGU6IFwicG9pbnRcIixcclxuICAgICAgcG9zaXRpb246IFswLCA1LCAtNV0sXHJcbiAgICAgIGludGVuc2l0eTogMi4wLFxyXG4gICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgIG5hbWU6IFwiQmFjayBMaWdodFwiLFxyXG4gICAgfSxcclxuICBdKTtcclxuICBjb25zdCBsaWdodFJlZnMgPSB1c2VSZWYoe30pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZE9iamVjdHMsIHNldFNlbGVjdGVkT2JqZWN0c10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkTGlnaHQsIHNldFNlbGVjdGVkTGlnaHRdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2xpZ2h0UG9zaXRpb25IaXN0b3J5LCBzZXRMaWdodFBvc2l0aW9uSGlzdG9yeV0gPSB1c2VTdGF0ZSh7fSk7XHJcbiAgY29uc3QgW3NjZW5lT2JqZWN0cywgc2V0U2NlbmVPYmplY3RzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbdHJhbnNmb3JtTW9kZSwgc2V0VHJhbnNmb3JtTW9kZV0gPSB1c2VTdGF0ZShcInRyYW5zbGF0ZVwiKTtcclxuICBjb25zdCBzZWxlY3Rpb25Hcm91cFJlZiA9IHVzZVJlZihuZXcgVEhSRUUuR3JvdXAoKSk7XHJcblxyXG4gIGNvbnNvbGUubG9nKFwicmVuZGVyaW5nZ2dnIGVkaXRvciBleHBlcmllbmNlXCIpO1xyXG5cclxuICBjb25zdCBbcmlnaHRQYW5lbFRhYiwgc2V0UmlnaHRQYW5lbFRhYl0gPSB1c2VTdGF0ZShudWxsKTtcclxuXHJcbiAgLy8gRGVjYWwgcGxhY2VtZW50IHN0YXRlXHJcbiAgY29uc3QgW2lzUGxhY2luZ0RlY2FsLCBzZXRJc1BsYWNpbmdEZWNhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dEZWNhbERlYnVnLCBzZXRTaG93RGVjYWxEZWJ1Z10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIFNjZW5lIGNvbnRyb2xzIHN0YXRlXHJcbiAgY29uc3QgW3Nob3dHcmlkLCBzZXRTaG93R3JpZF0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbd2lyZWZyYW1lLCBzZXRXaXJlZnJhbWVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtncm91bmRUeXBlLCBzZXRHcm91bmRUeXBlXSA9IHVzZVN0YXRlKFwibm9uZVwiKTtcclxuICBjb25zdCBbZW52UHJlc2V0LCBzZXRFbnZQcmVzZXRdID0gdXNlU3RhdGUoXCJoZHJpXzE1XCIpO1xyXG4gIGNvbnN0IFtiZ0NvbG9yLCBzZXRCZ0NvbG9yXSA9IHVzZVN0YXRlKFwiIzI3MjYzMVwiKTtcclxuICBjb25zdCBbc2hvd0Vudmlyb25tZW50LCBzZXRTaG93RW52aXJvbm1lbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzaG93TGlnaHRTcGhlcmVzLCBzZXRTaG93TGlnaHRTcGhlcmVzXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtjdXN0b21IZHJpLCBzZXRDdXN0b21IZHJpXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtlbnZJbnRlbnNpdHksIHNldEVudkludGVuc2l0eV0gPSB1c2VTdGF0ZSgxLjg1KTtcclxuICBjb25zdCBbZW52Qmx1ciwgc2V0RW52Qmx1cl0gPSB1c2VTdGF0ZSgwLjM1KTtcclxuICBjb25zdCBbZW52Um90YXRpb24sIHNldEVudlJvdGF0aW9uXSA9IHVzZVN0YXRlKDApO1xyXG5cclxuICBjb25zdCBbcG9zdFByb2Nlc3NpbmdFbmFibGVkLCBzZXRQb3N0UHJvY2Vzc2luZ0VuYWJsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwb3N0UHJvY2Vzc2luZ1NldHRpbmdzLCBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzXSA9IHVzZVN0YXRlKHtcclxuICAgIGF1dG9mb2N1czoge1xyXG4gICAgICBlbmFibGVkOiBmYWxzZSxcclxuICAgICAgYm9rZWhTY2FsZTogMTgsXHJcbiAgICB9LFxyXG4gICAgYmxvb206IHtcclxuICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgaW50ZW5zaXR5OiAwLjIyLFxyXG4gICAgICB0aHJlc2hvbGQ6IDEsXHJcbiAgICAgIHJhZGl1czogMC41LFxyXG4gICAgfSxcclxuICAgIGRvZjoge1xyXG4gICAgICBlbmFibGVkOiBmYWxzZSxcclxuICAgICAgZm9jdXNEaXN0YW5jZTogMCxcclxuICAgICAgYXBlcnR1cmU6IDAuMDEsXHJcbiAgICAgIGJva2VoU2NhbGU6IDMsXHJcbiAgICB9LFxyXG4gICAgdmlnbmV0dGU6IHtcclxuICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgZGFya25lc3M6IDAuNSxcclxuICAgICAgb2Zmc2V0OiAwLjEsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuICAvLyBTdG9yZSBpbml0aWFsIHN0YXRlXHJcbiAgY29uc3QgaW5pdGlhbFN0YXRlUmVmID0gdXNlUmVmKHtcclxuICAgIHNlbGVjdGVkTW9kZWw6IG51bGwsXHJcbiAgICBsaWdodHM6IFtdLFxyXG4gICAgZW52UHJlc2V0OiBudWxsLFxyXG4gICAgYmdDb2xvcjogbnVsbCxcclxuICAgIHBvc3RQcm9jZXNzaW5nU2V0dGluZ3M6IG51bGwsXHJcbiAgICBzaG93R3JpZDogbnVsbCxcclxuICAgIHdpcmVmcmFtZTogbnVsbCxcclxuICAgIGdyb3VuZFR5cGU6IG51bGwsXHJcbiAgICBzaG93RW52aXJvbm1lbnQ6IG51bGwsXHJcbiAgICBzaG93TGlnaHRTcGhlcmVzOiBudWxsLFxyXG4gICAgZW52SW50ZW5zaXR5OiBudWxsLFxyXG4gICAgZW52Qmx1cjogbnVsbCxcclxuICAgIGNhbWVyYVBvc2l0aW9uOiBudWxsLFxyXG4gICAgY2FtZXJhVGFyZ2V0OiBudWxsLFxyXG4gIH0pO1xyXG5cclxuICAvLyBBZGQgYSByZWYgdG8gc3RvcmUgdGhlIGluaXRpYWwgc3RhdGUgb2YgbW9kZWwgb2JqZWN0c1xyXG4gIGNvbnN0IG1vZGVsSW5pdGlhbFN0YXRlUmVmID0gdXNlUmVmKHtcclxuICAgIG9iamVjdHM6IFtdLFxyXG4gIH0pO1xyXG5cclxuICAvLyBDYXB0dXJlIGluaXRpYWwgc3RhdGUgd2hlbiBjb21wb25lbnQgbW91bnRzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGluaXRpYWxTdGF0ZVJlZi5jdXJyZW50ID0ge1xyXG4gICAgICBzZWxlY3RlZE1vZGVsLFxyXG4gICAgICBsaWdodHM6IFsuLi5saWdodHNdLFxyXG4gICAgICBlbnZQcmVzZXQsXHJcbiAgICAgIGJnQ29sb3IsXHJcbiAgICAgIHBvc3RQcm9jZXNzaW5nU2V0dGluZ3M6IHsgLi4ucG9zdFByb2Nlc3NpbmdTZXR0aW5ncyB9LFxyXG4gICAgICBzaG93R3JpZCxcclxuICAgICAgd2lyZWZyYW1lLFxyXG4gICAgICBncm91bmRUeXBlLFxyXG4gICAgICBzaG93RW52aXJvbm1lbnQsXHJcbiAgICAgIHNob3dMaWdodFNwaGVyZXMsXHJcbiAgICAgIGVudkludGVuc2l0eSxcclxuICAgICAgZW52Qmx1cixcclxuICAgICAgY2FtZXJhUG9zaXRpb246IG5ldyBUSFJFRS5WZWN0b3IzKDUsIDIsIDUpLFxyXG4gICAgICBjYW1lcmFUYXJnZXQ6IG5ldyBUSFJFRS5WZWN0b3IzKDAsIDEsIDApLFxyXG4gICAgfTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IG9yYml0Q29udHJvbHNSZWYgPSB1c2VSZWYoKTtcclxuICBjb25zdCBzY2VuZVJlZiA9IHVzZVJlZigpO1xyXG4gIGNvbnN0IFtyZW5kZXJlciwgc2V0UmVuZGVyZXJdID0gdXNlU3RhdGUobnVsbCk7XHJcblxyXG4gIC8vIEhpc3Rvcnkgc3RhdGUgZm9yIHVuZG8vcmVkb1xyXG4gIGNvbnN0IFtoaXN0b3J5LCBzZXRIaXN0b3J5XSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbaGlzdG9yeUluZGV4LCBzZXRIaXN0b3J5SW5kZXhdID0gdXNlU3RhdGUoLTEpO1xyXG4gIGNvbnN0IGlzVW5kb1JlZG9BY3Rpb25SZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gIGNvbnN0IHNraXBIaXN0b3J5VHJhY2tpbmdSZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gIGNvbnN0IGlzVW5kb1JlZG9JblByb2dyZXNzUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuXHJcbiAgY29uc3QgW3Nob3dNb2RlbFN0YXRzLCBzZXRTaG93TW9kZWxTdGF0c10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIERlYnVnIGZ1bmN0aW9uIHRvIGluc3BlY3QgaGlzdG9yeSBzdGF0ZVxyXG4gIGNvbnN0IGRlYnVnSGlzdG9yeSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiPT09IEhJU1RPUlkgREVCVUcgPT09XCIpO1xyXG4gICAgY29uc29sZS5sb2coXCJDdXJyZW50IGhpc3RvcnlJbmRleDpcIiwgaGlzdG9yeUluZGV4KTtcclxuICAgIGNvbnNvbGUubG9nKFwiSGlzdG9yeSBsZW5ndGg6XCIsIGhpc3RvcnkubGVuZ3RoKTtcclxuICAgIGNvbnNvbGUubG9nKFwiQ2FuIHVuZG86XCIsIGhpc3RvcnlJbmRleCA+IDApO1xyXG4gICAgY29uc29sZS5sb2coXCJDYW4gcmVkbzpcIiwgaGlzdG9yeUluZGV4IDwgaGlzdG9yeS5sZW5ndGggLSAxKTtcclxuICAgIGNvbnNvbGUubG9nKFwiaXNVbmRvUmVkb0FjdGlvblJlZi5jdXJyZW50OlwiLCBpc1VuZG9SZWRvQWN0aW9uUmVmLmN1cnJlbnQpO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwic2tpcEhpc3RvcnlUcmFja2luZ1JlZi5jdXJyZW50OlwiLFxyXG4gICAgICBza2lwSGlzdG9yeVRyYWNraW5nUmVmLmN1cnJlbnRcclxuICAgICk7XHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgXCJpc1VuZG9SZWRvSW5Qcm9ncmVzc1JlZi5jdXJyZW50OlwiLFxyXG4gICAgICBpc1VuZG9SZWRvSW5Qcm9ncmVzc1JlZi5jdXJyZW50XHJcbiAgICApO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwiSGlzdG9yeSBlbnRyaWVzOlwiLFxyXG4gICAgICBoaXN0b3J5Lm1hcCgoXywgaSkgPT4gYCR7aX0ke2kgPT09IGhpc3RvcnlJbmRleCA/IFwiIChjdXJyZW50KVwiIDogXCJcIn1gKVxyXG4gICAgKTtcclxuICAgIGNvbnNvbGUubG9nKFwiPT09PT09PT09PT09PT09PT09XCIpO1xyXG4gIH0sIFtoaXN0b3J5SW5kZXgsIGhpc3RvcnldKTtcclxuXHJcbiAgLy8gRXhwb3NlIGRlYnVnIGZ1bmN0aW9uIHRvIHdpbmRvdyBmb3IgdGVzdGluZ1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICB3aW5kb3cuZGVidWdIaXN0b3J5ID0gZGVidWdIaXN0b3J5O1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgZGVsZXRlIHdpbmRvdy5kZWJ1Z0hpc3Rvcnk7XHJcbiAgICB9O1xyXG4gIH0sIFtkZWJ1Z0hpc3RvcnldKTtcclxuXHJcbiAgLy8gR2V0IGZ1bGwgY29uZmlndXJhdGlvbiBmb3IgaGlzdG9yeVxyXG4gIGNvbnN0IGdldEZ1bGxDb25maWcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICByZXR1cm4gSlNPTi5wYXJzZShcclxuICAgICAgSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgIHNlbGVjdGVkTW9kZWwsXHJcbiAgICAgICAgdXBsb2FkZWRNb2RlbCxcclxuICAgICAgICBsaWdodHMsXHJcbiAgICAgICAgZW52UHJlc2V0LFxyXG4gICAgICAgIGJnQ29sb3IsXHJcbiAgICAgICAgc2hvd0Vudmlyb25tZW50LFxyXG4gICAgICAgIHNob3dMaWdodFNwaGVyZXMsXHJcbiAgICAgICAgY3VzdG9tSGRyaSxcclxuICAgICAgICBlbnZJbnRlbnNpdHksXHJcbiAgICAgICAgZW52Qmx1cixcclxuICAgICAgICBlbnZSb3RhdGlvbixcclxuICAgICAgICBwb3N0UHJvY2Vzc2luZ0VuYWJsZWQsXHJcbiAgICAgICAgcG9zdFByb2Nlc3NpbmdTZXR0aW5ncyxcclxuICAgICAgICBzaG93R3JpZCxcclxuICAgICAgICB3aXJlZnJhbWUsXHJcbiAgICAgICAgZ3JvdW5kVHlwZSxcclxuICAgICAgICBzZWxlY3RlZE9iamVjdHM6IHNlbGVjdGVkT2JqZWN0cy5tYXAoKG9iaikgPT4gb2JqLnV1aWQpLFxyXG4gICAgICAgIC8vIFN0b3JlIG9iamVjdCBzdGF0ZXMgZm9yIHRyYW5zZm9ybXMgYW5kIG1hdGVyaWFsc1xyXG4gICAgICAgIG9iamVjdFN0YXRlczogc2NlbmVPYmplY3RzLm1hcCgob2JqKSA9PiAoe1xyXG4gICAgICAgICAgdXVpZDogb2JqLnV1aWQsXHJcbiAgICAgICAgICBuYW1lOiBvYmoubmFtZSxcclxuICAgICAgICAgIHBvc2l0aW9uOiBvYmoucG9zaXRpb24udG9BcnJheSgpLFxyXG4gICAgICAgICAgcm90YXRpb246IG9iai5yb3RhdGlvbi50b0FycmF5KCksXHJcbiAgICAgICAgICBzY2FsZTogb2JqLnNjYWxlLnRvQXJyYXkoKSxcclxuICAgICAgICAgIG1hdGVyaWFsOiBvYmoubWF0ZXJpYWxcclxuICAgICAgICAgICAgPyB7XHJcbiAgICAgICAgICAgICAgICB0eXBlOiBvYmoubWF0ZXJpYWwudHlwZSxcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBvYmoubWF0ZXJpYWwuY29sb3JcclxuICAgICAgICAgICAgICAgICAgPyBvYmoubWF0ZXJpYWwuY29sb3IuZ2V0SGV4U3RyaW5nKClcclxuICAgICAgICAgICAgICAgICAgOiBcImZmZmZmZlwiLFxyXG4gICAgICAgICAgICAgICAgcm91Z2huZXNzOiBvYmoubWF0ZXJpYWwucm91Z2huZXNzLFxyXG4gICAgICAgICAgICAgICAgbWV0YWxuZXNzOiBvYmoubWF0ZXJpYWwubWV0YWxuZXNzLFxyXG4gICAgICAgICAgICAgICAgY2xlYXJjb2F0OiBvYmoubWF0ZXJpYWwuY2xlYXJjb2F0LFxyXG4gICAgICAgICAgICAgICAgY2xlYXJjb2F0Um91Z2huZXNzOiBvYmoubWF0ZXJpYWwuY2xlYXJjb2F0Um91Z2huZXNzLFxyXG4gICAgICAgICAgICAgICAgd2lyZWZyYW1lOiBvYmoubWF0ZXJpYWwud2lyZWZyYW1lLFxyXG4gICAgICAgICAgICAgICAgdHJhbnNwYXJlbnQ6IG9iai5tYXRlcmlhbC50cmFuc3BhcmVudCxcclxuICAgICAgICAgICAgICAgIG9wYWNpdHk6IG9iai5tYXRlcmlhbC5vcGFjaXR5LFxyXG4gICAgICAgICAgICAgICAgZW52TWFwSW50ZW5zaXR5OiBvYmoubWF0ZXJpYWwuZW52TWFwSW50ZW5zaXR5LFxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgOiBudWxsLFxyXG4gICAgICAgIH0pKSxcclxuICAgICAgfSlcclxuICAgICk7XHJcbiAgfSwgW1xyXG4gICAgc2VsZWN0ZWRNb2RlbCxcclxuICAgIHVwbG9hZGVkTW9kZWwsXHJcbiAgICBsaWdodHMsXHJcbiAgICBlbnZQcmVzZXQsXHJcbiAgICBiZ0NvbG9yLFxyXG4gICAgc2hvd0Vudmlyb25tZW50LFxyXG4gICAgc2hvd0xpZ2h0U3BoZXJlcyxcclxuICAgIGN1c3RvbUhkcmksXHJcbiAgICBlbnZJbnRlbnNpdHksXHJcbiAgICBlbnZCbHVyLFxyXG4gICAgZW52Um90YXRpb24sXHJcbiAgICBwb3N0UHJvY2Vzc2luZ0VuYWJsZWQsXHJcbiAgICBwb3N0UHJvY2Vzc2luZ1NldHRpbmdzLFxyXG4gICAgc2hvd0dyaWQsXHJcbiAgICB3aXJlZnJhbWUsXHJcbiAgICBncm91bmRUeXBlLFxyXG4gICAgc2VsZWN0ZWRPYmplY3RzLFxyXG4gICAgc2NlbmVPYmplY3RzLFxyXG4gIF0pO1xyXG5cclxuICAvLyBHZXQgY29uZmlndXJhdGlvbiBmb3IgdW5zYXZlZCBjaGFuZ2VzIGNvbXBhcmlzb24gKG9ubHkgc3BlY2lmaWMgZmllbGRzKVxyXG4gIGNvbnN0IGdldENvbXBhcmlzb25Db25maWcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBtYXRlcmlhbHM6IHNjZW5lT2JqZWN0cy5tYXAoKG9iaikgPT4gKHtcclxuICAgICAgICB1dWlkOiBvYmoudXVpZCxcclxuICAgICAgICBuYW1lOiBvYmoubmFtZSxcclxuICAgICAgICBtYXRlcmlhbDogb2JqLm1hdGVyaWFsXHJcbiAgICAgICAgICA/IHtcclxuICAgICAgICAgICAgICB0eXBlOiBvYmoubWF0ZXJpYWwudHlwZSxcclxuICAgICAgICAgICAgICBjb2xvcjogb2JqLm1hdGVyaWFsLmNvbG9yXHJcbiAgICAgICAgICAgICAgICA/IG9iai5tYXRlcmlhbC5jb2xvci5nZXRIZXhTdHJpbmcoKVxyXG4gICAgICAgICAgICAgICAgOiBcImZmZmZmZlwiLFxyXG4gICAgICAgICAgICAgIHJvdWdobmVzczogb2JqLm1hdGVyaWFsLnJvdWdobmVzcyxcclxuICAgICAgICAgICAgICBtZXRhbG5lc3M6IG9iai5tYXRlcmlhbC5tZXRhbG5lc3MsXHJcbiAgICAgICAgICAgICAgY2xlYXJjb2F0OiBvYmoubWF0ZXJpYWwuY2xlYXJjb2F0LFxyXG4gICAgICAgICAgICAgIGNsZWFyY29hdFJvdWdobmVzczogb2JqLm1hdGVyaWFsLmNsZWFyY29hdFJvdWdobmVzcyxcclxuICAgICAgICAgICAgICB3aXJlZnJhbWU6IG9iai5tYXRlcmlhbC53aXJlZnJhbWUsXHJcbiAgICAgICAgICAgICAgdHJhbnNwYXJlbnQ6IG9iai5tYXRlcmlhbC50cmFuc3BhcmVudCxcclxuICAgICAgICAgICAgICBvcGFjaXR5OiBvYmoubWF0ZXJpYWwub3BhY2l0eSxcclxuICAgICAgICAgICAgICBlbnZNYXBJbnRlbnNpdHk6IG9iai5tYXRlcmlhbC5lbnZNYXBJbnRlbnNpdHksXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIDogbnVsbCxcclxuICAgICAgfSkpLFxyXG4gICAgICBwb3N0UHJvY2Vzc2luZzoge1xyXG4gICAgICAgIGVuYWJsZWQ6IHBvc3RQcm9jZXNzaW5nRW5hYmxlZCxcclxuICAgICAgICBzZXR0aW5nczogcG9zdFByb2Nlc3NpbmdTZXR0aW5ncyxcclxuICAgICAgfSxcclxuICAgICAgbGlnaHRzOiBsaWdodHMubWFwKChsaWdodCkgPT4gKHtcclxuICAgICAgICAuLi5saWdodCxcclxuICAgICAgICBwb3NpdGlvbjpcclxuICAgICAgICAgIGxpZ2h0UmVmcy5jdXJyZW50W2xpZ2h0LmlkXT8ucG9zaXRpb24udG9BcnJheSgpIHx8IGxpZ2h0LnBvc2l0aW9uLFxyXG4gICAgICB9KSksXHJcbiAgICAgIGVudmlyb25tZW50OiB7XHJcbiAgICAgICAgcHJlc2V0OiBlbnZQcmVzZXQsXHJcbiAgICAgICAgaW50ZW5zaXR5OiBlbnZJbnRlbnNpdHksXHJcbiAgICAgICAgYmx1cjogZW52Qmx1cixcclxuICAgICAgICByb3RhdGlvbjogZW52Um90YXRpb24sXHJcbiAgICAgICAgc2hvd0Vudmlyb25tZW50LFxyXG4gICAgICAgIGJnQ29sb3IsXHJcbiAgICAgICAgY3VzdG9tSGRyaSxcclxuICAgICAgfSxcclxuICAgIH07XHJcbiAgfSwgW1xyXG4gICAgc2NlbmVPYmplY3RzLFxyXG4gICAgcG9zdFByb2Nlc3NpbmdFbmFibGVkLFxyXG4gICAgcG9zdFByb2Nlc3NpbmdTZXR0aW5ncyxcclxuICAgIGxpZ2h0cyxcclxuICAgIGVudlByZXNldCxcclxuICAgIGVudkludGVuc2l0eSxcclxuICAgIGVudkJsdXIsXHJcbiAgICBlbnZSb3RhdGlvbixcclxuICAgIHNob3dFbnZpcm9ubWVudCxcclxuICAgIGJnQ29sb3IsXHJcbiAgICBjdXN0b21IZHJpLFxyXG4gIF0pO1xyXG5cclxuICAvLyBDaGVjayBpZiB0aGVyZSBhcmUgdW5zYXZlZCBjaGFuZ2VzXHJcbiAgY29uc3QgaGFzVW5zYXZlZENoYW5nZXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBpZiAoIWRiU3luY2VkQ29uZmlnKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgY29uc3QgY3VycmVudENvbmZpZyA9IGdldENvbXBhcmlzb25Db25maWcoKTtcclxuICAgIGNvbnN0IHNhdmVkQ29uZmlnID0ge1xyXG4gICAgICBtYXRlcmlhbHM6IGRiU3luY2VkQ29uZmlnLm1hdGVyaWFscyB8fCBbXSxcclxuICAgICAgcG9zdFByb2Nlc3Npbmc6IGRiU3luY2VkQ29uZmlnLnBvc3RQcm9jZXNzaW5nIHx8IHtcclxuICAgICAgICBlbmFibGVkOiBmYWxzZSxcclxuICAgICAgICBzZXR0aW5nczoge30sXHJcbiAgICAgIH0sXHJcbiAgICAgIGxpZ2h0czogZGJTeW5jZWRDb25maWcubGlnaHRzIHx8IFtdLFxyXG4gICAgICBlbnZpcm9ubWVudDogZGJTeW5jZWRDb25maWcuZW52aXJvbm1lbnQgfHwge30sXHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShjdXJyZW50Q29uZmlnKSAhPT0gSlNPTi5zdHJpbmdpZnkoc2F2ZWRDb25maWcpO1xyXG4gIH0sIFtkYlN5bmNlZENvbmZpZywgZ2V0Q29tcGFyaXNvbkNvbmZpZ10pO1xyXG5cclxuICAvLyBSZXZlcnQgdG8gbGF0ZXN0IHNhdmVkIHZlcnNpb25cclxuICBjb25zdCByZXZlcnRUb1NhdmVkID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgaWYgKCFkYlN5bmNlZENvbmZpZykge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIk5vIHNhdmVkIHZlcnNpb24gdG8gcmV2ZXJ0IHRvXCIpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgY29uZmlybVJldmVydCA9IHdpbmRvdy5jb25maXJtKFxyXG4gICAgICBcIkFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byByZXZlcnQgYWxsIGNoYW5nZXMgdG8gdGhlIGxhc3Qgc2F2ZWQgdmVyc2lvbj8gVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS5cIlxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoY29uZmlybVJldmVydCkge1xyXG4gICAgICBoYW5kbGVMb2FkU2NlbmUoZGJTeW5jZWRDb25maWcpO1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKFwiUmV2ZXJ0ZWQgdG8gbGFzdCBzYXZlZCB2ZXJzaW9uXCIpO1xyXG4gICAgfVxyXG4gIH0sIFtkYlN5bmNlZENvbmZpZ10pO1xyXG5cclxuICAvLyBBZGQgaW5pdGlhbCBzdGF0ZSB0byBoaXN0b3J5IHdoZW4gY29tcG9uZW50IG1vdW50c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaGlzdG9yeS5sZW5ndGggPT09IDApIHtcclxuICAgICAgY29uc29sZS5sb2coXCJbSW5pdGlhbF0gQWRkaW5nIGluaXRpYWwgc3RhdGUgdG8gaGlzdG9yeVwiKTtcclxuICAgICAgY29uc3QgaW5pdGlhbFN0YXRlID0gZ2V0RnVsbENvbmZpZygpO1xyXG4gICAgICBzZXRIaXN0b3J5KFtpbml0aWFsU3RhdGVdKTtcclxuICAgICAgc2V0SGlzdG9yeUluZGV4KDApO1xyXG4gICAgfVxyXG4gIH0sIFtoaXN0b3J5Lmxlbmd0aCwgZ2V0RnVsbENvbmZpZ10pO1xyXG5cclxuICAvLyBBZGQgYSBuZXcgc3RhdGUgdG8gdGhlIGhpc3RvcnlcclxuICBjb25zdCBhZGRUb0hpc3RvcnkgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBpZiAoaXNVbmRvUmVkb0FjdGlvblJlZi5jdXJyZW50IHx8IHNraXBIaXN0b3J5VHJhY2tpbmdSZWYuY3VycmVudCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlthZGRUb0hpc3RvcnldIFNraXBwaW5nIC0gdW5kby9yZWRvIGFjdGlvbiBpbiBwcm9ncmVzc1wiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IG5ld1N0YXRlID0gZ2V0RnVsbENvbmZpZygpO1xyXG5cclxuICAgIC8vIENoZWNrIGlmIHRoZSBuZXcgc3RhdGUgaXMgZGlmZmVyZW50IGZyb20gdGhlIGN1cnJlbnQgc3RhdGVcclxuICAgIGlmIChoaXN0b3J5Lmxlbmd0aCA+IDAgJiYgaGlzdG9yeUluZGV4ID49IDApIHtcclxuICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gaGlzdG9yeVtoaXN0b3J5SW5kZXhdO1xyXG4gICAgICBpZiAoSlNPTi5zdHJpbmdpZnkoY3VycmVudFN0YXRlKSA9PT0gSlNPTi5zdHJpbmdpZnkobmV3U3RhdGUpKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJbYWRkVG9IaXN0b3J5XSBTa2lwcGluZyAtIHN0YXRlIHVuY2hhbmdlZFwiKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBuZXdIaXN0b3J5ID0gaGlzdG9yeS5zbGljZSgwLCBoaXN0b3J5SW5kZXggKyAxKTtcclxuICAgIHNldEhpc3RvcnkoWy4uLm5ld0hpc3RvcnksIG5ld1N0YXRlXSk7XHJcbiAgICBjb25zdCBuZXdJbmRleCA9IGhpc3RvcnlJbmRleCArIDE7XHJcbiAgICBzZXRIaXN0b3J5SW5kZXgobmV3SW5kZXgpO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwiW2FkZFRvSGlzdG9yeV0gQWRkZWQgc3RhdGUuIE5ldyBoaXN0b3J5SW5kZXg6XCIsXHJcbiAgICAgIG5ld0luZGV4LFxyXG4gICAgICBcIkhpc3RvcnkgbGVuZ3RoOlwiLFxyXG4gICAgICBuZXdIaXN0b3J5Lmxlbmd0aCArIDEsXHJcbiAgICAgIFwiQ2FuIHJlZG86XCIsXHJcbiAgICAgIG5ld0luZGV4IDwgbmV3SGlzdG9yeS5sZW5ndGhcclxuICAgICk7XHJcbiAgfSwgW2hpc3RvcnksIGhpc3RvcnlJbmRleCwgZ2V0RnVsbENvbmZpZ10pO1xyXG5cclxuICBjb25zdCBwZW5kaW5nU2VsZWN0ZWRPYmplY3RzVVVJRHMgPSB1c2VSZWYobnVsbCk7XHJcblxyXG4gIC8vIERlZmluZSBmb3JjZVVwZGF0ZSBhbmQgdmVyc2lvbiBzZXR0ZXJzIGVhcmx5IHNvIHRoZXkgY2FuIGJlIHVzZWQgaW4gcmVzdG9yZUNvbmZpZ1xyXG4gIGNvbnN0IFtfLCBzZXRGb3JjZVVwZGF0ZV0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBmb3JjZVVwZGF0ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHNldEZvcmNlVXBkYXRlKChmKSA9PiBmICsgMSksIFtdKTtcclxuICBjb25zdCBbdHJhbnNmb3JtVmVyc2lvbiwgc2V0VHJhbnNmb3JtVmVyc2lvbl0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbbWF0ZXJpYWxWZXJzaW9uLCBzZXRNYXRlcmlhbFZlcnNpb25dID0gdXNlU3RhdGUoMCk7XHJcblxyXG4gIGNvbnN0IHJlc3RvcmVDb25maWcgPSB1c2VDYWxsYmFjayhcclxuICAgIChjb25maWcpID0+IHtcclxuICAgICAgc2V0U2VsZWN0ZWRNb2RlbChjb25maWcuc2VsZWN0ZWRNb2RlbCk7XHJcbiAgICAgIHNldFVwbG9hZGVkTW9kZWwoY29uZmlnLnVwbG9hZGVkTW9kZWwpO1xyXG4gICAgICBzZXRMaWdodHMoY29uZmlnLmxpZ2h0cyk7XHJcbiAgICAgIHNldEVudlByZXNldChjb25maWcuZW52UHJlc2V0KTtcclxuICAgICAgc2V0QmdDb2xvcihjb25maWcuYmdDb2xvcik7XHJcbiAgICAgIHNldFNob3dFbnZpcm9ubWVudChjb25maWcuc2hvd0Vudmlyb25tZW50KTtcclxuICAgICAgc2V0U2hvd0xpZ2h0U3BoZXJlcyhjb25maWcuc2hvd0xpZ2h0U3BoZXJlcyk7XHJcbiAgICAgIHNldEN1c3RvbUhkcmkoY29uZmlnLmN1c3RvbUhkcmkpO1xyXG4gICAgICBzZXRFbnZJbnRlbnNpdHkoY29uZmlnLmVudkludGVuc2l0eSk7XHJcbiAgICAgIHNldEVudkJsdXIoY29uZmlnLmVudkJsdXIpO1xyXG4gICAgICBzZXRFbnZSb3RhdGlvbihjb25maWcuZW52Um90YXRpb24pO1xyXG4gICAgICBzZXRQb3N0UHJvY2Vzc2luZ0VuYWJsZWQoY29uZmlnLnBvc3RQcm9jZXNzaW5nRW5hYmxlZCk7XHJcbiAgICAgIHNldFBvc3RQcm9jZXNzaW5nU2V0dGluZ3MoY29uZmlnLnBvc3RQcm9jZXNzaW5nU2V0dGluZ3MpO1xyXG4gICAgICBzZXRTaG93R3JpZChjb25maWcuc2hvd0dyaWQpO1xyXG4gICAgICBzZXRXaXJlZnJhbWUoY29uZmlnLndpcmVmcmFtZSk7XHJcbiAgICAgIHNldEdyb3VuZFR5cGUoY29uZmlnLmdyb3VuZFR5cGUpO1xyXG5cclxuICAgICAgLy8gUmVzdG9yZSBvYmplY3Qgc3RhdGVzICh0cmFuc2Zvcm1zIGFuZCBtYXRlcmlhbHMpXHJcbiAgICAgIGlmIChjb25maWcub2JqZWN0U3RhdGVzICYmIHNjZW5lT2JqZWN0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uZmlnLm9iamVjdFN0YXRlcy5mb3JFYWNoKChzYXZlZFN0YXRlKSA9PiB7XHJcbiAgICAgICAgICBjb25zdCBvYmogPSBzY2VuZU9iamVjdHMuZmluZCgobykgPT4gby51dWlkID09PSBzYXZlZFN0YXRlLnV1aWQpO1xyXG4gICAgICAgICAgaWYgKG9iaikge1xyXG4gICAgICAgICAgICAvLyBSZXN0b3JlIHRyYW5zZm9ybVxyXG4gICAgICAgICAgICBvYmoucG9zaXRpb24uZnJvbUFycmF5KHNhdmVkU3RhdGUucG9zaXRpb24pO1xyXG4gICAgICAgICAgICBvYmoucm90YXRpb24uZnJvbUFycmF5KHNhdmVkU3RhdGUucm90YXRpb24pO1xyXG4gICAgICAgICAgICBvYmouc2NhbGUuZnJvbUFycmF5KHNhdmVkU3RhdGUuc2NhbGUpO1xyXG5cclxuICAgICAgICAgICAgLy8gUmVzdG9yZSBtYXRlcmlhbCBwcm9wZXJ0aWVzXHJcbiAgICAgICAgICAgIGlmIChzYXZlZFN0YXRlLm1hdGVyaWFsICYmIG9iai5tYXRlcmlhbCkge1xyXG4gICAgICAgICAgICAgIGNvbnN0IG1hdGVyaWFsID0gb2JqLm1hdGVyaWFsO1xyXG4gICAgICAgICAgICAgIGlmIChtYXRlcmlhbC5jb2xvcikge1xyXG4gICAgICAgICAgICAgICAgbWF0ZXJpYWwuY29sb3Iuc2V0SGV4KHBhcnNlSW50KHNhdmVkU3RhdGUubWF0ZXJpYWwuY29sb3IsIDE2KSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGlmIChzYXZlZFN0YXRlLm1hdGVyaWFsLnJvdWdobmVzcyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICBtYXRlcmlhbC5yb3VnaG5lc3MgPSBzYXZlZFN0YXRlLm1hdGVyaWFsLnJvdWdobmVzcztcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgaWYgKHNhdmVkU3RhdGUubWF0ZXJpYWwubWV0YWxuZXNzICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgIG1hdGVyaWFsLm1ldGFsbmVzcyA9IHNhdmVkU3RhdGUubWF0ZXJpYWwubWV0YWxuZXNzO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBpZiAoc2F2ZWRTdGF0ZS5tYXRlcmlhbC5jbGVhcmNvYXQgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgbWF0ZXJpYWwuY2xlYXJjb2F0ID0gc2F2ZWRTdGF0ZS5tYXRlcmlhbC5jbGVhcmNvYXQ7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGlmIChzYXZlZFN0YXRlLm1hdGVyaWFsLmNsZWFyY29hdFJvdWdobmVzcyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICBtYXRlcmlhbC5jbGVhcmNvYXRSb3VnaG5lc3MgPVxyXG4gICAgICAgICAgICAgICAgICBzYXZlZFN0YXRlLm1hdGVyaWFsLmNsZWFyY29hdFJvdWdobmVzcztcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgaWYgKHNhdmVkU3RhdGUubWF0ZXJpYWwud2lyZWZyYW1lICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgIG1hdGVyaWFsLndpcmVmcmFtZSA9IHNhdmVkU3RhdGUubWF0ZXJpYWwud2lyZWZyYW1lO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBpZiAoc2F2ZWRTdGF0ZS5tYXRlcmlhbC50cmFuc3BhcmVudCAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICBtYXRlcmlhbC50cmFuc3BhcmVudCA9IHNhdmVkU3RhdGUubWF0ZXJpYWwudHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGlmIChzYXZlZFN0YXRlLm1hdGVyaWFsLm9wYWNpdHkgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgbWF0ZXJpYWwub3BhY2l0eSA9IHNhdmVkU3RhdGUubWF0ZXJpYWwub3BhY2l0eTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgaWYgKHNhdmVkU3RhdGUubWF0ZXJpYWwuZW52TWFwSW50ZW5zaXR5ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgIG1hdGVyaWFsLmVudk1hcEludGVuc2l0eSA9IHNhdmVkU3RhdGUubWF0ZXJpYWwuZW52TWFwSW50ZW5zaXR5O1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBtYXRlcmlhbC5uZWVkc1VwZGF0ZSA9IHRydWU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICAvLyBGb3JjZSB1cGRhdGUgdG8gcmVmbGVjdCBjaGFuZ2VzXHJcbiAgICAgICAgZm9yY2VVcGRhdGUoKTtcclxuICAgICAgICAvLyBEb24ndCBpbmNyZW1lbnQgdmVyc2lvbnMgZHVyaW5nIHJlc3RvcmUgdG8gYXZvaWQgdHJpZ2dlcmluZyBoaXN0b3J5IGxvb3BcclxuICAgICAgICBpZiAoIWlzVW5kb1JlZG9BY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgc2V0TWF0ZXJpYWxWZXJzaW9uKCh2KSA9PiB2ICsgMSk7XHJcbiAgICAgICAgICBzZXRUcmFuc2Zvcm1WZXJzaW9uKCh2KSA9PiB2ICsgMSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTdG9yZSBzZWxlY3RlZE9iamVjdHMgVVVJRHMgdG8gYmUgcmVzdG9yZWQgYWZ0ZXIgc2NlbmVPYmplY3RzIHVwZGF0ZVxyXG4gICAgICBwZW5kaW5nU2VsZWN0ZWRPYmplY3RzVVVJRHMuY3VycmVudCA9IGNvbmZpZy5zZWxlY3RlZE9iamVjdHMgfHwgW107XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiW3Jlc3RvcmVDb25maWddIFJlc3RvcmluZyBjb25maWdcIiwgY29uZmlnKTtcclxuICAgIH0sXHJcbiAgICBbc2NlbmVPYmplY3RzLCBmb3JjZVVwZGF0ZSwgc2V0TWF0ZXJpYWxWZXJzaW9uLCBzZXRUcmFuc2Zvcm1WZXJzaW9uXVxyXG4gICk7XHJcblxyXG4gIC8vIEFmdGVyIHNjZW5lT2JqZWN0cyB1cGRhdGUsIHJlc3RvcmUgc2VsZWN0ZWRPYmplY3RzIGZyb20gVVVJRHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHBlbmRpbmdTZWxlY3RlZE9iamVjdHNVVUlEcy5jdXJyZW50ICYmIHNjZW5lT2JqZWN0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGNvbnN0IHV1aWRzID0gcGVuZGluZ1NlbGVjdGVkT2JqZWN0c1VVSURzLmN1cnJlbnQ7XHJcbiAgICAgIGNvbnN0IG5ld1NlbGVjdGVkID0gc2NlbmVPYmplY3RzLmZpbHRlcigob2JqKSA9PlxyXG4gICAgICAgIHV1aWRzLmluY2x1ZGVzKG9iai51dWlkKVxyXG4gICAgICApO1xyXG4gICAgICBzZXRTZWxlY3RlZE9iamVjdHMobmV3U2VsZWN0ZWQpO1xyXG4gICAgICBwZW5kaW5nU2VsZWN0ZWRPYmplY3RzVVVJRHMuY3VycmVudCA9IG51bGw7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiW3Jlc3RvcmVDb25maWddIFJlc3RvcmVkIHNlbGVjdGVkT2JqZWN0c1wiLCBuZXdTZWxlY3RlZCk7XHJcbiAgICB9XHJcbiAgfSwgW3NjZW5lT2JqZWN0c10pO1xyXG5cclxuICAvLyBVbmRvIGFjdGlvblxyXG4gIGNvbnN0IHVuZG9BY3Rpb24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAvLyBQcmV2ZW50IHJhcGlkIHVuZG8gb3BlcmF0aW9uc1xyXG4gICAgaWYgKGlzVW5kb1JlZG9JblByb2dyZXNzUmVmLmN1cnJlbnQpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJbdW5kb0FjdGlvbl0gU2tpcHBpbmcgLSB1bmRvL3JlZG8gYWxyZWFkeSBpbiBwcm9ncmVzc1wiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcIlt1bmRvQWN0aW9uXSBDdXJyZW50IGhpc3RvcnlJbmRleDpcIixcclxuICAgICAgaGlzdG9yeUluZGV4LFxyXG4gICAgICBcIkhpc3RvcnkgbGVuZ3RoOlwiLFxyXG4gICAgICBoaXN0b3J5Lmxlbmd0aFxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoaGlzdG9yeUluZGV4ID4gMCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlt1bmRvQWN0aW9uXSBQZXJmb3JtaW5nIHVuZG8gdG8gaW5kZXg6XCIsIGhpc3RvcnlJbmRleCAtIDEpO1xyXG5cclxuICAgICAgLy8gU2V0IGFsbCBmbGFncyB0byBwcmV2ZW50IGludGVyZmVyZW5jZVxyXG4gICAgICBpc1VuZG9SZWRvSW5Qcm9ncmVzc1JlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgaXNVbmRvUmVkb0FjdGlvblJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgc2tpcEhpc3RvcnlUcmFja2luZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcclxuXHJcbiAgICAgIGNvbnN0IHByZXZDb25maWcgPSBoaXN0b3J5W2hpc3RvcnlJbmRleCAtIDFdO1xyXG4gICAgICByZXN0b3JlQ29uZmlnKHByZXZDb25maWcpO1xyXG4gICAgICBzZXRIaXN0b3J5SW5kZXgoaGlzdG9yeUluZGV4IC0gMSk7XHJcblxyXG4gICAgICAvLyBSZXNldCBmbGFncyBhZnRlciBvcGVyYXRpb24gY29tcGxldGVzXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGlzVW5kb1JlZG9BY3Rpb25SZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIHNraXBIaXN0b3J5VHJhY2tpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIGlzVW5kb1JlZG9JblByb2dyZXNzUmVmLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIlt1bmRvQWN0aW9uXSBVbmRvIGNvbXBsZXRlZCwgYWxsIGZsYWdzIHJlc2V0XCIpO1xyXG4gICAgICB9LCAxMDAwKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiW3VuZG9BY3Rpb25dIENhbm5vdCB1bmRvIC0gYXQgYmVnaW5uaW5nIG9mIGhpc3RvcnlcIik7XHJcbiAgICB9XHJcbiAgfSwgW2hpc3RvcnlJbmRleCwgaGlzdG9yeSwgcmVzdG9yZUNvbmZpZ10pO1xyXG5cclxuICAvLyBSZWRvIGFjdGlvblxyXG4gIGNvbnN0IHJlZG9BY3Rpb24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAvLyBQcmV2ZW50IHJhcGlkIHJlZG8gb3BlcmF0aW9uc1xyXG4gICAgaWYgKGlzVW5kb1JlZG9JblByb2dyZXNzUmVmLmN1cnJlbnQpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJbcmVkb0FjdGlvbl0gU2tpcHBpbmcgLSB1bmRvL3JlZG8gYWxyZWFkeSBpbiBwcm9ncmVzc1wiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGRlYnVnSGlzdG9yeSgpO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwiW3JlZG9BY3Rpb25dIEN1cnJlbnQgaGlzdG9yeUluZGV4OlwiLFxyXG4gICAgICBoaXN0b3J5SW5kZXgsXHJcbiAgICAgIFwiSGlzdG9yeSBsZW5ndGg6XCIsXHJcbiAgICAgIGhpc3RvcnkubGVuZ3RoXHJcbiAgICApO1xyXG5cclxuICAgIGlmIChoaXN0b3J5SW5kZXggPCBoaXN0b3J5Lmxlbmd0aCAtIDEpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJbcmVkb0FjdGlvbl0gUGVyZm9ybWluZyByZWRvIHRvIGluZGV4OlwiLCBoaXN0b3J5SW5kZXggKyAxKTtcclxuXHJcbiAgICAgIC8vIFNldCBhbGwgZmxhZ3MgdG8gcHJldmVudCBpbnRlcmZlcmVuY2VcclxuICAgICAgaXNVbmRvUmVkb0luUHJvZ3Jlc3NSZWYuY3VycmVudCA9IHRydWU7XHJcbiAgICAgIGlzVW5kb1JlZG9BY3Rpb25SZWYuY3VycmVudCA9IHRydWU7XHJcbiAgICAgIHNraXBIaXN0b3J5VHJhY2tpbmdSZWYuY3VycmVudCA9IHRydWU7XHJcblxyXG4gICAgICBjb25zdCBuZXh0Q29uZmlnID0gaGlzdG9yeVtoaXN0b3J5SW5kZXggKyAxXTtcclxuICAgICAgY29uc29sZS5sb2coXCJbcmVkb0FjdGlvbl0gTmV4dCBjb25maWc6XCIsIG5leHRDb25maWcpO1xyXG4gICAgICByZXN0b3JlQ29uZmlnKG5leHRDb25maWcpO1xyXG4gICAgICBzZXRIaXN0b3J5SW5kZXgoaGlzdG9yeUluZGV4ICsgMSk7XHJcblxyXG4gICAgICAvLyBSZXNldCBmbGFncyBhZnRlciBvcGVyYXRpb24gY29tcGxldGVzXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGlzVW5kb1JlZG9BY3Rpb25SZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIHNraXBIaXN0b3J5VHJhY2tpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgIGlzVW5kb1JlZG9JblByb2dyZXNzUmVmLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIltyZWRvQWN0aW9uXSBSZWRvIGNvbXBsZXRlZCwgYWxsIGZsYWdzIHJlc2V0XCIpO1xyXG4gICAgICB9LCAxMDAwKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiW3JlZG9BY3Rpb25dIENhbm5vdCByZWRvIC0gYXQgZW5kIG9mIGhpc3RvcnlcIik7XHJcbiAgICB9XHJcbiAgfSwgW2hpc3RvcnlJbmRleCwgaGlzdG9yeSwgcmVzdG9yZUNvbmZpZywgZGVidWdIaXN0b3J5XSk7XHJcblxyXG4gIC8vIFRyYWNrIGNoYW5nZXMgdG8gb2JqZWN0cyBhbmQgbGlnaHRzIGZvciBoaXN0b3J5XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHRyYWNrQ2hhbmdlcyA9ICgpID0+IHtcclxuICAgICAgaWYgKHNlbGVjdGVkT2JqZWN0cy5sZW5ndGggPiAwIHx8IGxpZ2h0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgYWRkVG9IaXN0b3J5KHtcclxuICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cyxcclxuICAgICAgICAgIGxpZ2h0cyxcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBEZWJvdW5jZSB0byBhdm9pZCB0b28gbWFueSBoaXN0b3J5IGVudHJpZXNcclxuICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQodHJhY2tDaGFuZ2VzLCA1MDApO1xyXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gIH0sIFtzZWxlY3RlZE9iamVjdHMsIGxpZ2h0cywgYWRkVG9IaXN0b3J5XSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZU9iamVjdENsaWNrID0gdXNlQ2FsbGJhY2soXHJcbiAgICAoZSkgPT4ge1xyXG4gICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG5cclxuICAgICAgLy8gSWYgcGxhY2luZyBkZWNhbCwgbGV0IHRoZSBkZWNhbCBwbGFjZW1lbnQgaGFuZGxlciBkbyBpdHMgam9iICh0cmlnZ2VyZWQgYnkgbW9kZWwncyBvbkNsaWNrKVxyXG4gICAgICBpZiAoaXNQbGFjaW5nRGVjYWwpIHtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiT2JqZWN0IGNsaWNrZWQ6XCIsIGUub2JqZWN0Lm5hbWUsIGUub2JqZWN0KTtcclxuXHJcbiAgICAgIGlmIChlLnNoaWZ0S2V5KSB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzKChwcmV2KSA9PiB7XHJcbiAgICAgICAgICAvLyBFbnN1cmUgcHJldiBpcyBhbHdheXMgYW4gYXJyYXlcclxuICAgICAgICAgIGNvbnN0IHByZXZBcnJheSA9IEFycmF5LmlzQXJyYXkocHJldikgPyBwcmV2IDogW3ByZXZdLmZpbHRlcihCb29sZWFuKTtcclxuXHJcbiAgICAgICAgICBpZiAocHJldkFycmF5LmluY2x1ZGVzKGUub2JqZWN0KSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcHJldkFycmF5LmZpbHRlcigob2JqKSA9PiBvYmogIT09IGUub2JqZWN0KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBbLi4ucHJldkFycmF5LCBlLm9iamVjdF07XHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtlLm9iamVjdF0pO1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgW2lzUGxhY2luZ0RlY2FsLCBzZXRTZWxlY3RlZE9iamVjdHNdXHJcbiAgKTtcclxuXHJcbiAgLy8gQWRkIGRlYnVnZ2luZyBmb3Igc2VsZWN0ZWRPYmplY3RzIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJFeHBlcmllbmNlTmV3IC0gc2VsZWN0ZWRPYmplY3RzIGNoYW5nZWQ6XCIsIHNlbGVjdGVkT2JqZWN0cyk7XHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgXCJFeHBlcmllbmNlTmV3IC0gc2VsZWN0ZWRPYmplY3RzIGxlbmd0aDpcIixcclxuICAgICAgc2VsZWN0ZWRPYmplY3RzPy5sZW5ndGhcclxuICAgICk7XHJcbiAgfSwgW3NlbGVjdGVkT2JqZWN0c10pO1xyXG5cclxuICAvLyBBZGQgZGVidWdnaW5nIGZvciBzY2VuZU9iamVjdHMgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIkV4cGVyaWVuY2VOZXcgLSBzY2VuZU9iamVjdHMgY2hhbmdlZDpcIiwgc2NlbmVPYmplY3RzKTtcclxuICAgIGNvbnNvbGUubG9nKFwiRXhwZXJpZW5jZU5ldyAtIHNjZW5lT2JqZWN0cyBsZW5ndGg6XCIsIHNjZW5lT2JqZWN0cz8ubGVuZ3RoKTtcclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcIkV4cGVyaWVuY2VOZXcgLSBzY2VuZU9iamVjdHMgZGV0YWlsczpcIixcclxuICAgICAgc2NlbmVPYmplY3RzPy5tYXAoKG9iaikgPT4gKHtcclxuICAgICAgICBuYW1lOiBvYmo/Lm5hbWUsXHJcbiAgICAgICAgdHlwZTogb2JqPy50eXBlLFxyXG4gICAgICAgIHVzZXJEYXRhOiBvYmo/LnVzZXJEYXRhLFxyXG4gICAgICB9KSlcclxuICAgICk7XHJcbiAgfSwgW3NjZW5lT2JqZWN0c10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVQb2ludGVyTWlzc2VkID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgLy8gSWYgaW4gZGVjYWwgcGxhY2VtZW50IG1vZGUsIGEgXCJtaXNzZWRcIiBjbGljayAob24gdGhlIGNhbnZhcyBiYWNrZ3JvdW5kKVxyXG4gICAgLy8gc2hvdWxkIHByb2JhYmx5IGNhbmNlbCBwbGFjZW1lbnQgbW9kZSwgb3IgZG8gbm90aGluZyB0byBhbGxvdyBtb2RlbCBjbGljay10aHJvdWdoLlxyXG4gICAgLy8gRm9yIG5vdywgbGV0J3MgcHJldmVudCBpdCBmcm9tIGRlc2VsZWN0aW5nIG9iamVjdHMgaWYgcGxhY2luZyBkZWNhbC5cclxuICAgIGlmIChpc1BsYWNpbmdEZWNhbCkge1xyXG4gICAgICAvLyBzZXRJc1BsYWNpbmdEZWNhbChmYWxzZSk7IC8vIE9wdGlvbmFsbHksIGNhbmNlbCBwbGFjZW1lbnQgb24gbWlzc2VkIGNsaWNrXHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbXSk7XHJcbiAgICBzZXRTZWxlY3RlZExpZ2h0KG51bGwpO1xyXG4gICAgLy8gRG8gbm90IGNsb3NlIHRoZSByaWdodFBhbmVsVGFiIGhlcmUsIGFzIHRoYXQncyB1c3VhbGx5IGhhbmRsZWQgYnkgY2xpY2tpbmcgb3V0c2lkZSB0aGUgcGFuZWwgaXRzZWxmLlxyXG4gIH0sIFtpc1BsYWNpbmdEZWNhbCwgc2V0SXNQbGFjaW5nRGVjYWxdKTtcclxuXHJcbiAgLy8gQWRkIHRoZSBzZWxlY3Rpb24gZ3JvdXAgdG8gdGhlIHNjZW5lXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFRoaXMgZWZmZWN0IHJ1bnMgb25jZSB0byBhZGQgdGhlIHNlbGVjdGlvbiBncm91cCB0byB0aGUgc2NlbmVcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIC8vIENsZWFuIHVwIG9uIHVubW91bnRcclxuICAgICAgaWYgKHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQgJiYgc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudC5wYXJlbnQpIHtcclxuICAgICAgICBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LnBhcmVudC5yZW1vdmUoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gRW5zdXJlIHNlbGVjdGVkT2JqZWN0cyBpcyBhbHdheXMgYW4gYXJyYXlcclxuICAgIGNvbnN0IG9iamVjdHNBcnJheSA9IEFycmF5LmlzQXJyYXkoc2VsZWN0ZWRPYmplY3RzKVxyXG4gICAgICA/IHNlbGVjdGVkT2JqZWN0c1xyXG4gICAgICA6IFtzZWxlY3RlZE9iamVjdHNdLmZpbHRlcihCb29sZWFuKTtcclxuXHJcbiAgICB3aGlsZSAoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudC5jaGlsZHJlbi5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQucmVtb3ZlKHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuY2hpbGRyZW5bMF0pO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChvYmplY3RzQXJyYXkubGVuZ3RoID4gMCkge1xyXG4gICAgICBjb25zdCBjZW50ZXIgPSBuZXcgVEhSRUUuVmVjdG9yMygpO1xyXG4gICAgICBvYmplY3RzQXJyYXkuZm9yRWFjaCgob2JqKSA9PiB7XHJcbiAgICAgICAgY2VudGVyLmFkZChvYmoucG9zaXRpb24pO1xyXG4gICAgICB9KTtcclxuICAgICAgY2VudGVyLmRpdmlkZVNjYWxhcihvYmplY3RzQXJyYXkubGVuZ3RoKTtcclxuXHJcbiAgICAgIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQucG9zaXRpb24uY29weShjZW50ZXIpO1xyXG5cclxuICAgICAgb2JqZWN0c0FycmF5LmZvckVhY2goKG9iaikgPT4ge1xyXG4gICAgICAgIGNvbnN0IGNsb25lID0gb2JqLmNsb25lKCk7XHJcbiAgICAgICAgLy8gTWFrZSB0aGUgY2xvbmUgaW52aXNpYmxlIHdoaWxlIGtlZXBpbmcgaXRzIGdlb21ldHJ5IGZvciB0cmFuc2Zvcm1zXHJcbiAgICAgICAgY2xvbmUudHJhdmVyc2UoKGNoaWxkKSA9PiB7XHJcbiAgICAgICAgICBpZiAoY2hpbGQuaXNNZXNoKSB7XHJcbiAgICAgICAgICAgIGNoaWxkLnZpc2libGUgPSBmYWxzZTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBjbG9uZS5wb3NpdGlvbi5zdWIoY2VudGVyKTtcclxuICAgICAgICBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LmFkZChjbG9uZSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH0sIFtzZWxlY3RlZE9iamVjdHNdKTtcclxuXHJcbiAgY29uc3QgdXBkYXRlT2JqZWN0c0Zyb21Hcm91cCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIC8vIEVuc3VyZSBzZWxlY3RlZE9iamVjdHMgaXMgYWx3YXlzIGFuIGFycmF5XHJcbiAgICBjb25zdCBvYmplY3RzQXJyYXkgPSBBcnJheS5pc0FycmF5KHNlbGVjdGVkT2JqZWN0cylcclxuICAgICAgPyBzZWxlY3RlZE9iamVjdHNcclxuICAgICAgOiBbc2VsZWN0ZWRPYmplY3RzXS5maWx0ZXIoQm9vbGVhbik7XHJcblxyXG4gICAgaWYgKG9iamVjdHNBcnJheS5sZW5ndGggPiAwICYmIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQpIHtcclxuICAgICAgb2JqZWN0c0FycmF5LmZvckVhY2goKG9iaiwgaW5kZXgpID0+IHtcclxuICAgICAgICBpZiAoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudC5jaGlsZHJlbltpbmRleF0pIHtcclxuICAgICAgICAgIGNvbnN0IHdvcmxkUG9zID0gbmV3IFRIUkVFLlZlY3RvcjMoKTtcclxuICAgICAgICAgIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuY2hpbGRyZW5baW5kZXhdLmdldFdvcmxkUG9zaXRpb24od29ybGRQb3MpO1xyXG4gICAgICAgICAgb2JqLnBvc2l0aW9uLmNvcHkod29ybGRQb3MpO1xyXG5cclxuICAgICAgICAgIG9iai5yb3RhdGlvbi5jb3B5KHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQucm90YXRpb24pO1xyXG4gICAgICAgICAgb2JqLnNjYWxlLmNvcHkoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudC5zY2FsZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9LCBbc2VsZWN0ZWRPYmplY3RzXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNjZW5lVXBkYXRlID0gdXNlQ2FsbGJhY2soKHNjZW5lKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcImhhbmRsZVNjZW5lVXBkYXRlIGNhbGxlZCB3aXRoIHNjZW5lOlwiLCBzY2VuZSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIlNjZW5lIHR5cGU6XCIsIHNjZW5lLnR5cGUpO1xyXG4gICAgY29uc29sZS5sb2coXCJTY2VuZSBjaGlsZHJlbiBjb3VudDpcIiwgc2NlbmUuY2hpbGRyZW4/Lmxlbmd0aCk7XHJcblxyXG4gICAgc2V0U2NlbmVPYmplY3RzKFtdKTtcclxuICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbXSk7XHJcblxyXG4gICAgY29uc3Qgc3RhdHMgPSBjYWxjdWxhdGVNb2RlbFN0YXRzKHNjZW5lKTtcclxuICAgIHNldE1vZGVsU3RhdHMoc3RhdHMpO1xyXG4gICAgc2V0U2hhZG93c09uTW9kZWwoc2NlbmUpO1xyXG5cclxuICAgIGNvbnN0IG9iamVjdHMgPSBbXTtcclxuICAgIGNvbnN0IGluaXRpYWxPYmplY3RzID0gW107XHJcblxyXG4gICAgc2NlbmUudHJhdmVyc2UoKG9iamVjdCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICBcIlRyYXZlcnNpbmcgb2JqZWN0OlwiLFxyXG4gICAgICAgIG9iamVjdC5uYW1lLFxyXG4gICAgICAgIG9iamVjdC50eXBlLFxyXG4gICAgICAgIFwiaXNNZXNoOlwiLFxyXG4gICAgICAgIG9iamVjdC5pc01lc2gsXHJcbiAgICAgICAgXCJ1c2VyRGF0YTpcIixcclxuICAgICAgICBvYmplY3QudXNlckRhdGFcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGlmIChvYmplY3QuaXNNZXNoKSB7XHJcbiAgICAgICAgaWYgKCFvYmplY3QubmFtZSkge1xyXG4gICAgICAgICAgb2JqZWN0Lm5hbWUgPSBgTWVzaF8ke29iamVjdC5pZH1gO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICBcIkFkZGluZyBtZXNoIHRvIG9iamVjdHM6XCIsXHJcbiAgICAgICAgICBvYmplY3QubmFtZSxcclxuICAgICAgICAgIFwidXNlckRhdGE6XCIsXHJcbiAgICAgICAgICBvYmplY3QudXNlckRhdGFcclxuICAgICAgICApO1xyXG4gICAgICAgIG9iamVjdHMucHVzaChvYmplY3QpO1xyXG5cclxuICAgICAgICBpbml0aWFsT2JqZWN0cy5wdXNoKHtcclxuICAgICAgICAgIGlkOiBvYmplY3QudXVpZCxcclxuICAgICAgICAgIG5hbWU6IG9iamVjdC5uYW1lLFxyXG4gICAgICAgICAgcG9zaXRpb246IG9iamVjdC5wb3NpdGlvbi5jbG9uZSgpLFxyXG4gICAgICAgICAgcm90YXRpb246IG9iamVjdC5yb3RhdGlvbi5jbG9uZSgpLFxyXG4gICAgICAgICAgc2NhbGU6IG9iamVjdC5zY2FsZS5jbG9uZSgpLFxyXG4gICAgICAgICAgbWF0ZXJpYWw6IG9iamVjdC5tYXRlcmlhbFxyXG4gICAgICAgICAgICA/IHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiBvYmplY3QubWF0ZXJpYWwuY29sb3JcclxuICAgICAgICAgICAgICAgICAgPyBvYmplY3QubWF0ZXJpYWwuY29sb3IuZ2V0SGV4KClcclxuICAgICAgICAgICAgICAgICAgOiAweGZmZmZmZixcclxuICAgICAgICAgICAgICAgIHJvdWdobmVzczogb2JqZWN0Lm1hdGVyaWFsLnJvdWdobmVzcyxcclxuICAgICAgICAgICAgICAgIG1ldGFsbmVzczogb2JqZWN0Lm1hdGVyaWFsLm1ldGFsbmVzcyxcclxuICAgICAgICAgICAgICAgIHdpcmVmcmFtZTogb2JqZWN0Lm1hdGVyaWFsLndpcmVmcmFtZSxcclxuICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50OiBvYmplY3QubWF0ZXJpYWwudHJhbnNwYXJlbnQsXHJcbiAgICAgICAgICAgICAgICBvcGFjaXR5OiBvYmplY3QubWF0ZXJpYWwub3BhY2l0eSxcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIDogbnVsbCxcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIFwiRm91bmRcIixcclxuICAgICAgb2JqZWN0cy5sZW5ndGgsXHJcbiAgICAgIFwibWVzaCBvYmplY3RzOlwiLFxyXG4gICAgICBvYmplY3RzLm1hcCgob2JqKSA9PiAoeyBuYW1lOiBvYmoubmFtZSwgdXNlckRhdGE6IG9iai51c2VyRGF0YSB9KSlcclxuICAgICk7XHJcblxyXG4gICAgbW9kZWxJbml0aWFsU3RhdGVSZWYuY3VycmVudC5vYmplY3RzID0gaW5pdGlhbE9iamVjdHM7XHJcbiAgICBzZXRTY2VuZU9iamVjdHMob2JqZWN0cyk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBBZGQgdGhpcyBlZmZlY3QgdG8gZGV0ZWN0IG1vZGVsIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkTW9kZWwgIT09IGxhc3RTZWxlY3RlZE1vZGVsKSB7XHJcbiAgICAgIC8vIENsZWFyIHRoZSBzY2VuZSBvYmplY3RzIHdoZW4gbW9kZWwgY2hhbmdlc1xyXG4gICAgICBzZXRTY2VuZU9iamVjdHMoW10pO1xyXG4gICAgICBzZXRTZWxlY3RlZE9iamVjdHMoW10pO1xyXG4gICAgICBzZXRMYXN0U2VsZWN0ZWRNb2RlbChzZWxlY3RlZE1vZGVsKTtcclxuICAgIH1cclxuICB9LCBbc2VsZWN0ZWRNb2RlbCwgbGFzdFNlbGVjdGVkTW9kZWxdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTGlnaHRNb3ZlZCA9IHVzZUNhbGxiYWNrKChsaWdodElkLCBuZXdQb3NpdGlvbikgPT4ge1xyXG4gICAgc2V0TGlnaHRQb3NpdGlvbkhpc3RvcnkoKHByZXYpID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIFtsaWdodElkXTogWy4uLihwcmV2W2xpZ2h0SWRdIHx8IFtdKSwgbmV3UG9zaXRpb25dLFxyXG4gICAgfSkpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgdW5kb0xpZ2h0TW92ZSA9IHVzZUNhbGxiYWNrKChsaWdodElkKSA9PiB7XHJcbiAgICBzZXRMaWdodFBvc2l0aW9uSGlzdG9yeSgocHJldikgPT4ge1xyXG4gICAgICBjb25zdCBoaXN0b3J5ID0gcHJldltsaWdodElkXSB8fCBbXTtcclxuICAgICAgaWYgKGhpc3RvcnkubGVuZ3RoIDw9IDEpIHJldHVybiBwcmV2O1xyXG5cclxuICAgICAgY29uc3QgbmV3SGlzdG9yeSA9IGhpc3Rvcnkuc2xpY2UoMCwgLTEpO1xyXG4gICAgICBjb25zdCBsYXN0UG9zaXRpb24gPSBuZXdIaXN0b3J5W25ld0hpc3RvcnkubGVuZ3RoIC0gMV07XHJcblxyXG4gICAgICBpZiAobGlnaHRSZWZzLmN1cnJlbnRbbGlnaHRJZF0pIHtcclxuICAgICAgICBsaWdodFJlZnMuY3VycmVudFtsaWdodElkXS5wb3NpdGlvbi5jb3B5KGxhc3RQb3NpdGlvbik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBbbGlnaHRJZF06IG5ld0hpc3RvcnksXHJcbiAgICAgIH07XHJcbiAgICB9KTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIC8vIEhhbmRsZSB1bmRvL3JlZG8ga2V5Ym9hcmQgc2hvcnRjdXRzXHJcbiAgICAgIGlmIChldmVudC5jdHJsS2V5KSB7XHJcbiAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJ6XCIgJiYgIWV2ZW50LnNoaWZ0S2V5KSB7XHJcbiAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgICAgdW5kb0FjdGlvbigpO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZXZlbnQua2V5ID09PSBcInlcIiB8fCAoZXZlbnQua2V5ID09PSBcInpcIiAmJiBldmVudC5zaGlmdEtleSkpIHtcclxuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgICAgICByZWRvQWN0aW9uKCk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgRXNjYXBlIGtleSBmb3IgZGVjYWwgcGxhY2VtZW50XHJcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwiRXNjYXBlXCIgJiYgaXNQbGFjaW5nRGVjYWwpIHtcclxuICAgICAgICBzZXRJc1BsYWNpbmdEZWNhbChmYWxzZSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgbGlnaHQgdW5kb1xyXG4gICAgICBpZiAoZXZlbnQuY3RybEtleSAmJiBldmVudC5rZXkgPT09IFwielwiICYmIHNlbGVjdGVkTGlnaHQpIHtcclxuICAgICAgICB1bmRvTGlnaHRNb3ZlKHNlbGVjdGVkTGlnaHQpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgdHJhbnNmb3JtIG1vZGUgc2hvcnRjdXRzXHJcbiAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIHN3aXRjaCAoZXZlbnQua2V5KSB7XHJcbiAgICAgICAgICBjYXNlIFwiZ1wiOlxyXG4gICAgICAgICAgICBzZXRUcmFuc2Zvcm1Nb2RlKFwidHJhbnNsYXRlXCIpO1xyXG4gICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgIGNhc2UgXCJyXCI6XHJcbiAgICAgICAgICAgIHNldFRyYW5zZm9ybU1vZGUoXCJyb3RhdGVcIik7XHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgY2FzZSBcInNcIjpcclxuICAgICAgICAgICAgc2V0VHJhbnNmb3JtTW9kZShcInNjYWxlXCIpO1xyXG4gICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgIGNhc2UgXCJFc2NhcGVcIjpcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtdKTtcclxuICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgc2VsZWN0IGFsbFxyXG4gICAgICBpZiAoZXZlbnQuY3RybEtleSAmJiBldmVudC5rZXkgPT09IFwiYVwiKSB7XHJcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgICBpZiAoc2NlbmVPYmplY3RzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbLi4uc2NlbmVPYmplY3RzXSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93bik7XHJcbiAgfSwgW1xyXG4gICAgc2VsZWN0ZWRMaWdodCxcclxuICAgIHVuZG9MaWdodE1vdmUsXHJcbiAgICBzY2VuZU9iamVjdHMsXHJcbiAgICBzZWxlY3RlZE9iamVjdHMubGVuZ3RoLFxyXG4gICAgdW5kb0FjdGlvbixcclxuICAgIHJlZG9BY3Rpb24sXHJcbiAgICBpc1BsYWNpbmdEZWNhbCxcclxuICAgIHNldElzUGxhY2luZ0RlY2FsLFxyXG4gIF0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKHVwbG9hZGVkTW9kZWwpIHtcclxuICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVwbG9hZGVkTW9kZWwpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChjdXN0b21IZHJpKSB7XHJcbiAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChjdXN0b21IZHJpKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbdXBsb2FkZWRNb2RlbCwgY3VzdG9tSGRyaV0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVDYW1lcmFWaWV3ID0gdXNlQ2FsbGJhY2soKHZpZXcpID0+IHtcclxuICAgIHNldENhbWVyYVZpZXcob3JiaXRDb250cm9sc1JlZi5jdXJyZW50LCB2aWV3KTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIEFkZCB0aGUgcGFuZWxSZWYgdG8gRXhwZXJpZW5jZU5ldyBpbnN0ZWFkIG9mIGluIFRvb2xiYXJcclxuICBjb25zdCBwYW5lbFJlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgY29uc3QgW2FjdGl2ZUFuaW1hdGlvbiwgc2V0QWN0aXZlQW5pbWF0aW9uXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFthbmltYXRpb25TdGFydFRpbWUsIHNldEFuaW1hdGlvblN0YXJ0VGltZV0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCByZXF1ZXN0UmVmID0gdXNlUmVmKCk7XHJcblxyXG4gIC8vIEFuaW1hdGlvbiBsb29wIGhhbmRsZXJcclxuICBjb25zdCBhbmltYXRlQ2FtZXJhID0gdXNlQ2FsbGJhY2soXHJcbiAgICAodGltZXN0YW1wKSA9PiB7XHJcbiAgICAgIGlmICghYWN0aXZlQW5pbWF0aW9uIHx8ICFvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENhbGN1bGF0ZSBhbmltYXRpb24gcHJvZ3Jlc3MgKDAgdG8gMSlcclxuICAgICAgY29uc3QgYW5pbWF0aW9uID0gcHJlZGVmaW5lZEFuaW1hdGlvbnMuZmluZChcclxuICAgICAgICAoYSkgPT4gYS5pZCA9PT0gYWN0aXZlQW5pbWF0aW9uXHJcbiAgICAgICk7XHJcbiAgICAgIGlmICghYW5pbWF0aW9uKSByZXR1cm47XHJcblxyXG4gICAgICBpZiAoYW5pbWF0aW9uU3RhcnRUaW1lID09PSAwKSB7XHJcbiAgICAgICAgc2V0QW5pbWF0aW9uU3RhcnRUaW1lKHRpbWVzdGFtcCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGVsYXBzZWQgPSB0aW1lc3RhbXAgLSBhbmltYXRpb25TdGFydFRpbWU7XHJcbiAgICAgIGNvbnN0IHByb2dyZXNzID0gKGVsYXBzZWQgJSBhbmltYXRpb24uZHVyYXRpb24pIC8gYW5pbWF0aW9uLmR1cmF0aW9uO1xyXG5cclxuICAgICAgLy8gR2V0IHRoZSB0YXJnZXQgcG9zaXRpb24gKGNlbnRlciBvZiBzZWxlY3RlZCBvYmplY3RzIG9yIHNjZW5lIGNlbnRlcilcclxuICAgICAgY29uc3QgdGFyZ2V0ID0gbmV3IFRIUkVFLlZlY3RvcjMoMCwgMCwgMCk7XHJcbiAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIC8vIENhbGN1bGF0ZSBjZW50ZXIgb2Ygc2VsZWN0ZWQgb2JqZWN0c1xyXG4gICAgICAgIGNvbnN0IG9iamVjdHNBcnJheSA9IEFycmF5LmlzQXJyYXkoc2VsZWN0ZWRPYmplY3RzKVxyXG4gICAgICAgICAgPyBzZWxlY3RlZE9iamVjdHNcclxuICAgICAgICAgIDogW3NlbGVjdGVkT2JqZWN0c10uZmlsdGVyKEJvb2xlYW4pO1xyXG5cclxuICAgICAgICBpZiAob2JqZWN0c0FycmF5Lmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIGNvbnN0IGNlbnRlciA9IG5ldyBUSFJFRS5WZWN0b3IzKCk7XHJcbiAgICAgICAgICBvYmplY3RzQXJyYXkuZm9yRWFjaCgob2JqKSA9PiB7XHJcbiAgICAgICAgICAgIGNlbnRlci5hZGQob2JqLnBvc2l0aW9uKTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgY2VudGVyLmRpdmlkZVNjYWxhcihvYmplY3RzQXJyYXkubGVuZ3RoKTtcclxuICAgICAgICAgIHRhcmdldC5jb3B5KGNlbnRlcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBBbmltYXRlIGNhbWVyYSB1c2luZyB0aGUgYW5pbWF0aW9uIGZ1bmN0aW9uXHJcbiAgICAgIGFuaW1hdGlvbi5hbmltYXRlKFxyXG4gICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5vYmplY3QsXHJcbiAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LFxyXG4gICAgICAgIHByb2dyZXNzLFxyXG4gICAgICAgIHRhcmdldFxyXG4gICAgICApO1xyXG5cclxuICAgICAgcmVxdWVzdFJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGVDYW1lcmEpO1xyXG4gICAgfSxcclxuICAgIFthY3RpdmVBbmltYXRpb24sIG9yYml0Q29udHJvbHNSZWYsIGFuaW1hdGlvblN0YXJ0VGltZSwgc2VsZWN0ZWRPYmplY3RzXVxyXG4gICk7XHJcblxyXG4gIC8vIFNldHVwIGFuaW1hdGlvbiBsb29wXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChhY3RpdmVBbmltYXRpb24pIHtcclxuICAgICAgc2V0QW5pbWF0aW9uU3RhcnRUaW1lKDApO1xyXG4gICAgICByZXF1ZXN0UmVmLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoYW5pbWF0ZUNhbWVyYSk7XHJcblxyXG4gICAgICAvLyBEaXNhYmxlIG9yYml0IGNvbnRyb2xzIGR1cmluZyBhbmltYXRpb25cclxuICAgICAgaWYgKG9yYml0Q29udHJvbHNSZWYuY3VycmVudCkge1xyXG4gICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5lbmFibGVkID0gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIFJlLWVuYWJsZSBvcmJpdCBjb250cm9scyB3aGVuIGFuaW1hdGlvbiBzdG9wc1xyXG4gICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50ICYmIHNlbGVjdGVkT2JqZWN0cy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQuZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAocmVxdWVzdFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUocmVxdWVzdFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbYWN0aXZlQW5pbWF0aW9uLCBhbmltYXRlQ2FtZXJhLCBzZWxlY3RlZE9iamVjdHNdKTtcclxuXHJcbiAgLy8gSGFuZGxlIGFuaW1hdGlvbiBwbGF5YmFjayBmcm9tIE91dGxpbmVyXHJcbiAgY29uc3QgaGFuZGxlUGxheUFuaW1hdGlvbiA9IHVzZUNhbGxiYWNrKChhbmltYXRpb25JZCkgPT4ge1xyXG4gICAgc2V0QWN0aXZlQW5pbWF0aW9uKGFuaW1hdGlvbklkKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IGdldEVudmlyb25tZW50UHJlc2V0ID0gKCkgPT4ge1xyXG4gICAgcmV0dXJuIGVudlByZXNldDtcclxuICB9O1xyXG5cclxuICAvLyBSZXNldCBmdW5jdGlvblxyXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgLy8gQ2hlY2sgaWYgdGhlcmUncyBhIHNhdmVkIHNjZW5lIGluIGxvY2FsU3RvcmFnZVxyXG4gICAgY29uc3Qgc2F2ZWRTY2VuZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYWdhcGVfc2F2ZWRfc2NlbmVcIik7XHJcbiAgICBpZiAoc2F2ZWRTY2VuZSkge1xyXG4gICAgICBjb25zdCBjb25maXJtUmVzZXQgPSB3aW5kb3cuY29uZmlybShcclxuICAgICAgICBcIlRoZXJlIGlzIGEgc2F2ZWQgc2NlbmUgaW4geW91ciBicm93c2VyIHN0b3JhZ2UuIFJlc2V0dGluZyB3aWxsIGNsZWFyIHRoaXMgc2F2ZWQgc2NlbmUuIERvIHlvdSB3YW50IHRvIGNvbnRpbnVlP1wiXHJcbiAgICAgICk7XHJcbiAgICAgIGlmICghY29uZmlybVJlc2V0KSB7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIENsZWFyIHRoZSBzYXZlZCBzY2VuZSBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcImFnYXBlX3NhdmVkX3NjZW5lXCIpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiUmVzZXR0aW5nIHNjZW5lIHRvIGluaXRpYWwgc3RhdGVcIik7XHJcbiAgICBzZXRTY2VuZU9iamVjdHMoW10pO1xyXG4gICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtdKTtcclxuXHJcbiAgICAvLyBSZXNldCBsaWdodHMgdG8gZGVmYXVsdCBjb25maWd1cmF0aW9uXHJcbiAgICBzZXRMaWdodHMoW1xyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiZGVmYXVsdERpcmVjdGlvbmFsXCIsXHJcbiAgICAgICAgdHlwZTogXCJkaXJlY3Rpb25hbFwiLFxyXG4gICAgICAgIHBvc2l0aW9uOiBbMi41LCA1LjUsIDVdLFxyXG4gICAgICAgIGludGVuc2l0eTogMS4wLFxyXG4gICAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcclxuICAgICAgICBuYW1lOiBcIk1haW4gRGlyZWN0aW9uYWwgTGlnaHRcIixcclxuICAgICAgICBjYXN0U2hhZG93OiB0cnVlLFxyXG4gICAgICAgIGhlbHBlclZpc2libGU6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwia2V5TGlnaHRcIixcclxuICAgICAgICB0eXBlOiBcInBvaW50XCIsXHJcbiAgICAgICAgcG9zaXRpb246IFs1LCA1LCA1XSxcclxuICAgICAgICBpbnRlbnNpdHk6IDguMCxcclxuICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgbmFtZTogXCJLZXkgTGlnaHRcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcImZpbGxMaWdodFwiLFxyXG4gICAgICAgIHR5cGU6IFwicG9pbnRcIixcclxuICAgICAgICBwb3NpdGlvbjogWy01LCA1LCAtNV0sXHJcbiAgICAgICAgaW50ZW5zaXR5OiA0LjAsXHJcbiAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICAgIG5hbWU6IFwiRmlsbCBMaWdodFwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiYmFja0xpZ2h0XCIsXHJcbiAgICAgICAgdHlwZTogXCJwb2ludFwiLFxyXG4gICAgICAgIHBvc2l0aW9uOiBbMCwgNSwgLTVdLFxyXG4gICAgICAgIGludGVuc2l0eTogMi4wLFxyXG4gICAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcclxuICAgICAgICBuYW1lOiBcIkJhY2sgTGlnaHRcIixcclxuICAgICAgfSxcclxuICAgIF0pO1xyXG5cclxuICAgIC8vIFJlc2V0IGVudmlyb25tZW50IHNldHRpbmdzXHJcbiAgICBzZXRFbnZQcmVzZXQoXCJoZHJpXzE1XCIpO1xyXG4gICAgc2V0QmdDb2xvcihcIiMyMDIwMjBcIik7XHJcbiAgICBzZXRTaG93RW52aXJvbm1lbnQoZmFsc2UpO1xyXG4gICAgc2V0U2hvd0xpZ2h0U3BoZXJlcyh0cnVlKTtcclxuICAgIHNldEN1c3RvbUhkcmkobnVsbCk7XHJcbiAgICBzZXRFbnZJbnRlbnNpdHkoMS41KTtcclxuICAgIHNldEVudkJsdXIoMC4zNSk7XHJcbiAgICBzZXRFbnZSb3RhdGlvbigwKTtcclxuXHJcbiAgICAvLyBSZXNldCBwb3N0LXByb2Nlc3Npbmcgc2V0dGluZ3NcclxuICAgIHNldFBvc3RQcm9jZXNzaW5nRW5hYmxlZChmYWxzZSk7XHJcbiAgICBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzKHtcclxuICAgICAgYXV0b2ZvY3VzOiB7XHJcbiAgICAgICAgZW5hYmxlZDogZmFsc2UsXHJcbiAgICAgICAgYm9rZWhTY2FsZTogMTgsXHJcbiAgICAgIH0sXHJcbiAgICAgIGJsb29tOiB7XHJcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICBpbnRlbnNpdHk6IDAuMTIsXHJcbiAgICAgICAgdGhyZXNob2xkOiAwLjgsXHJcbiAgICAgICAgcmFkaXVzOiAwLjQsXHJcbiAgICAgIH0sXHJcbiAgICAgIGRvZjoge1xyXG4gICAgICAgIGVuYWJsZWQ6IGZhbHNlLFxyXG4gICAgICAgIGZvY3VzRGlzdGFuY2U6IDAsXHJcbiAgICAgICAgYXBlcnR1cmU6IDAuMDEsXHJcbiAgICAgICAgYm9rZWhTY2FsZTogMyxcclxuICAgICAgfSxcclxuICAgICAgdmlnbmV0dGU6IHtcclxuICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgIGRhcmtuZXNzOiAwLjUsXHJcbiAgICAgICAgb2Zmc2V0OiAwLjEsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBSZXNldCBzY2VuZSBjb250cm9sc1xyXG4gICAgc2V0U2hvd0dyaWQodHJ1ZSk7XHJcbiAgICBzZXRXaXJlZnJhbWUoZmFsc2UpO1xyXG4gICAgc2V0R3JvdW5kVHlwZShcIm5vbmVcIik7XHJcblxyXG4gICAgLy8gUmVzZXQgY2FtZXJhIHBvc2l0aW9uXHJcbiAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50KSB7XHJcbiAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5vYmplY3QucG9zaXRpb24uc2V0KDUsIDIsIDUpO1xyXG4gICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudGFyZ2V0LnNldCgwLCAxLCAwKTtcclxuICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LnVwZGF0ZSgpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlc2V0IHRyYW5zZm9ybSBzdGF0ZVxyXG4gICAgc2V0VHJhbnNmb3JtTW9kZShcInRyYW5zbGF0ZVwiKTtcclxuICAgIHNldFNlbGVjdGVkTGlnaHQobnVsbCk7XHJcblxyXG4gICAgLy8gUmVzZXQgbWF0ZXJpYWwgc2hhZGVyc1xyXG4gICAgc2V0VXNlRGlhbW9uZFNoYWRlcihmYWxzZSk7XHJcbiAgICBzZXRVc2VQcmVtaXVtV2hpdGVHb2xkKGZhbHNlKTtcclxuICAgIHNldFVzZVByZW1pdW1Sb3NlR29sZChmYWxzZSk7XHJcblxyXG4gICAgLy8gQ2xlYXIgaGlzdG9yeVxyXG4gICAgc2V0SGlzdG9yeShbXSk7XHJcbiAgICBzZXRIaXN0b3J5SW5kZXgoLTEpO1xyXG4gICAgc2V0TGlnaHRQb3NpdGlvbkhpc3Rvcnkoe30pO1xyXG5cclxuICAgIC8vIFJlc2V0IG1vZGVsIHN0YXRzXHJcbiAgICBzZXRNb2RlbFN0YXRzKHtcclxuICAgICAgdmVydGljZXM6IDAsXHJcbiAgICAgIHRyaWFuZ2xlczogMCxcclxuICAgICAgbWF0ZXJpYWxzOiAwLFxyXG4gICAgICBhbmltYXRpb25zOiAwLFxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gUmVzZXQgcmlnaHQgcGFuZWwgdGFiXHJcbiAgICBzZXRSaWdodFBhbmVsVGFiKG51bGwpO1xyXG5cclxuICAgIC8vIFJlc2V0IGFjdGl2ZSBhbmltYXRpb25cclxuICAgIHNldEFjdGl2ZUFuaW1hdGlvbihudWxsKTtcclxuICAgIHNldEFuaW1hdGlvblN0YXJ0VGltZSgwKTtcclxuICAgIGlmIChyZXF1ZXN0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUocmVxdWVzdFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhciBhbnkgY3VzdG9tIEhEUkkgb3IgdXBsb2FkZWQgbW9kZWwgVVJMc1xyXG4gICAgaWYgKHVwbG9hZGVkTW9kZWwpIHtcclxuICAgICAgVVJMLnJldm9rZU9iamVjdFVSTCh1cGxvYWRlZE1vZGVsKTtcclxuICAgIH1cclxuICAgIGlmIChjdXN0b21IZHJpKSB7XHJcbiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoY3VzdG9tSGRyaSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRm9yY2UgYSBtb2RlbCByZWxvYWQgYnkgc2V0dGluZyBzZWxlY3RlZE1vZGVsIHRvIG51bGwgZmlyc3RcclxuICAgIHNldFNlbGVjdGVkTW9kZWwobnVsbCk7XHJcbiAgICAvLyBUaGVuIHNldCBpdCBiYWNrIHRvIHVwbG9hZGVkIGFmdGVyIGEgc21hbGwgZGVsYXlcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBzZXRTZWxlY3RlZE1vZGVsKFwidXBsb2FkZWRcIik7XHJcbiAgICB9LCAxMDApO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3Qgc2NyZWVuc2hvdFJlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgY29uc3QgY2FwdHVyZVNjcmVlbnNob3QgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBpZiAoc2NyZWVuc2hvdFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiVGFraW5nIHNjcmVlbnNob3RcIik7XHJcbiAgICAgIHNjcmVlbnNob3RSZWYuY3VycmVudCgpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIlNjcmVlbnNob3QgZnVuY3Rpb24gbm90IGF2YWlsYWJsZSB5ZXRcIik7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBMb2FkIHNhdmVkIHNjZW5lIGZyb20gbG9jYWxTdG9yYWdlIG9uIG1vdW50XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGxvYWRTYXZlZFNjZW5lID0gKCkgPT4ge1xyXG4gICAgICBjb25zdCBzYXZlZFNjZW5lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJhZ2FwZV9zYXZlZF9zY2VuZVwiKTtcclxuICAgICAgaWYgKHNhdmVkU2NlbmUpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3Qgc2NlbmVDb25maWcgPSBKU09OLnBhcnNlKHNhdmVkU2NlbmUpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCJMb2FkaW5nIHNhdmVkIHNjZW5lOlwiLCBzY2VuZUNvbmZpZyk7XHJcblxyXG4gICAgICAgICAgLy8gT25seSBsb2FkIHRoZSBzYXZlZCBjb25maWd1cmF0aW9uIG9uIGluaXRpYWwgbG9hZFxyXG4gICAgICAgICAgaWYgKCFsYXN0U2VsZWN0ZWRNb2RlbCkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICBcIkluaXRpYWwgbG9hZCAtIHNldHRpbmcgc2F2ZWQgbW9kZWw6XCIsXHJcbiAgICAgICAgICAgICAgc2NlbmVDb25maWcubW9kZWxcclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRNb2RlbChcInVwbG9hZGVkXCIpOyAvLyBBbHdheXMgdXNlIHVwbG9hZGVkIGZvciBTMyBtb2RlbHNcclxuXHJcbiAgICAgICAgICAgIC8vIExvYWQgdGhlIHJlc3Qgb2YgdGhlIHNhdmVkIHNjZW5lIGNvbmZpZ3VyYXRpb25cclxuICAgICAgICAgICAgc2V0TGlnaHRzKHNjZW5lQ29uZmlnLmxpZ2h0cyk7XHJcbiAgICAgICAgICAgIHNldEVudlByZXNldChzY2VuZUNvbmZpZy5lbnZpcm9ubWVudC5wcmVzZXQpO1xyXG4gICAgICAgICAgICBzZXRCZ0NvbG9yKHNjZW5lQ29uZmlnLmVudmlyb25tZW50LmJnQ29sb3IpO1xyXG4gICAgICAgICAgICBzZXRTaG93RW52aXJvbm1lbnQoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuc2hvd0Vudmlyb25tZW50KTtcclxuICAgICAgICAgICAgc2V0U2hvd0xpZ2h0U3BoZXJlcyhzY2VuZUNvbmZpZy5lbnZpcm9ubWVudC5zaG93TGlnaHRTcGhlcmVzKTtcclxuICAgICAgICAgICAgc2V0RW52SW50ZW5zaXR5KHNjZW5lQ29uZmlnLmVudmlyb25tZW50LmludGVuc2l0eSk7XHJcbiAgICAgICAgICAgIHNldEVudkJsdXIoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuYmx1cik7XHJcbiAgICAgICAgICAgIHNldEVudlJvdGF0aW9uKHNjZW5lQ29uZmlnLmVudmlyb25tZW50LnJvdGF0aW9uKTtcclxuICAgICAgICAgICAgc2V0UG9zdFByb2Nlc3NpbmdFbmFibGVkKHNjZW5lQ29uZmlnLnBvc3RQcm9jZXNzaW5nLmVuYWJsZWQpO1xyXG4gICAgICAgICAgICBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzKHNjZW5lQ29uZmlnLnBvc3RQcm9jZXNzaW5nLnNldHRpbmdzKTtcclxuICAgICAgICAgICAgc2V0U2hvd0dyaWQoc2NlbmVDb25maWcuc2NlbmVDb250cm9scy5zaG93R3JpZCk7XHJcbiAgICAgICAgICAgIHNldFdpcmVmcmFtZShzY2VuZUNvbmZpZy5zY2VuZUNvbnRyb2xzLndpcmVmcmFtZSk7XHJcbiAgICAgICAgICAgIHNldEdyb3VuZFR5cGUoc2NlbmVDb25maWcuc2NlbmVDb250cm9scy5ncm91bmRUeXBlKTtcclxuXHJcbiAgICAgICAgICAgIC8vIFJlc3RvcmUgY2FtZXJhIHBvc2l0aW9uIGFmdGVyIGEgc2hvcnQgZGVsYXkgdG8gZW5zdXJlIHNjZW5lIGlzIGxvYWRlZFxyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50ICYmIHNjZW5lQ29uZmlnLmNhbWVyYSkge1xyXG4gICAgICAgICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Lm9iamVjdC5wb3NpdGlvbi5mcm9tQXJyYXkoXHJcbiAgICAgICAgICAgICAgICAgIHNjZW5lQ29uZmlnLmNhbWVyYS5wb3NpdGlvblxyXG4gICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC50YXJnZXQuZnJvbUFycmF5KFxyXG4gICAgICAgICAgICAgICAgICBzY2VuZUNvbmZpZy5jYW1lcmEudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LnVwZGF0ZSgpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSwgMTAwMCk7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKGNvbmZpZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIC8vIEZvciBleHBsaWNpdCBsb2FkcyB3aXRoIGNvbmZpZ1JlZiwgZG8gYSBjb21wbGV0ZSBsb2FkXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiRXhwbGljaXQgbG9hZCB3aXRoIGNvbmZpZ1JlZlwiKTtcclxuICAgICAgICAgICAgaGFuZGxlTG9hZFNjZW5lKHNjZW5lQ29uZmlnKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBDcmVhdGUgYSBmdW5jdGlvbiB0byByZXN0b3JlIG1hdGVyaWFsc1xyXG4gICAgICAgICAgY29uc3QgcmVzdG9yZU1hdGVyaWFscyA9ICgpID0+IHtcclxuICAgICAgICAgICAgaWYgKCFzY2VuZUNvbmZpZy5tYXRlcmlhbHMgfHwgIXNjZW5lT2JqZWN0cy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIk5vIG1hdGVyaWFscyB0byByZXN0b3JlIG9yIHNjZW5lIG9iamVjdHMgbm90IHJlYWR5XCIpO1xyXG4gICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgXCJBdHRlbXB0aW5nIHRvIHJlc3RvcmUgbWF0ZXJpYWxzIGZvciBvYmplY3RzOlwiLFxyXG4gICAgICAgICAgICAgIHNjZW5lT2JqZWN0c1xyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIlNhdmVkIG1hdGVyaWFsczpcIiwgc2NlbmVDb25maWcubWF0ZXJpYWxzKTtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHNhdmVkTWF0ZXJpYWxzTWFwID0gbmV3IE1hcChcclxuICAgICAgICAgICAgICBzY2VuZUNvbmZpZy5tYXRlcmlhbHMubWFwKChtKSA9PiBbbS5uYW1lLCBtLm1hdGVyaWFsXSlcclxuICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgIHNjZW5lT2JqZWN0cy5mb3JFYWNoKChvYmopID0+IHtcclxuICAgICAgICAgICAgICBpZiAob2JqLm1hdGVyaWFsICYmIHNhdmVkTWF0ZXJpYWxzTWFwLmhhcyhvYmoubmFtZSkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNhdmVkTWF0ZXJpYWwgPSBzYXZlZE1hdGVyaWFsc01hcC5nZXQob2JqLm5hbWUpO1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgICAgIFwiUmVzdG9yaW5nIG1hdGVyaWFsIGZvciBvYmplY3Q6XCIsXHJcbiAgICAgICAgICAgICAgICAgIG9iai5uYW1lLFxyXG4gICAgICAgICAgICAgICAgICBzYXZlZE1hdGVyaWFsXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgYXBwbHlNYXRlcmlhbFRvT2JqZWN0KG9iaiwgc2F2ZWRNYXRlcmlhbCk7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgICAgICBcIkNvdWxkIG5vdCBmaW5kIHNhdmVkIG1hdGVyaWFsIGZvciBvYmplY3Q6XCIsXHJcbiAgICAgICAgICAgICAgICAgIG9iai5uYW1lXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIC8vIFNldCB1cCBhbiBpbnRlcnZhbCB0byBjaGVjayBmb3Igc2NlbmUgb2JqZWN0c1xyXG4gICAgICAgICAgY29uc3QgY2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICAgICAgaWYgKHNjZW5lT2JqZWN0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKTtcclxuICAgICAgICAgICAgICAvLyBBZGQgYSBzbWFsbCBkZWxheSB0byBlbnN1cmUgYWxsIG1hdGVyaWFscyBhcmUgcHJvcGVybHkgaW5pdGlhbGl6ZWRcclxuICAgICAgICAgICAgICBzZXRUaW1lb3V0KHJlc3RvcmVNYXRlcmlhbHMsIDUwMCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sIDEwMCk7XHJcblxyXG4gICAgICAgICAgLy8gQ2xlYW4gdXAgaW50ZXJ2YWwgYWZ0ZXIgNSBzZWNvbmRzIGlmIHNjZW5lIG9iamVjdHMgbmV2ZXIgYmVjb21lIGF2YWlsYWJsZVxyXG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7XHJcbiAgICAgICAgICB9LCA1MDAwKTtcclxuXHJcbiAgICAgICAgICAvLyBSZXN0b3JlIGNhbWVyYSBwb3NpdGlvbiBhZnRlciBhIHNob3J0IGRlbGF5IHRvIGVuc3VyZSBzY2VuZSBpcyBsb2FkZWRcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50ICYmIHNjZW5lQ29uZmlnLmNhbWVyYSkge1xyXG4gICAgICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5vYmplY3QucG9zaXRpb24uZnJvbUFycmF5KFxyXG4gICAgICAgICAgICAgICAgc2NlbmVDb25maWcuY2FtZXJhLnBvc2l0aW9uXHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudGFyZ2V0LmZyb21BcnJheShcclxuICAgICAgICAgICAgICAgIHNjZW5lQ29uZmlnLmNhbWVyYS50YXJnZXRcclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC51cGRhdGUoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSwgMTAwMCk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBsb2FkaW5nIHNhdmVkIHNjZW5lOlwiLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIExvYWQgc2F2ZWQgc2NlbmUgaW1tZWRpYXRlbHkgb24gbW91bnRcclxuICAgIGxvYWRTYXZlZFNjZW5lKCk7XHJcblxyXG4gICAgLy8gQWxzbyBzZXQgdXAgYW4gaW50ZXJ2YWwgdG8gY2hlY2sgZm9yIHNjZW5lIHJlYWRpbmVzc1xyXG4gICAgY29uc3QgY2hlY2tTY2VuZVJlYWR5ID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICBpZiAoc2NlbmVSZWYuY3VycmVudCAmJiBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhckludGVydmFsKGNoZWNrU2NlbmVSZWFkeSk7XHJcbiAgICAgICAgbG9hZFNhdmVkU2NlbmUoKTtcclxuICAgICAgfVxyXG4gICAgfSwgMTAwKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChjaGVja1NjZW5lUmVhZHkpO1xyXG4gIH0sIFtzY2VuZU9iamVjdHMsIHNlbGVjdGVkTW9kZWxdKTtcclxuXHJcbiAgY29uc3QgW2NvbmZpZ1N0YXRlLCBzZXRDb25maWdTdGF0ZV0gPSB1c2VTdGF0ZShudWxsKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzY2VuZU9iamVjdHMubGVuZ3RoID4gMCAmJiBjb25maWdTdGF0ZSAmJiBjb25maWdTdGF0ZS5tYXRlcmlhbHMpIHtcclxuICAgICAgY29uc3QgYXBwbHlNYXRlcmlhbHMgPSAoKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJBcHBseWluZyBtYXRlcmlhbHMgdG8gc2NlbmUgb2JqZWN0czpcIiwgc2NlbmVPYmplY3RzKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIlNhdmVkIG1hdGVyaWFscyBjb25maWd1cmF0aW9uOlwiLCBjb25maWdTdGF0ZS5tYXRlcmlhbHMpO1xyXG4gICAgICAgIHNjZW5lT2JqZWN0cy5mb3JFYWNoKChvYmopID0+IHtcclxuICAgICAgICAgIGNvbnN0IG1hdGVyaWFsQ29uZmlnID0gY29uZmlnU3RhdGUubWF0ZXJpYWxzLmZpbmQoXHJcbiAgICAgICAgICAgIChtKSA9PiBtLm5hbWUgPT09IG9iai5uYW1lIHx8IG0uaWQgPT09IG9iai51dWlkXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgaWYgKG1hdGVyaWFsQ29uZmlnICYmIG1hdGVyaWFsQ29uZmlnLm1hdGVyaWFsKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgIFwiRm91bmQgbWF0ZXJpYWwgY29uZmlnIGZvciBvYmplY3Q6XCIsXHJcbiAgICAgICAgICAgICAgb2JqLm5hbWUsXHJcbiAgICAgICAgICAgICAgbWF0ZXJpYWxDb25maWcubWF0ZXJpYWxcclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgYXBwbHlNYXRlcmlhbFRvT2JqZWN0KG9iaiwgbWF0ZXJpYWxDb25maWcubWF0ZXJpYWwpO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJObyBtYXRlcmlhbCBjb25maWcgZm91bmQgZm9yIG9iamVjdDpcIiwgb2JqLm5hbWUpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9O1xyXG4gICAgICAvLyBBZGQgYSBzbWFsbCBkZWxheSB0byBlbnN1cmUgYWxsIG1hdGVyaWFscyBhcmUgcHJvcGVybHkgaW5pdGlhbGl6ZWRcclxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dChhcHBseU1hdGVyaWFscywgNTAwKTtcclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgfVxyXG4gIH0sIFtzY2VuZU9iamVjdHMsIGNvbmZpZ1N0YXRlXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvYWRTY2VuZSA9IChjb25maWcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTG9hZGluZyBzY2VuZSBjb25maWd1cmF0aW9uOlwiLCBjb25maWcpO1xyXG5cclxuICAgICAgLy8gU3RvcmUgdGhlIGNvbmZpZyBpbiB0aGUgcmVmXHJcbiAgICAgIGNvbmZpZ1JlZi5jdXJyZW50ID0gY29uZmlnO1xyXG4gICAgICB3b3JraW5nQ29uZmlnUmVmLmN1cnJlbnQgPSBjb25maWc7IC8vIEFsd2F5cyBrZWVwIHRoaXMgaW4gc3luYyB3aXRoIGxhc3QgYXBwbGllZCBjb25maWdcclxuICAgICAgc2V0Q29uZmlnU3RhdGUoY29uZmlnKTsgLy8gPC0tIGtlZXAgaW4gc3luYyBmb3IgZWZmZWN0XHJcblxyXG4gICAgICAvLyBDbGVhciBzY2VuZSBvYmplY3RzIGFuZCBzZWxlY3RlZCBvYmplY3RzIGZpcnN0XHJcbiAgICAgIHNldFNjZW5lT2JqZWN0cyhbXSk7XHJcbiAgICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbXSk7XHJcbiAgICAgIHNldExhc3RTZWxlY3RlZE1vZGVsKG51bGwpO1xyXG5cclxuICAgICAgLy8gUmVzdG9yZSBzY2VuZSBzZXR0aW5nc1xyXG4gICAgICBzZXRTZWxlY3RlZE1vZGVsKFwidXBsb2FkZWRcIik7XHJcbiAgICAgIHNldExpZ2h0cyhjb25maWcubGlnaHRzKTtcclxuICAgICAgc2V0RW52UHJlc2V0KGNvbmZpZy5lbnZpcm9ubWVudC5wcmVzZXQpO1xyXG4gICAgICBzZXRCZ0NvbG9yKGNvbmZpZy5lbnZpcm9ubWVudC5iZ0NvbG9yKTtcclxuICAgICAgc2V0U2hvd0Vudmlyb25tZW50KGNvbmZpZy5lbnZpcm9ubWVudC5zaG93RW52aXJvbm1lbnQpO1xyXG4gICAgICBzZXRTaG93TGlnaHRTcGhlcmVzKGNvbmZpZy5lbnZpcm9ubWVudC5zaG93TGlnaHRTcGhlcmVzKTtcclxuICAgICAgc2V0RW52SW50ZW5zaXR5KGNvbmZpZy5lbnZpcm9ubWVudC5pbnRlbnNpdHkpO1xyXG4gICAgICBzZXRFbnZCbHVyKGNvbmZpZy5lbnZpcm9ubWVudC5ibHVyKTtcclxuICAgICAgc2V0RW52Um90YXRpb24oY29uZmlnLmVudmlyb25tZW50LnJvdGF0aW9uKTtcclxuICAgICAgc2V0UG9zdFByb2Nlc3NpbmdFbmFibGVkKGNvbmZpZy5wb3N0UHJvY2Vzc2luZy5lbmFibGVkKTtcclxuICAgICAgc2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5ncyhjb25maWcucG9zdFByb2Nlc3Npbmcuc2V0dGluZ3MpO1xyXG4gICAgICBzZXRTaG93R3JpZChjb25maWcuc2NlbmVDb250cm9scy5zaG93R3JpZCk7XHJcbiAgICAgIHNldFdpcmVmcmFtZShjb25maWcuc2NlbmVDb250cm9scy53aXJlZnJhbWUpO1xyXG4gICAgICBzZXRHcm91bmRUeXBlKGNvbmZpZy5zY2VuZUNvbnRyb2xzLmdyb3VuZFR5cGUpO1xyXG5cclxuICAgICAgLy8gV2FpdCBmb3IgdGhlIHNjZW5lIHRvIHVwZGF0ZSBiZWZvcmUgYXBwbHlpbmcgY2FtZXJhIHBvc2l0aW9uXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGlmIChvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQgJiYgY29uZmlnLmNhbWVyYSkge1xyXG4gICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Lm9iamVjdC5wb3NpdGlvbi5mcm9tQXJyYXkoXHJcbiAgICAgICAgICAgIGNvbmZpZy5jYW1lcmEucG9zaXRpb25cclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudGFyZ2V0LmZyb21BcnJheShjb25maWcuY2FtZXJhLnRhcmdldCk7XHJcbiAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudXBkYXRlKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAxMDAwKTtcclxuXHJcbiAgICAgIC8vIFJlc3RvcmUgbWF0ZXJpYWxzIGFmdGVyIHNjZW5lT2JqZWN0cyBhcmUgcmVhZHlcclxuICAgICAgY29uc3QgcmVzdG9yZU1hdGVyaWFscyA9ICgpID0+IHtcclxuICAgICAgICBpZiAoIWNvbmZpZy5tYXRlcmlhbHMgfHwgIXNjZW5lT2JqZWN0cy5sZW5ndGgpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwiTm8gbWF0ZXJpYWxzIHRvIHJlc3RvcmUgb3Igc2NlbmUgb2JqZWN0cyBub3QgcmVhZHlcIik7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiUmVzdG9yaW5nIG1hdGVyaWFscyBmb3Igb2JqZWN0czpcIiwgc2NlbmVPYmplY3RzKTtcclxuICAgICAgICBjb25zdCBzYXZlZE1hdGVyaWFsc01hcCA9IG5ldyBNYXAoXHJcbiAgICAgICAgICBjb25maWcubWF0ZXJpYWxzLm1hcCgobSkgPT4gW20ubmFtZSwgbS5tYXRlcmlhbF0pXHJcbiAgICAgICAgKTtcclxuICAgICAgICBzY2VuZU9iamVjdHMuZm9yRWFjaCgob2JqKSA9PiB7XHJcbiAgICAgICAgICBpZiAob2JqLm1hdGVyaWFsICYmIHNhdmVkTWF0ZXJpYWxzTWFwLmhhcyhvYmoubmFtZSkpIHtcclxuICAgICAgICAgICAgY29uc3Qgc2F2ZWRNYXRlcmlhbCA9IHNhdmVkTWF0ZXJpYWxzTWFwLmdldChvYmoubmFtZSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgIFwiUmVzdG9yaW5nIG1hdGVyaWFsIGZvciBvYmplY3Q6XCIsXHJcbiAgICAgICAgICAgICAgb2JqLm5hbWUsXHJcbiAgICAgICAgICAgICAgc2F2ZWRNYXRlcmlhbFxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgICBhcHBseU1hdGVyaWFsVG9PYmplY3Qob2JqLCBzYXZlZE1hdGVyaWFsKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiQ291bGQgbm90IGZpbmQgc2F2ZWQgbWF0ZXJpYWwgZm9yIG9iamVjdDpcIiwgb2JqLm5hbWUpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9O1xyXG4gICAgICBpZiAoc2NlbmVPYmplY3RzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBzZXRUaW1lb3V0KHJlc3RvcmVNYXRlcmlhbHMsIDUwMCk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc3QgY2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICAgIGlmIChzY2VuZU9iamVjdHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpO1xyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KHJlc3RvcmVNYXRlcmlhbHMsIDUwMCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSwgMTAwKTtcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7XHJcbiAgICAgICAgfSwgNTAwMCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBsb2FkaW5nIHNjZW5lIGNvbmZpZ3VyYXRpb246XCIsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTYXZlU2NlbmUgPSBhc3luYyAoc2NlbmVOYW1lLCBkZXNjcmlwdGlvbiwgc2F2ZVR5cGUpID0+IHtcclxuICAgIC8vIFVzZSB3b3JraW5nQ29uZmlnUmVmIGlmIGl0IGV4aXN0cyBhbmQgbWF0Y2hlcyB0aGUgY3VycmVudCBzdGF0ZSwgb3RoZXJ3aXNlIGJ1aWxkIGZyb20gc3RhdGVcclxuICAgIGxldCBzY2VuZUNvbmZpZztcclxuICAgIGlmICh3b3JraW5nQ29uZmlnUmVmLmN1cnJlbnQpIHtcclxuICAgICAgLy8gVXNlIGEgc2hhbGxvdyBjb21wYXJlIGZvciB0b3AtbGV2ZWwga2V5cyAob3IgYWx3YXlzIHVzZSB3b3JraW5nQ29uZmlnUmVmIGZvciBub3cpXHJcbiAgICAgIHNjZW5lQ29uZmlnID0ge1xyXG4gICAgICAgIC4uLndvcmtpbmdDb25maWdSZWYuY3VycmVudCxcclxuICAgICAgICBuYW1lOiBzY2VuZU5hbWUsXHJcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIH07XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBmYWxsYmFjayB0byBidWlsZGluZyBmcm9tIHN0YXRlXHJcbiAgICAgIHNjZW5lQ29uZmlnID0ge1xyXG4gICAgICAgIG5hbWU6IHNjZW5lTmFtZSxcclxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbCxcclxuICAgICAgICBsaWdodHM6IGxpZ2h0cy5tYXAoKGxpZ2h0KSA9PiAoe1xyXG4gICAgICAgICAgLi4ubGlnaHQsXHJcbiAgICAgICAgICBwb3NpdGlvbjpcclxuICAgICAgICAgICAgbGlnaHRSZWZzLmN1cnJlbnRbbGlnaHQuaWRdPy5wb3NpdGlvbi50b0FycmF5KCkgfHwgbGlnaHQucG9zaXRpb24sXHJcbiAgICAgICAgfSkpLFxyXG4gICAgICAgIGVudmlyb25tZW50OiB7XHJcbiAgICAgICAgICBwcmVzZXQ6IGVudlByZXNldCxcclxuICAgICAgICAgIGludGVuc2l0eTogZW52SW50ZW5zaXR5LFxyXG4gICAgICAgICAgYmx1cjogZW52Qmx1cixcclxuICAgICAgICAgIHJvdGF0aW9uOiBlbnZSb3RhdGlvbixcclxuICAgICAgICAgIHNob3dFbnZpcm9ubWVudCxcclxuICAgICAgICAgIGJnQ29sb3IsXHJcbiAgICAgICAgICBjdXN0b21IZHJpLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgcG9zdFByb2Nlc3Npbmc6IHtcclxuICAgICAgICAgIGVuYWJsZWQ6IHBvc3RQcm9jZXNzaW5nRW5hYmxlZCxcclxuICAgICAgICAgIHNldHRpbmdzOiBwb3N0UHJvY2Vzc2luZ1NldHRpbmdzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgc2NlbmVDb250cm9sczoge1xyXG4gICAgICAgICAgc2hvd0dyaWQsXHJcbiAgICAgICAgICB3aXJlZnJhbWUsXHJcbiAgICAgICAgICBncm91bmRUeXBlLFxyXG4gICAgICAgICAgc2hvd0xpZ2h0U3BoZXJlcyxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGNhbWVyYToge1xyXG4gICAgICAgICAgcG9zaXRpb246IG9yYml0Q29udHJvbHNSZWYuY3VycmVudD8ub2JqZWN0LnBvc2l0aW9uLnRvQXJyYXkoKSxcclxuICAgICAgICAgIHRhcmdldDogb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Py50YXJnZXQudG9BcnJheSgpLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgbWF0ZXJpYWxzOiBzY2VuZU9iamVjdHMubWFwKChvYmopID0+IHtcclxuICAgICAgICAgIGNvbnN0IG1hdGVyaWFsID0gb2JqLm1hdGVyaWFsO1xyXG4gICAgICAgICAgaWYgKCFtYXRlcmlhbClcclxuICAgICAgICAgICAgcmV0dXJuIHsgaWQ6IG9iai51dWlkLCBuYW1lOiBvYmoubmFtZSwgbWF0ZXJpYWw6IG51bGwgfTtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGlkOiBvYmoudXVpZCxcclxuICAgICAgICAgICAgbmFtZTogb2JqLm5hbWUsXHJcbiAgICAgICAgICAgIG1hdGVyaWFsOiB7XHJcbiAgICAgICAgICAgICAgdHlwZTogbWF0ZXJpYWwudHlwZSxcclxuICAgICAgICAgICAgICBjb2xvcjogbWF0ZXJpYWwuY29sb3IgPyBtYXRlcmlhbC5jb2xvci5nZXRIZXhTdHJpbmcoKSA6IFwiZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgcm91Z2huZXNzOiBtYXRlcmlhbC5yb3VnaG5lc3MsXHJcbiAgICAgICAgICAgICAgbWV0YWxuZXNzOiBtYXRlcmlhbC5tZXRhbG5lc3MsXHJcbiAgICAgICAgICAgICAgd2lyZWZyYW1lOiBtYXRlcmlhbC53aXJlZnJhbWUsXHJcbiAgICAgICAgICAgICAgdHJhbnNwYXJlbnQ6IG1hdGVyaWFsLnRyYW5zcGFyZW50LFxyXG4gICAgICAgICAgICAgIG9wYWNpdHk6IG1hdGVyaWFsLm9wYWNpdHksXHJcbiAgICAgICAgICAgICAgZW52TWFwSW50ZW5zaXR5OiBtYXRlcmlhbC5lbnZNYXBJbnRlbnNpdHksXHJcbiAgICAgICAgICAgICAgLi4uKG1hdGVyaWFsLnR5cGUgPT09IFwiTWVzaFJlZnJhY3Rpb25NYXRlcmlhbFwiICYmIHtcclxuICAgICAgICAgICAgICAgIGFiZXJyYXRpb25TdHJlbmd0aDogbWF0ZXJpYWwuYWJlcnJhdGlvblN0cmVuZ3RoLFxyXG4gICAgICAgICAgICAgICAgdG9uZU1hcHBlZDogbWF0ZXJpYWwudG9uZU1hcHBlZCxcclxuICAgICAgICAgICAgICAgIGlvcjogbWF0ZXJpYWwuaW9yLFxyXG4gICAgICAgICAgICAgICAgY29sb3JFbnZNYXBSb3RZOiBtYXRlcmlhbC5jb2xvckVudk1hcFJvdFksXHJcbiAgICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgIH0pLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChzYXZlVHlwZSA9PT0gXCJsb2NhbFwiKSB7XHJcbiAgICAgIC8vIFNhdmUgdG8gbG9jYWxTdG9yYWdlXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiYWdhcGVfc2F2ZWRfc2NlbmVcIiwgSlNPTi5zdHJpbmdpZnkoc2NlbmVDb25maWcpKTtcclxuICAgIH0gZWxzZSBpZiAoc2F2ZVR5cGUgPT09IFwiZGJcIikge1xyXG4gICAgICAvLyBTYXZlIHRvIERCIChjbG91ZClcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB2ZXJzaW9uID0gYXdhaXQgY3JlYXRlVmVyc2lvbih7XHJcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXHJcbiAgICAgICAgICB3b3Jrc3BhY2VJZDogcHJvamVjdC5faWQsXHJcbiAgICAgICAgICBuYW1lOiBzY2VuZU5hbWUsXHJcbiAgICAgICAgICBjb25maWc6IHNjZW5lQ29uZmlnLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRlc2NyaXB0aW9uLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwidmVyc2lvblwiLCB2ZXJzaW9uKTtcclxuICAgICAgICBpZiAodmVyc2lvbikge1xyXG4gICAgICAgICAgYWRkVmVyc2lvbihwcm9qZWN0Ll9pZCwgdmVyc2lvbik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJTY2VuZSBzYXZlZCB0byBjbG91ZCFcIik7XHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIHNhdmUgc2NlbmUgdG8gY2xvdWRcIik7XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIEV4cG9ydCBhcyBmaWxlXHJcbiAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoc2NlbmVDb25maWcsIG51bGwsIDIpXSwge1xyXG4gICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICB9KTtcclxuICAgICAgY29uc3QgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcclxuICAgICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJhXCIpO1xyXG4gICAgICBhLmhyZWYgPSB1cmw7XHJcbiAgICAgIGEuZG93bmxvYWQgPSBgJHtzY2VuZU5hbWUucmVwbGFjZSgvXFxzKy9nLCBcIl9cIil9Lmpzb25gO1xyXG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpO1xyXG4gICAgICBhLmNsaWNrKCk7XHJcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSk7XHJcbiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBFbmdyYXZpbmcgc3RhdGVcclxuICBjb25zdCBbZW5ncmF2aW5nVGV4dCwgc2V0RW5ncmF2aW5nVGV4dF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbZGVjYWxQb3NpdGlvbiwgc2V0RGVjYWxQb3NpdGlvbl0gPSB1c2VTdGF0ZShbMCwgMS42OCwgMF0pO1xyXG4gIGNvbnN0IFtkZWNhbFNjYWxlLCBzZXREZWNhbFNjYWxlXSA9IHVzZVN0YXRlKFsyLjgsIDAuOSwgMS41XSk7XHJcbiAgY29uc3QgW2RlY2FsUm90YXRpb24sIHNldERlY2FsUm90YXRpb25dID0gdXNlU3RhdGUoWy1NYXRoLlBJIC8gMiwgMCwgMF0pO1xyXG4gIGNvbnN0IFtkZWNhbENvbG9yLCBzZXREZWNhbENvbG9yXSA9IHVzZVN0YXRlKFwiI2NiY2JjYlwiKTtcclxuICBjb25zdCBbc2VsZWN0ZWRGb250LCBzZXRTZWxlY3RlZEZvbnRdID0gdXNlU3RhdGUoXHJcbiAgICBcIi9mb250cy90dGYvUm9ib3RvU2VyaWYtUmVndWxhci50dGZcIlxyXG4gICk7XHJcblxyXG4gIC8vIERpYW1vbmQgc2hhZGVyIHN0YXRlXHJcbiAgY29uc3QgW3VzZURpYW1vbmRTaGFkZXIsIHNldFVzZURpYW1vbmRTaGFkZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFt1c2VQcmVtaXVtV2hpdGVHb2xkLCBzZXRVc2VQcmVtaXVtV2hpdGVHb2xkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbdXNlUHJlbWl1bVJvc2VHb2xkLCBzZXRVc2VQcmVtaXVtUm9zZUdvbGRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBoYW5kbGVFbmdyYXZlVGV4dCA9IHVzZUNhbGxiYWNrKCh0ZXh0KSA9PiB7XHJcbiAgICBzZXRFbmdyYXZpbmdUZXh0KHRleHQpO1xyXG4gICAgLy8gU3RvcmUgaW4gbG9jYWxTdG9yYWdlIHRvIG1ha2UgaXQgcGVyc2lzdCBhY3Jvc3MgcGFuZWwgY2hhbmdlcyBhbmQgcGFnZSByZWxvYWRzXHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImVuZ3JhdmluZ1RleHRcIiwgdGV4dCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVGb250Q2hhbmdlID0gdXNlQ2FsbGJhY2soKGZvbnQpID0+IHtcclxuICAgIHNldFNlbGVjdGVkRm9udChmb250KTtcclxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZW5ncmF2aW5nRm9udFwiLCBmb250KTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIExvYWQgc2F2ZWQgZW5ncmF2aW5nIHRleHQgb24gbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2F2ZWRFbmdyYXZpbmdUZXh0ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJlbmdyYXZpbmdUZXh0XCIpO1xyXG4gICAgaWYgKHNhdmVkRW5ncmF2aW5nVGV4dCkge1xyXG4gICAgICBzZXRFbmdyYXZpbmdUZXh0KHNhdmVkRW5ncmF2aW5nVGV4dCk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWNhbFBsYWNlbWVudCA9IHVzZUNhbGxiYWNrKFxyXG4gICAgKGV2ZW50KSA9PiB7XHJcbiAgICAgIGhhbmRsZURlY2FsUGxhY2VtZW50KFxyXG4gICAgICAgIGV2ZW50LFxyXG4gICAgICAgIHNldElzUGxhY2luZ0RlY2FsLFxyXG4gICAgICAgIHNldERlY2FsUG9zaXRpb24sXHJcbiAgICAgICAgc2V0RGVjYWxSb3RhdGlvblxyXG4gICAgICApO1xyXG4gICAgfSxcclxuICAgIFtzZXREZWNhbFBvc2l0aW9uLCBzZXREZWNhbFJvdGF0aW9uLCBzZXRJc1BsYWNpbmdEZWNhbF1cclxuICApO1xyXG5cclxuICAvLyBHZXQgcHJlZGVmaW5lZCBkZWNhbCBwb3NpdGlvbnMgYmFzZWQgb24gc2VsZWN0ZWQgbW9kZWxcclxuICBjb25zdCBnZXREZWZhdWx0RGVjYWxQb3NpdGlvbiA9IHVzZUNhbGxiYWNrKChtb2RlbElkKSA9PiB7XHJcbiAgICBjb25zdCBtb2RlbENvbmZpZ3MgPSB7XHJcbiAgICAgIGNob3NlbnJpbmc6IFswLCAxLjY4LCAwXSxcclxuICAgICAgdGVzdHJpbmc6IFswLCAxLjY4LCAwXSxcclxuICAgICAgYWdhcGVyaW5nMTogWzAsIDEuMiwgMF0sXHJcbiAgICAgIGFnYXBlcmluZzI6IFswLCAxLjIsIDBdLFxyXG4gICAgICByaW5nYTogWzAsIDAuMjYsIDBdLFxyXG4gICAgICByaW5nYjogWzAsIDAuNSwgMF0sXHJcbiAgICAgIHJpbmdjOiBbMCwgMC4yNiwgMF0sXHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIG1vZGVsQ29uZmlnc1ttb2RlbElkXSB8fCBbMCwgMS42OCwgMF07XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBnZXREZWZhdWx0RGVjYWxSb3RhdGlvbiA9IHVzZUNhbGxiYWNrKChtb2RlbElkKSA9PiB7XHJcbiAgICBjb25zdCBtb2RlbENvbmZpZ3MgPSB7XHJcbiAgICAgIGNob3NlbnJpbmc6IFstTWF0aC5QSSAvIDIsIDAsIDBdLFxyXG4gICAgICB0ZXN0cmluZzogWy1NYXRoLlBJIC8gMiwgMCwgMF0sXHJcbiAgICAgIGFnYXBlcmluZzE6IFstTWF0aC5QSSAvIDIsIDAsIDBdLFxyXG4gICAgICBhZ2FwZXJpbmcyOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcclxuICAgICAgcmluZ2E6IFstTWF0aC5QSSAvIDIsIDAsIDBdLFxyXG4gICAgICByaW5nYjogWy1NYXRoLlBJIC8gMiwgMCwgMF0sXHJcbiAgICAgIHJpbmdjOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcclxuICAgIH07XHJcbiAgICByZXR1cm4gbW9kZWxDb25maWdzW21vZGVsSWRdIHx8IFstTWF0aC5QSSAvIDIsIDAsIDBdO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgZ2V0RGVmYXVsdERlY2FsU2NhbGUgPSB1c2VDYWxsYmFjaygobW9kZWxJZCkgPT4ge1xyXG4gICAgY29uc3QgbW9kZWxDb25maWdzID0ge1xyXG4gICAgICBjaG9zZW5yaW5nOiBbNi4wLCAyLjAsIDEuNV0sXHJcbiAgICAgIHRlc3RyaW5nOiBbNi4wLCAyLjAsIDEuNV0sXHJcbiAgICAgIGFnYXBlcmluZzE6IFszLjIsIDEuOCwgMS4wXSxcclxuICAgICAgYWdhcGVyaW5nMjogWzMuMiwgMS44LCAxLjBdLFxyXG4gICAgICByaW5nYTogWzEuOCwgMi4wLCAwLjhdLFxyXG4gICAgICByaW5nYjogWzEuOCwgMi4wLCAwLjhdLFxyXG4gICAgICByaW5nYzogWzEuOCwgMi4wLCAwLjhdLFxyXG4gICAgfTtcclxuICAgIHJldHVybiBtb2RlbENvbmZpZ3NbbW9kZWxJZF0gfHwgWzQuMiwgMi4wLCAxLjVdO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gQ2FsY3VsYXRlIGR5bmFtaWMgc2NhbGUgYmFzZWQgb24gdGV4dCBsZW5ndGggYW5kIG1vZGVsIHR5cGVcclxuICBjb25zdCBjYWxjdWxhdGVEeW5hbWljU2NhbGUgPSB1c2VDYWxsYmFjaygoYmFzZVNjYWxlLCB0ZXh0LCBtb2RlbElkKSA9PiB7XHJcbiAgICBpZiAoIXRleHQgfHwgdGV4dC5sZW5ndGggPT09IDApIHJldHVybiBiYXNlU2NhbGU7XHJcblxyXG4gICAgY29uc3QgdGV4dExlbmd0aCA9IHRleHQubGVuZ3RoO1xyXG4gICAgbGV0IFtzY2FsZVgsIHNjYWxlWSwgc2NhbGVaXSA9IGJhc2VTY2FsZTtcclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgc2NhbGUgbXVsdGlwbGllciBiYXNlZCBvbiB0ZXh0IGxlbmd0aFxyXG4gICAgbGV0IHNjYWxlTXVsdGlwbGllciA9IDEuMDtcclxuXHJcbiAgICBpZiAodGV4dExlbmd0aCA+IDEwKSB7XHJcbiAgICAgIC8vIEZvciB2ZXJ5IGxvbmcgdGV4dCwgc2NhbGUgdXAgbW9yZSBhZ2dyZXNzaXZlbHlcclxuICAgICAgc2NhbGVNdWx0aXBsaWVyID0gMS4zICsgKHRleHRMZW5ndGggLSAxMCkgKiAwLjA4O1xyXG4gICAgfSBlbHNlIGlmICh0ZXh0TGVuZ3RoID4gNikge1xyXG4gICAgICAvLyBGb3IgbWVkaXVtIGxlbmd0aCB0ZXh0LCBzY2FsZSB1cCBtb2RlcmF0ZWx5XHJcbiAgICAgIHNjYWxlTXVsdGlwbGllciA9IDEuMCArICh0ZXh0TGVuZ3RoIC0gNikgKiAwLjA4O1xyXG4gICAgfSBlbHNlIGlmICh0ZXh0TGVuZ3RoIDwgNCkge1xyXG4gICAgICAvLyBGb3Igc2hvcnQgdGV4dCwgc2NhbGUgZG93biBzbGlnaHRseSB0byBwcmV2ZW50IG92ZXJzaXplZCBhcHBlYXJhbmNlXHJcbiAgICAgIHNjYWxlTXVsdGlwbGllciA9IDAuOCArIHRleHRMZW5ndGggKiAwLjA1O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEFwcGx5IHRleHQgbGVuZ3RoIHNjYWxpbmcgdG8gWC1heGlzICh3aWR0aCkgcHJpbWFyaWx5XHJcbiAgICBzY2FsZVggPSBzY2FsZVggKiBzY2FsZU11bHRpcGxpZXI7XHJcblxyXG4gICAgLy8gRm9yIHZlcnkgbG9uZyB0ZXh0LCBhbHNvIGFkanVzdCB0aGUgaGVpZ2h0IHNsaWdodGx5XHJcbiAgICBpZiAodGV4dExlbmd0aCA+IDgpIHtcclxuICAgICAgc2NhbGVZID0gc2NhbGVZICogTWF0aC5taW4oMS4yLCAxLjAgKyAodGV4dExlbmd0aCAtIDgpICogMC4wMik7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2FwIG1heGltdW0gc2NhbGUgdG8gcHJldmVudCBleGNlc3NpdmUgc2l6aW5nICh1cGRhdGVkIGZvciBsYXJnZXIgWSB2YWx1ZXMpXHJcbiAgICBjb25zdCBtYXhTY2FsZSA9IHtcclxuICAgICAgY2hvc2VucmluZzogWzEyLjAsIDIuNSwgMi4wXSxcclxuICAgICAgdGVzdHJpbmc6IFsxMi4wLCAyLjUsIDIuMF0sXHJcbiAgICAgIGFnYXBlcmluZzE6IFs2LjUsIDIuMiwgMS41XSxcclxuICAgICAgYWdhcGVyaW5nMjogWzYuNSwgMi4yLCAxLjVdLFxyXG4gICAgICByaW5nYTogWzQuMCwgMi41LCAxLjJdLFxyXG4gICAgICByaW5nYjogWzQuMCwgMi41LCAxLjJdLFxyXG4gICAgICByaW5nYzogWzQuMCwgMi41LCAxLjJdLFxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBsaW1pdHMgPSBtYXhTY2FsZVttb2RlbElkXSB8fCBbOC41LCAyLjUsIDIuMF07XHJcbiAgICBzY2FsZVggPSBNYXRoLm1pbihzY2FsZVgsIGxpbWl0c1swXSk7XHJcbiAgICBzY2FsZVkgPSBNYXRoLm1pbihzY2FsZVksIGxpbWl0c1sxXSk7XHJcbiAgICBzY2FsZVogPSBNYXRoLm1pbihzY2FsZVosIGxpbWl0c1syXSk7XHJcblxyXG4gICAgcmV0dXJuIFtzY2FsZVgsIHNjYWxlWSwgc2NhbGVaXTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIFVwZGF0ZSBzY2FsZSB3aGVuIHRleHQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRNb2RlbCAmJiBlbmdyYXZpbmdUZXh0KSB7XHJcbiAgICAgIGNvbnN0IGJhc2VTY2FsZSA9IGdldERlZmF1bHREZWNhbFNjYWxlKHNlbGVjdGVkTW9kZWwpO1xyXG4gICAgICBjb25zdCBkeW5hbWljU2NhbGUgPSBjYWxjdWxhdGVEeW5hbWljU2NhbGUoXHJcbiAgICAgICAgYmFzZVNjYWxlLFxyXG4gICAgICAgIGVuZ3JhdmluZ1RleHQsXHJcbiAgICAgICAgc2VsZWN0ZWRNb2RlbFxyXG4gICAgICApO1xyXG4gICAgICBzZXREZWNhbFNjYWxlKGR5bmFtaWNTY2FsZSk7XHJcbiAgICB9XHJcbiAgfSwgW1xyXG4gICAgZW5ncmF2aW5nVGV4dCxcclxuICAgIHNlbGVjdGVkTW9kZWwsXHJcbiAgICBnZXREZWZhdWx0RGVjYWxTY2FsZSxcclxuICAgIGNhbGN1bGF0ZUR5bmFtaWNTY2FsZSxcclxuICBdKTtcclxuXHJcbiAgLy8gSW5pdGlhbGl6ZSBkZWNhbCBwb3NpdGlvbiBiYXNlZCBvbiBzZWxlY3RlZCBtb2RlbFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRNb2RlbCkge1xyXG4gICAgICBzZXREZWNhbFBvc2l0aW9uKGdldERlZmF1bHREZWNhbFBvc2l0aW9uKHNlbGVjdGVkTW9kZWwpKTtcclxuICAgICAgc2V0RGVjYWxSb3RhdGlvbihnZXREZWZhdWx0RGVjYWxSb3RhdGlvbihzZWxlY3RlZE1vZGVsKSk7XHJcbiAgICAgIC8vIE9ubHkgc2V0IGluaXRpYWwgc2NhbGUgaWYgdGhlcmUncyBubyBlbmdyYXZpbmcgdGV4dCB5ZXRcclxuICAgICAgaWYgKCFlbmdyYXZpbmdUZXh0KSB7XHJcbiAgICAgICAgc2V0RGVjYWxTY2FsZShnZXREZWZhdWx0RGVjYWxTY2FsZShzZWxlY3RlZE1vZGVsKSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbXHJcbiAgICBzZWxlY3RlZE1vZGVsLFxyXG4gICAgZ2V0RGVmYXVsdERlY2FsUG9zaXRpb24sXHJcbiAgICBnZXREZWZhdWx0RGVjYWxSb3RhdGlvbixcclxuICAgIGdldERlZmF1bHREZWNhbFNjYWxlLFxyXG4gICAgZW5ncmF2aW5nVGV4dCxcclxuICBdKTtcclxuXHJcbiAgY29uc3QgW2lzRnVsbHNjcmVlbiwgc2V0SXNGdWxsc2NyZWVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gR2V0IGNhbWVyYSBwb3NpdGlvbiBiYXNlZCBvbiBkZXZpY2UgdHlwZVxyXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XHJcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcclxuICAgIH07XHJcblxyXG4gICAgY2hlY2tNb2JpbGUoKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGNoZWNrTW9iaWxlKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgY2hlY2tNb2JpbGUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gQWRkIHN0YXRlIHRvIGNvbnRyb2wgb3V0bGluZXIgY29sbGFwc2VkIHN0YXRlIC0gaW5pdGlhbGl6ZSBiYXNlZCBvbiBkZXZpY2UgdHlwZVxyXG4gIGNvbnN0IFtvdXRsaW5lckNvbGxhcHNlZCwgc2V0T3V0bGluZXJDb2xsYXBzZWRdID0gdXNlU3RhdGUoKCkgPT4ge1xyXG4gICAgLy8gSW5pdGlhbGl6ZSBiYXNlZCBvbiBkZXZpY2UgdHlwZSAtIGNsb3NlZCBvbiBtb2JpbGUsIG9wZW4gb24gZGVza3RvcFxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcclxuICAgICAgcmV0dXJuIHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGZhbHNlOyAvLyBEZWZhdWx0IHRvIG9wZW4gb24gc2VydmVyLXNpZGUgcmVuZGVyaW5nXHJcbiAgfSk7XHJcblxyXG4gIC8vIEVmZmVjdCB0byBjbG9zZSBvdXRsaW5lciB3aGVuIHRvb2xiYXIgcGFuZWxzIGFyZSBvcGVuZWQgb24gbW9iaWxlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc01vYmlsZSAmJiByaWdodFBhbmVsVGFiKSB7XHJcbiAgICAgIHNldE91dGxpbmVyQ29sbGFwc2VkKHRydWUpO1xyXG4gICAgfVxyXG4gIH0sIFtpc01vYmlsZSwgcmlnaHRQYW5lbFRhYl0pO1xyXG5cclxuICAvLyBFZmZlY3QgdG8gdXBkYXRlIG91dGxpbmVyIGNvbGxhcHNlZCBzdGF0ZSB3aGVuIG1vYmlsZSBzdGF0ZSBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFVwZGF0ZSBvdXRsaW5lciBzdGF0ZSBiYXNlZCBvbiBkZXZpY2UgdHlwZSAtIGNsb3NlZCBvbiBtb2JpbGUsIG9wZW4gb24gZGVza3RvcFxyXG4gICAgc2V0T3V0bGluZXJDb2xsYXBzZWQoaXNNb2JpbGUpO1xyXG4gIH0sIFtpc01vYmlsZV0pO1xyXG5cclxuICBjb25zdCBnZXRDYW1lcmFQb3NpdGlvbiA9ICgpID0+IHtcclxuICAgIGlmIChpc01vYmlsZSkge1xyXG4gICAgICByZXR1cm4gWy0xLjg1LCAzLjI1LCA0LjVdO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFstMS44NSwgMS41NSwgMy41XTtcclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gT24gaW5pdGlhbCBtb3VudCwgZmV0Y2ggYW5kIGFwcGx5IHRoZSBjdXJyZW50IHZlcnNpb24gZnJvbSBEQlxyXG4gICAgY29uc3QgZmV0Y2hBbmRBcHBseUN1cnJlbnRWZXJzaW9uID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRWZXJzaW9uID0gYXdhaXQgZ2V0Q3VycmVudFZlcnNpb24oe1xyXG4gICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgICAgd29ya3NwYWNlSWQ6IHByb2plY3QuX2lkLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGlmIChjdXJyZW50VmVyc2lvbiAmJiBjdXJyZW50VmVyc2lvbi5jb25maWcpIHtcclxuICAgICAgICAgIGhhbmRsZUxvYWRTY2VuZShjdXJyZW50VmVyc2lvbi5jb25maWcpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBsb2FkIGN1cnJlbnQgdmVyc2lvbiBmcm9tIERCXCIsIGVycik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgICBmZXRjaEFuZEFwcGx5Q3VycmVudFZlcnNpb24oKTtcclxuICAgIC8vIE9ubHkgcnVuIG9uIG1vdW50XHJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPFRvYXN0Q29udGFpbmVyIC8+XHJcbiAgICAgIDxIZWFkZXJcclxuICAgICAgICBvblJlc2V0PXtoYW5kbGVSZXNldH1cclxuICAgICAgICBvblNjcmVlbnNob3Q9e2NhcHR1cmVTY3JlZW5zaG90fVxyXG4gICAgICAgIG9uU2F2ZVNjZW5lPXtoYW5kbGVTYXZlU2NlbmV9XHJcbiAgICAgICAgb25Mb2FkU2NlbmU9e2hhbmRsZUxvYWRTY2VuZX1cclxuICAgICAgICBpc0Z1bGxzY3JlZW49e2lzRnVsbHNjcmVlbn1cclxuICAgICAgICBvblRvZ2dsZUZ1bGxzY3JlZW49eygpID0+IHNldElzRnVsbHNjcmVlbighaXNGdWxsc2NyZWVuKX1cclxuICAgICAgICBwcm9qZWN0PXtwcm9qZWN0fVxyXG4gICAgICAgIHVzZXI9e3VzZXJ9XHJcbiAgICAgICAgb25VbmRvPXt1bmRvQWN0aW9ufVxyXG4gICAgICAgIG9uUmVkbz17cmVkb0FjdGlvbn1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIDxJbml0aWFsT3ZlcmxheVBhbmVsIC8+XHJcblxyXG4gICAgICB7LyogTGVmdCBUb29sYmFyICovfVxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgbGVmdC01IHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgei1bMTAwMF0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgICBpc0Z1bGxzY3JlZW4gPyBcIm9wYWNpdHktMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgOiBcIm9wYWNpdHktMTAwXCJcclxuICAgICAgICB9YH1cclxuICAgICAgPlxyXG4gICAgICAgIDxUb29sYmFyXHJcbiAgICAgICAgICB0cmFuc2Zvcm1Nb2RlPXt0cmFuc2Zvcm1Nb2RlfVxyXG4gICAgICAgICAgc2V0VHJhbnNmb3JtTW9kZT17c2V0VHJhbnNmb3JtTW9kZX1cclxuICAgICAgICAgIHVuZG9BY3Rpb249e3VuZG9BY3Rpb259XHJcbiAgICAgICAgICByZWRvQWN0aW9uPXtyZWRvQWN0aW9ufVxyXG4gICAgICAgICAgcmlnaHRQYW5lbFRhYj17cmlnaHRQYW5lbFRhYn1cclxuICAgICAgICAgIHNldFJpZ2h0UGFuZWxUYWI9e3NldFJpZ2h0UGFuZWxUYWJ9XHJcbiAgICAgICAgICBvcmllbnRhdGlvbj1cInZlcnRpY2FsXCJcclxuICAgICAgICAgIHNjZW5lT2JqZWN0cz17c2NlbmVPYmplY3RzfVxyXG4gICAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzPXtzZXRTZWxlY3RlZE9iamVjdHN9XHJcbiAgICAgICAgICBzZWxlY3RlZE9iamVjdHM9e3NlbGVjdGVkT2JqZWN0c31cclxuICAgICAgICAgIHBhbmVsUmVmPXtwYW5lbFJlZn1cclxuICAgICAgICAgIGlzTW9iaWxlPXtpc01vYmlsZX1cclxuICAgICAgICAgIHNldE91dGxpbmVyQ29sbGFwc2VkPXtzZXRPdXRsaW5lckNvbGxhcHNlZH1cclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBEcm9wZG93biBQYW5lbHMgZm9yIGVhY2ggdG9vbGJhciBvcHRpb24gLSBhcHBlYXJzIGJlc2lkZSB0b29sYmFyIHdoZW4gc2VsZWN0ZWQgKi99XHJcbiAgICAgIHtyaWdodFBhbmVsVGFiICYmIChcclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICByZWY9e3BhbmVsUmVmfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgbGVmdC0yMCB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGJnLWdyYWRpZW50LXRvLXRsIGZyb20tWyMzMjM0M0RdIHRvLVsjMTQxOTJEXSBwLTQgcm91bmRlZC1sZyBzaGFkb3ctbGcgei1bMTAwMF0gbWF4LWgtWzgwdmhdIG92ZXJmbG93LXktYXV0byB3LVsyNTBweF0gbWQ6dy1bMzAwcHhdIGJvcmRlciBib3JkZXItWyNBM0EzQTNdLzMwIHNjcm9sbGJhci1ub25lXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICB7cmlnaHRQYW5lbFRhYiA9PT0gXCJ0cmFuc2Zvcm1cIiAmJiAoXHJcbiAgICAgICAgICAgIDxUcmFuc2Zvcm1QYW5lbFxyXG4gICAgICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cz17c2VsZWN0ZWRPYmplY3RzfVxyXG4gICAgICAgICAgICAgIHRyYW5zZm9ybU1vZGU9e3RyYW5zZm9ybU1vZGV9XHJcbiAgICAgICAgICAgICAgc2V0VHJhbnNmb3JtTW9kZT17c2V0VHJhbnNmb3JtTW9kZX1cclxuICAgICAgICAgICAgICB1bmRvQWN0aW9uPXt1bmRvQWN0aW9ufVxyXG4gICAgICAgICAgICAgIHJlZG9BY3Rpb249e3JlZG9BY3Rpb259XHJcbiAgICAgICAgICAgICAgYWRkVG9IaXN0b3J5PXthZGRUb0hpc3Rvcnl9XHJcbiAgICAgICAgICAgICAgaW52YWxpZGF0ZT17aW52YWxpZGF0ZX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAge3JpZ2h0UGFuZWxUYWIgPT09IFwibWF0ZXJpYWxcIiAmJiAoXHJcbiAgICAgICAgICAgIDxNYXRlcmlhbFBhbmVsXHJcbiAgICAgICAgICAgICAgc2VsZWN0ZWRPYmplY3RzPXtzZWxlY3RlZE9iamVjdHN9XHJcbiAgICAgICAgICAgICAgdXNlRGlhbW9uZFNoYWRlcj17dXNlRGlhbW9uZFNoYWRlcn1cclxuICAgICAgICAgICAgICBzZXRVc2VEaWFtb25kU2hhZGVyPXtzZXRVc2VEaWFtb25kU2hhZGVyfVxyXG4gICAgICAgICAgICAgIHVzZVByZW1pdW1XaGl0ZUdvbGQ9e3VzZVByZW1pdW1XaGl0ZUdvbGR9XHJcbiAgICAgICAgICAgICAgc2V0VXNlUHJlbWl1bVdoaXRlR29sZD17c2V0VXNlUHJlbWl1bVdoaXRlR29sZH1cclxuICAgICAgICAgICAgICB1c2VQcmVtaXVtUm9zZUdvbGQ9e3VzZVByZW1pdW1Sb3NlR29sZH1cclxuICAgICAgICAgICAgICBzZXRVc2VQcmVtaXVtUm9zZUdvbGQ9e3NldFVzZVByZW1pdW1Sb3NlR29sZH1cclxuICAgICAgICAgICAgICByZW5kZXJlcj17cmVuZGVyZXJ9XHJcbiAgICAgICAgICAgICAgZm9yY2VVcGRhdGU9e2ZvcmNlVXBkYXRlfVxyXG4gICAgICAgICAgICAgIHNldE1hdGVyaWFsVmVyc2lvbj17c2V0TWF0ZXJpYWxWZXJzaW9ufVxyXG4gICAgICAgICAgICAgIGludmFsaWRhdGU9e2ludmFsaWRhdGV9XHJcbiAgICAgICAgICAgICAgYWRkVG9IaXN0b3J5PXthZGRUb0hpc3Rvcnl9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgIHtyaWdodFBhbmVsVGFiID09PSBcInBvc3Rwcm9jZXNzaW5nXCIgJiYgKFxyXG4gICAgICAgICAgICA8UG9zdFByb2Nlc3NpbmdQYW5lbFxyXG4gICAgICAgICAgICAgIHBvc3RQcm9jZXNzaW5nRW5hYmxlZD17cG9zdFByb2Nlc3NpbmdFbmFibGVkfVxyXG4gICAgICAgICAgICAgIHNldFBvc3RQcm9jZXNzaW5nRW5hYmxlZD17c2V0UG9zdFByb2Nlc3NpbmdFbmFibGVkfVxyXG4gICAgICAgICAgICAgIHBvc3RQcm9jZXNzaW5nU2V0dGluZ3M9e3Bvc3RQcm9jZXNzaW5nU2V0dGluZ3N9XHJcbiAgICAgICAgICAgICAgc2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5ncz17c2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5nc31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAge3JpZ2h0UGFuZWxUYWIgPT09IFwiZW52aXJvbm1lbnRcIiAmJiAoXHJcbiAgICAgICAgICAgIDxFbnZpcm9ubWVudFBhbmVsXHJcbiAgICAgICAgICAgICAgZW52UHJlc2V0PXtlbnZQcmVzZXR9XHJcbiAgICAgICAgICAgICAgc2V0RW52UHJlc2V0PXtzZXRFbnZQcmVzZXR9XHJcbiAgICAgICAgICAgICAgYmdDb2xvcj17YmdDb2xvcn1cclxuICAgICAgICAgICAgICBzZXRCZ0NvbG9yPXtzZXRCZ0NvbG9yfVxyXG4gICAgICAgICAgICAgIHNob3dFbnZpcm9ubWVudD17c2hvd0Vudmlyb25tZW50fVxyXG4gICAgICAgICAgICAgIHNldFNob3dFbnZpcm9ubWVudD17c2V0U2hvd0Vudmlyb25tZW50fVxyXG4gICAgICAgICAgICAgIGN1c3RvbUhkcmk9e2N1c3RvbUhkcml9XHJcbiAgICAgICAgICAgICAgc2V0Q3VzdG9tSGRyaT17c2V0Q3VzdG9tSGRyaX1cclxuICAgICAgICAgICAgICBlbnZJbnRlbnNpdHk9e2VudkludGVuc2l0eX1cclxuICAgICAgICAgICAgICBzZXRFbnZJbnRlbnNpdHk9e3NldEVudkludGVuc2l0eX1cclxuICAgICAgICAgICAgICBzaG93TW9kZWxTdGF0cz17c2hvd01vZGVsU3RhdHN9XHJcbiAgICAgICAgICAgICAgc2V0U2hvd01vZGVsU3RhdHM9e3NldFNob3dNb2RlbFN0YXRzfVxyXG4gICAgICAgICAgICAgIGVudkJsdXI9e2VudkJsdXJ9XHJcbiAgICAgICAgICAgICAgc2V0RW52Qmx1cj17c2V0RW52Qmx1cn1cclxuICAgICAgICAgICAgICBlbnZSb3RhdGlvbj17ZW52Um90YXRpb259XHJcbiAgICAgICAgICAgICAgc2V0RW52Um90YXRpb249e3NldEVudlJvdGF0aW9ufVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7cmlnaHRQYW5lbFRhYiA9PT0gXCJsaWdodHNcIiAmJiAoXHJcbiAgICAgICAgICAgIDxMaWdodFBhbmVsXHJcbiAgICAgICAgICAgICAgbGlnaHRzPXtsaWdodHN9XHJcbiAgICAgICAgICAgICAgc2V0TGlnaHRzPXtzZXRMaWdodHN9XHJcbiAgICAgICAgICAgICAgbGlnaHRSZWZzPXtsaWdodFJlZnN9XHJcbiAgICAgICAgICAgICAgc2hvd0dyaWQ9e3Nob3dHcmlkfVxyXG4gICAgICAgICAgICAgIHNldFNob3dHcmlkPXtzZXRTaG93R3JpZH1cclxuICAgICAgICAgICAgICB3aXJlZnJhbWU9e3dpcmVmcmFtZX1cclxuICAgICAgICAgICAgICBzZXRXaXJlZnJhbWU9e3NldFdpcmVmcmFtZX1cclxuICAgICAgICAgICAgICBzaG93TGlnaHRTcGhlcmVzPXtzaG93TGlnaHRTcGhlcmVzfVxyXG4gICAgICAgICAgICAgIHNldFNob3dMaWdodFNwaGVyZXM9e3NldFNob3dMaWdodFNwaGVyZXN9XHJcbiAgICAgICAgICAgICAgb25SZXNldExpZ2h0cz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gUmVzZXQgbGlnaHRzIHRvIGRlZmF1bHQgY29uZmlndXJhdGlvblxyXG4gICAgICAgICAgICAgICAgY29uc3QgZGVmYXVsdExpZ2h0cyA9IFtcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBcImRlZmF1bHREaXJlY3Rpb25hbFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IFwiZGlyZWN0aW9uYWxcIixcclxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogWzIuNSwgNS41LCA1XSxcclxuICAgICAgICAgICAgICAgICAgICBpbnRlbnNpdHk6IDEuMCxcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJNYWluIERpcmVjdGlvbmFsIExpZ2h0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgY2FzdFNoYWRvdzogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICBoZWxwZXJWaXNpYmxlOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBcImtleUxpZ2h0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJwb2ludFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBbNSwgNSwgNV0sXHJcbiAgICAgICAgICAgICAgICAgICAgaW50ZW5zaXR5OiA4LjAsXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IFwiS2V5IExpZ2h0XCIsXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICBpZDogXCJmaWxsTGlnaHRcIixcclxuICAgICAgICAgICAgICAgICAgICB0eXBlOiBcInBvaW50XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFstNSwgNSwgLTVdLFxyXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eTogNC4wLFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcclxuICAgICAgICAgICAgICAgICAgICBuYW1lOiBcIkZpbGwgTGlnaHRcIixcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBcImJhY2tMaWdodFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IFwicG9pbnRcIixcclxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogWzAsIDUsIC01XSxcclxuICAgICAgICAgICAgICAgICAgICBpbnRlbnNpdHk6IDIuMCxcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJCYWNrIExpZ2h0XCIsXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBdO1xyXG4gICAgICAgICAgICAgICAgc2V0TGlnaHRzKGRlZmF1bHRMaWdodHMpO1xyXG4gICAgICAgICAgICAgICAgLy8gQ2xlYXIgc2VsZWN0ZWQgbGlnaHRcclxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkTGlnaHQobnVsbCk7XHJcbiAgICAgICAgICAgICAgICAvLyBDbGVhciBsaWdodCBwb3NpdGlvbiBoaXN0b3J5XHJcbiAgICAgICAgICAgICAgICBzZXRMaWdodFBvc2l0aW9uSGlzdG9yeSh7fSk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAge3JpZ2h0UGFuZWxUYWIgPT09IFwiZW5ncmF2aW5nXCIgJiYgKFxyXG4gICAgICAgICAgICA8RW5ncmF2aW5nUGFuZWxcclxuICAgICAgICAgICAgICBvbkVuZ3JhdmVUZXh0PXtoYW5kbGVFbmdyYXZlVGV4dH1cclxuICAgICAgICAgICAgICBvbkRlY2FsUG9zaXRpb25DaGFuZ2U9e3NldERlY2FsUG9zaXRpb259XHJcbiAgICAgICAgICAgICAgb25EZWNhbFNjYWxlQ2hhbmdlPXtzZXREZWNhbFNjYWxlfVxyXG4gICAgICAgICAgICAgIG9uRGVjYWxSb3RhdGlvbkNoYW5nZT17c2V0RGVjYWxSb3RhdGlvbn1cclxuICAgICAgICAgICAgICBkZWNhbFBvc2l0aW9uPXtkZWNhbFBvc2l0aW9ufVxyXG4gICAgICAgICAgICAgIGRlY2FsU2NhbGU9e2RlY2FsU2NhbGV9XHJcbiAgICAgICAgICAgICAgZGVjYWxSb3RhdGlvbj17ZGVjYWxSb3RhdGlvbn1cclxuICAgICAgICAgICAgICBkZWNhbENvbG9yPXtkZWNhbENvbG9yfVxyXG4gICAgICAgICAgICAgIG9uRGVjYWxDb2xvckNoYW5nZT17c2V0RGVjYWxDb2xvcn1cclxuICAgICAgICAgICAgICBvbkZvbnRDaGFuZ2U9e2hhbmRsZUZvbnRDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgc2VsZWN0ZWRGb250PXtzZWxlY3RlZEZvbnR9XHJcbiAgICAgICAgICAgICAgaXNQbGFjaW5nRGVjYWw9e2lzUGxhY2luZ0RlY2FsfVxyXG4gICAgICAgICAgICAgIHNldElzUGxhY2luZ0RlY2FsPXtzZXRJc1BsYWNpbmdEZWNhbH1cclxuICAgICAgICAgICAgICBzaG93RGVidWc9e3Nob3dEZWNhbERlYnVnfVxyXG4gICAgICAgICAgICAgIG9uU2hvd0RlYnVnQ2hhbmdlPXtzZXRTaG93RGVjYWxEZWJ1Z31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogUmlnaHQgUGFuZWwgLSBDb2xsYXBzaWJsZSBPdXRsaW5lciAqL31cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17YGZpeGVkIHJpZ2h0LTUgdG9wLTI0IHotWzk5OV0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgICBpc0Z1bGxzY3JlZW4gPyBcIm9wYWNpdHktMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgOiBcIm9wYWNpdHktMTAwXCJcclxuICAgICAgICB9YH1cclxuICAgICAgPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tdGwgZnJvbS1bIzMyMzQzRF0gdG8tWyMxNDE5MkRdIHNoYWRvdy1sZyByb3VuZGVkLWxnIG92ZXJmbG93LXZpc2libGUgc2Nyb2xsYmFyLW5vbmVcIj5cclxuICAgICAgICAgIDxPdXRsaW5lclxyXG4gICAgICAgICAgICBzY2VuZU9iamVjdHM9e3NjZW5lT2JqZWN0c31cclxuICAgICAgICAgICAgc2VsZWN0ZWRPYmplY3RzPXtzZWxlY3RlZE9iamVjdHN9XHJcbiAgICAgICAgICAgIG9uU2VsZWN0T2JqZWN0PXtzZXRTZWxlY3RlZE9iamVjdHN9XHJcbiAgICAgICAgICAgIG9uQ2FtZXJhVmlldz17aGFuZGxlQ2FtZXJhVmlld31cclxuICAgICAgICAgICAgb25QbGF5QW5pbWF0aW9uPXtoYW5kbGVQbGF5QW5pbWF0aW9ufVxyXG4gICAgICAgICAgICBvblNhdmVTY2VuZT17aGFuZGxlU2F2ZVNjZW5lfVxyXG4gICAgICAgICAgICBncm91bmRUeXBlPXtncm91bmRUeXBlfVxyXG4gICAgICAgICAgICBzZXRHcm91bmRUeXBlPXtzZXRHcm91bmRUeXBlfVxyXG4gICAgICAgICAgICBjb2xsYXBzZWQ9e291dGxpbmVyQ29sbGFwc2VkfVxyXG4gICAgICAgICAgICBzZXRDb2xsYXBzZWQ9e3NldE91dGxpbmVyQ29sbGFwc2VkfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogQ2FudmFzIC0gYWRqdXN0IHRvIGFjY291bnQgZm9yIGhlYWRlciBhbmQgdG9vbGJhciAqL31cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIlxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBcImxpbmVhci1ncmFkaWVudCh0byBib3R0b20gcmlnaHQsICMzNjM2NDMsICM1QzVDNkUpXCIsXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxDYW52YXNcclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIlxyXG4gICAgICAgICAgY2FtZXJhPXt7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiBnZXRDYW1lcmFQb3NpdGlvbigpLFxyXG4gICAgICAgICAgICBmb3Y6IDQ1LFxyXG4gICAgICAgICAgICBuZWFyOiAwLjEsXHJcbiAgICAgICAgICAgIGZhcjogNTAsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgc2hhZG93c1xyXG4gICAgICAgICAgcmVmPXtzY2VuZVJlZn1cclxuICAgICAgICAgIGdsPXt7XHJcbiAgICAgICAgICAgIGFudGlhbGlhczogdHJ1ZSxcclxuICAgICAgICAgICAgcHJlc2VydmVEcmF3aW5nQnVmZmVyOiB0cnVlLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIG9uUG9pbnRlck1pc3NlZD17aGFuZGxlUG9pbnRlck1pc3NlZH1cclxuICAgICAgICAgIHJheWNhc3Rlcj17e1xyXG4gICAgICAgICAgICBjb21wdXRlT2Zmc2V0czogKGUpID0+ICh7IG9mZnNldFg6IGUuY2xpZW50WCwgb2Zmc2V0WTogZS5jbGllbnRZIH0pLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIGZyYW1lbG9vcD1cImRlbWFuZFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPFN1c3BlbnNlXHJcbiAgICAgICAgICAgIGZhbGxiYWNrPXtcclxuICAgICAgICAgICAgICA8SHRtbD5cclxuICAgICAgICAgICAgICAgIDxMb2FkZXIgLz5cclxuICAgICAgICAgICAgICA8L0h0bWw+XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgey8qIDxmb2cgYXR0YWNoPVwiZm9nXCIgYXJncz17W2JnQ29sb3IgfHwgXCIjMzYzNjQzXCIsIDE1LCA0MF19IC8+ICovfVxyXG5cclxuICAgICAgICAgICAgPFNjcmVlbnNob3RUYWtlclxyXG4gICAgICAgICAgICAgIG9uU2NyZWVuc2hvdFJlZj17KGZuKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBzY3JlZW5zaG90UmVmLmN1cnJlbnQgPSBmbjtcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgPFJlbmRlcmVyUHJvdmlkZXJcclxuICAgICAgICAgICAgICBvblJlbmRlcmVyUmVhZHk9eyhnbCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgc2V0UmVuZGVyZXIoZ2wpO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICA8U2NlbmVMaWdodHNcclxuICAgICAgICAgICAgICBsaWdodHM9e2xpZ2h0c31cclxuICAgICAgICAgICAgICBsaWdodFJlZnM9e2xpZ2h0UmVmc31cclxuICAgICAgICAgICAgICBzZWxlY3RlZExpZ2h0PXtzZWxlY3RlZExpZ2h0fVxyXG4gICAgICAgICAgICAgIG9uTGlnaHRTZWxlY3Q9e3NldFNlbGVjdGVkTGlnaHR9XHJcbiAgICAgICAgICAgICAgb25MaWdodE1vdmVkPXtoYW5kbGVMaWdodE1vdmVkfVxyXG4gICAgICAgICAgICAgIHNob3dMaWdodFNwaGVyZXM9e3Nob3dMaWdodFNwaGVyZXN9XHJcbiAgICAgICAgICAgICAgdGhyZWVQb2ludExpZ2h0aW5nPXt0cnVlfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8RW52aXJvbm1lbnRcclxuICAgICAgICAgICAgICBwcmVzZXQ9e2dldEVudmlyb25tZW50UHJlc2V0KCl9XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZD17c2hvd0Vudmlyb25tZW50fVxyXG4gICAgICAgICAgICAgIGJnQ29sb3I9e2JnQ29sb3J9XHJcbiAgICAgICAgICAgICAgY3VzdG9tSGRyaT17Y3VzdG9tSGRyaX1cclxuICAgICAgICAgICAgICBpbnRlbnNpdHk9e2VudkludGVuc2l0eX1cclxuICAgICAgICAgICAgICBibHVyPXtlbnZCbHVyfVxyXG4gICAgICAgICAgICAgIGVudlJvdGF0aW9uPXtlbnZSb3RhdGlvbn1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIDxTZWxlY3Rpb25PdXRsaW5lIHNlbGVjdGVkT2JqZWN0cz17c2VsZWN0ZWRPYmplY3RzfT5cclxuICAgICAgICAgICAgICA8TW9kZWxTdGFnZVxyXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cclxuICAgICAgICAgICAgICAgIHdpcmVmcmFtZT17d2lyZWZyYW1lfVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlU2NlbmVVcGRhdGU9e2hhbmRsZVNjZW5lVXBkYXRlfVxyXG4gICAgICAgICAgICAgICAgaGFuZGxlT2JqZWN0Q2xpY2s9e2hhbmRsZU9iamVjdENsaWNrfVxyXG4gICAgICAgICAgICAgICAgdXBsb2FkZWRNb2RlbD17dXBsb2FkZWRNb2RlbH1cclxuICAgICAgICAgICAgICAgIGdyb3VuZFR5cGU9e2dyb3VuZFR5cGV9XHJcbiAgICAgICAgICAgICAgICBlbmdyYXZpbmdUZXh0PXtlbmdyYXZpbmdUZXh0fVxyXG4gICAgICAgICAgICAgICAgYWN0aXZlQW5pbWF0aW9uPXthY3RpdmVBbmltYXRpb259XHJcbiAgICAgICAgICAgICAgICBkZWNhbFBvc2l0aW9uPXtkZWNhbFBvc2l0aW9ufVxyXG4gICAgICAgICAgICAgICAgZGVjYWxTY2FsZT17ZGVjYWxTY2FsZX1cclxuICAgICAgICAgICAgICAgIGRlY2FsUm90YXRpb249e2RlY2FsUm90YXRpb259XHJcbiAgICAgICAgICAgICAgICBkZWNhbENvbG9yPXtkZWNhbENvbG9yfVxyXG4gICAgICAgICAgICAgICAgZm9udD17c2VsZWN0ZWRGb250fVxyXG4gICAgICAgICAgICAgICAgaXNQbGFjaW5nRGVjYWw9e2lzUGxhY2luZ0RlY2FsfVxyXG4gICAgICAgICAgICAgICAgb25EZWNhbFBsYWNlbWVudD17aGFuZGxlRGVjYWxQbGFjZW1lbnR9XHJcbiAgICAgICAgICAgICAgICBzaG93RGVjYWxEZWJ1Zz17c2hvd0RlY2FsRGVidWd9XHJcbiAgICAgICAgICAgICAgICBzY2VuZU9iamVjdHM9e3NjZW5lT2JqZWN0c31cclxuICAgICAgICAgICAgICAgIHVzZURpYW1vbmRTaGFkZXI9e3VzZURpYW1vbmRTaGFkZXJ9XHJcbiAgICAgICAgICAgICAgICB1c2VQcmVtaXVtV2hpdGVHb2xkPXt1c2VQcmVtaXVtV2hpdGVHb2xkfVxyXG4gICAgICAgICAgICAgICAgdXNlUHJlbWl1bVJvc2VHb2xkPXt1c2VQcmVtaXVtUm9zZUdvbGR9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9TZWxlY3Rpb25PdXRsaW5lPlxyXG5cclxuICAgICAgICAgICAgPE9yYml0Q29udHJvbHNcclxuICAgICAgICAgICAgICByZWY9e29yYml0Q29udHJvbHNSZWZ9XHJcbiAgICAgICAgICAgICAgdGFyZ2V0PXtbMCwgMC41LCAwXX1cclxuICAgICAgICAgICAgICBtYWtlRGVmYXVsdFxyXG4gICAgICAgICAgICAgIGVuYWJsZURhbXBpbmc9e3RydWV9XHJcbiAgICAgICAgICAgICAgLy8gbWluUG9sYXJBbmdsZT17MH1cclxuICAgICAgICAgICAgICAvLyBtYXhQb2xhckFuZ2xlPXtNYXRoLlBJIC8gMi4yNX1cclxuICAgICAgICAgICAgICAvLyBtaW5EaXN0YW5jZT17Mi41fVxyXG4gICAgICAgICAgICAgIG1heERpc3RhbmNlPXsyMH1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIHtzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMCAmJiByaWdodFBhbmVsVGFiID09PSBcInRyYW5zZm9ybVwiICYmIChcclxuICAgICAgICAgICAgICA8VHJhbnNmb3JtQ29udHJvbHNcclxuICAgICAgICAgICAgICAgIG9iamVjdD17XHJcbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cy5sZW5ndGggPT09IDFcclxuICAgICAgICAgICAgICAgICAgICA/IHNlbGVjdGVkT2JqZWN0c1swXVxyXG4gICAgICAgICAgICAgICAgICAgIDogc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgbW9kZT17dHJhbnNmb3JtTW9kZX1cclxuICAgICAgICAgICAgICAgIG9uTW91c2VEb3duPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQpXHJcbiAgICAgICAgICAgICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LmVuYWJsZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICBvbk1vdXNlVXA9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgaWYgKG9yYml0Q29udHJvbHNSZWYuY3VycmVudClcclxuICAgICAgICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQuZW5hYmxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMSkgdXBkYXRlT2JqZWN0c0Zyb21Hcm91cCgpO1xyXG4gICAgICAgICAgICAgICAgICBmb3JjZVVwZGF0ZSgpOyAvLyBFbnN1cmUgaW1tZWRpYXRlIHVwZGF0ZSBhZnRlciB0cmFuc2Zvcm1cclxuICAgICAgICAgICAgICAgICAgaW52YWxpZGF0ZSgpOyAvLyBGb3JjZSBSM0YgdG8gcmUtcmVuZGVyIGFmdGVyIHRyYW5zZm9ybVxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIG9uT2JqZWN0Q2hhbmdlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMSkgdXBkYXRlT2JqZWN0c0Zyb21Hcm91cCgpO1xyXG4gICAgICAgICAgICAgICAgICBmb3JjZVVwZGF0ZSgpOyAvLyBFbnN1cmUgaW1tZWRpYXRlIHVwZGF0ZSBhZnRlciB0cmFuc2Zvcm1cclxuICAgICAgICAgICAgICAgICAgaW52YWxpZGF0ZSgpOyAvLyBGb3JjZSBSM0YgdG8gcmUtcmVuZGVyIGFmdGVyIHRyYW5zZm9ybVxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7Z3JvdW5kVHlwZSA9PT0gXCJncmlkXCIgJiYgKFxyXG4gICAgICAgICAgICAgIDxncmlkSGVscGVyIGFyZ3M9e1s1MCwgNTAsIFwiIzY2NjY2NlwiLCBcIiMzMzMzMzNcIl19IHJlY2VpdmVTaGFkb3cgLz5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2dyb3VuZFR5cGUgPT09IFwic29saWRcIiAmJiAoXHJcbiAgICAgICAgICAgICAgPG1lc2hcclxuICAgICAgICAgICAgICAgIHJvdGF0aW9uLXg9ey1NYXRoLlBJIC8gMn1cclxuICAgICAgICAgICAgICAgIHJlY2VpdmVTaGFkb3dcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uPXtbMCwgLTAuMDEsIDBdfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxwbGFuZUdlb21ldHJ5IGFyZ3M9e1syMDAsIDIwMF19IC8+XHJcbiAgICAgICAgICAgICAgICA8bWVzaFN0YW5kYXJkTWF0ZXJpYWxcclxuICAgICAgICAgICAgICAgICAgY29sb3I9XCIjNWQ1ZDVkXCJcclxuICAgICAgICAgICAgICAgICAgd2lyZWZyYW1lPXt3aXJlZnJhbWV9XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50PXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICBvcGFjaXR5PXswLjd9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvbWVzaD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2dyb3VuZFR5cGUgPT09IFwicmVmbGVjdGl2ZVwiICYmIChcclxuICAgICAgICAgICAgICA8bWVzaFxyXG4gICAgICAgICAgICAgICAgcmVjZWl2ZVNoYWRvd1xyXG4gICAgICAgICAgICAgICAgcm90YXRpb249e1stTWF0aC5QSSAvIDIsIDAsIDBdfVxyXG4gICAgICAgICAgICAgICAgcG9zaXRpb249e1swLCAtMC4wMSwgMF19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPHBsYW5lR2VvbWV0cnkgYXJncz17WzgwLCA4MF19IC8+XHJcbiAgICAgICAgICAgICAgICA8TWVzaFJlZmxlY3Rvck1hdGVyaWFsXHJcbiAgICAgICAgICAgICAgICAgIGJsdXI9e1s4MDAsIDgwMF19XHJcbiAgICAgICAgICAgICAgICAgIHJlc29sdXRpb249ezIwNDh9XHJcbiAgICAgICAgICAgICAgICAgIG1peEJsdXI9ezAuNX1cclxuICAgICAgICAgICAgICAgICAgbWl4U3RyZW5ndGg9ezEwfVxyXG4gICAgICAgICAgICAgICAgICByb3VnaG5lc3M9ezF9XHJcbiAgICAgICAgICAgICAgICAgIGRlcHRoU2NhbGU9ezF9XHJcbiAgICAgICAgICAgICAgICAgIG1pbkRlcHRoVGhyZXNob2xkPXswLjR9XHJcbiAgICAgICAgICAgICAgICAgIG1heERlcHRoVGhyZXNob2xkPXsxLjJ9XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yPVwiIzM2MzY0M1wiXHJcbiAgICAgICAgICAgICAgICAgIG1ldGFsbmVzcz17MC4wMX1cclxuICAgICAgICAgICAgICAgICAgbWlycm9yPXswLjN9XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50PXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICBvcGFjaXR5PXswLjV9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvbWVzaD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2dyb3VuZFR5cGUgPT09IFwibm9uZVwiICYmIChcclxuICAgICAgICAgICAgICA8bWVzaFxyXG4gICAgICAgICAgICAgICAgcmVjZWl2ZVNoYWRvd1xyXG4gICAgICAgICAgICAgICAgcm90YXRpb249e1stTWF0aC5QSSAvIDIsIDAsIDBdfVxyXG4gICAgICAgICAgICAgICAgcG9zaXRpb249e1swLCAtMC4wMSwgMF19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPHBsYW5lR2VvbWV0cnkgYXJncz17WzIwMCwgMjAwXX0gLz5cclxuICAgICAgICAgICAgICAgIDxwcmltaXRpdmVcclxuICAgICAgICAgICAgICAgICAgb2JqZWN0PXtcclxuICAgICAgICAgICAgICAgICAgICBuZXcgVEhSRUUuU2hhZG93TWF0ZXJpYWwoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC4xLFxyXG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I6IG5ldyBUSFJFRS5Db2xvcigweDAwMDAwMCksXHJcbiAgICAgICAgICAgICAgICAgICAgICB0cmFuc3BhcmVudDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvbWVzaD5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPEdpem1vSGVscGVyIGFsaWdubWVudD1cImJvdHRvbS1yaWdodFwiIG1hcmdpbj17WzgwLCA4MF19PlxyXG4gICAgICAgICAgICAgIDxHaXptb1ZpZXdwb3J0XHJcbiAgICAgICAgICAgICAgICBsYWJlbENvbG9yPVwid2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgYXhpc0NvbG9ycz17W1wicmVkXCIsIFwiZ3JlZW5cIiwgXCJibHVlXCJdfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR2l6bW9IZWxwZXI+XHJcblxyXG4gICAgICAgICAgICA8cHJpbWl0aXZlIG9iamVjdD17c2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudH0gLz5cclxuXHJcbiAgICAgICAgICAgIDxQb3N0UHJvY2Vzc2luZ0VmZmVjdHNcclxuICAgICAgICAgICAgICBlbmFibGVkPXtwb3N0UHJvY2Vzc2luZ0VuYWJsZWR9XHJcbiAgICAgICAgICAgICAgc2V0dGluZ3M9e3Bvc3RQcm9jZXNzaW5nU2V0dGluZ3N9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L1N1c3BlbnNlPlxyXG4gICAgICAgIDwvQ2FudmFzPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHtzaG93TW9kZWxTdGF0cyAmJiA8TW9kZWxTdGF0cyBzdGF0cz17bW9kZWxTdGF0c30gLz59XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJHaXptb0hlbHBlciIsIkdpem1vVmlld3BvcnQiLCJIdG1sIiwiTG9hZGVyIiwiTWVzaFJlZmxlY3Rvck1hdGVyaWFsIiwiT3JiaXRDb250cm9scyIsIlRyYW5zZm9ybUNvbnRyb2xzIiwiQ2FudmFzIiwidXNlVGhyZWUiLCJpbnZhbGlkYXRlIiwiU3VzcGVuc2UiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiVG9hc3RDb250YWluZXIiLCJUSFJFRSIsIkVudmlyb25tZW50IiwiRW52aXJvbm1lbnRQYW5lbCIsIkhlYWRlciIsIkxpZ2h0UGFuZWwiLCJNYXRlcmlhbFBhbmVsIiwiTW9kZWxTdGF0cyIsIk91dGxpbmVyIiwiUG9zdFByb2Nlc3NpbmdQYW5lbCIsIlNjZW5lTGlnaHRzIiwiVG9vbGJhciIsIlRyYW5zZm9ybVBhbmVsIiwic2V0Q2FtZXJhVmlldyIsImFwcGx5TWF0ZXJpYWxUb09iamVjdCIsImNhbGN1bGF0ZU1vZGVsU3RhdHMiLCJzZXRTaGFkb3dzT25Nb2RlbCIsInByZWRlZmluZWRBbmltYXRpb25zIiwiRW5ncmF2aW5nUGFuZWwiLCJJbml0aWFsT3ZlcmxheVBhbmVsIiwiTW9kZWxTdGFnZSIsIlBvc3RQcm9jZXNzaW5nRWZmZWN0cyIsIlNlbGVjdGlvbk91dGxpbmUiLCJjcmVhdGVWZXJzaW9uIiwiZ2V0Q3VycmVudFZlcnNpb24iLCJ0b2FzdCIsInVzZVZlcnNpb25TdGF0ZSIsInVzZUNvbmZpZ1N0YXRlIiwiU2NyZWVuc2hvdFRha2VyIiwib25TY3JlZW5zaG90UmVmIiwiZ2wiLCJzY2VuZSIsImNhbWVyYSIsInJlbmRlciIsImRhdGFVUkwiLCJkb21FbGVtZW50IiwidG9EYXRhVVJMIiwibGluayIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInJlcGxhY2UiLCJzdWJzdHJpbmciLCJkb3dubG9hZCIsImhyZWYiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInNldFRpbWVvdXQiLCJyZW1vdmVDaGlsZCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsIk1hdGVyaWFsUGFuZWxXcmFwcGVyIiwicmVuZGVyZXIiLCJwcm9wcyIsIlJlbmRlcmVyUHJvdmlkZXIiLCJjaGlsZHJlbiIsIm9uUmVuZGVyZXJSZWFkeSIsIkVkaXRvckV4cGVyaWVuY2UiLCJtb2RlbFVybCIsInByb2plY3QiLCJ1c2VyIiwiYWRkVmVyc2lvbiIsIndvcmtpbmdDb25maWciLCJkYlN5bmNlZENvbmZpZyIsInNldFdvcmtpbmdDb25maWciLCJzZXREYlN5bmNlZENvbmZpZyIsInJlc2V0V29ya2luZ0NvbmZpZyIsImNvbmZpZ1JlZiIsIndvcmtpbmdDb25maWdSZWYiLCJzZWxlY3RlZE1vZGVsIiwic2V0U2VsZWN0ZWRNb2RlbCIsImxhc3RTZWxlY3RlZE1vZGVsIiwic2V0TGFzdFNlbGVjdGVkTW9kZWwiLCJ1cGxvYWRlZE1vZGVsIiwic2V0VXBsb2FkZWRNb2RlbCIsIm1vZGVsU3RhdHMiLCJzZXRNb2RlbFN0YXRzIiwidmVydGljZXMiLCJ0cmlhbmdsZXMiLCJtYXRlcmlhbHMiLCJhbmltYXRpb25zIiwibGlnaHRzIiwic2V0TGlnaHRzIiwiaWQiLCJ0eXBlIiwicG9zaXRpb24iLCJpbnRlbnNpdHkiLCJjb2xvciIsIm5hbWUiLCJjYXN0U2hhZG93IiwiaGVscGVyVmlzaWJsZSIsImxpZ2h0UmVmcyIsInNlbGVjdGVkT2JqZWN0cyIsInNldFNlbGVjdGVkT2JqZWN0cyIsInNlbGVjdGVkTGlnaHQiLCJzZXRTZWxlY3RlZExpZ2h0IiwibGlnaHRQb3NpdGlvbkhpc3RvcnkiLCJzZXRMaWdodFBvc2l0aW9uSGlzdG9yeSIsInNjZW5lT2JqZWN0cyIsInNldFNjZW5lT2JqZWN0cyIsInRyYW5zZm9ybU1vZGUiLCJzZXRUcmFuc2Zvcm1Nb2RlIiwic2VsZWN0aW9uR3JvdXBSZWYiLCJHcm91cCIsInJpZ2h0UGFuZWxUYWIiLCJzZXRSaWdodFBhbmVsVGFiIiwiaXNQbGFjaW5nRGVjYWwiLCJzZXRJc1BsYWNpbmdEZWNhbCIsInNob3dEZWNhbERlYnVnIiwic2V0U2hvd0RlY2FsRGVidWciLCJzaG93R3JpZCIsInNldFNob3dHcmlkIiwid2lyZWZyYW1lIiwic2V0V2lyZWZyYW1lIiwiZ3JvdW5kVHlwZSIsInNldEdyb3VuZFR5cGUiLCJlbnZQcmVzZXQiLCJzZXRFbnZQcmVzZXQiLCJiZ0NvbG9yIiwic2V0QmdDb2xvciIsInNob3dFbnZpcm9ubWVudCIsInNldFNob3dFbnZpcm9ubWVudCIsInNob3dMaWdodFNwaGVyZXMiLCJzZXRTaG93TGlnaHRTcGhlcmVzIiwiY3VzdG9tSGRyaSIsInNldEN1c3RvbUhkcmkiLCJlbnZJbnRlbnNpdHkiLCJzZXRFbnZJbnRlbnNpdHkiLCJlbnZCbHVyIiwic2V0RW52Qmx1ciIsImVudlJvdGF0aW9uIiwic2V0RW52Um90YXRpb24iLCJwb3N0UHJvY2Vzc2luZ0VuYWJsZWQiLCJzZXRQb3N0UHJvY2Vzc2luZ0VuYWJsZWQiLCJwb3N0UHJvY2Vzc2luZ1NldHRpbmdzIiwic2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5ncyIsImF1dG9mb2N1cyIsImVuYWJsZWQiLCJib2tlaFNjYWxlIiwiYmxvb20iLCJ0aHJlc2hvbGQiLCJyYWRpdXMiLCJkb2YiLCJmb2N1c0Rpc3RhbmNlIiwiYXBlcnR1cmUiLCJ2aWduZXR0ZSIsImRhcmtuZXNzIiwib2Zmc2V0IiwiaW5pdGlhbFN0YXRlUmVmIiwiY2FtZXJhUG9zaXRpb24iLCJjYW1lcmFUYXJnZXQiLCJtb2RlbEluaXRpYWxTdGF0ZVJlZiIsIm9iamVjdHMiLCJjdXJyZW50IiwiVmVjdG9yMyIsIm9yYml0Q29udHJvbHNSZWYiLCJzY2VuZVJlZiIsInNldFJlbmRlcmVyIiwiaGlzdG9yeSIsInNldEhpc3RvcnkiLCJoaXN0b3J5SW5kZXgiLCJzZXRIaXN0b3J5SW5kZXgiLCJpc1VuZG9SZWRvQWN0aW9uUmVmIiwic2tpcEhpc3RvcnlUcmFja2luZ1JlZiIsImlzVW5kb1JlZG9JblByb2dyZXNzUmVmIiwic2hvd01vZGVsU3RhdHMiLCJzZXRTaG93TW9kZWxTdGF0cyIsImRlYnVnSGlzdG9yeSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwid2luZG93IiwiZ2V0RnVsbENvbmZpZyIsIkpTT04iLCJwYXJzZSIsInN0cmluZ2lmeSIsIm9iaiIsInV1aWQiLCJvYmplY3RTdGF0ZXMiLCJ0b0FycmF5Iiwicm90YXRpb24iLCJzY2FsZSIsIm1hdGVyaWFsIiwiZ2V0SGV4U3RyaW5nIiwicm91Z2huZXNzIiwibWV0YWxuZXNzIiwiY2xlYXJjb2F0IiwiY2xlYXJjb2F0Um91Z2huZXNzIiwidHJhbnNwYXJlbnQiLCJvcGFjaXR5IiwiZW52TWFwSW50ZW5zaXR5IiwiZ2V0Q29tcGFyaXNvbkNvbmZpZyIsInBvc3RQcm9jZXNzaW5nIiwic2V0dGluZ3MiLCJsaWdodCIsImVudmlyb25tZW50IiwicHJlc2V0IiwiYmx1ciIsImhhc1Vuc2F2ZWRDaGFuZ2VzIiwiY3VycmVudENvbmZpZyIsInNhdmVkQ29uZmlnIiwicmV2ZXJ0VG9TYXZlZCIsImNvbmZpcm1SZXZlcnQiLCJjb25maXJtIiwiaGFuZGxlTG9hZFNjZW5lIiwic3VjY2VzcyIsImluaXRpYWxTdGF0ZSIsImFkZFRvSGlzdG9yeSIsIm5ld1N0YXRlIiwiY3VycmVudFN0YXRlIiwibmV3SGlzdG9yeSIsInNsaWNlIiwibmV3SW5kZXgiLCJwZW5kaW5nU2VsZWN0ZWRPYmplY3RzVVVJRHMiLCJzZXRGb3JjZVVwZGF0ZSIsImZvcmNlVXBkYXRlIiwiZiIsInRyYW5zZm9ybVZlcnNpb24iLCJzZXRUcmFuc2Zvcm1WZXJzaW9uIiwibWF0ZXJpYWxWZXJzaW9uIiwic2V0TWF0ZXJpYWxWZXJzaW9uIiwicmVzdG9yZUNvbmZpZyIsImNvbmZpZyIsImZvckVhY2giLCJzYXZlZFN0YXRlIiwiZmluZCIsIm8iLCJmcm9tQXJyYXkiLCJzZXRIZXgiLCJwYXJzZUludCIsInVuZGVmaW5lZCIsIm5lZWRzVXBkYXRlIiwidiIsInV1aWRzIiwibmV3U2VsZWN0ZWQiLCJmaWx0ZXIiLCJpbmNsdWRlcyIsInVuZG9BY3Rpb24iLCJwcmV2Q29uZmlnIiwicmVkb0FjdGlvbiIsIm5leHRDb25maWciLCJ0cmFja0NoYW5nZXMiLCJ0aW1lb3V0SWQiLCJjbGVhclRpbWVvdXQiLCJoYW5kbGVPYmplY3RDbGljayIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJvYmplY3QiLCJzaGlmdEtleSIsInByZXYiLCJwcmV2QXJyYXkiLCJBcnJheSIsImlzQXJyYXkiLCJCb29sZWFuIiwidXNlckRhdGEiLCJoYW5kbGVQb2ludGVyTWlzc2VkIiwicGFyZW50IiwicmVtb3ZlIiwib2JqZWN0c0FycmF5IiwiY2VudGVyIiwiYWRkIiwiZGl2aWRlU2NhbGFyIiwiY29weSIsImNsb25lIiwidHJhdmVyc2UiLCJjaGlsZCIsImlzTWVzaCIsInZpc2libGUiLCJzdWIiLCJ1cGRhdGVPYmplY3RzRnJvbUdyb3VwIiwiaW5kZXgiLCJ3b3JsZFBvcyIsImdldFdvcmxkUG9zaXRpb24iLCJoYW5kbGVTY2VuZVVwZGF0ZSIsInN0YXRzIiwiaW5pdGlhbE9iamVjdHMiLCJwdXNoIiwiZ2V0SGV4IiwiaGFuZGxlTGlnaHRNb3ZlZCIsImxpZ2h0SWQiLCJuZXdQb3NpdGlvbiIsInVuZG9MaWdodE1vdmUiLCJsYXN0UG9zaXRpb24iLCJoYW5kbGVLZXlEb3duIiwiZXZlbnQiLCJjdHJsS2V5Iiwia2V5IiwicHJldmVudERlZmF1bHQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIlVSTCIsInJldm9rZU9iamVjdFVSTCIsImhhbmRsZUNhbWVyYVZpZXciLCJ2aWV3IiwicGFuZWxSZWYiLCJhY3RpdmVBbmltYXRpb24iLCJzZXRBY3RpdmVBbmltYXRpb24iLCJhbmltYXRpb25TdGFydFRpbWUiLCJzZXRBbmltYXRpb25TdGFydFRpbWUiLCJyZXF1ZXN0UmVmIiwiYW5pbWF0ZUNhbWVyYSIsImFuaW1hdGlvbiIsImEiLCJlbGFwc2VkIiwicHJvZ3Jlc3MiLCJkdXJhdGlvbiIsInRhcmdldCIsImFuaW1hdGUiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJjYW5jZWxBbmltYXRpb25GcmFtZSIsImhhbmRsZVBsYXlBbmltYXRpb24iLCJhbmltYXRpb25JZCIsImdldEVudmlyb25tZW50UHJlc2V0IiwiaGFuZGxlUmVzZXQiLCJzYXZlZFNjZW5lIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNvbmZpcm1SZXNldCIsInJlbW92ZUl0ZW0iLCJzZXQiLCJ1cGRhdGUiLCJzZXRVc2VEaWFtb25kU2hhZGVyIiwic2V0VXNlUHJlbWl1bVdoaXRlR29sZCIsInNldFVzZVByZW1pdW1Sb3NlR29sZCIsInNjcmVlbnNob3RSZWYiLCJjYXB0dXJlU2NyZWVuc2hvdCIsImxvYWRTYXZlZFNjZW5lIiwic2NlbmVDb25maWciLCJtb2RlbCIsInNjZW5lQ29udHJvbHMiLCJyZXN0b3JlTWF0ZXJpYWxzIiwic2F2ZWRNYXRlcmlhbHNNYXAiLCJNYXAiLCJtIiwiaGFzIiwic2F2ZWRNYXRlcmlhbCIsImdldCIsImNoZWNrSW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjaGVja1NjZW5lUmVhZHkiLCJjb25maWdTdGF0ZSIsInNldENvbmZpZ1N0YXRlIiwiYXBwbHlNYXRlcmlhbHMiLCJtYXRlcmlhbENvbmZpZyIsImhhbmRsZVNhdmVTY2VuZSIsInNjZW5lTmFtZSIsImRlc2NyaXB0aW9uIiwic2F2ZVR5cGUiLCJhYmVycmF0aW9uU3RyZW5ndGgiLCJ0b25lTWFwcGVkIiwiaW9yIiwiY29sb3JFbnZNYXBSb3RZIiwic2V0SXRlbSIsInZlcnNpb24iLCJ1c2VySWQiLCJ3b3Jrc3BhY2VJZCIsIl9pZCIsImVyciIsImJsb2IiLCJCbG9iIiwidXJsIiwiY3JlYXRlT2JqZWN0VVJMIiwiZW5ncmF2aW5nVGV4dCIsInNldEVuZ3JhdmluZ1RleHQiLCJkZWNhbFBvc2l0aW9uIiwic2V0RGVjYWxQb3NpdGlvbiIsImRlY2FsU2NhbGUiLCJzZXREZWNhbFNjYWxlIiwiZGVjYWxSb3RhdGlvbiIsInNldERlY2FsUm90YXRpb24iLCJNYXRoIiwiUEkiLCJkZWNhbENvbG9yIiwic2V0RGVjYWxDb2xvciIsInNlbGVjdGVkRm9udCIsInNldFNlbGVjdGVkRm9udCIsInVzZURpYW1vbmRTaGFkZXIiLCJ1c2VQcmVtaXVtV2hpdGVHb2xkIiwidXNlUHJlbWl1bVJvc2VHb2xkIiwiaGFuZGxlRW5ncmF2ZVRleHQiLCJ0ZXh0IiwiaGFuZGxlRm9udENoYW5nZSIsImZvbnQiLCJzYXZlZEVuZ3JhdmluZ1RleHQiLCJoYW5kbGVEZWNhbFBsYWNlbWVudCIsImdldERlZmF1bHREZWNhbFBvc2l0aW9uIiwibW9kZWxJZCIsIm1vZGVsQ29uZmlncyIsImNob3NlbnJpbmciLCJ0ZXN0cmluZyIsImFnYXBlcmluZzEiLCJhZ2FwZXJpbmcyIiwicmluZ2EiLCJyaW5nYiIsInJpbmdjIiwiZ2V0RGVmYXVsdERlY2FsUm90YXRpb24iLCJnZXREZWZhdWx0RGVjYWxTY2FsZSIsImNhbGN1bGF0ZUR5bmFtaWNTY2FsZSIsImJhc2VTY2FsZSIsInRleHRMZW5ndGgiLCJzY2FsZVgiLCJzY2FsZVkiLCJzY2FsZVoiLCJzY2FsZU11bHRpcGxpZXIiLCJtaW4iLCJtYXhTY2FsZSIsImxpbWl0cyIsImR5bmFtaWNTY2FsZSIsImlzRnVsbHNjcmVlbiIsInNldElzRnVsbHNjcmVlbiIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJjaGVja01vYmlsZSIsImlubmVyV2lkdGgiLCJvdXRsaW5lckNvbGxhcHNlZCIsInNldE91dGxpbmVyQ29sbGFwc2VkIiwiZ2V0Q2FtZXJhUG9zaXRpb24iLCJmZXRjaEFuZEFwcGx5Q3VycmVudFZlcnNpb24iLCJjdXJyZW50VmVyc2lvbiIsIm9uUmVzZXQiLCJvblNjcmVlbnNob3QiLCJvblNhdmVTY2VuZSIsIm9uTG9hZFNjZW5lIiwib25Ub2dnbGVGdWxsc2NyZWVuIiwib25VbmRvIiwib25SZWRvIiwiZGl2IiwiY2xhc3NOYW1lIiwib3JpZW50YXRpb24iLCJyZWYiLCJvblJlc2V0TGlnaHRzIiwiZGVmYXVsdExpZ2h0cyIsIm9uRW5ncmF2ZVRleHQiLCJvbkRlY2FsUG9zaXRpb25DaGFuZ2UiLCJvbkRlY2FsU2NhbGVDaGFuZ2UiLCJvbkRlY2FsUm90YXRpb25DaGFuZ2UiLCJvbkRlY2FsQ29sb3JDaGFuZ2UiLCJvbkZvbnRDaGFuZ2UiLCJzaG93RGVidWciLCJvblNob3dEZWJ1Z0NoYW5nZSIsIm9uU2VsZWN0T2JqZWN0Iiwib25DYW1lcmFWaWV3Iiwib25QbGF5QW5pbWF0aW9uIiwiY29sbGFwc2VkIiwic2V0Q29sbGFwc2VkIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiZm92IiwibmVhciIsImZhciIsInNoYWRvd3MiLCJhbnRpYWxpYXMiLCJwcmVzZXJ2ZURyYXdpbmdCdWZmZXIiLCJvblBvaW50ZXJNaXNzZWQiLCJyYXljYXN0ZXIiLCJjb21wdXRlT2Zmc2V0cyIsIm9mZnNldFgiLCJjbGllbnRYIiwib2Zmc2V0WSIsImNsaWVudFkiLCJmcmFtZWxvb3AiLCJmYWxsYmFjayIsImZuIiwib25MaWdodFNlbGVjdCIsIm9uTGlnaHRNb3ZlZCIsInRocmVlUG9pbnRMaWdodGluZyIsIm9uRGVjYWxQbGFjZW1lbnQiLCJtYWtlRGVmYXVsdCIsImVuYWJsZURhbXBpbmciLCJtYXhEaXN0YW5jZSIsIm1vZGUiLCJvbk1vdXNlRG93biIsIm9uTW91c2VVcCIsIm9uT2JqZWN0Q2hhbmdlIiwiZ3JpZEhlbHBlciIsImFyZ3MiLCJyZWNlaXZlU2hhZG93IiwibWVzaCIsInJvdGF0aW9uLXgiLCJwbGFuZUdlb21ldHJ5IiwibWVzaFN0YW5kYXJkTWF0ZXJpYWwiLCJyZXNvbHV0aW9uIiwibWl4Qmx1ciIsIm1peFN0cmVuZ3RoIiwiZGVwdGhTY2FsZSIsIm1pbkRlcHRoVGhyZXNob2xkIiwibWF4RGVwdGhUaHJlc2hvbGQiLCJtaXJyb3IiLCJwcmltaXRpdmUiLCJTaGFkb3dNYXRlcmlhbCIsIkNvbG9yIiwiYWxpZ25tZW50IiwibWFyZ2luIiwibGFiZWxDb2xvciIsImF4aXNDb2xvcnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});