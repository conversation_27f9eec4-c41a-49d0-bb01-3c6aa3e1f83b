"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+s3-request-presigner@3.300.0";
exports.ids = ["vendor-chunks/@aws-sdk+s3-request-presigner@3.300.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst SHA256_HEADER = \"X-Amz-Content-Sha256\";\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst HOST_HEADER = \"host\";\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzMy1yZXF1ZXN0LXByZXNpZ25lckAzLjMwMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zMy1yZXF1ZXN0LXByZXNpZ25lci9kaXN0LWVzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9jb25zdGFudHMuanM/MGJlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVU5TSUdORURfUEFZTE9BRCA9IFwiVU5TSUdORUQtUEFZTE9BRFwiO1xuZXhwb3J0IGNvbnN0IFNIQTI1Nl9IRUFERVIgPSBcIlgtQW16LUNvbnRlbnQtU2hhMjU2XCI7XG5leHBvcnQgY29uc3QgQUxHT1JJVEhNX1FVRVJZX1BBUkFNID0gXCJYLUFtei1BbGdvcml0aG1cIjtcbmV4cG9ydCBjb25zdCBDUkVERU5USUFMX1FVRVJZX1BBUkFNID0gXCJYLUFtei1DcmVkZW50aWFsXCI7XG5leHBvcnQgY29uc3QgQU1aX0RBVEVfUVVFUllfUEFSQU0gPSBcIlgtQW16LURhdGVcIjtcbmV4cG9ydCBjb25zdCBTSUdORURfSEVBREVSU19RVUVSWV9QQVJBTSA9IFwiWC1BbXotU2lnbmVkSGVhZGVyc1wiO1xuZXhwb3J0IGNvbnN0IEVYUElSRVNfUVVFUllfUEFSQU0gPSBcIlgtQW16LUV4cGlyZXNcIjtcbmV4cG9ydCBjb25zdCBIT1NUX0hFQURFUiA9IFwiaG9zdFwiO1xuZXhwb3J0IGNvbnN0IEFMR09SSVRITV9JREVOVElGSUVSID0gXCJBV1M0LUhNQUMtU0hBMjU2XCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst SHA256_HEADER = \"X-Amz-Content-Sha256\";\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst HOST_HEADER = \"host\";\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3MzLXJlcXVlc3QtcHJlc2lnbmVyQDMuMzAwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3MzLXJlcXVlc3QtcHJlc2lnbmVyL2Rpc3QtZXMvY29uc3RhbnRzLmpzPzhlN2MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFVOU0lHTkVEX1BBWUxPQUQgPSBcIlVOU0lHTkVELVBBWUxPQURcIjtcbmV4cG9ydCBjb25zdCBTSEEyNTZfSEVBREVSID0gXCJYLUFtei1Db250ZW50LVNoYTI1NlwiO1xuZXhwb3J0IGNvbnN0IEFMR09SSVRITV9RVUVSWV9QQVJBTSA9IFwiWC1BbXotQWxnb3JpdGhtXCI7XG5leHBvcnQgY29uc3QgQ1JFREVOVElBTF9RVUVSWV9QQVJBTSA9IFwiWC1BbXotQ3JlZGVudGlhbFwiO1xuZXhwb3J0IGNvbnN0IEFNWl9EQVRFX1FVRVJZX1BBUkFNID0gXCJYLUFtei1EYXRlXCI7XG5leHBvcnQgY29uc3QgU0lHTkVEX0hFQURFUlNfUVVFUllfUEFSQU0gPSBcIlgtQW16LVNpZ25lZEhlYWRlcnNcIjtcbmV4cG9ydCBjb25zdCBFWFBJUkVTX1FVRVJZX1BBUkFNID0gXCJYLUFtei1FeHBpcmVzXCI7XG5leHBvcnQgY29uc3QgSE9TVF9IRUFERVIgPSBcImhvc3RcIjtcbmV4cG9ydCBjb25zdCBBTEdPUklUSE1fSURFTlRJRklFUiA9IFwiQVdTNC1ITUFDLVNIQTI1NlwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSignedUrl: () => (/* binding */ getSignedUrl)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-endpoint */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/protocol-http */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-format-url */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js\");\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./presigner */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n\n\n\n\nconst getSignedUrl = async (client, command, options = {}) => {\n    let s3Presigner;\n    if (typeof client.config.endpointProvider === \"function\") {\n        const endpointV2 = await (0,_aws_sdk_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions)(command.input, command.constructor, client.config);\n        const authScheme = endpointV2.properties?.authSchemes?.[0];\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner({\n            ...client.config,\n            signingName: authScheme?.signingName,\n            region: async () => authScheme?.signingRegion,\n        });\n    }\n    else {\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner(client.config);\n    }\n    const presignInterceptMiddleware = (next, context) => async (args) => {\n        const { request } = args;\n        if (!_aws_sdk_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest.isInstance(request)) {\n            throw new Error(\"Request to be presigned is not an valid HTTP request.\");\n        }\n        delete request.headers[\"amz-sdk-invocation-id\"];\n        delete request.headers[\"amz-sdk-request\"];\n        delete request.headers[\"x-amz-user-agent\"];\n        const presigned = await s3Presigner.presign(request, {\n            ...options,\n            signingRegion: options.signingRegion ?? context[\"signing_region\"],\n            signingService: options.signingService ?? context[\"signing_service\"],\n        });\n        return {\n            response: {},\n            output: {\n                $metadata: { httpStatusCode: 200 },\n                presigned,\n            },\n        };\n    };\n    const middlewareName = \"presignInterceptMiddleware\";\n    const clientStack = client.middlewareStack.clone();\n    clientStack.addRelativeTo(presignInterceptMiddleware, {\n        name: middlewareName,\n        relation: \"before\",\n        toMiddleware: \"awsAuthMiddleware\",\n        override: true,\n    });\n    const handler = command.resolveMiddleware(clientStack, client.config, {});\n    const { output } = await handler({ input: command.input });\n    const { presigned } = output;\n    return (0,_aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_2__.formatUrl)(presigned);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSignedUrl: () => (/* binding */ getSignedUrl)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-endpoint@3.299.0/node_modules/@aws-sdk/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/protocol-http */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+protocol-http@3.296.0/node_modules/@aws-sdk/protocol-http/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-format-url */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js\");\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./presigner */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n\n\n\n\nconst getSignedUrl = async (client, command, options = {}) => {\n    let s3Presigner;\n    if (typeof client.config.endpointProvider === \"function\") {\n        const endpointV2 = await (0,_aws_sdk_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions)(command.input, command.constructor, client.config);\n        const authScheme = endpointV2.properties?.authSchemes?.[0];\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner({\n            ...client.config,\n            signingName: authScheme?.signingName,\n            region: async () => authScheme?.signingRegion,\n        });\n    }\n    else {\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner(client.config);\n    }\n    const presignInterceptMiddleware = (next, context) => async (args) => {\n        const { request } = args;\n        if (!_aws_sdk_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest.isInstance(request)) {\n            throw new Error(\"Request to be presigned is not an valid HTTP request.\");\n        }\n        delete request.headers[\"amz-sdk-invocation-id\"];\n        delete request.headers[\"amz-sdk-request\"];\n        delete request.headers[\"x-amz-user-agent\"];\n        const presigned = await s3Presigner.presign(request, {\n            ...options,\n            signingRegion: options.signingRegion ?? context[\"signing_region\"],\n            signingService: options.signingService ?? context[\"signing_service\"],\n        });\n        return {\n            response: {},\n            output: {\n                $metadata: { httpStatusCode: 200 },\n                presigned,\n            },\n        };\n    };\n    const middlewareName = \"presignInterceptMiddleware\";\n    const clientStack = client.middlewareStack.clone();\n    clientStack.addRelativeTo(presignInterceptMiddleware, {\n        name: middlewareName,\n        relation: \"before\",\n        toMiddleware: \"awsAuthMiddleware\",\n        override: true,\n    });\n    const handler = command.resolveMiddleware(clientStack, client.config, {});\n    const { output } = await handler({ input: command.input });\n    const { presigned } = output;\n    return (0,_aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_2__.formatUrl)(presigned);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSignedUrl */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./presigner */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _presigner__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _presigner__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzMy1yZXF1ZXN0LXByZXNpZ25lckAzLjMwMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zMy1yZXF1ZXN0LXByZXNpZ25lci9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErQjtBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3MzLXJlcXVlc3QtcHJlc2lnbmVyQDMuMzAwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3MzLXJlcXVlc3QtcHJlc2lnbmVyL2Rpc3QtZXMvaW5kZXguanM/ZmRhZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9nZXRTaWduZWRVcmxcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3ByZXNpZ25lclwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3RequestPresigner: () => (/* reexport safe */ _presigner__WEBPACK_IMPORTED_MODULE_1__.S3RequestPresigner),\n/* harmony export */   getSignedUrl: () => (/* reexport safe */ _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__.getSignedUrl)\n/* harmony export */ });\n/* harmony import */ var _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSignedUrl */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\");\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./presigner */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStCO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9pbmRleC5qcz8yNGZkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2dldFNpZ25lZFVybFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vcHJlc2lnbmVyXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3RequestPresigner: () => (/* binding */ S3RequestPresigner)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/signature-v4-multi-region */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\");\n\n\nclass S3RequestPresigner {\n    constructor(options) {\n        const resolvedOptions = {\n            service: options.signingName || options.service || \"s3\",\n            uriEscapePath: options.uriEscapePath || false,\n            applyChecksum: options.applyChecksum || false,\n            ...options,\n        };\n        this.signer = new _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__.SignatureV4MultiRegion(resolvedOptions);\n    }\n    presign(requestToSign, { unsignableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        unsignableHeaders.add(\"content-type\");\n        Object.keys(requestToSign.headers)\n            .map((header) => header.toLowerCase())\n            .filter((header) => header.startsWith(\"x-amz-server-side-encryption\"))\n            .forEach((header) => {\n            unhoistableHeaders.add(header);\n        });\n        requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SHA256_HEADER] = _constants__WEBPACK_IMPORTED_MODULE_1__.UNSIGNED_PAYLOAD;\n        const currentHostHeader = requestToSign.headers.host;\n        const port = requestToSign.port;\n        const expectedHostHeader = `${requestToSign.hostname}${requestToSign.port != null ? \":\" + port : \"\"}`;\n        if (!currentHostHeader || (currentHostHeader === requestToSign.hostname && requestToSign.port != null)) {\n            requestToSign.headers.host = expectedHostHeader;\n        }\n        return this.signer.presign(requestToSign, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3RequestPresigner: () => (/* binding */ S3RequestPresigner)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/signature-v4-multi-region */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4-multi-region@3.299.0/node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\");\n\n\nclass S3RequestPresigner {\n    constructor(options) {\n        const resolvedOptions = {\n            service: options.signingName || options.service || \"s3\",\n            uriEscapePath: options.uriEscapePath || false,\n            applyChecksum: options.applyChecksum || false,\n            ...options,\n        };\n        this.signer = new _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__.SignatureV4MultiRegion(resolvedOptions);\n    }\n    presign(requestToSign, { unsignableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        unsignableHeaders.add(\"content-type\");\n        Object.keys(requestToSign.headers)\n            .map((header) => header.toLowerCase())\n            .filter((header) => header.startsWith(\"x-amz-server-side-encryption\"))\n            .forEach((header) => {\n            unhoistableHeaders.add(header);\n        });\n        requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SHA256_HEADER] = _constants__WEBPACK_IMPORTED_MODULE_1__.UNSIGNED_PAYLOAD;\n        const currentHostHeader = requestToSign.headers.host;\n        const port = requestToSign.port;\n        const expectedHostHeader = `${requestToSign.hostname}${requestToSign.port != null ? \":\" + port : \"\"}`;\n        if (!currentHostHeader || (currentHostHeader === requestToSign.hostname && requestToSign.port != null)) {\n            requestToSign.headers.host = expectedHostHeader;\n        }\n        return this.signer.presign(requestToSign, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9wcmVzaWduZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRFO0FBQ2Q7QUFDdkQ7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixzRkFBc0I7QUFDaEQ7QUFDQSw2QkFBNkIsNEVBQTRFLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDhCQUE4QixxREFBYSxJQUFJLHdEQUFnQjtBQUMvRDtBQUNBO0FBQ0Esc0NBQXNDLHVCQUF1QixFQUFFLDZDQUE2QztBQUM1RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrczMtcmVxdWVzdC1wcmVzaWduZXJAMy4zMDAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9wcmVzaWduZXIuanM/ODg2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTaWduYXR1cmVWNE11bHRpUmVnaW9uIH0gZnJvbSBcIkBhd3Mtc2RrL3NpZ25hdHVyZS12NC1tdWx0aS1yZWdpb25cIjtcbmltcG9ydCB7IFNIQTI1Nl9IRUFERVIsIFVOU0lHTkVEX1BBWUxPQUQgfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCBjbGFzcyBTM1JlcXVlc3RQcmVzaWduZXIge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgcmVzb2x2ZWRPcHRpb25zID0ge1xuICAgICAgICAgICAgc2VydmljZTogb3B0aW9ucy5zaWduaW5nTmFtZSB8fCBvcHRpb25zLnNlcnZpY2UgfHwgXCJzM1wiLFxuICAgICAgICAgICAgdXJpRXNjYXBlUGF0aDogb3B0aW9ucy51cmlFc2NhcGVQYXRoIHx8IGZhbHNlLFxuICAgICAgICAgICAgYXBwbHlDaGVja3N1bTogb3B0aW9ucy5hcHBseUNoZWNrc3VtIHx8IGZhbHNlLFxuICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5zaWduZXIgPSBuZXcgU2lnbmF0dXJlVjRNdWx0aVJlZ2lvbihyZXNvbHZlZE9wdGlvbnMpO1xuICAgIH1cbiAgICBwcmVzaWduKHJlcXVlc3RUb1NpZ24sIHsgdW5zaWduYWJsZUhlYWRlcnMgPSBuZXcgU2V0KCksIHVuaG9pc3RhYmxlSGVhZGVycyA9IG5ldyBTZXQoKSwgLi4ub3B0aW9ucyB9ID0ge30pIHtcbiAgICAgICAgdW5zaWduYWJsZUhlYWRlcnMuYWRkKFwiY29udGVudC10eXBlXCIpO1xuICAgICAgICBPYmplY3Qua2V5cyhyZXF1ZXN0VG9TaWduLmhlYWRlcnMpXG4gICAgICAgICAgICAubWFwKChoZWFkZXIpID0+IGhlYWRlci50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgLmZpbHRlcigoaGVhZGVyKSA9PiBoZWFkZXIuc3RhcnRzV2l0aChcIngtYW16LXNlcnZlci1zaWRlLWVuY3J5cHRpb25cIikpXG4gICAgICAgICAgICAuZm9yRWFjaCgoaGVhZGVyKSA9PiB7XG4gICAgICAgICAgICB1bmhvaXN0YWJsZUhlYWRlcnMuYWRkKGhlYWRlcik7XG4gICAgICAgIH0pO1xuICAgICAgICByZXF1ZXN0VG9TaWduLmhlYWRlcnNbU0hBMjU2X0hFQURFUl0gPSBVTlNJR05FRF9QQVlMT0FEO1xuICAgICAgICBjb25zdCBjdXJyZW50SG9zdEhlYWRlciA9IHJlcXVlc3RUb1NpZ24uaGVhZGVycy5ob3N0O1xuICAgICAgICBjb25zdCBwb3J0ID0gcmVxdWVzdFRvU2lnbi5wb3J0O1xuICAgICAgICBjb25zdCBleHBlY3RlZEhvc3RIZWFkZXIgPSBgJHtyZXF1ZXN0VG9TaWduLmhvc3RuYW1lfSR7cmVxdWVzdFRvU2lnbi5wb3J0ICE9IG51bGwgPyBcIjpcIiArIHBvcnQgOiBcIlwifWA7XG4gICAgICAgIGlmICghY3VycmVudEhvc3RIZWFkZXIgfHwgKGN1cnJlbnRIb3N0SGVhZGVyID09PSByZXF1ZXN0VG9TaWduLmhvc3RuYW1lICYmIHJlcXVlc3RUb1NpZ24ucG9ydCAhPSBudWxsKSkge1xuICAgICAgICAgICAgcmVxdWVzdFRvU2lnbi5oZWFkZXJzLmhvc3QgPSBleHBlY3RlZEhvc3RIZWFkZXI7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuc2lnbmVyLnByZXNpZ24ocmVxdWVzdFRvU2lnbiwge1xuICAgICAgICAgICAgZXhwaXJlc0luOiA5MDAsXG4gICAgICAgICAgICB1bnNpZ25hYmxlSGVhZGVycyxcbiAgICAgICAgICAgIHVuaG9pc3RhYmxlSGVhZGVycyxcbiAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+s3-request-presigner@3.300.0/node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\n");

/***/ })

};
;