import React, { useEffect } from "react";
import { useGLTF } from "@react-three/drei";

const UploadedModel = ({ url, wireframe, onLoad }) => {
  const { scene } = useGLTF(url);

  useEffect(() => {
    scene.traverse((child) => {
      if (child.isMesh) {
        child.material.wireframe = wireframe;
      }
    });
    onLoad?.(scene);
  }, [scene, wireframe, onLoad]);

  return <primitive object={scene} position={[0, 0, 0]} />;
};

export default UploadedModel;
