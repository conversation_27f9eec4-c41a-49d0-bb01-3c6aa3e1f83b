"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-floater/es/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-floater/es/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReactFloater)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var popper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! popper.js */ \"(ssr)/./node_modules/.pnpm/popper.js@1.16.1/node_modules/popper.js/dist/esm/popper.js\");\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js\");\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(deepmerge__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var is_lite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! is-lite */ \"(ssr)/./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js\");\n/* harmony import */ var tree_changes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tree-changes */ \"(ssr)/./node_modules/.pnpm/tree-changes@0.9.3/node_modules/tree-changes/esm/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\n\n\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar DEFAULTS = {flip:{padding:20},preventOverflow:{padding:10}};\n\nvar VALIDATOR_ARG_ERROR_MESSAGE='The typeValidator argument must be a function '+'with the signature function(props, propName, componentName).';var MESSAGE_ARG_ERROR_MESSAGE='The error message is optional, but must be a string if provided.';function propIsRequired(condition,props,propName,componentName){if(typeof condition==='boolean'){return condition;}if(typeof condition==='function'){return condition(props,propName,componentName);}if(Boolean(condition)===true){return Boolean(condition);}return false;}function propExists(props,propName){return Object.hasOwnProperty.call(props,propName);}function missingPropError(props,propName,componentName,message){if(message){return new Error(message);}return new Error(\"Required \".concat(props[propName],\" `\").concat(propName,\"` was not specified in `\").concat(componentName,\"`.\"));}function guardAgainstInvalidArgTypes(typeValidator,message){if(typeof typeValidator!=='function'){throw new TypeError(VALIDATOR_ARG_ERROR_MESSAGE);}if(Boolean(message)&&typeof message!=='string'){throw new TypeError(MESSAGE_ARG_ERROR_MESSAGE);}}function isRequiredIf(typeValidator,condition,message){guardAgainstInvalidArgTypes(typeValidator,message);return function(props,propName,componentName){for(var _len=arguments.length,rest=new Array(_len>3?_len-3:0),_key=3;_key<_len;_key++){rest[_key-3]=arguments[_key];}if(propIsRequired(condition,props,propName,componentName)){if(propExists(props,propName)){return typeValidator.apply(void 0,[props,propName,componentName].concat(rest));}return missingPropError(props,propName,componentName,message);}// Is not required, so just run typeValidator.\nreturn typeValidator.apply(void 0,[props,propName,componentName].concat(rest));};}\n\nvar STATUS = {INIT:'init',IDLE:'idle',OPENING:'opening',OPEN:'open',CLOSING:'closing',ERROR:'error'};\n\nvar isReact16=(react_dom__WEBPACK_IMPORTED_MODULE_2___default().createPortal)!==undefined;function canUseDOM(){return !!(typeof window!=='undefined'&&window.document&&window.document.createElement);}function isMobile(){return 'ontouchstart'in window&&/Mobi/.test(navigator.userAgent);}/**\n * Log method calls if debug is enabled\n *\n * @private\n * @param {Object}       arg\n * @param {string}       arg.title    - The title the logger was called from\n * @param {Object|Array} [arg.data]   - The data to be logged\n * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n */function log(_ref){var title=_ref.title,data=_ref.data,_ref$warn=_ref.warn,warn=_ref$warn===void 0?false:_ref$warn,_ref$debug=_ref.debug,debug=_ref$debug===void 0?false:_ref$debug;/* eslint-disable no-console */var logFn=warn?console.warn||console.error:console.log;if(debug&&title&&data){console.groupCollapsed(\"%creact-floater: \".concat(title),'color: #9b00ff; font-weight: bold; font-size: 12px;');if(Array.isArray(data)){data.forEach(function(d){if(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"].plainObject(d)&&d.key){logFn.apply(console,[d.key,d.value]);}else {logFn.apply(console,[d]);}});}else {logFn.apply(console,[data]);}console.groupEnd();}/* eslint-enable */}function on(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.addEventListener(event,cb,capture);}function off(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.removeEventListener(event,cb,capture);}function once(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var _nextCB;// eslint-disable-next-line prefer-const\n_nextCB=function nextCB(e){cb(e);off(element,event,_nextCB);};on(element,event,_nextCB,capture);}function noop(){}\n\nvar ReactFloaterPortal=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterPortal,_React$Component);var _super=_createSuper(ReactFloaterPortal);function ReactFloaterPortal(){_classCallCheck(this,ReactFloaterPortal);return _super.apply(this,arguments);}_createClass(ReactFloaterPortal,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM())return;if(!this.node){this.appendNode();}if(!isReact16){this.renderPortal();}}},{key:\"componentDidUpdate\",value:function componentDidUpdate(){if(!canUseDOM())return;if(!isReact16){this.renderPortal();}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM()||!this.node)return;if(!isReact16){react_dom__WEBPACK_IMPORTED_MODULE_2___default().unmountComponentAtNode(this.node);}if(this.node&&this.node.parentNode===document.body){document.body.removeChild(this.node);this.node=undefined;}}},{key:\"appendNode\",value:function appendNode(){var _this$props=this.props,id=_this$props.id,zIndex=_this$props.zIndex;if(!this.node){this.node=document.createElement('div');/* istanbul ignore else */if(id){this.node.id=id;}if(zIndex){this.node.style.zIndex=zIndex;}document.body.appendChild(this.node);}}},{key:\"renderPortal\",value:function renderPortal(){if(!canUseDOM())return null;var _this$props2=this.props,children=_this$props2.children,setRef=_this$props2.setRef;if(!this.node){this.appendNode();}/* istanbul ignore else */if(isReact16){return/*#__PURE__*/react_dom__WEBPACK_IMPORTED_MODULE_2___default().createPortal(children,this.node);}var portal=react_dom__WEBPACK_IMPORTED_MODULE_2___default().unstable_renderSubtreeIntoContainer(this,children.length>1?/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",null,children):children[0],this.node);setRef(portal);return null;}},{key:\"renderReact16\",value:function renderReact16(){var _this$props3=this.props,hasChildren=_this$props3.hasChildren,placement=_this$props3.placement,target=_this$props3.target;if(!hasChildren){if(target||placement==='center'){return this.renderPortal();}return null;}return this.renderPortal();}},{key:\"render\",value:function render(){if(!isReact16){return null;}return this.renderReact16();}}]);return ReactFloaterPortal;}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));_defineProperty(ReactFloaterPortal,\"propTypes\",{children:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().element),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().array)]),hasChildren:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),id:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number)]),placement:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string),setRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,target:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)]),zIndex:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number)});\n\nvar FloaterArrow=/*#__PURE__*/function(_React$Component){_inherits(FloaterArrow,_React$Component);var _super=_createSuper(FloaterArrow);function FloaterArrow(){_classCallCheck(this,FloaterArrow);return _super.apply(this,arguments);}_createClass(FloaterArrow,[{key:\"parentStyle\",get:function get(){var _this$props=this.props,placement=_this$props.placement,styles=_this$props.styles;var length=styles.arrow.length;var arrow={pointerEvents:'none',position:'absolute',width:'100%'};/* istanbul ignore else */if(placement.startsWith('top')){arrow.bottom=0;arrow.left=0;arrow.right=0;arrow.height=length;}else if(placement.startsWith('bottom')){arrow.left=0;arrow.right=0;arrow.top=0;arrow.height=length;}else if(placement.startsWith('left')){arrow.right=0;arrow.top=0;arrow.bottom=0;}else if(placement.startsWith('right')){arrow.left=0;arrow.top=0;}return arrow;}},{key:\"render\",value:function render(){var _this$props2=this.props,placement=_this$props2.placement,setArrowRef=_this$props2.setArrowRef,styles=_this$props2.styles;var _styles$arrow=styles.arrow,color=_styles$arrow.color,display=_styles$arrow.display,length=_styles$arrow.length,margin=_styles$arrow.margin,position=_styles$arrow.position,spread=_styles$arrow.spread;var arrowStyles={display:display,position:position};var points;var x=spread;var y=length;/* istanbul ignore else */if(placement.startsWith('top')){points=\"0,0 \".concat(x/2,\",\").concat(y,\" \").concat(x,\",0\");arrowStyles.bottom=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('bottom')){points=\"\".concat(x,\",\").concat(y,\" \").concat(x/2,\",0 0,\").concat(y);arrowStyles.top=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('left')){y=spread;x=length;points=\"0,0 \".concat(x,\",\").concat(y/2,\" 0,\").concat(y);arrowStyles.right=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}else if(placement.startsWith('right')){y=spread;x=length;points=\"\".concat(x,\",\").concat(y,\" \").concat(x,\",0 0,\").concat(y/2);arrowStyles.left=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__arrow\",style:this.parentStyle},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\",{ref:setArrowRef,style:arrowStyles},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\",{width:x,height:y,version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\"},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"polygon\",{points:points,fill:color}))));}}]);return FloaterArrow;}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));_defineProperty(FloaterArrow,\"propTypes\",{placement:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string).isRequired,setArrowRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object).isRequired});\n\nvar _excluded$1=[\"color\",\"height\",\"width\"];function FloaterCloseBtn(_ref){var handleClick=_ref.handleClick,styles=_ref.styles;var color=styles.color,height=styles.height,width=styles.width,style=_objectWithoutProperties(styles,_excluded$1);return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\",{\"aria-label\":\"close\",onClick:handleClick,style:style,type:\"button\"},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\",{width:\"\".concat(width,\"px\"),height:\"\".concat(height,\"px\"),viewBox:\"0 0 18 18\",version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\",preserveAspectRatio:\"xMidYMid\"},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"g\",null,/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\",{d:\"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",fill:color}))));}FloaterCloseBtn.propTypes={handleClick:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object).isRequired};\n\nfunction FloaterContainer(_ref){var content=_ref.content,footer=_ref.footer,handleClick=_ref.handleClick,open=_ref.open,positionWrapper=_ref.positionWrapper,showCloseButton=_ref.showCloseButton,title=_ref.title,styles=_ref.styles;var output={content:/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(content)?content:/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__content\",style:styles.content},content)};if(title){output.title=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(title)?title:/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__title\",style:styles.title},title);}if(footer){output.footer=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(footer)?footer:/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__footer\",style:styles.footer},footer);}if((showCloseButton||positionWrapper)&&!is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open)){output.close=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FloaterCloseBtn,{styles:styles.close,handleClick:handleClick});}return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__container\",style:styles.container},output.close,output.title,output.content,output.footer);}FloaterContainer.propTypes={content:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,footer:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),handleClick:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,open:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),positionWrapper:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool).isRequired,showCloseButton:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool).isRequired,styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object).isRequired,title:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node)};\n\nvar Floater=/*#__PURE__*/function(_React$Component){_inherits(Floater,_React$Component);var _super=_createSuper(Floater);function Floater(){_classCallCheck(this,Floater);return _super.apply(this,arguments);}_createClass(Floater,[{key:\"style\",get:function get(){var _this$props=this.props,disableAnimation=_this$props.disableAnimation,component=_this$props.component,placement=_this$props.placement,hideArrow=_this$props.hideArrow,status=_this$props.status,styles=_this$props.styles;var length=styles.arrow.length,floater=styles.floater,floaterCentered=styles.floaterCentered,floaterClosing=styles.floaterClosing,floaterOpening=styles.floaterOpening,floaterWithAnimation=styles.floaterWithAnimation,floaterWithComponent=styles.floaterWithComponent;var element={};if(!hideArrow){if(placement.startsWith('top')){element.padding=\"0 0 \".concat(length,\"px\");}else if(placement.startsWith('bottom')){element.padding=\"\".concat(length,\"px 0 0\");}else if(placement.startsWith('left')){element.padding=\"0 \".concat(length,\"px 0 0\");}else if(placement.startsWith('right')){element.padding=\"0 0 0 \".concat(length,\"px\");}}if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1){element=_objectSpread2(_objectSpread2({},element),floaterOpening);}if(status===STATUS.CLOSING){element=_objectSpread2(_objectSpread2({},element),floaterClosing);}if(status===STATUS.OPEN&&!disableAnimation){element=_objectSpread2(_objectSpread2({},element),floaterWithAnimation);}if(placement==='center'){element=_objectSpread2(_objectSpread2({},element),floaterCentered);}if(component){element=_objectSpread2(_objectSpread2({},element),floaterWithComponent);}return _objectSpread2(_objectSpread2({},floater),element);}},{key:\"render\",value:function render(){var _this$props2=this.props,component=_this$props2.component,closeFn=_this$props2.handleClick,hideArrow=_this$props2.hideArrow,setFloaterRef=_this$props2.setFloaterRef,status=_this$props2.status;var output={};var classes=['__floater'];if(component){if(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(component)){output.content=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(component,{closeFn:closeFn});}else {output.content=component({closeFn:closeFn});}}else {output.content=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FloaterContainer,this.props);}if(status===STATUS.OPEN){classes.push('__floater__open');}if(!hideArrow){output.arrow=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(FloaterArrow,this.props);}return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{ref:setFloaterRef,className:classes.join(' '),style:this.style},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\",{className:\"__floater__body\"},output.content,output.arrow));}}]);return Floater;}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));_defineProperty(Floater,\"propTypes\",{component:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().element)]),content:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),disableAnimation:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool).isRequired,footer:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),handleClick:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,hideArrow:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool).isRequired,open:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),placement:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string).isRequired,positionWrapper:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool).isRequired,setArrowRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,setFloaterRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,showCloseButton:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),status:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string).isRequired,styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object).isRequired,title:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node)});\n\nvar ReactFloaterWrapper=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterWrapper,_React$Component);var _super=_createSuper(ReactFloaterWrapper);function ReactFloaterWrapper(){_classCallCheck(this,ReactFloaterWrapper);return _super.apply(this,arguments);}_createClass(ReactFloaterWrapper,[{key:\"render\",value:function render(){var _this$props=this.props,children=_this$props.children,handleClick=_this$props.handleClick,handleMouseEnter=_this$props.handleMouseEnter,handleMouseLeave=_this$props.handleMouseLeave,setChildRef=_this$props.setChildRef,setWrapperRef=_this$props.setWrapperRef,style=_this$props.style,styles=_this$props.styles;var element;/* istanbul ignore else */if(children){if(react__WEBPACK_IMPORTED_MODULE_0___default().Children.count(children)===1){if(!/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().isValidElement(children)){element=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\",null,children);}else {var refProp=is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"function\"](children.type)?'innerRef':'ref';element=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().cloneElement(react__WEBPACK_IMPORTED_MODULE_0___default().Children.only(children),_defineProperty({},refProp,setChildRef));}}else {element=children;}}if(!element){return null;}return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\",{ref:setWrapperRef,style:_objectSpread2(_objectSpread2({},styles),style),onClick:handleClick,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave},element);}}]);return ReactFloaterWrapper;}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));_defineProperty(ReactFloaterWrapper,\"propTypes\",{children:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),handleClick:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,handleMouseEnter:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,handleMouseLeave:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,setChildRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,setWrapperRef:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func).isRequired,style:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object).isRequired});\n\nvar defaultOptions={zIndex:100};function getStyles(styles){var options=deepmerge__WEBPACK_IMPORTED_MODULE_1___default()(defaultOptions,styles.options||{});return {wrapper:{cursor:'help',display:'inline-flex',flexDirection:'column',zIndex:options.zIndex},wrapperPosition:{left:-1000,position:'absolute',top:-1000,visibility:'hidden'},floater:{display:'inline-block',filter:'drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))',maxWidth:300,opacity:0,position:'relative',transition:'opacity 0.3s',visibility:'hidden',zIndex:options.zIndex},floaterOpening:{opacity:1,visibility:'visible'},floaterWithAnimation:{opacity:1,transition:'opacity 0.3s, transform 0.2s',visibility:'visible'},floaterWithComponent:{maxWidth:'100%'},floaterClosing:{opacity:0,visibility:'visible'},floaterCentered:{left:'50%',position:'fixed',top:'50%',transform:'translate(-50%, -50%)'},container:{backgroundColor:'#fff',color:'#666',minHeight:60,minWidth:200,padding:20,position:'relative',zIndex:10},title:{borderBottom:'1px solid #555',color:'#555',fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:'transparent',border:0,borderRadius:0,color:'#555',fontSize:0,height:15,outline:'none',padding:10,position:'absolute',right:0,top:0,width:15,WebkitAppearance:'none'},footer:{borderTop:'1px solid #ccc',fontSize:13,marginTop:10,paddingTop:5},arrow:{color:'#fff',display:'inline-flex',length:16,margin:8,position:'absolute',spread:32},options:options};}\n\nvar _excluded=[\"arrow\",\"flip\",\"offset\"];var POSITIONING_PROPS=['position','top','right','bottom','left'];var ReactFloater=/*#__PURE__*/function(_React$Component){_inherits(ReactFloater,_React$Component);var _super=_createSuper(ReactFloater);function ReactFloater(props){var _this;_classCallCheck(this,ReactFloater);_this=_super.call(this,props);/* istanbul ignore else */_defineProperty(_assertThisInitialized(_this),\"setArrowRef\",function(ref){_this.arrowRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setChildRef\",function(ref){_this.childRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setFloaterRef\",function(ref){_this.floaterRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setWrapperRef\",function(ref){_this.wrapperRef=ref;});_defineProperty(_assertThisInitialized(_this),\"handleTransitionEnd\",function(){var status=_this.state.status;var callback=_this.props.callback;/* istanbul ignore else */if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}_this.setState({status:status===STATUS.OPENING?STATUS.OPEN:STATUS.IDLE},function(){var newStatus=_this.state.status;callback(newStatus===STATUS.OPEN?'open':'close',_this.props);});});_defineProperty(_assertThisInitialized(_this),\"handleClick\",function(){var _this$props=_this.props,event=_this$props.event,open=_this$props.open;if(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open))return;var _this$state=_this.state,positionWrapper=_this$state.positionWrapper,status=_this$state.status;/* istanbul ignore else */if(_this.event==='click'||_this.event==='hover'&&positionWrapper){log({title:'click',data:[{event:event,status:status===STATUS.OPEN?'closing':'opening'}],debug:_this.debug});_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseEnter\",function(){var _this$props2=_this.props,event=_this$props2.event,open=_this$props2.open;if(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open)||isMobile())return;var status=_this.state.status;/* istanbul ignore else */if(_this.event==='hover'&&status===STATUS.IDLE){log({title:'mouseEnter',data:[{key:'originalEvent',value:event}],debug:_this.debug});clearTimeout(_this.eventDelayTimeout);_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseLeave\",function(){var _this$props3=_this.props,event=_this$props3.event,eventDelay=_this$props3.eventDelay,open=_this$props3.open;if(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open)||isMobile())return;var _this$state2=_this.state,status=_this$state2.status,positionWrapper=_this$state2.positionWrapper;/* istanbul ignore else */if(_this.event==='hover'){log({title:'mouseLeave',data:[{key:'originalEvent',value:event}],debug:_this.debug});if(!eventDelay){_this.toggle(STATUS.IDLE);}else if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1&&!positionWrapper&&!_this.eventDelayTimeout){_this.eventDelayTimeout=setTimeout(function(){delete _this.eventDelayTimeout;_this.toggle();},eventDelay*1000);}}});_this.state={currentPlacement:props.placement,needsUpdate:false,positionWrapper:props.wrapperOptions.position&&!!props.target,status:STATUS.INIT,statusWrapper:STATUS.INIT};_this._isMounted=false;_this.hasMounted=false;if(canUseDOM()){window.addEventListener('load',function(){if(_this.popper){_this.popper.instance.update();}if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}});}return _this;}_createClass(ReactFloater,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM())return;var positionWrapper=this.state.positionWrapper;var _this$props5=this.props,children=_this$props5.children,open=_this$props5.open,target=_this$props5.target;this._isMounted=true;log({title:'init',data:{hasChildren:!!children,hasTarget:!!target,isControlled:is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open),positionWrapper:positionWrapper,target:this.target,floater:this.floaterRef},debug:this.debug});if(!this.hasMounted){this.initPopper();this.hasMounted=true;}if(!children&&target&&!is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open));}},{key:\"componentDidUpdate\",value:function componentDidUpdate(prevProps,prevState){if(!canUseDOM())return;var _this$props6=this.props,autoOpen=_this$props6.autoOpen,open=_this$props6.open,target=_this$props6.target,wrapperOptions=_this$props6.wrapperOptions;var _treeChanges=(0,tree_changes__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(prevState,this.state),changedFrom=_treeChanges.changedFrom,changed=_treeChanges.changed;if(prevProps.open!==open){var forceStatus;// always follow `open` in controlled mode\nif(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"boolean\"](open)){forceStatus=open?STATUS.OPENING:STATUS.CLOSING;}this.toggle(forceStatus);}if(prevProps.wrapperOptions.position!==wrapperOptions.position||prevProps.target!==target){this.changeWrapperPosition(this.props);}if(changed('status',STATUS.IDLE)&&open){this.toggle(STATUS.OPEN);}else if(changedFrom('status',STATUS.INIT,STATUS.IDLE)&&autoOpen){this.toggle(STATUS.OPEN);}if(this.popper&&changed('status',STATUS.OPENING)){this.popper.instance.update();}if(this.floaterRef&&(changed('status',STATUS.OPENING)||changed('status',STATUS.CLOSING))){once(this.floaterRef,'transitionend',this.handleTransitionEnd);}if(changed('needsUpdate',true)){this.rebuildPopper();}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM())return;this._isMounted=false;if(this.popper){this.popper.instance.destroy();}if(this.wrapperPopper){this.wrapperPopper.instance.destroy();}}},{key:\"initPopper\",value:function initPopper(){var _this2=this;var target=arguments.length>0&&arguments[0]!==undefined?arguments[0]:this.target;var positionWrapper=this.state.positionWrapper;var _this$props7=this.props,disableFlip=_this$props7.disableFlip,getPopper=_this$props7.getPopper,hideArrow=_this$props7.hideArrow,offset=_this$props7.offset,placement=_this$props7.placement,wrapperOptions=_this$props7.wrapperOptions;var flipBehavior=placement==='top'||placement==='bottom'?'flip':['right','bottom-end','top-end','left','top-start','bottom-start'];/* istanbul ignore else */if(placement==='center'){this.setState({status:STATUS.IDLE});}else if(target&&this.floaterRef){var _this$options=this.options,arrow=_this$options.arrow,flip=_this$options.flip,offsetOptions=_this$options.offset,rest=_objectWithoutProperties(_this$options,_excluded);new popper_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](target,this.floaterRef,{placement:placement,modifiers:_objectSpread2({arrow:_objectSpread2({enabled:!hideArrow,element:this.arrowRef},arrow),flip:_objectSpread2({enabled:!disableFlip,behavior:flipBehavior},flip),offset:_objectSpread2({offset:\"0, \".concat(offset,\"px\")},offsetOptions)},rest),onCreate:function onCreate(data){var _this2$floaterRef;_this2.popper=data;if(!((_this2$floaterRef=_this2.floaterRef)!==null&&_this2$floaterRef!==void 0&&_this2$floaterRef.isConnected)){_this2.setState({needsUpdate:true});return;}getPopper(data,'floater');if(_this2._isMounted){_this2.setState({currentPlacement:data.placement,status:STATUS.IDLE});}if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}},onUpdate:function onUpdate(data){_this2.popper=data;var currentPlacement=_this2.state.currentPlacement;if(_this2._isMounted&&data.placement!==currentPlacement){_this2.setState({currentPlacement:data.placement});}}});}if(positionWrapper){var wrapperOffset=!is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"].undefined(wrapperOptions.offset)?wrapperOptions.offset:0;new popper_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](this.target,this.wrapperRef,{placement:wrapperOptions.placement||placement,modifiers:{arrow:{enabled:false},offset:{offset:\"0, \".concat(wrapperOffset,\"px\")},flip:{enabled:false}},onCreate:function onCreate(data){_this2.wrapperPopper=data;if(_this2._isMounted){_this2.setState({statusWrapper:STATUS.IDLE});}getPopper(data,'wrapper');if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}}});}}},{key:\"rebuildPopper\",value:function rebuildPopper(){var _this3=this;this.floaterRefInterval=setInterval(function(){var _this3$floaterRef;if((_this3$floaterRef=_this3.floaterRef)!==null&&_this3$floaterRef!==void 0&&_this3$floaterRef.isConnected){clearInterval(_this3.floaterRefInterval);_this3.setState({needsUpdate:false});_this3.initPopper();}},50);}},{key:\"changeWrapperPosition\",value:function changeWrapperPosition(_ref){var target=_ref.target,wrapperOptions=_ref.wrapperOptions;this.setState({positionWrapper:wrapperOptions.position&&!!target});}},{key:\"toggle\",value:function toggle(forceStatus){var status=this.state.status;var nextStatus=status===STATUS.OPEN?STATUS.CLOSING:STATUS.OPENING;if(!is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"].undefined(forceStatus)){nextStatus=forceStatus;}this.setState({status:nextStatus});}},{key:\"debug\",get:function get(){var debug=this.props.debug;return debug||canUseDOM()&&'ReactFloaterDebug'in window&&!!window.ReactFloaterDebug;}},{key:\"event\",get:function get(){var _this$props8=this.props,disableHoverToClick=_this$props8.disableHoverToClick,event=_this$props8.event;if(event==='hover'&&isMobile()&&!disableHoverToClick){return 'click';}return event;}},{key:\"options\",get:function get(){var options=this.props.options;return deepmerge__WEBPACK_IMPORTED_MODULE_1___default()(DEFAULTS,options||{});}},{key:\"styles\",get:function get(){var _this4=this;var _this$state3=this.state,status=_this$state3.status,positionWrapper=_this$state3.positionWrapper,statusWrapper=_this$state3.statusWrapper;var styles=this.props.styles;var nextStyles=deepmerge__WEBPACK_IMPORTED_MODULE_1___default()(getStyles(styles),styles);if(positionWrapper){var wrapperStyles;if(!([STATUS.IDLE].indexOf(status)!==-1)||!([STATUS.IDLE].indexOf(statusWrapper)!==-1)){wrapperStyles=nextStyles.wrapperPosition;}else {wrapperStyles=this.wrapperPopper.styles;}nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),wrapperStyles);}/* istanbul ignore else */if(this.target){var targetStyles=window.getComputedStyle(this.target);/* istanbul ignore else */if(this.wrapperStyles){nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);}else if(!(['relative','static'].indexOf(targetStyles.position)!==-1)){this.wrapperStyles={};if(!positionWrapper){POSITIONING_PROPS.forEach(function(d){_this4.wrapperStyles[d]=targetStyles[d];});nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);this.target.style.position='relative';this.target.style.top='auto';this.target.style.right='auto';this.target.style.bottom='auto';this.target.style.left='auto';}}}return nextStyles;}},{key:\"target\",get:function get(){if(!canUseDOM())return null;var target=this.props.target;if(target){if(is_lite__WEBPACK_IMPORTED_MODULE_3__[\"default\"].domElement(target)){return target;}return document.querySelector(target);}return this.childRef||this.wrapperRef;}},{key:\"render\",value:function render(){var _this$state4=this.state,currentPlacement=_this$state4.currentPlacement,positionWrapper=_this$state4.positionWrapper,status=_this$state4.status;var _this$props9=this.props,children=_this$props9.children,component=_this$props9.component,content=_this$props9.content,disableAnimation=_this$props9.disableAnimation,footer=_this$props9.footer,hideArrow=_this$props9.hideArrow,id=_this$props9.id,open=_this$props9.open,showCloseButton=_this$props9.showCloseButton,style=_this$props9.style,target=_this$props9.target,title=_this$props9.title;var wrapper=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ReactFloaterWrapper,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:style,styles:this.styles.wrapper},children);var output={};if(positionWrapper){output.wrapperInPortal=wrapper;}else {output.wrapperAsChildren=wrapper;}return/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\",null,/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ReactFloaterPortal,{hasChildren:!!children,id:id,placement:currentPlacement,setRef:this.setFloaterRef,target:target,zIndex:this.styles.options.zIndex},/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Floater,{component:component,content:content,disableAnimation:disableAnimation,footer:footer,handleClick:this.handleClick,hideArrow:hideArrow||currentPlacement==='center',open:open,placement:currentPlacement,positionWrapper:positionWrapper,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:showCloseButton,status:status,styles:this.styles,title:title}),output.wrapperInPortal),output.wrapperAsChildren);}}]);return ReactFloater;}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));_defineProperty(ReactFloater,\"propTypes\",{autoOpen:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),callback:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func),children:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),component:isRequiredIf(prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().element)]),function(props){return !props.content;}),content:isRequiredIf((prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),function(props){return !props.component;}),debug:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),disableAnimation:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),disableFlip:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),disableHoverToClick:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),event:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOf(['hover','click']),eventDelay:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number),footer:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),getPopper:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().func),hideArrow:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),id:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number)]),offset:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number),open:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),options:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),placement:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto','center']),showCloseButton:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),style:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),styles:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),target:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),(prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)]),title:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().node),wrapperOptions:prop_types__WEBPACK_IMPORTED_MODULE_4___default().shape({offset:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().number),placement:prop_types__WEBPACK_IMPORTED_MODULE_4___default().oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto']),position:(prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool)})});_defineProperty(ReactFloater,\"defaultProps\",{autoOpen:false,callback:noop,debug:false,disableAnimation:false,disableFlip:false,disableHoverToClick:false,event:'click',eventDelay:0.4,getPopper:noop,hideArrow:false,offset:15,placement:'bottom',showCloseButton:false,styles:{},target:null,wrapperOptions:{position:false}});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-floater@0.7.9_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-floater/es/index.js\n");

/***/ })

};
;