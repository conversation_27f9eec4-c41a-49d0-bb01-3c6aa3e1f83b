"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // Check if there are unsaved changes\n    const hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig,\n        handleLoadScene\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Update the config state for unsaved changes tracking\n            setDbSyncedConfig(config);\n            setWorkingConfig(config);\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                    // Update the synced config to reflect the new saved state\n                    setDbSyncedConfig(sceneConfig);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    // Update working config when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentConfig = getComparisonConfig();\n        setWorkingConfig(currentConfig);\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri,\n        setWorkingConfig,\n        getComparisonConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                    // Set this as the baseline for unsaved changes tracking\n                    setDbSyncedConfig(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1895,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction,\n                hasUnsavedChanges: hasUnsavedChanges(),\n                onRevertToSaved: revertToSaved\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1896,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1911,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1919,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1914,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1943,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1955,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1972,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1981,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2002,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2060,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1938,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2088,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2087,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2082,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2133,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2132,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2195,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2207,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2233,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2236,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2257,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2251,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2281,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2275,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2293,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2301,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2130,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2104,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2309,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"Zu6reylKdVxDBNMNNh5yXp3Tze8=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});