import {
  <PERSON>Composer,
  Bloom,
  // DepthOfField,
  Vignette,
  ToneMapping,
  Autofocus,
} from "@react-three/postprocessing";
import { ToneMappingMode } from "postprocessing";

export function PostProcessingEffects({ enabled, settings }) {
  if (!enabled) return null;

  return (
    <EffectComposer>
      {settings.autofocus.enabled && (
        <Autofocus bokehScale={settings.autofocus.bokehScale} />
      )}
      {settings.bloom.enabled && (
        <Bloom
          intensity={settings.bloom.intensity}
          luminanceThreshold={settings.bloom.threshold}
          luminanceSmoothing={settings.bloom.radius}
          mipmapBlur={true}
        />
      )}
      {/* {settings.dof.enabled && (
        <DepthOfField
          focusDistance={settings.dof.focusDistance}
          focalLength={settings.dof.aperture * 30}
          bokehScale={settings.dof.bokehScale}
        />
      )} */}
      {settings.vignette.enabled && (
        <Vignette
          offset={settings.vignette.offset}
          darkness={settings.vignette.darkness}
        />
      )}
      <ToneMapping mode={ToneMappingMode.ACES_FILMIC} />
    </EffectComposer>
  );
}
