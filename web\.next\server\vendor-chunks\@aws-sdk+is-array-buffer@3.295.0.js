"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+is-array-buffer@3.295.0";
exports.ids = ["vendor-chunks/@aws-sdk+is-array-buffer@3.295.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer)\n/* harmony export */ });\nconst isArrayBuffer = (arg) => (typeof ArrayBuffer === \"function\" && arg instanceof ArrayBuffer) ||\n    Object.prototype.toString.call(arg) === \"[object ArrayBuffer]\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytpcy1hcnJheS1idWZmZXJAMy4yOTUuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvaXMtYXJyYXktYnVmZmVyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsraXMtYXJyYXktYnVmZmVyQDMuMjk1LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2lzLWFycmF5LWJ1ZmZlci9kaXN0LWVzL2luZGV4LmpzPzhkMjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGlzQXJyYXlCdWZmZXIgPSAoYXJnKSA9PiAodHlwZW9mIEFycmF5QnVmZmVyID09PSBcImZ1bmN0aW9uXCIgJiYgYXJnIGluc3RhbmNlb2YgQXJyYXlCdWZmZXIpIHx8XG4gICAgT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGFyZykgPT09IFwiW29iamVjdCBBcnJheUJ1ZmZlcl1cIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer)\n/* harmony export */ });\nconst isArrayBuffer = (arg) => (typeof ArrayBuffer === \"function\" && arg instanceof ArrayBuffer) ||\n    Object.prototype.toString.call(arg) === \"[object ArrayBuffer]\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsraXMtYXJyYXktYnVmZmVyQDMuMjk1LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2lzLWFycmF5LWJ1ZmZlci9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2lzLWFycmF5LWJ1ZmZlckAzLjI5NS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9pcy1hcnJheS1idWZmZXIvZGlzdC1lcy9pbmRleC5qcz8xNDRmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc0FycmF5QnVmZmVyID0gKGFyZykgPT4gKHR5cGVvZiBBcnJheUJ1ZmZlciA9PT0gXCJmdW5jdGlvblwiICYmIGFyZyBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB8fFxuICAgIE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChhcmcpID09PSBcIltvYmplY3QgQXJyYXlCdWZmZXJdXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js\n");

/***/ })

};
;