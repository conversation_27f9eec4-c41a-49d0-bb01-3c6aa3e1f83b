"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti";
exports.ids = ["vendor-chunks/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/AccumulativeShadows.js":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/AccumulativeShadows.js ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccumulativeShadows: () => (/* binding */ AccumulativeShadows),\n/* harmony export */   RandomizedLight: () => (/* binding */ RandomizedLight),\n/* harmony export */   accumulativeContext: () => (/* binding */ accumulativeContext)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _shaderMaterial_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shaderMaterial.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shaderMaterial.js\");\n/* harmony import */ var _materials_DiscardMaterial_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../materials/DiscardMaterial.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/DiscardMaterial.js\");\n/* harmony import */ var _helpers_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/constants.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/constants.js\");\n\n\n\n\n\n\n\n\nfunction isLight(object) {\n  return object.isLight;\n}\nfunction isGeometry(object) {\n  return !!object.geometry;\n}\nconst accumulativeContext = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\nconst SoftShadowMaterial = /* @__PURE__ */(0,_shaderMaterial_js__WEBPACK_IMPORTED_MODULE_2__.shaderMaterial)({\n  color: /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_3__.Color(),\n  blend: 2.0,\n  alphaTest: 0.75,\n  opacity: 0,\n  map: null\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }`, `varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <${_helpers_constants_js__WEBPACK_IMPORTED_MODULE_4__.version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n   }`);\nconst AccumulativeShadows = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  children,\n  temporal,\n  frames = 40,\n  limit = Infinity,\n  blend = 20,\n  scale = 10,\n  opacity = 1,\n  alphaTest = 0.75,\n  color = 'black',\n  colorBlend = 2,\n  resolution = 1024,\n  toneMapped = true,\n  ...props\n}, forwardRef) => {\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.e)({\n    SoftShadowMaterial\n  });\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.D)(state => state.gl);\n  const scene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.D)(state => state.scene);\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.D)(state => state.camera);\n  const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.D)(state => state.invalidate);\n  const gPlane = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const gLights = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const [plm] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new ProgressiveLightMap(gl, scene, resolution));\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    plm.configure(gPlane.current);\n  }, []);\n  const api = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({\n    lights: new Map(),\n    temporal: !!temporal,\n    frames: Math.max(2, frames),\n    blend: Math.max(2, frames === Infinity ? blend : frames),\n    count: 0,\n    getMesh: () => gPlane.current,\n    reset: () => {\n      // Clear buffers, reset opacities, set frame count to 0\n      plm.clear();\n      const material = gPlane.current.material;\n      material.opacity = 0;\n      material.alphaTest = 0;\n      api.count = 0;\n    },\n    update: (frames = 1) => {\n      // Adapt the opacity-blend ratio to the number of frames\n      const material = gPlane.current.material;\n      if (!api.temporal) {\n        material.opacity = opacity;\n        material.alphaTest = alphaTest;\n      } else {\n        material.opacity = Math.min(opacity, material.opacity + opacity / api.blend);\n        material.alphaTest = Math.min(alphaTest, material.alphaTest + alphaTest / api.blend);\n      }\n\n      // Switch accumulative lights on\n      gLights.current.visible = true;\n      // Collect scene lights and meshes\n      plm.prepare();\n\n      // Update the lightmap and the accumulative lights\n      for (let i = 0; i < frames; i++) {\n        api.lights.forEach(light => light.update());\n        plm.update(camera, api.blend);\n      }\n      // Switch lights off\n      gLights.current.visible = false;\n      // Restore lights and meshes\n      plm.finish();\n    }\n  }), [plm, camera, scene, temporal, frames, blend, opacity, alphaTest]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    // Reset internals, buffers, ...\n    api.reset();\n    // Update lightmap\n    if (!api.temporal && api.frames !== Infinity) api.update(api.blend);\n  });\n\n  // Expose api, allow children to set itself as the main light source\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(forwardRef, () => api, [api]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_5__.F)(() => {\n    if ((api.temporal || api.frames === Infinity) && api.count < api.frames && api.count < limit) {\n      invalidate();\n      api.update();\n      api.count++;\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", props, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    traverse: () => null,\n    ref: gLights\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(accumulativeContext.Provider, {\n    value: api\n  }, children)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"mesh\", {\n    receiveShadow: true,\n    ref: gPlane,\n    scale: scale,\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"planeGeometry\", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"softShadowMaterial\", {\n    transparent: true,\n    depthWrite: false,\n    toneMapped: toneMapped,\n    color: color,\n    blend: colorBlend,\n    map: plm.progressiveLightMap2.texture\n  })));\n});\nconst RandomizedLight = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  castShadow = true,\n  bias = 0.001,\n  mapSize = 512,\n  size = 5,\n  near = 0.5,\n  far = 500,\n  frames = 1,\n  position = [0, 0, 0],\n  radius = 1,\n  amount = 8,\n  intensity = _helpers_constants_js__WEBPACK_IMPORTED_MODULE_4__.version >= 155 ? Math.PI : 1,\n  ambient = 0.5,\n  ...props\n}, forwardRef) => {\n  const gLights = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const length = new three__WEBPACK_IMPORTED_MODULE_3__.Vector3(...position).length();\n  const parent = react__WEBPACK_IMPORTED_MODULE_1__.useContext(accumulativeContext);\n  const update = react__WEBPACK_IMPORTED_MODULE_1__.useCallback(() => {\n    let light;\n    if (gLights.current) {\n      for (let l = 0; l < gLights.current.children.length; l++) {\n        light = gLights.current.children[l];\n        if (Math.random() > ambient) {\n          light.position.set(position[0] + three__WEBPACK_IMPORTED_MODULE_3__.MathUtils.randFloatSpread(radius), position[1] + three__WEBPACK_IMPORTED_MODULE_3__.MathUtils.randFloatSpread(radius), position[2] + three__WEBPACK_IMPORTED_MODULE_3__.MathUtils.randFloatSpread(radius));\n        } else {\n          let lambda = Math.acos(2 * Math.random() - 1) - Math.PI / 2.0;\n          let phi = 2 * Math.PI * Math.random();\n          light.position.set(Math.cos(lambda) * Math.cos(phi) * length, Math.abs(Math.cos(lambda) * Math.sin(phi) * length), Math.sin(lambda) * length);\n        }\n      }\n    }\n  }, [radius, ambient, length, ...position]);\n  const api = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({\n    update\n  }), [update]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(forwardRef, () => api, [api]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    var _parent$lights;\n    const group = gLights.current;\n    if (parent) (_parent$lights = parent.lights) == null || _parent$lights.set(group.uuid, api);\n    return () => {\n      var _parent$lights2;\n      return void (parent == null || (_parent$lights2 = parent.lights) == null ? void 0 : _parent$lights2.delete(group.uuid));\n    };\n  }, [parent, api]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: gLights\n  }, props), Array.from({\n    length: amount\n  }, (_, index) => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"directionalLight\", {\n    key: index,\n    castShadow: castShadow,\n    \"shadow-bias\": bias,\n    \"shadow-mapSize\": [mapSize, mapSize],\n    intensity: intensity / amount\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"orthographicCamera\", {\n    attach: \"shadow-camera\",\n    args: [-size, size, size, -size, near, far]\n  }))));\n});\n\n// Based on \"Progressive Light Map Accumulator\", by [zalo](https://github.com/zalo/)\nclass ProgressiveLightMap {\n  constructor(renderer, scene, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.scene = scene;\n    this.buffer1Active = false;\n    this.lights = [];\n    this.meshes = [];\n    this.object = null;\n    this.clearColor = new three__WEBPACK_IMPORTED_MODULE_3__.Color();\n    this.clearAlpha = 0;\n\n    // Create the Progressive LightMap Texture\n    const textureParams = {\n      type: three__WEBPACK_IMPORTED_MODULE_3__.HalfFloatType,\n      magFilter: three__WEBPACK_IMPORTED_MODULE_3__.NearestFilter,\n      minFilter: three__WEBPACK_IMPORTED_MODULE_3__.NearestFilter\n    };\n    this.progressiveLightMap1 = new three__WEBPACK_IMPORTED_MODULE_3__.WebGLRenderTarget(this.res, this.res, textureParams);\n    this.progressiveLightMap2 = new three__WEBPACK_IMPORTED_MODULE_3__.WebGLRenderTarget(this.res, this.res, textureParams);\n\n    // Inject some spicy new logic into a standard phong material\n    this.discardMat = new _materials_DiscardMaterial_js__WEBPACK_IMPORTED_MODULE_6__.DiscardMaterial();\n    this.targetMat = new three__WEBPACK_IMPORTED_MODULE_3__.MeshLambertMaterial({\n      fog: false\n    });\n    this.previousShadowMap = {\n      value: this.progressiveLightMap1.texture\n    };\n    this.averagingWindow = {\n      value: 100\n    };\n    this.targetMat.onBeforeCompile = shader => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader = 'varying vec2 vUv;\\n' + shader.vertexShader.slice(0, -1) + 'vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }';\n\n      // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n      const bodyStart = shader.fragmentShader.indexOf('void main() {');\n      shader.fragmentShader = 'varying vec2 vUv;\\n' + shader.fragmentShader.slice(0, bodyStart) + 'uniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' + shader.fragmentShader.slice(bodyStart - 1, -1) + `\\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }`;\n\n      // Set the Previous Frame's Texture Buffer and Averaging Window\n      shader.uniforms.previousShadowMap = this.previousShadowMap;\n      shader.uniforms.averagingWindow = this.averagingWindow;\n    };\n  }\n  clear() {\n    this.renderer.getClearColor(this.clearColor);\n    this.clearAlpha = this.renderer.getClearAlpha();\n    this.renderer.setClearColor('black', 1);\n    this.renderer.setRenderTarget(this.progressiveLightMap1);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(this.progressiveLightMap2);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(null);\n    this.renderer.setClearColor(this.clearColor, this.clearAlpha);\n    this.lights = [];\n    this.meshes = [];\n    this.scene.traverse(object => {\n      if (isGeometry(object)) {\n        this.meshes.push({\n          object,\n          material: object.material\n        });\n      } else if (isLight(object)) {\n        this.lights.push({\n          object,\n          intensity: object.intensity\n        });\n      }\n    });\n  }\n  prepare() {\n    this.lights.forEach(light => light.object.intensity = 0);\n    this.meshes.forEach(mesh => mesh.object.material = this.discardMat);\n  }\n  finish() {\n    this.lights.forEach(light => light.object.intensity = light.intensity);\n    this.meshes.forEach(mesh => mesh.object.material = mesh.material);\n  }\n  configure(object) {\n    this.object = object;\n  }\n  update(camera, blendWindow = 100) {\n    if (!this.object) return;\n    // Set each object's material to the UV Unwrapped Surface Mapping Version\n    this.averagingWindow.value = blendWindow;\n    this.object.material = this.targetMat;\n    // Ping-pong two surface buffers for reading/writing\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1;\n    // Render the object's surface maps\n    const oldBg = this.scene.background;\n    this.scene.background = null;\n    this.renderer.setRenderTarget(activeMap);\n    this.previousShadowMap.value = inactiveMap.texture;\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    this.renderer.setRenderTarget(null);\n    this.scene.background = oldBg;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/AccumulativeShadows.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Bounds.js":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Bounds.js ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bounds: () => (/* binding */ Bounds),\n/* harmony export */   useBounds: () => (/* binding */ useBounds)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\nvar AnimationState = /*#__PURE__*/function (AnimationState) {\n  AnimationState[AnimationState[\"NONE\"] = 0] = \"NONE\";\n  AnimationState[AnimationState[\"START\"] = 1] = \"START\";\n  AnimationState[AnimationState[\"ACTIVE\"] = 2] = \"ACTIVE\";\n  return AnimationState;\n}(AnimationState || {});\nconst isOrthographic = def => def && def.isOrthographicCamera;\nconst isBox3 = def => def && def.isBox3;\nconst interpolateFuncDefault = t => {\n  // Imitates the previously used THREE.MathUtils.damp\n  return 1 - Math.exp(-5 * t) + 0.007 * t;\n};\nconst context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nfunction Bounds({\n  children,\n  maxDuration = 1.0,\n  margin = 1.2,\n  observe,\n  fit,\n  clip,\n  interpolateFunc = interpolateFuncDefault,\n  onFit\n}) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const {\n    camera,\n    size,\n    invalidate\n  } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)();\n  const controls = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)(state => state.controls);\n  const onFitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onFit);\n  onFitRef.current = onFit;\n  const origin = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n    camPos: new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(),\n    camRot: new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion(),\n    camZoom: 1\n  });\n  const goal = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n    camPos: undefined,\n    camRot: undefined,\n    camZoom: undefined,\n    camUp: undefined,\n    target: undefined\n  });\n  const animationState = react__WEBPACK_IMPORTED_MODULE_0__.useRef(AnimationState.NONE);\n  const t = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0); // represent animation state from 0 to 1\n\n  const [box] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_2__.Box3());\n  const api = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    function getSize() {\n      const boxSize = box.getSize(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3());\n      const center = box.getCenter(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3());\n      const maxSize = Math.max(boxSize.x, boxSize.y, boxSize.z);\n      const fitHeightDistance = isOrthographic(camera) ? maxSize * 4 : maxSize / (2 * Math.atan(Math.PI * camera.fov / 360));\n      const fitWidthDistance = isOrthographic(camera) ? maxSize * 4 : fitHeightDistance / camera.aspect;\n      const distance = margin * Math.max(fitHeightDistance, fitWidthDistance);\n      return {\n        box,\n        size: boxSize,\n        center,\n        distance\n      };\n    }\n    return {\n      getSize,\n      refresh(object) {\n        if (isBox3(object)) box.copy(object);else {\n          const target = object || ref.current;\n          if (!target) return this;\n          target.updateWorldMatrix(true, true);\n          box.setFromObject(target);\n        }\n        if (box.isEmpty()) {\n          const max = camera.position.length() || 10;\n          box.setFromCenterAndSize(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(max, max, max));\n        }\n        origin.current.camPos.copy(camera.position);\n        origin.current.camRot.copy(camera.quaternion);\n        isOrthographic(camera) && (origin.current.camZoom = camera.zoom);\n        goal.current.camPos = undefined;\n        goal.current.camRot = undefined;\n        goal.current.camZoom = undefined;\n        goal.current.camUp = undefined;\n        goal.current.target = undefined;\n        return this;\n      },\n      reset() {\n        const {\n          center,\n          distance\n        } = getSize();\n        const direction = camera.position.clone().sub(center).normalize();\n        goal.current.camPos = center.clone().addScaledVector(direction, distance);\n        goal.current.target = center.clone();\n        const mCamRot = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4().lookAt(goal.current.camPos, goal.current.target, camera.up);\n        goal.current.camRot = new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      moveTo(position) {\n        goal.current.camPos = Array.isArray(position) ? new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(...position) : position.clone();\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      lookAt({\n        target,\n        up\n      }) {\n        goal.current.target = Array.isArray(target) ? new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(...target) : target.clone();\n        if (up) {\n          goal.current.camUp = Array.isArray(up) ? new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(...up) : up.clone();\n        } else {\n          goal.current.camUp = camera.up.clone();\n        }\n        const mCamRot = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4().lookAt(goal.current.camPos || camera.position, goal.current.target, goal.current.camUp);\n        goal.current.camRot = new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion().setFromRotationMatrix(mCamRot);\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        return this;\n      },\n      /**\n       * @deprecated Use moveTo and lookAt instead\n       */\n      to({\n        position,\n        target\n      }) {\n        return this.moveTo(position).lookAt({\n          target\n        });\n      },\n      fit() {\n        if (!isOrthographic(camera)) {\n          // For non-orthographic cameras, fit should behave exactly like reset\n          return this.reset();\n        }\n\n        // For orthographic cameras, fit should only modify the zoom value\n        let maxHeight = 0,\n          maxWidth = 0;\n        const vertices = [new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.min.x, box.min.y, box.min.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.min.x, box.max.y, box.min.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.min.x, box.min.y, box.max.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.min.x, box.max.y, box.max.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.max.x, box.max.y, box.max.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.max.x, box.max.y, box.min.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.max.x, box.min.y, box.max.z), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(box.max.x, box.min.y, box.min.z)];\n\n        // Transform the center and each corner to camera space\n        const pos = goal.current.camPos || camera.position;\n        const target = goal.current.target || (controls == null ? void 0 : controls.target);\n        const up = goal.current.camUp || camera.up;\n        const mCamWInv = target ? new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4().lookAt(pos, target, up).setPosition(pos).invert() : camera.matrixWorldInverse;\n        for (const v of vertices) {\n          v.applyMatrix4(mCamWInv);\n          maxHeight = Math.max(maxHeight, Math.abs(v.y));\n          maxWidth = Math.max(maxWidth, Math.abs(v.x));\n        }\n        maxHeight *= 2;\n        maxWidth *= 2;\n        const zoomForHeight = (camera.top - camera.bottom) / maxHeight;\n        const zoomForWidth = (camera.right - camera.left) / maxWidth;\n        goal.current.camZoom = Math.min(zoomForHeight, zoomForWidth) / margin;\n        animationState.current = AnimationState.START;\n        t.current = 0;\n        onFitRef.current && onFitRef.current(this.getSize());\n        return this;\n      },\n      clip() {\n        const {\n          distance\n        } = getSize();\n        camera.near = distance / 100;\n        camera.far = distance * 100;\n        camera.updateProjectionMatrix();\n        if (controls) {\n          controls.maxDistance = distance * 10;\n          controls.update();\n        }\n        invalidate();\n        return this;\n      }\n    };\n  }, [box, camera, controls, margin, invalidate]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    if (controls) {\n      // Try to prevent drag hijacking\n      const callback = () => {\n        if (controls && goal.current.target && animationState.current !== AnimationState.NONE) {\n          const front = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3().setFromMatrixColumn(camera.matrix, 2);\n          const d0 = origin.current.camPos.distanceTo(controls.target);\n          const d1 = (goal.current.camPos || origin.current.camPos).distanceTo(goal.current.target);\n          const d = (1 - t.current) * d0 + t.current * d1;\n          controls.target.copy(camera.position).addScaledVector(front, -d);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      };\n      controls.addEventListener('start', callback);\n      return () => controls.removeEventListener('start', callback);\n    }\n  }, [controls]);\n\n  // Scale pointer on window resize\n  const count = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    if (observe || count.current++ === 0) {\n      api.refresh();\n      if (fit) api.reset().fit();\n      if (clip) api.clip();\n    }\n  }, [size, clip, fit, observe, camera, controls]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.F)((state, delta) => {\n    // This [additional animation step START] is needed to guarantee that delta used in animation isn't absurdly high (2-3 seconds) which is actually possible if rendering happens on demand...\n    if (animationState.current === AnimationState.START) {\n      animationState.current = AnimationState.ACTIVE;\n      invalidate();\n    } else if (animationState.current === AnimationState.ACTIVE) {\n      t.current += delta / maxDuration;\n      if (t.current >= 1) {\n        goal.current.camPos && camera.position.copy(goal.current.camPos);\n        goal.current.camRot && camera.quaternion.copy(goal.current.camRot);\n        goal.current.camUp && camera.up.copy(goal.current.camUp);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n        if (controls && goal.current.target) {\n          controls.target.copy(goal.current.target);\n          controls.update();\n        }\n        animationState.current = AnimationState.NONE;\n      } else {\n        const k = interpolateFunc(t.current);\n        goal.current.camPos && camera.position.lerpVectors(origin.current.camPos, goal.current.camPos, k);\n        goal.current.camRot && camera.quaternion.slerpQuaternions(origin.current.camRot, goal.current.camRot, k);\n        goal.current.camUp && camera.up.set(0, 1, 0).applyQuaternion(camera.quaternion);\n        goal.current.camZoom && isOrthographic(camera) && (camera.zoom = (1 - k) * origin.current.camZoom + k * goal.current.camZoom);\n        camera.updateMatrixWorld();\n        camera.updateProjectionMatrix();\n      }\n      invalidate();\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(context.Provider, {\n    value: api\n  }, children));\n}\nfunction useBounds() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Bounds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Center.js":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Center.js ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Center: () => (/* binding */ Center)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\nconst Center = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function Center({\n  children,\n  disable,\n  disableX,\n  disableY,\n  disableZ,\n  left,\n  right,\n  top,\n  bottom,\n  front,\n  back,\n  onCentered,\n  precise = true,\n  cacheKey = 0,\n  ...props\n}, fRef) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const outer = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const inner = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    outer.current.matrixWorld.identity();\n    const box3 = new three__WEBPACK_IMPORTED_MODULE_2__.Box3().setFromObject(inner.current, precise);\n    const center = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\n    const sphere = new three__WEBPACK_IMPORTED_MODULE_2__.Sphere();\n    const width = box3.max.x - box3.min.x;\n    const height = box3.max.y - box3.min.y;\n    const depth = box3.max.z - box3.min.z;\n    box3.getCenter(center);\n    box3.getBoundingSphere(sphere);\n    const vAlign = top ? height / 2 : bottom ? -height / 2 : 0;\n    const hAlign = left ? -width / 2 : right ? width / 2 : 0;\n    const dAlign = front ? depth / 2 : back ? -depth / 2 : 0;\n    outer.current.position.set(disable || disableX ? 0 : -center.x + hAlign, disable || disableY ? 0 : -center.y + vAlign, disable || disableZ ? 0 : -center.z + dAlign);\n\n    // Only fire onCentered if the bounding box has changed\n    if (typeof onCentered !== 'undefined') {\n      onCentered({\n        parent: ref.current.parent,\n        container: ref.current,\n        width,\n        height,\n        depth,\n        boundingBox: box3,\n        boundingSphere: sphere,\n        center: center,\n        verticalAlignment: vAlign,\n        horizontalAlignment: hAlign,\n        depthAlignment: dAlign\n      });\n    }\n  }, [cacheKey, onCentered, top, left, front, disable, disableX, disableY, disableZ, precise, right, bottom, back]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref\n  }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    ref: outer\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    ref: inner\n  }, children)));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Center.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Clone.js":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Clone.js ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Clone: () => (/* binding */ Clone)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/utils/SkeletonUtils.js\");\n\n\n\n\n\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = {};\n  for (const key of keys) {\n    spread[key] = child[key];\n  }\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof three__WEBPACK_IMPORTED_MODULE_2__.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return three_stdlib__WEBPACK_IMPORTED_MODULE_3__.SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]);\n\n  // Deal with arrayed clones\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  }\n\n  // Singleton clones\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Element, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, spread, props, {\n    ref: forwardRef\n  }), object.children.map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Clone.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/ContactShadows.js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/ContactShadows.js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactShadows: () => (/* binding */ ContactShadows)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/shaders/HorizontalBlurShader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/shaders/VerticalBlurShader.js\");\n\n\n\n\n\n\nconst ContactShadows = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  scale = 10,\n  frames = Infinity,\n  opacity = 1,\n  width = 1,\n  height = 1,\n  blur = 1,\n  near = 0,\n  far = 10,\n  resolution = 512,\n  smooth = true,\n  color = '#000000',\n  depthWrite = false,\n  renderOrder,\n  ...props\n}, fref) => {\n  const ref = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const scene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.scene);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  const shadowCamera = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  width = width * (Array.isArray(scale) ? scale[0] : scale || 1);\n  height = height * (Array.isArray(scale) ? scale[1] : scale || 1);\n  const [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur] = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const renderTarget = new three__WEBPACK_IMPORTED_MODULE_3__.WebGLRenderTarget(resolution, resolution);\n    const renderTargetBlur = new three__WEBPACK_IMPORTED_MODULE_3__.WebGLRenderTarget(resolution, resolution);\n    renderTargetBlur.texture.generateMipmaps = renderTarget.texture.generateMipmaps = false;\n    const planeGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.PlaneGeometry(width, height).rotateX(Math.PI / 2);\n    const blurPlane = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(planeGeometry);\n    const depthMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshDepthMaterial();\n    depthMaterial.depthTest = depthMaterial.depthWrite = false;\n    depthMaterial.onBeforeCompile = shader => {\n      shader.uniforms = {\n        ...shader.uniforms,\n        ucolor: {\n          value: new three__WEBPACK_IMPORTED_MODULE_3__.Color(color)\n        }\n      };\n      shader.fragmentShader = shader.fragmentShader.replace(`void main() {`,\n      //\n      `uniform vec3 ucolor;\n           void main() {\n          `);\n      shader.fragmentShader = shader.fragmentShader.replace('vec4( vec3( 1.0 - fragCoordZ ), opacity );',\n      // Colorize the shadow, multiply by the falloff so that the center can remain darker\n      'vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );');\n    };\n    const horizontalBlurMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial(three_stdlib__WEBPACK_IMPORTED_MODULE_4__.HorizontalBlurShader);\n    const verticalBlurMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.VerticalBlurShader);\n    verticalBlurMaterial.depthTest = horizontalBlurMaterial.depthTest = false;\n    return [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur];\n  }, [resolution, width, height, scale, color]);\n  const blurShadows = blur => {\n    blurPlane.visible = true;\n    blurPlane.material = horizontalBlurMaterial;\n    horizontalBlurMaterial.uniforms.tDiffuse.value = renderTarget.texture;\n    horizontalBlurMaterial.uniforms.h.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTargetBlur);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.material = verticalBlurMaterial;\n    verticalBlurMaterial.uniforms.tDiffuse.value = renderTargetBlur.texture;\n    verticalBlurMaterial.uniforms.v.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTarget);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.visible = false;\n  };\n  let count = 0;\n  let initialBackground;\n  let initialOverrideMaterial;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(() => {\n    if (shadowCamera.current && (frames === Infinity || count < frames)) {\n      count++;\n      initialBackground = scene.background;\n      initialOverrideMaterial = scene.overrideMaterial;\n      ref.current.visible = false;\n      scene.background = null;\n      scene.overrideMaterial = depthMaterial;\n      gl.setRenderTarget(renderTarget);\n      gl.render(scene, shadowCamera.current);\n      blurShadows(blur);\n      if (smooth) blurShadows(blur * 0.4);\n      gl.setRenderTarget(null);\n      ref.current.visible = true;\n      scene.overrideMaterial = initialOverrideMaterial;\n      scene.background = initialBackground;\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    \"rotation-x\": Math.PI / 2\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"mesh\", {\n    renderOrder: renderOrder,\n    geometry: planeGeometry,\n    scale: [1, -1, 1],\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    map: renderTarget.texture,\n    opacity: opacity,\n    depthWrite: depthWrite\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"orthographicCamera\", {\n    ref: shadowCamera,\n    args: [-width / 2, width / 2, height / 2, -height / 2, near, far]\n  }));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/ContactShadows.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: () => (/* binding */ Environment),\n/* harmony export */   EnvironmentCube: () => (/* binding */ EnvironmentCube),\n/* harmony export */   EnvironmentMap: () => (/* binding */ EnvironmentMap),\n/* harmony export */   EnvironmentPortal: () => (/* binding */ EnvironmentPortal)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/objects/GroundProjectedEnv.js\");\n/* harmony import */ var _useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useEnvironment.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/useEnvironment.js\");\n\n\n\n\n\n\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.k)(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.k)(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(rest);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.scene);\n  const camera = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const [virtualScene] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Scene());\n  const fbo = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const fbo = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = three__WEBPACK_IMPORTED_MODULE_4__.HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.h)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(props);\n  const texture = props.map || textureDefault;\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.e)({\n    GroundProjectedEnvImpl: three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GroundProjectedEnv\n  }), []);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    map: texture\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentPortal, props) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, props);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Fbo.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Fbo.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fbo: () => (/* binding */ Fbo),\n/* harmony export */   useFBO: () => (/* binding */ useFBO)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\n// TODO: consume this from three >r154 when SemVer allows\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)(state => state.size);\n  const viewport = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const target = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const target = new three__WEBPACK_IMPORTED_MODULE_2__.WebGLRenderTarget(_width, _height, {\n      minFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n      magFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n      type: three__WEBPACK_IMPORTED_MODULE_2__.HalfFloatType,\n      ...targetSettings\n    });\n    if (depth) {\n      target.depthTexture = new three__WEBPACK_IMPORTED_MODULE_2__.DepthTexture(_width, _height, three__WEBPACK_IMPORTED_MODULE_2__.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\nconst Fbo = ({\n  children,\n  width,\n  height,\n  ...settings\n}) => {\n  const target = useFBO(width, height, settings);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children == null ? void 0 : children(target));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9jb3JlL0Ziby5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0I7QUFDQTtBQUNlOztBQUU5Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHFEQUFRO0FBQ3ZCLG1CQUFtQixxREFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixpQkFBaUIsMENBQWE7QUFDOUIsdUJBQXVCLG9EQUF1QjtBQUM5QyxpQkFBaUIsK0NBQWtCO0FBQ25DLGlCQUFpQiwrQ0FBa0I7QUFDbkMsWUFBWSxnREFBbUI7QUFDL0I7QUFDQSxLQUFLO0FBQ0w7QUFDQSxnQ0FBZ0MsK0NBQWtCLGtCQUFrQiw0Q0FBZTtBQUNuRjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxrREFBcUI7QUFDdkI7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLDJDQUFjO0FBQ3hEOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmVhY3QtdGhyZWUrZHJlaUA5LjEyMi4wX0ByZWFjdC10aHJlZStmaWJlckA4LjE4LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjNfYzU3NnZnbWVlNGh4bXIzYWptcjRkZ2N6dGkvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL2NvcmUvRmJvLmpzP2Y1MTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0ICogYXMgVEhSRUUgZnJvbSAndGhyZWUnO1xuaW1wb3J0IHsgdXNlVGhyZWUgfSBmcm9tICdAcmVhY3QtdGhyZWUvZmliZXInO1xuXG4vLyBUT0RPOiBjb25zdW1lIHRoaXMgZnJvbSB0aHJlZSA+cjE1NCB3aGVuIFNlbVZlciBhbGxvd3NcblxuLy8g8J+RhyB1bmNvbW1lbnQgd2hlbiBUUyB2ZXJzaW9uIHN1cHBvcnRzIGZ1bmN0aW9uIG92ZXJsb2Fkc1xuLy8gZXhwb3J0IGZ1bmN0aW9uIHVzZUZCTyhzZXR0aW5ncz86IEZCT1NldHRpbmdzKVxuZnVuY3Rpb24gdXNlRkJPKC8qKiBXaWR0aCBpbiBwaXhlbHMsIG9yIHNldHRpbmdzICh3aWxsIHJlbmRlciBmdWxsc2NyZWVuIGJ5IGRlZmF1bHQpICovXG53aWR0aCwgLyoqIEhlaWdodCBpbiBwaXhlbHMgKi9cbmhlaWdodCwgLyoqU2V0dGluZ3MgKi9cbnNldHRpbmdzKSB7XG4gIGNvbnN0IHNpemUgPSB1c2VUaHJlZShzdGF0ZSA9PiBzdGF0ZS5zaXplKTtcbiAgY29uc3Qgdmlld3BvcnQgPSB1c2VUaHJlZShzdGF0ZSA9PiBzdGF0ZS52aWV3cG9ydCk7XG4gIGNvbnN0IF93aWR0aCA9IHR5cGVvZiB3aWR0aCA9PT0gJ251bWJlcicgPyB3aWR0aCA6IHNpemUud2lkdGggKiB2aWV3cG9ydC5kcHI7XG4gIGNvbnN0IF9oZWlnaHQgPSB0eXBlb2YgaGVpZ2h0ID09PSAnbnVtYmVyJyA/IGhlaWdodCA6IHNpemUuaGVpZ2h0ICogdmlld3BvcnQuZHByO1xuICBjb25zdCBfc2V0dGluZ3MgPSAodHlwZW9mIHdpZHRoID09PSAnbnVtYmVyJyA/IHNldHRpbmdzIDogd2lkdGgpIHx8IHt9O1xuICBjb25zdCB7XG4gICAgc2FtcGxlcyA9IDAsXG4gICAgZGVwdGgsXG4gICAgLi4udGFyZ2V0U2V0dGluZ3NcbiAgfSA9IF9zZXR0aW5ncztcbiAgY29uc3QgdGFyZ2V0ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgdGFyZ2V0ID0gbmV3IFRIUkVFLldlYkdMUmVuZGVyVGFyZ2V0KF93aWR0aCwgX2hlaWdodCwge1xuICAgICAgbWluRmlsdGVyOiBUSFJFRS5MaW5lYXJGaWx0ZXIsXG4gICAgICBtYWdGaWx0ZXI6IFRIUkVFLkxpbmVhckZpbHRlcixcbiAgICAgIHR5cGU6IFRIUkVFLkhhbGZGbG9hdFR5cGUsXG4gICAgICAuLi50YXJnZXRTZXR0aW5nc1xuICAgIH0pO1xuICAgIGlmIChkZXB0aCkge1xuICAgICAgdGFyZ2V0LmRlcHRoVGV4dHVyZSA9IG5ldyBUSFJFRS5EZXB0aFRleHR1cmUoX3dpZHRoLCBfaGVpZ2h0LCBUSFJFRS5GbG9hdFR5cGUpO1xuICAgIH1cbiAgICB0YXJnZXQuc2FtcGxlcyA9IHNhbXBsZXM7XG4gICAgcmV0dXJuIHRhcmdldDtcbiAgfSwgW10pO1xuICBSZWFjdC51c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIHRhcmdldC5zZXRTaXplKF93aWR0aCwgX2hlaWdodCk7XG4gICAgaWYgKHNhbXBsZXMpIHRhcmdldC5zYW1wbGVzID0gc2FtcGxlcztcbiAgfSwgW3NhbXBsZXMsIHRhcmdldCwgX3dpZHRoLCBfaGVpZ2h0XSk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHRhcmdldC5kaXNwb3NlKCk7XG4gIH0sIFtdKTtcbiAgcmV0dXJuIHRhcmdldDtcbn1cbmNvbnN0IEZibyA9ICh7XG4gIGNoaWxkcmVuLFxuICB3aWR0aCxcbiAgaGVpZ2h0LFxuICAuLi5zZXR0aW5nc1xufSkgPT4ge1xuICBjb25zdCB0YXJnZXQgPSB1c2VGQk8od2lkdGgsIGhlaWdodCwgc2V0dGluZ3MpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUmVhY3QuRnJhZ21lbnQsIG51bGwsIGNoaWxkcmVuID09IG51bGwgPyB2b2lkIDAgOiBjaGlsZHJlbih0YXJnZXQpKTtcbn07XG5cbmV4cG9ydCB7IEZibywgdXNlRkJPIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Fbo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Float.js":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Float.js ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Float: () => (/* binding */ Float)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n\n\nconst Float = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  autoInvalidate = false,\n  ...props\n}, forwardRef) => {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(forwardRef, () => ref.current, []);\n  const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(Math.random() * 10000);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.F)(state => {\n    var _floatingRange$, _floatingRange$2;\n    if (!enabled || speed === 0) return;\n    if (autoInvalidate) state.invalidate();\n    const t = offset.current + state.clock.elapsedTime;\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = three__WEBPACK_IMPORTED_MODULE_2__.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", props, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", {\n    ref: ref,\n    matrixAutoUpdate: false\n  }, children));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9jb3JlL0Zsb2F0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQStCO0FBQ2U7QUFDZjs7QUFFL0IsNkJBQTZCLDZDQUFnQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGNBQWMseUNBQVk7QUFDMUIsRUFBRSxzREFBeUI7QUFDM0IsaUJBQWlCLHlDQUFZO0FBQzdCLEVBQUUscURBQVE7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDRDQUFlO0FBQy9CO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLGdEQUFtQiw4QkFBOEIsZ0RBQW1CO0FBQzFGO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsQ0FBQzs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9jb3JlL0Zsb2F0LmpzPzYyMDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRnJhbWUgfSBmcm9tICdAcmVhY3QtdGhyZWUvZmliZXInO1xuaW1wb3J0ICogYXMgVEhSRUUgZnJvbSAndGhyZWUnO1xuXG5jb25zdCBGbG9hdCA9IC8qIEBfX1BVUkVfXyAqL1JlYWN0LmZvcndhcmRSZWYoKHtcbiAgY2hpbGRyZW4sXG4gIGVuYWJsZWQgPSB0cnVlLFxuICBzcGVlZCA9IDEsXG4gIHJvdGF0aW9uSW50ZW5zaXR5ID0gMSxcbiAgZmxvYXRJbnRlbnNpdHkgPSAxLFxuICBmbG9hdGluZ1JhbmdlID0gWy0wLjEsIDAuMV0sXG4gIGF1dG9JbnZhbGlkYXRlID0gZmFsc2UsXG4gIC4uLnByb3BzXG59LCBmb3J3YXJkUmVmKSA9PiB7XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgUmVhY3QudXNlSW1wZXJhdGl2ZUhhbmRsZShmb3J3YXJkUmVmLCAoKSA9PiByZWYuY3VycmVudCwgW10pO1xuICBjb25zdCBvZmZzZXQgPSBSZWFjdC51c2VSZWYoTWF0aC5yYW5kb20oKSAqIDEwMDAwKTtcbiAgdXNlRnJhbWUoc3RhdGUgPT4ge1xuICAgIHZhciBfZmxvYXRpbmdSYW5nZSQsIF9mbG9hdGluZ1JhbmdlJDI7XG4gICAgaWYgKCFlbmFibGVkIHx8IHNwZWVkID09PSAwKSByZXR1cm47XG4gICAgaWYgKGF1dG9JbnZhbGlkYXRlKSBzdGF0ZS5pbnZhbGlkYXRlKCk7XG4gICAgY29uc3QgdCA9IG9mZnNldC5jdXJyZW50ICsgc3RhdGUuY2xvY2suZWxhcHNlZFRpbWU7XG4gICAgcmVmLmN1cnJlbnQucm90YXRpb24ueCA9IE1hdGguY29zKHQgLyA0ICogc3BlZWQpIC8gOCAqIHJvdGF0aW9uSW50ZW5zaXR5O1xuICAgIHJlZi5jdXJyZW50LnJvdGF0aW9uLnkgPSBNYXRoLnNpbih0IC8gNCAqIHNwZWVkKSAvIDggKiByb3RhdGlvbkludGVuc2l0eTtcbiAgICByZWYuY3VycmVudC5yb3RhdGlvbi56ID0gTWF0aC5zaW4odCAvIDQgKiBzcGVlZCkgLyAyMCAqIHJvdGF0aW9uSW50ZW5zaXR5O1xuICAgIGxldCB5UG9zaXRpb24gPSBNYXRoLnNpbih0IC8gNCAqIHNwZWVkKSAvIDEwO1xuICAgIHlQb3NpdGlvbiA9IFRIUkVFLk1hdGhVdGlscy5tYXBMaW5lYXIoeVBvc2l0aW9uLCAtMC4xLCAwLjEsIChfZmxvYXRpbmdSYW5nZSQgPSBmbG9hdGluZ1JhbmdlID09IG51bGwgPyB2b2lkIDAgOiBmbG9hdGluZ1JhbmdlWzBdKSAhPT0gbnVsbCAmJiBfZmxvYXRpbmdSYW5nZSQgIT09IHZvaWQgMCA/IF9mbG9hdGluZ1JhbmdlJCA6IC0wLjEsIChfZmxvYXRpbmdSYW5nZSQyID0gZmxvYXRpbmdSYW5nZSA9PSBudWxsID8gdm9pZCAwIDogZmxvYXRpbmdSYW5nZVsxXSkgIT09IG51bGwgJiYgX2Zsb2F0aW5nUmFuZ2UkMiAhPT0gdm9pZCAwID8gX2Zsb2F0aW5nUmFuZ2UkMiA6IDAuMSk7XG4gICAgcmVmLmN1cnJlbnQucG9zaXRpb24ueSA9IHlQb3NpdGlvbiAqIGZsb2F0SW50ZW5zaXR5O1xuICAgIHJlZi5jdXJyZW50LnVwZGF0ZU1hdHJpeCgpO1xuICB9KTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZ3JvdXBcIiwgcHJvcHMsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZ3JvdXBcIiwge1xuICAgIHJlZjogcmVmLFxuICAgIG1hdHJpeEF1dG9VcGRhdGU6IGZhbHNlXG4gIH0sIGNoaWxkcmVuKSk7XG59KTtcblxuZXhwb3J0IHsgRmxvYXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Float.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GizmoHelper: () => (/* binding */ GizmoHelper),\n/* harmony export */   useGizmoContext: () => (/* binding */ useGizmoContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _OrthographicCamera_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OrthographicCamera.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrthographicCamera.js\");\n/* harmony import */ var _Hud_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Hud.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Hud.js\");\n\n\n\n\n\n\nconst Context = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\nconst useGizmoContext = () => {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n};\nconst turnRate = 2 * Math.PI; // turn rate in angles per second\nconst dummy = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Object3D();\nconst matrix = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Matrix4();\nconst [q1, q2] = [/* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Quaternion(), /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Quaternion()];\nconst target = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Vector3();\nconst targetPosition = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_1__.Vector3();\nconst isOrbitControls = controls => {\n  return 'minPolarAngle' in controls;\n};\nconst isCameraControls = controls => {\n  return 'getTarget' in controls;\n};\nconst GizmoHelper = ({\n  alignment = 'bottom-right',\n  margin = [80, 80],\n  renderPriority = 1,\n  onUpdate,\n  onTarget,\n  children\n}) => {\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.size);\n  const mainCamera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.camera);\n  // @ts-ignore\n  const defaultControls = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.controls);\n  const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.invalidate);\n  const gizmoRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const virtualCam = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const animating = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const radius = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const focusPoint = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new three__WEBPACK_IMPORTED_MODULE_1__.Vector3(0, 0, 0));\n  const defaultUp = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new three__WEBPACK_IMPORTED_MODULE_1__.Vector3(0, 0, 0));\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    defaultUp.current.copy(mainCamera.up);\n    dummy.up.copy(mainCamera.up);\n  }, [mainCamera]);\n  const tweenCamera = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(direction => {\n    animating.current = true;\n    if (defaultControls || onTarget) {\n      focusPoint.current = (onTarget == null ? void 0 : onTarget()) || (isCameraControls(defaultControls) ? defaultControls.getTarget(focusPoint.current) : defaultControls == null ? void 0 : defaultControls.target);\n    }\n    radius.current = mainCamera.position.distanceTo(target);\n\n    // Rotate from current camera orientation\n    q1.copy(mainCamera.quaternion);\n\n    // To new current camera orientation\n    targetPosition.copy(direction).multiplyScalar(radius.current).add(target);\n    dummy.lookAt(targetPosition);\n    q2.copy(dummy.quaternion);\n    invalidate();\n  }, [defaultControls, mainCamera, onTarget, invalidate]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)((_, delta) => {\n    if (virtualCam.current && gizmoRef.current) {\n      var _gizmoRef$current;\n      // Animate step\n      if (animating.current) {\n        if (q1.angleTo(q2) < 0.01) {\n          animating.current = false;\n          // Orbit controls uses UP vector as the orbit axes,\n          // so we need to reset it after the animation is done\n          // moving it around for the controls to work correctly\n          if (isOrbitControls(defaultControls)) {\n            mainCamera.up.copy(defaultUp.current);\n          }\n        } else {\n          const step = delta * turnRate;\n          // animate position by doing a slerp and then scaling the position on the unit sphere\n          q1.rotateTowards(q2, step);\n          // animate orientation\n          mainCamera.position.set(0, 0, 1).applyQuaternion(q1).multiplyScalar(radius.current).add(focusPoint.current);\n          mainCamera.up.set(0, 1, 0).applyQuaternion(q1).normalize();\n          mainCamera.quaternion.copy(q1);\n          if (isCameraControls(defaultControls)) defaultControls.setPosition(mainCamera.position.x, mainCamera.position.y, mainCamera.position.z);\n          if (onUpdate) onUpdate();else if (defaultControls) defaultControls.update(delta);\n          invalidate();\n        }\n      }\n\n      // Sync Gizmo with main camera orientation\n      matrix.copy(mainCamera.matrix).invert();\n      (_gizmoRef$current = gizmoRef.current) == null || _gizmoRef$current.quaternion.setFromRotationMatrix(matrix);\n    }\n  });\n  const gizmoHelperContext = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    tweenCamera\n  }), [tweenCamera]);\n\n  // Position gizmo component within scene\n  const [marginX, marginY] = margin;\n  const x = alignment.endsWith('-center') ? 0 : alignment.endsWith('-left') ? -size.width / 2 + marginX : size.width / 2 - marginX;\n  const y = alignment.startsWith('center-') ? 0 : alignment.startsWith('top-') ? size.height / 2 - marginY : -size.height / 2 + marginY;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Hud_js__WEBPACK_IMPORTED_MODULE_3__.Hud, {\n    renderPriority: renderPriority\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Context.Provider, {\n    value: gizmoHelperContext\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_OrthographicCamera_js__WEBPACK_IMPORTED_MODULE_4__.OrthographicCamera, {\n    makeDefault: true,\n    ref: virtualCam,\n    position: [0, 0, 200]\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", {\n    ref: gizmoRef,\n    position: [x, y, 0]\n  }, children)));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GizmoViewport: () => (/* binding */ GizmoViewport)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _GizmoHelper_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GizmoHelper.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n\n\n\n\n\n\nfunction Axis({\n  scale = [0.8, 0.05, 0.05],\n  color,\n  rotation\n}) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    rotation: rotation\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"mesh\", {\n    position: [0.4, 0, 0]\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"boxGeometry\", {\n    args: scale\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"meshBasicMaterial\", {\n    color: color,\n    toneMapped: false\n  })));\n}\nfunction AxisHead({\n  onClick,\n  font,\n  disabled,\n  arcStyle,\n  label,\n  labelColor,\n  axisHeadScale = 1,\n  ...props\n}) {\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  const texture = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 64;\n    canvas.height = 64;\n    const context = canvas.getContext('2d');\n    context.beginPath();\n    context.arc(32, 32, 16, 0, 2 * Math.PI);\n    context.closePath();\n    context.fillStyle = arcStyle;\n    context.fill();\n    if (label) {\n      context.font = font;\n      context.textAlign = 'center';\n      context.fillStyle = labelColor;\n      context.fillText(label, 32, 41);\n    }\n    return new three__WEBPACK_IMPORTED_MODULE_3__.CanvasTexture(canvas);\n  }, [arcStyle, label, labelColor, font]);\n  const [active, setActive] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n  const scale = (label ? 1 : 0.75) * (active ? 1.2 : 1) * axisHeadScale;\n  const handlePointerOver = e => {\n    e.stopPropagation();\n    setActive(true);\n  };\n  const handlePointerOut = e => {\n    e.stopPropagation();\n    setActive(false);\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"sprite\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    scale: scale,\n    onPointerOver: !disabled ? handlePointerOver : undefined,\n    onPointerOut: !disabled ? onClick || handlePointerOut : undefined\n  }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"spriteMaterial\", {\n    map: texture,\n    \"map-anisotropy\": gl.capabilities.getMaxAnisotropy() || 1,\n    alphaTest: 0.3,\n    opacity: label ? 1 : 0.75,\n    toneMapped: false\n  }));\n}\nconst GizmoViewport = ({\n  hideNegativeAxes,\n  hideAxisHeads,\n  disabled,\n  font = '18px Inter var, Arial, sans-serif',\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  axisHeadScale = 1,\n  axisScale,\n  labels = ['X', 'Y', 'Z'],\n  labelColor = '#000',\n  onClick,\n  ...props\n}) => {\n  const [colorX, colorY, colorZ] = axisColors;\n  const {\n    tweenCamera\n  } = (0,_GizmoHelper_js__WEBPACK_IMPORTED_MODULE_4__.useGizmoContext)();\n  const axisHeadProps = {\n    font,\n    disabled,\n    labelColor,\n    onClick,\n    axisHeadScale,\n    onPointerDown: !disabled ? e => {\n      tweenCamera(e.object.position);\n      e.stopPropagation();\n    } : undefined\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    scale: 40\n  }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Axis, {\n    color: colorX,\n    rotation: [0, 0, 0],\n    scale: axisScale\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Axis, {\n    color: colorY,\n    rotation: [0, 0, Math.PI / 2],\n    scale: axisScale\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Axis, {\n    color: colorZ,\n    rotation: [0, -Math.PI / 2, 0],\n    scale: axisScale\n  }), !hideAxisHeads && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorX,\n    position: [1, 0, 0],\n    label: labels[0]\n  }, axisHeadProps)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorY,\n    position: [0, 1, 0],\n    label: labels[1]\n  }, axisHeadProps)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorZ,\n    position: [0, 0, 1],\n    label: labels[2]\n  }, axisHeadProps)), !hideNegativeAxes && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorX,\n    position: [-1, 0, 0]\n  }, axisHeadProps)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorY,\n    position: [0, -1, 0]\n  }, axisHeadProps)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(AxisHead, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    arcStyle: colorZ,\n    position: [0, 0, -1]\n  }, axisHeadProps)))));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Gltf.js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Gltf.js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Gltf: () => (/* binding */ Gltf),\n/* harmony export */   useGLTF: () => (/* binding */ useGLTF)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/loaders/DRACOLoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/libs/MeshoptDecoder.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/loaders/GLTFLoader.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _Clone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Clone.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Clone.js\");\n\n\n\n\n\n\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new three_stdlib__WEBPACK_IMPORTED_MODULE_2__.DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder === 'function' ? (0,three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder)() : three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder);\n    }\n  };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader) => (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.H)(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader) => _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.H.preload(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = path => _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.H.clear(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n//\n\nconst Gltf = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  src,\n  useDraco,\n  useMeshOpt,\n  extendLoader,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Clone_js__WEBPACK_IMPORTED_MODULE_6__.Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Gltf.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Hud.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Hud.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hud: () => (/* binding */ Hud)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\nfunction RenderHud({\n  defaultScene,\n  defaultCamera,\n  renderPriority = 1\n}) {\n  const {\n    gl,\n    scene,\n    camera\n  } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)();\n  let oldCLear;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.F)(() => {\n    oldCLear = gl.autoClear;\n    if (renderPriority === 1) {\n      // Clear scene and render the default scene\n      gl.autoClear = true;\n      gl.render(defaultScene, defaultCamera);\n    }\n    // Disable cleaning and render the portal with its own camera\n    gl.autoClear = false;\n    gl.clearDepth();\n    gl.render(scene, camera);\n    // Restore default\n    gl.autoClear = oldCLear;\n  }, renderPriority);\n  // Without an element that receives pointer events state.pointer will always be 0/0\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", {\n    onPointerOver: () => null\n  });\n}\nfunction Hud({\n  children,\n  renderPriority = 1\n}) {\n  const {\n    scene: defaultScene,\n    camera: defaultCamera\n  } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)();\n  const [hudScene] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_2__.Scene());\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.h)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RenderHud, {\n    defaultScene: defaultScene,\n    defaultCamera: defaultCamera,\n    renderPriority: renderPriority\n  })), hudScene, {\n    events: {\n      priority: renderPriority + 1\n    }\n  }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Hud.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeshReflectorMaterial: () => (/* binding */ MeshReflectorMaterial)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _materials_BlurPass_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../materials/BlurPass.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/BlurPass.js\");\n/* harmony import */ var _materials_MeshReflectorMaterial_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../materials/MeshReflectorMaterial.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/MeshReflectorMaterial.js\");\n\n\n\n\n\n\n\nconst MeshReflectorMaterial = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 1,\n  resolution = 256,\n  blur = [0, 0],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  reflectorOffset = 0,\n  ...props\n}, ref) => {\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.e)({\n    MeshReflectorMaterialImpl: _materials_MeshReflectorMaterial_js__WEBPACK_IMPORTED_MODULE_3__.MeshReflectorMaterial\n  });\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    gl\n  }) => gl);\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    camera\n  }) => camera);\n  const scene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const materialRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => materialRef.current, []);\n  const [reflectorPlane] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Plane());\n  const [normal] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3());\n  const [reflectorWorldPosition] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3());\n  const [cameraWorldPosition] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3());\n  const [rotationMatrix] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Matrix4());\n  const [lookAtPosition] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3(0, 0, -1));\n  const [clipPlane] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector4());\n  const [view] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3());\n  const [target] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector3());\n  const [q] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Vector4());\n  const [textureMatrix] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Matrix4());\n  const [virtualCamera] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.PerspectiveCamera());\n  const beforeRender = react__WEBPACK_IMPORTED_MODULE_1__.useCallback(() => {\n    var _materialRef$current;\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current = materialRef.current) == null ? void 0 : _materialRef$current.__r3f.parent);\n    if (!parent) return;\n    reflectorWorldPosition.setFromMatrixPosition(parent.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(parent.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    reflectorWorldPosition.addScaledVector(normal, reflectorOffset);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition);\n    // Avoid rendering when reflector is facing away\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix);\n    // Update the texture matrix\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(parent.matrixWorld);\n    // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14];\n    // Calculate the scaled plane vector\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q));\n    // Replacing the third row of the projection matrix\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, [camera, reflectorOffset]);\n  const [fbo1, fbo2, blurpass, reflectorProps] = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const parameters = {\n      minFilter: three__WEBPACK_IMPORTED_MODULE_4__.LinearFilter,\n      magFilter: three__WEBPACK_IMPORTED_MODULE_4__.LinearFilter,\n      type: three__WEBPACK_IMPORTED_MODULE_4__.HalfFloatType\n    };\n    const fbo1 = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new three__WEBPACK_IMPORTED_MODULE_4__.DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = three__WEBPACK_IMPORTED_MODULE_4__.DepthFormat;\n    fbo1.depthTexture.type = three__WEBPACK_IMPORTED_MODULE_4__.UnsignedShortType;\n    const fbo2 = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new _materials_BlurPass_js__WEBPACK_IMPORTED_MODULE_5__.BlurPass({\n      gl,\n      resolution,\n      width: blur[0],\n      height: blur[1],\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blur, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, distortion, distortionMap, mixContrast]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(() => {\n    var _materialRef$current2;\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current2 = materialRef.current) == null ? void 0 : _materialRef$current2.__r3f.parent);\n    if (!parent) return;\n    parent.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    parent.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"meshReflectorMaterialImpl\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    attach: \"material\"\n    // Defines can't be updated dynamically, so we need to recreate the material\n    ,\n    key: 'key' + reflectorProps['defines-USE_BLUR'] + reflectorProps['defines-USE_DEPTH'] + reflectorProps['defines-USE_DISTORTION'],\n    ref: materialRef\n  }, reflectorProps, props));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrbitControls: () => (/* binding */ OrbitControls)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/controls/OrbitControls.js\");\n\n\n\n\n\nconst OrbitControls = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.invalidate);\n  const defaultCamera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.camera);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  const events = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.events);\n  const setEvents = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.setEvents);\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.set);\n  const get = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.get);\n  const performance = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => new three_stdlib__WEBPACK_IMPORTED_MODULE_3__.OrbitControls(explCamera), [explCamera]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrthographicCamera.js":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrthographicCamera.js ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrthographicCamera: () => (/* binding */ OrthographicCamera)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _Fbo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Fbo.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Fbo.js\");\n\n\n\n\n\nconst isFunction = node => typeof node === 'function';\nconst OrthographicCamera = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  children,\n  makeDefault,\n  ...props\n}, ref) => {\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    set\n  }) => set);\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    camera\n  }) => camera);\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    size\n  }) => size);\n  const cameraRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const fbo = (0,_Fbo_js__WEBPACK_IMPORTED_MODULE_3__.useFBO)(resolution);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.updateProjectionMatrix();\n    }\n  }, [size, props]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"orthographicCamera\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    left: size.width / -2,\n    right: size.width / 2,\n    top: size.height / 2,\n    bottom: size.height / -2,\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrthographicCamera.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/PerspectiveCamera.js":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/PerspectiveCamera.js ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerspectiveCamera: () => (/* binding */ PerspectiveCamera)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _Fbo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Fbo.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Fbo.js\");\n\n\n\n\n\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    set\n  }) => set);\n  const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    camera\n  }) => camera);\n  const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(({\n    size\n  }) => size);\n  const cameraRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const fbo = (0,_Fbo_js__WEBPACK_IMPORTED_MODULE_3__.useFBO)(resolution);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"perspectiveCamera\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/PerspectiveCamera.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Progress.js":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Progress.js ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress),\n/* harmony export */   useProgress: () => (/* binding */ useProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@5.0.3_@types+react@18.3.4_react@18.3.1_use-sync-external-store@1.2.2_react@18.3.1_/node_modules/zustand/esm/react.mjs\");\n\n\n\n\nlet saveLastTotalLoaded = 0;\nconst useProgress = /* @__PURE__ */(0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)(set => {\n  three__WEBPACK_IMPORTED_MODULE_2__.DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n  three__WEBPACK_IMPORTED_MODULE_2__.DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n  three__WEBPACK_IMPORTED_MODULE_2__.DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n  three__WEBPACK_IMPORTED_MODULE_2__.DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\n\n//\n\nfunction Progress({\n  children\n}) {\n  const result = useProgress();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children == null ? void 0 : children(result));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Progress.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Stage.js":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Stage.js ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stage: () => (/* binding */ Stage)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Environment_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Environment.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _ContactShadows_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ContactShadows.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/ContactShadows.js\");\n/* harmony import */ var _Center_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Center.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Center.js\");\n/* harmony import */ var _AccumulativeShadows_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AccumulativeShadows.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/AccumulativeShadows.js\");\n/* harmony import */ var _Bounds_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bounds.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Bounds.js\");\n\n\n\n\n\n\n\n\nconst presets = {\n  rembrandt: {\n    main: [1, 2, 1],\n    fill: [-2, -0.5, -2]\n  },\n  portrait: {\n    main: [-1, 2, 0.5],\n    fill: [-1, 0.5, -1.5]\n  },\n  upfront: {\n    main: [0, 2, 1],\n    fill: [-1, 0.5, -1.5]\n  },\n  soft: {\n    main: [-2, 4, 4],\n    fill: [-1, 0.5, -1.5]\n  }\n};\nfunction Refit({\n  radius,\n  adjustCamera\n}) {\n  const api = (0,_Bounds_js__WEBPACK_IMPORTED_MODULE_2__.useBounds)();\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (adjustCamera) api.refresh().clip().fit();\n  }, [radius, adjustCamera]);\n  return null;\n}\nfunction Stage({\n  children,\n  center,\n  adjustCamera = true,\n  intensity = 0.5,\n  shadows = 'contact',\n  environment = 'city',\n  preset = 'rembrandt',\n  ...props\n}) {\n  var _bias, _normalBias, _size, _offset, _amount, _radius, _ambient, _intensity;\n  const config = typeof preset === 'string' ? presets[preset] : preset;\n  const [{\n    radius,\n    height\n  }, set] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n    radius: 0,\n    width: 0,\n    height: 0,\n    depth: 0\n  });\n  const shadowBias = (_bias = shadows == null ? void 0 : shadows.bias) !== null && _bias !== void 0 ? _bias : -0.0001;\n  const normalBias = (_normalBias = shadows == null ? void 0 : shadows.normalBias) !== null && _normalBias !== void 0 ? _normalBias : 0;\n  const shadowSize = (_size = shadows == null ? void 0 : shadows.size) !== null && _size !== void 0 ? _size : 1024;\n  const shadowOffset = (_offset = shadows == null ? void 0 : shadows.offset) !== null && _offset !== void 0 ? _offset : 0;\n  const contactShadow = shadows === 'contact' || (shadows == null ? void 0 : shadows.type) === 'contact';\n  const accumulativeShadow = shadows === 'accumulative' || (shadows == null ? void 0 : shadows.type) === 'accumulative';\n  const shadowSpread = {\n    ...(typeof shadows === 'object' ? shadows : {})\n  };\n  const environmentProps = !environment ? null : typeof environment === 'string' ? {\n    preset: environment\n  } : environment;\n  const onCentered = react__WEBPACK_IMPORTED_MODULE_1__.useCallback(props => {\n    const {\n      width,\n      height,\n      depth,\n      boundingSphere\n    } = props;\n    set({\n      radius: boundingSphere.radius,\n      width,\n      height,\n      depth\n    });\n    if (center != null && center.onCentered) center.onCentered(props);\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"ambientLight\", {\n    intensity: intensity / 3\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"spotLight\", {\n    penumbra: 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    intensity: intensity * 2,\n    castShadow: !!shadows,\n    \"shadow-bias\": shadowBias,\n    \"shadow-normalBias\": normalBias,\n    \"shadow-mapSize\": shadowSize\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"pointLight\", {\n    position: [config.fill[0] * radius, config.fill[1] * radius, config.fill[2] * radius],\n    intensity: intensity\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Bounds_js__WEBPACK_IMPORTED_MODULE_2__.Bounds, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    fit: !!adjustCamera,\n    clip: !!adjustCamera,\n    margin: Number(adjustCamera),\n    observe: true\n  }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Refit, {\n    radius: radius,\n    adjustCamera: adjustCamera\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Center_js__WEBPACK_IMPORTED_MODULE_3__.Center, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, center, {\n    position: [0, shadowOffset / 2, 0],\n    onCentered: onCentered\n  }), children)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n    position: [0, -height / 2 - shadowOffset / 2, 0]\n  }, contactShadow && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ContactShadows_js__WEBPACK_IMPORTED_MODULE_4__.ContactShadows, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    scale: radius * 4,\n    far: radius,\n    blur: 2\n  }, shadowSpread)), accumulativeShadow && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_AccumulativeShadows_js__WEBPACK_IMPORTED_MODULE_5__.AccumulativeShadows, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    temporal: true,\n    frames: 100,\n    alphaTest: 0.9,\n    toneMapped: true,\n    scale: radius * 4\n  }, shadowSpread), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_AccumulativeShadows_js__WEBPACK_IMPORTED_MODULE_5__.RandomizedLight, {\n    amount: (_amount = shadowSpread.amount) !== null && _amount !== void 0 ? _amount : 8,\n    radius: (_radius = shadowSpread.radius) !== null && _radius !== void 0 ? _radius : radius,\n    ambient: (_ambient = shadowSpread.ambient) !== null && _ambient !== void 0 ? _ambient : 0.5,\n    intensity: (_intensity = shadowSpread.intensity) !== null && _intensity !== void 0 ? _intensity : 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    size: radius * 4,\n    bias: -shadowBias,\n    mapSize: shadowSize\n  }))), environment && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Environment_js__WEBPACK_IMPORTED_MODULE_6__.Environment, environmentProps));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Stage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Texture.js":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Texture.js ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsObject: () => (/* binding */ IsObject),\n/* harmony export */   Texture: () => (/* binding */ Texture),\n/* harmony export */   useTexture: () => (/* binding */ useTexture)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\n\nconst IsObject = url => url === Object(url) && !Array.isArray(url) && typeof url !== 'function';\nfunction useTexture(input, onLoad) {\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.D)(state => state.gl);\n  const textures = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.H)(three__WEBPACK_IMPORTED_MODULE_2__.TextureLoader, IsObject(input) ? Object.values(input) : input);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    onLoad == null || onLoad(textures);\n  }, [onLoad]);\n\n  // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n  // NOTE: only available for WebGLRenderer\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if ('initTexture' in gl) {\n      let textureArray = [];\n      if (Array.isArray(textures)) {\n        textureArray = textures;\n      } else if (textures instanceof three__WEBPACK_IMPORTED_MODULE_2__.Texture) {\n        textureArray = [textures];\n      } else if (IsObject(textures)) {\n        textureArray = Object.values(textures);\n      }\n      textureArray.forEach(texture => {\n        if (texture instanceof three__WEBPACK_IMPORTED_MODULE_2__.Texture) {\n          gl.initTexture(texture);\n        }\n      });\n    }\n  }, [gl, textures]);\n  const mappedTextures = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (IsObject(input)) {\n      const keyed = {};\n      let i = 0;\n      for (const key in input) keyed[key] = textures[i++];\n      return keyed;\n    } else {\n      return textures;\n    }\n  }, [input, textures]);\n  return mappedTextures;\n}\nuseTexture.preload = url => _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.H.preload(three__WEBPACK_IMPORTED_MODULE_2__.TextureLoader, url);\nuseTexture.clear = input => _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.H.clear(three__WEBPACK_IMPORTED_MODULE_2__.TextureLoader, input);\n\n//\n\nconst Texture = ({\n  children,\n  input,\n  onLoad\n}) => {\n  const ret = useTexture(input, onLoad);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children == null ? void 0 : children(ret));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Texture.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransformControls: () => (/* binding */ TransformControls)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/controls/TransformControls.js\");\n\n\n\n\n\n\nconst TransformControls = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  children,\n  domElement,\n  onChange,\n  onMouseDown,\n  onMouseUp,\n  onObjectChange,\n  object,\n  makeDefault,\n  camera,\n  // Transform\n  enabled,\n  axis,\n  mode,\n  translationSnap,\n  rotationSnap,\n  scaleSnap,\n  space,\n  size,\n  showX,\n  showY,\n  showZ,\n  ...props\n}, ref) => {\n  // @ts-expect-error new in @react-three/fiber@7.0.5\n  const defaultControls = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.controls);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  const events = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.events);\n  const defaultCamera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.camera);\n  const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.invalidate);\n  const get = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.get);\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => new three_stdlib__WEBPACK_IMPORTED_MODULE_3__.TransformControls(explCamera, explDomElement), [explCamera, explDomElement]);\n  const group = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (object) {\n      controls.attach(object instanceof three__WEBPACK_IMPORTED_MODULE_4__.Object3D ? object : object.current);\n    } else if (group.current instanceof three__WEBPACK_IMPORTED_MODULE_4__.Object3D) {\n      controls.attach(group.current);\n    }\n    return () => void controls.detach();\n  }, [object, children, controls]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (defaultControls) {\n      const callback = event => defaultControls.enabled = !event.value;\n      controls.addEventListener('dragging-changed', callback);\n      return () => controls.removeEventListener('dragging-changed', callback);\n    }\n  }, [controls, defaultControls]);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n  const onMouseDownRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n  const onMouseUpRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n  const onObjectChangeRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => void (onChangeRef.current = onChange), [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => void (onMouseDownRef.current = onMouseDown), [onMouseDown]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => void (onMouseUpRef.current = onMouseUp), [onMouseUp]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => void (onObjectChangeRef.current = onObjectChange), [onObjectChange]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const onChange = e => {\n      invalidate();\n      onChangeRef.current == null || onChangeRef.current(e);\n    };\n    const onMouseDown = e => onMouseDownRef.current == null ? void 0 : onMouseDownRef.current(e);\n    const onMouseUp = e => onMouseUpRef.current == null ? void 0 : onMouseUpRef.current(e);\n    const onObjectChange = e => onObjectChangeRef.current == null ? void 0 : onObjectChangeRef.current(e);\n    controls.addEventListener('change', onChange);\n    controls.addEventListener('mouseDown', onMouseDown);\n    controls.addEventListener('mouseUp', onMouseUp);\n    controls.addEventListener('objectChange', onObjectChange);\n    return () => {\n      controls.removeEventListener('change', onChange);\n      controls.removeEventListener('mouseDown', onMouseDown);\n      controls.removeEventListener('mouseUp', onMouseUp);\n      controls.removeEventListener('objectChange', onObjectChange);\n    };\n  }, [invalidate, controls]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", {\n    ref: ref,\n    object: controls,\n    enabled: enabled,\n    axis: axis,\n    mode: mode,\n    translationSnap: translationSnap,\n    rotationSnap: rotationSnap,\n    scaleSnap: scaleSnap,\n    space: space,\n    size: size,\n    showX: showX,\n    showY: showY,\n    showZ: showZ\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: group\n  }, props), children));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shaderMaterial.js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shaderMaterial.js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shaderMaterial: () => (/* binding */ shaderMaterial)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\nfunction shaderMaterial(uniforms, vertexShader, fragmentShader, onInit) {\n  const material = class material extends three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial {\n    constructor(parameters = {}) {\n      const entries = Object.entries(uniforms);\n      // Create unforms and shaders\n      super({\n        uniforms: entries.reduce((acc, [name, value]) => {\n          const uniform = three__WEBPACK_IMPORTED_MODULE_0__.UniformsUtils.clone({\n            [name]: {\n              value\n            }\n          });\n          return {\n            ...acc,\n            ...uniform\n          };\n        }, {}),\n        vertexShader,\n        fragmentShader\n      });\n      // Create getter/setters\n      this.key = '';\n      entries.forEach(([name]) => Object.defineProperty(this, name, {\n        get: () => this.uniforms[name].value,\n        set: v => this.uniforms[name].value = v\n      }));\n\n      // Assign parameters, this might include uniforms\n      Object.assign(this, parameters);\n      // Call onInit\n      if (onInit) onInit(this);\n    }\n  };\n  material.key = three__WEBPACK_IMPORTED_MODULE_0__.MathUtils.generateUUID();\n  return material;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9jb3JlL3NoYWRlck1hdGVyaWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCOztBQUUvQjtBQUNBLDBDQUEwQyxpREFBb0I7QUFDOUQsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLGdEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLElBQUk7QUFDYjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiw0Q0FBZTtBQUNoQztBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmVhY3QtdGhyZWUrZHJlaUA5LjEyMi4wX0ByZWFjdC10aHJlZStmaWJlckA4LjE4LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjNfYzU3NnZnbWVlNGh4bXIzYWptcjRkZ2N6dGkvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL2NvcmUvc2hhZGVyTWF0ZXJpYWwuanM/MGIyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBUSFJFRSBmcm9tICd0aHJlZSc7XG5cbmZ1bmN0aW9uIHNoYWRlck1hdGVyaWFsKHVuaWZvcm1zLCB2ZXJ0ZXhTaGFkZXIsIGZyYWdtZW50U2hhZGVyLCBvbkluaXQpIHtcbiAgY29uc3QgbWF0ZXJpYWwgPSBjbGFzcyBtYXRlcmlhbCBleHRlbmRzIFRIUkVFLlNoYWRlck1hdGVyaWFsIHtcbiAgICBjb25zdHJ1Y3RvcihwYXJhbWV0ZXJzID0ge30pIHtcbiAgICAgIGNvbnN0IGVudHJpZXMgPSBPYmplY3QuZW50cmllcyh1bmlmb3Jtcyk7XG4gICAgICAvLyBDcmVhdGUgdW5mb3JtcyBhbmQgc2hhZGVyc1xuICAgICAgc3VwZXIoe1xuICAgICAgICB1bmlmb3JtczogZW50cmllcy5yZWR1Y2UoKGFjYywgW25hbWUsIHZhbHVlXSkgPT4ge1xuICAgICAgICAgIGNvbnN0IHVuaWZvcm0gPSBUSFJFRS5Vbmlmb3Jtc1V0aWxzLmNsb25lKHtcbiAgICAgICAgICAgIFtuYW1lXToge1xuICAgICAgICAgICAgICB2YWx1ZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5hY2MsXG4gICAgICAgICAgICAuLi51bmlmb3JtXG4gICAgICAgICAgfTtcbiAgICAgICAgfSwge30pLFxuICAgICAgICB2ZXJ0ZXhTaGFkZXIsXG4gICAgICAgIGZyYWdtZW50U2hhZGVyXG4gICAgICB9KTtcbiAgICAgIC8vIENyZWF0ZSBnZXR0ZXIvc2V0dGVyc1xuICAgICAgdGhpcy5rZXkgPSAnJztcbiAgICAgIGVudHJpZXMuZm9yRWFjaCgoW25hbWVdKSA9PiBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgbmFtZSwge1xuICAgICAgICBnZXQ6ICgpID0+IHRoaXMudW5pZm9ybXNbbmFtZV0udmFsdWUsXG4gICAgICAgIHNldDogdiA9PiB0aGlzLnVuaWZvcm1zW25hbWVdLnZhbHVlID0gdlxuICAgICAgfSkpO1xuXG4gICAgICAvLyBBc3NpZ24gcGFyYW1ldGVycywgdGhpcyBtaWdodCBpbmNsdWRlIHVuaWZvcm1zXG4gICAgICBPYmplY3QuYXNzaWduKHRoaXMsIHBhcmFtZXRlcnMpO1xuICAgICAgLy8gQ2FsbCBvbkluaXRcbiAgICAgIGlmIChvbkluaXQpIG9uSW5pdCh0aGlzKTtcbiAgICB9XG4gIH07XG4gIG1hdGVyaWFsLmtleSA9IFRIUkVFLk1hdGhVdGlscy5nZW5lcmF0ZVVVSUQoKTtcbiAgcmV0dXJuIG1hdGVyaWFsO1xufVxuXG5leHBvcnQgeyBzaGFkZXJNYXRlcmlhbCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shaderMaterial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shapes.js":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shapes.js ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* binding */ Box),\n/* harmony export */   Capsule: () => (/* binding */ Capsule),\n/* harmony export */   Circle: () => (/* binding */ Circle),\n/* harmony export */   Cone: () => (/* binding */ Cone),\n/* harmony export */   Cylinder: () => (/* binding */ Cylinder),\n/* harmony export */   Dodecahedron: () => (/* binding */ Dodecahedron),\n/* harmony export */   Extrude: () => (/* binding */ Extrude),\n/* harmony export */   Icosahedron: () => (/* binding */ Icosahedron),\n/* harmony export */   Lathe: () => (/* binding */ Lathe),\n/* harmony export */   Octahedron: () => (/* binding */ Octahedron),\n/* harmony export */   Plane: () => (/* binding */ Plane),\n/* harmony export */   Polyhedron: () => (/* binding */ Polyhedron),\n/* harmony export */   Ring: () => (/* binding */ Ring),\n/* harmony export */   Shape: () => (/* binding */ Shape),\n/* harmony export */   Sphere: () => (/* binding */ Sphere),\n/* harmony export */   Tetrahedron: () => (/* binding */ Tetrahedron),\n/* harmony export */   Torus: () => (/* binding */ Torus),\n/* harmony export */   TorusKnot: () => (/* binding */ TorusKnot),\n/* harmony export */   Tube: () => (/* binding */ Tube)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n\n\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(fref, () => ref.current);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"mesh\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      ref: ref\n    }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = /* @__PURE__ */create('box');\nconst Circle = /* @__PURE__ */create('circle');\nconst Cone = /* @__PURE__ */create('cone');\nconst Cylinder = /* @__PURE__ */create('cylinder');\nconst Sphere = /* @__PURE__ */create('sphere');\nconst Plane = /* @__PURE__ */create('plane');\nconst Tube = /* @__PURE__ */create('tube');\nconst Torus = /* @__PURE__ */create('torus');\nconst TorusKnot = /* @__PURE__ */create('torusKnot');\nconst Tetrahedron = /* @__PURE__ */create('tetrahedron');\nconst Ring = /* @__PURE__ */create('ring');\nconst Polyhedron = /* @__PURE__ */create('polyhedron');\nconst Icosahedron = /* @__PURE__ */create('icosahedron');\nconst Octahedron = /* @__PURE__ */create('octahedron');\nconst Dodecahedron = /* @__PURE__ */create('dodecahedron');\nconst Extrude = /* @__PURE__ */create('extrude');\nconst Lathe = /* @__PURE__ */create('lathe');\nconst Capsule = /* @__PURE__ */create('capsule');\nconst Shape = /* @__PURE__ */create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new three__WEBPACK_IMPORTED_MODULE_2__.Box3().setFromBufferAttribute(pos);\n  const b3size = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new three__WEBPACK_IMPORTED_MODULE_2__.Float32BufferAttribute(uv, 2));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9jb3JlL3NoYXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUMzQjtBQUNBOztBQUUvQjtBQUNBO0FBQ0Esc0JBQXNCLDZDQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsZ0JBQWdCLHlDQUFZO0FBQzVCLElBQUksc0RBQXlCO0FBQzdCLElBQUksa0RBQXFCO0FBQ3pCLHdCQUF3QixnREFBbUIsU0FBUyw4RUFBUTtBQUM1RDtBQUNBLEtBQUssdUJBQXVCLGdEQUFtQjtBQUMvQztBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHVDQUFVO0FBQzNCLHFCQUFxQiwwQ0FBYTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsZUFBZTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MseURBQTRCO0FBQzlELENBQUM7O0FBRW1MIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByZWFjdC10aHJlZStkcmVpQDkuMTIyLjBfQHJlYWN0LXRocmVlK2ZpYmVyQDguMTguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguM19jNTc2dmdtZWU0aHhtcjNham1yNGRnY3p0aS9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2RyZWkvY29yZS9zaGFwZXMuanM/MWQ5MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSAnQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kcyc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgKiBhcyBUSFJFRSBmcm9tICd0aHJlZSc7XG5cbmZ1bmN0aW9uIGNyZWF0ZSh0eXBlLCBlZmZlY3QpIHtcbiAgY29uc3QgRWwgPSB0eXBlICsgJ0dlb21ldHJ5JztcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKCh7XG4gICAgYXJncyxcbiAgICBjaGlsZHJlbixcbiAgICAuLi5wcm9wc1xuICB9LCBmcmVmKSA9PiB7XG4gICAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICAgIFJlYWN0LnVzZUltcGVyYXRpdmVIYW5kbGUoZnJlZiwgKCkgPT4gcmVmLmN1cnJlbnQpO1xuICAgIFJlYWN0LnVzZUxheW91dEVmZmVjdCgoKSA9PiB2b2lkIChlZmZlY3QgPT0gbnVsbCA/IHZvaWQgMCA6IGVmZmVjdChyZWYuY3VycmVudCkpKTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJtZXNoXCIsIF9leHRlbmRzKHtcbiAgICAgIHJlZjogcmVmXG4gICAgfSwgcHJvcHMpLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChFbCwge1xuICAgICAgYXR0YWNoOiBcImdlb21ldHJ5XCIsXG4gICAgICBhcmdzOiBhcmdzXG4gICAgfSksIGNoaWxkcmVuKTtcbiAgfSk7XG59XG5jb25zdCBCb3ggPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ2JveCcpO1xuY29uc3QgQ2lyY2xlID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdjaXJjbGUnKTtcbmNvbnN0IENvbmUgPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ2NvbmUnKTtcbmNvbnN0IEN5bGluZGVyID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdjeWxpbmRlcicpO1xuY29uc3QgU3BoZXJlID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdzcGhlcmUnKTtcbmNvbnN0IFBsYW5lID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdwbGFuZScpO1xuY29uc3QgVHViZSA9IC8qIEBfX1BVUkVfXyAqL2NyZWF0ZSgndHViZScpO1xuY29uc3QgVG9ydXMgPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ3RvcnVzJyk7XG5jb25zdCBUb3J1c0tub3QgPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ3RvcnVzS25vdCcpO1xuY29uc3QgVGV0cmFoZWRyb24gPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ3RldHJhaGVkcm9uJyk7XG5jb25zdCBSaW5nID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdyaW5nJyk7XG5jb25zdCBQb2x5aGVkcm9uID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdwb2x5aGVkcm9uJyk7XG5jb25zdCBJY29zYWhlZHJvbiA9IC8qIEBfX1BVUkVfXyAqL2NyZWF0ZSgnaWNvc2FoZWRyb24nKTtcbmNvbnN0IE9jdGFoZWRyb24gPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ29jdGFoZWRyb24nKTtcbmNvbnN0IERvZGVjYWhlZHJvbiA9IC8qIEBfX1BVUkVfXyAqL2NyZWF0ZSgnZG9kZWNhaGVkcm9uJyk7XG5jb25zdCBFeHRydWRlID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdleHRydWRlJyk7XG5jb25zdCBMYXRoZSA9IC8qIEBfX1BVUkVfXyAqL2NyZWF0ZSgnbGF0aGUnKTtcbmNvbnN0IENhcHN1bGUgPSAvKiBAX19QVVJFX18gKi9jcmVhdGUoJ2NhcHN1bGUnKTtcbmNvbnN0IFNoYXBlID0gLyogQF9fUFVSRV9fICovY3JlYXRlKCdzaGFwZScsICh7XG4gIGdlb21ldHJ5XG59KSA9PiB7XG4gIC8vIENhbGN1bGF0ZSBVVnMgKGJ5IGh0dHBzOi8vZGlzY291cnNlLnRocmVlanMub3JnL3UvcHJpc29uZXI4NDkpXG4gIC8vIGh0dHBzOi8vZGlzY291cnNlLnRocmVlanMub3JnL3QvY3VzdG9tLXNoYXBlLWluLWltYWdlLW5vdC13b3JraW5nLzQ5MzQ4LzEwXG4gIGNvbnN0IHBvcyA9IGdlb21ldHJ5LmF0dHJpYnV0ZXMucG9zaXRpb247XG4gIGNvbnN0IGIzID0gbmV3IFRIUkVFLkJveDMoKS5zZXRGcm9tQnVmZmVyQXR0cmlidXRlKHBvcyk7XG4gIGNvbnN0IGIzc2l6ZSA9IG5ldyBUSFJFRS5WZWN0b3IzKCk7XG4gIGIzLmdldFNpemUoYjNzaXplKTtcbiAgY29uc3QgdXYgPSBbXTtcbiAgbGV0IHggPSAwLFxuICAgIHkgPSAwLFxuICAgIHUgPSAwLFxuICAgIHYgPSAwO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHBvcy5jb3VudDsgaSsrKSB7XG4gICAgeCA9IHBvcy5nZXRYKGkpO1xuICAgIHkgPSBwb3MuZ2V0WShpKTtcbiAgICB1ID0gKHggLSBiMy5taW4ueCkgLyBiM3NpemUueDtcbiAgICB2ID0gKHkgLSBiMy5taW4ueSkgLyBiM3NpemUueTtcbiAgICB1di5wdXNoKHUsIHYpO1xuICB9XG4gIGdlb21ldHJ5LnNldEF0dHJpYnV0ZSgndXYnLCBuZXcgVEhSRUUuRmxvYXQzMkJ1ZmZlckF0dHJpYnV0ZSh1diwgMikpO1xufSk7XG5cbmV4cG9ydCB7IEJveCwgQ2Fwc3VsZSwgQ2lyY2xlLCBDb25lLCBDeWxpbmRlciwgRG9kZWNhaGVkcm9uLCBFeHRydWRlLCBJY29zYWhlZHJvbiwgTGF0aGUsIE9jdGFoZWRyb24sIFBsYW5lLCBQb2x5aGVkcm9uLCBSaW5nLCBTaGFwZSwgU3BoZXJlLCBUZXRyYWhlZHJvbiwgVG9ydXMsIFRvcnVzS25vdCwgVHViZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shapes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/useEnvironment.js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/useEnvironment.js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnvironment: () => (/* binding */ useEnvironment)\n/* harmony export */ });\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/loaders/RGBELoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/.pnpm/three-stdlib@2.36.0_three@0.167.1/node_modules/three-stdlib/loaders/EXRLoader.js\");\n/* harmony import */ var _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @monogrid/gainmap-js */ \"(ssr)/./node_modules/.pnpm/@monogrid+gainmap-js@3.1.0_three@0.167.1/node_modules/@monogrid/gainmap-js/dist/decode.js\");\n/* harmony import */ var _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/environment-assets.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/environment-assets.js\");\n/* harmony import */ var _helpers_deprecated_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/deprecated.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/deprecated.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n\n\n\n\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  let loader = null;\n  let multiFile = false;\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.D)(state => state.gl);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.H.clear(\n      // @ts-expect-error\n      loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.H)(\n  // @ts-expect-error\n  loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? three__WEBPACK_IMPORTED_MODULE_3__.CubeReflectionMapping : three__WEBPACK_IMPORTED_MODULE_3__.EquirectangularReflectionMapping;\n  if ('colorSpace' in texture) texture.colorSpace = (encoding !== null && encoding !== void 0 ? encoding : isCubemap) ? 'srgb' : 'srgb-linear';else texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubemap) ? _helpers_deprecated_js__WEBPACK_IMPORTED_MODULE_4__.sRGBEncoding : _helpers_deprecated_js__WEBPACK_IMPORTED_MODULE_4__.LinearEncoding;\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.H.preload(\n  // @ts-expect-error\n  loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.H.clear(\n  // @ts-expect-error\n  loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(_helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? three__WEBPACK_IMPORTED_MODULE_3__.CubeTextureLoader : extension === 'hdr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_5__.RGBELoader : extension === 'exr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_6__.EXRLoader : extension === 'jpg' || extension === 'jpeg' ? _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_7__.HDRJPGLoader : extension === 'webp' ? _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_7__.GainMapLoader : null;\n  return loader;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/useEnvironment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/constants.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/constants.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\nconst getVersion = () => parseInt(three__WEBPACK_IMPORTED_MODULE_0__.REVISION.replace(/\\D+/g, ''));\nconst version = /* @__PURE__ */getVersion();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9oZWxwZXJzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQzs7QUFFakMsa0NBQWtDLDJDQUFRO0FBQzFDOztBQUVtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmVhY3QtdGhyZWUrZHJlaUA5LjEyMi4wX0ByZWFjdC10aHJlZStmaWJlckA4LjE4LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjNfYzU3NnZnbWVlNGh4bXIzYWptcjRkZ2N6dGkvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL2hlbHBlcnMvY29uc3RhbnRzLmpzPzhhNzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUkVWSVNJT04gfSBmcm9tICd0aHJlZSc7XG5cbmNvbnN0IGdldFZlcnNpb24gPSAoKSA9PiBwYXJzZUludChSRVZJU0lPTi5yZXBsYWNlKC9cXEQrL2csICcnKSk7XG5jb25zdCB2ZXJzaW9uID0gLyogQF9fUFVSRV9fICovZ2V0VmVyc2lvbigpO1xuXG5leHBvcnQgeyB2ZXJzaW9uIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/deprecated.js":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/deprecated.js ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinearEncoding: () => (/* binding */ LinearEncoding),\n/* harmony export */   sRGBEncoding: () => (/* binding */ sRGBEncoding),\n/* harmony export */   setUpdateRange: () => (/* binding */ setUpdateRange)\n/* harmony export */ });\n/**\n * Sets `BufferAttribute.updateRange` since r159.\n */\nconst setUpdateRange = (attribute, updateRange) => {\n  if ('updateRanges' in attribute) {\n    // r159\n    // @ts-ignore\n    attribute.updateRanges[0] = updateRange;\n  } else {\n    attribute.updateRange = updateRange;\n  }\n};\nconst LinearEncoding = 3000;\nconst sRGBEncoding = 3001;\n\n/**\n * TextureEncoding was deprecated in r152, and removed in r162.\n */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9oZWxwZXJzL2RlcHJlY2F0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFd0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9oZWxwZXJzL2RlcHJlY2F0ZWQuanM/YWFjYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNldHMgYEJ1ZmZlckF0dHJpYnV0ZS51cGRhdGVSYW5nZWAgc2luY2UgcjE1OS5cbiAqL1xuY29uc3Qgc2V0VXBkYXRlUmFuZ2UgPSAoYXR0cmlidXRlLCB1cGRhdGVSYW5nZSkgPT4ge1xuICBpZiAoJ3VwZGF0ZVJhbmdlcycgaW4gYXR0cmlidXRlKSB7XG4gICAgLy8gcjE1OVxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBhdHRyaWJ1dGUudXBkYXRlUmFuZ2VzWzBdID0gdXBkYXRlUmFuZ2U7XG4gIH0gZWxzZSB7XG4gICAgYXR0cmlidXRlLnVwZGF0ZVJhbmdlID0gdXBkYXRlUmFuZ2U7XG4gIH1cbn07XG5jb25zdCBMaW5lYXJFbmNvZGluZyA9IDMwMDA7XG5jb25zdCBzUkdCRW5jb2RpbmcgPSAzMDAxO1xuXG4vKipcbiAqIFRleHR1cmVFbmNvZGluZyB3YXMgZGVwcmVjYXRlZCBpbiByMTUyLCBhbmQgcmVtb3ZlZCBpbiByMTYyLlxuICovXG5cbmV4cG9ydCB7IExpbmVhckVuY29kaW5nLCBzUkdCRW5jb2RpbmcsIHNldFVwZGF0ZVJhbmdlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/deprecated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/environment-assets.js":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/environment-assets.js ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   presetsObj: () => (/* binding */ presetsObj)\n/* harmony export */ });\nconst presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9oZWxwZXJzL2Vudmlyb25tZW50LWFzc2V0cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmVhY3QtdGhyZWUrZHJlaUA5LjEyMi4wX0ByZWFjdC10aHJlZStmaWJlckA4LjE4LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjNfYzU3NnZnbWVlNGh4bXIzYWptcjRkZ2N6dGkvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL2hlbHBlcnMvZW52aXJvbm1lbnQtYXNzZXRzLmpzPzc1MDUiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcHJlc2V0c09iaiA9IHtcbiAgYXBhcnRtZW50OiAnbGVib21ib18xay5oZHInLFxuICBjaXR5OiAncG90c2RhbWVyX3BsYXR6XzFrLmhkcicsXG4gIGRhd246ICdraWFyYV8xX2Rhd25fMWsuaGRyJyxcbiAgZm9yZXN0OiAnZm9yZXN0X3Nsb3BlXzFrLmhkcicsXG4gIGxvYmJ5OiAnc3RfZmFnYW5zX2ludGVyaW9yXzFrLmhkcicsXG4gIG5pZ2h0OiAnZGlraG9sb2xvX25pZ2h0XzFrLmhkcicsXG4gIHBhcms6ICdyb29pdG91X3BhcmtfMWsuaGRyJyxcbiAgc3R1ZGlvOiAnc3R1ZGlvX3NtYWxsXzAzXzFrLmhkcicsXG4gIHN1bnNldDogJ3ZlbmljZV9zdW5zZXRfMWsuaGRyJyxcbiAgd2FyZWhvdXNlOiAnZW1wdHlfd2FyZWhvdXNlXzAxXzFrLmhkcidcbn07XG5cbmV4cG9ydCB7IHByZXNldHNPYmogfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/environment-assets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/BlurPass.js":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/BlurPass.js ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlurPass: () => (/* binding */ BlurPass)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _ConvolutionMaterial_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConvolutionMaterial.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/ConvolutionMaterial.js\");\n\n\n\nclass BlurPass {\n  constructor({\n    gl,\n    resolution,\n    width = 500,\n    height = 500,\n    minDepthThreshold = 0,\n    maxDepthThreshold = 1,\n    depthScale = 0,\n    depthToBlurRatioBias = 0.25\n  }) {\n    this.renderToScreen = false;\n    this.renderTargetA = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(resolution, resolution, {\n      minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n      magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n      stencilBuffer: false,\n      depthBuffer: false,\n      type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType\n    });\n    this.renderTargetB = this.renderTargetA.clone();\n    this.convolutionMaterial = new _ConvolutionMaterial_js__WEBPACK_IMPORTED_MODULE_1__.ConvolutionMaterial();\n    this.convolutionMaterial.setTexelSize(1.0 / width, 1.0 / height);\n    this.convolutionMaterial.setResolution(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(width, height));\n    this.scene = new three__WEBPACK_IMPORTED_MODULE_0__.Scene();\n    // @ts-expect-error fixed in r154\n    this.camera = new three__WEBPACK_IMPORTED_MODULE_0__.Camera();\n    this.convolutionMaterial.uniforms.minDepthThreshold.value = minDepthThreshold;\n    this.convolutionMaterial.uniforms.maxDepthThreshold.value = maxDepthThreshold;\n    this.convolutionMaterial.uniforms.depthScale.value = depthScale;\n    this.convolutionMaterial.uniforms.depthToBlurRatioBias.value = depthToBlurRatioBias;\n    this.convolutionMaterial.defines.USE_DEPTH = depthScale > 0;\n    const vertices = new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]);\n    const uvs = new Float32Array([0, 0, 2, 0, 0, 2]);\n    const geometry = new three__WEBPACK_IMPORTED_MODULE_0__.BufferGeometry();\n    geometry.setAttribute('position', new three__WEBPACK_IMPORTED_MODULE_0__.BufferAttribute(vertices, 3));\n    geometry.setAttribute('uv', new three__WEBPACK_IMPORTED_MODULE_0__.BufferAttribute(uvs, 2));\n    this.screen = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh(geometry, this.convolutionMaterial);\n    this.screen.frustumCulled = false;\n    this.scene.add(this.screen);\n  }\n  render(renderer, inputBuffer, outputBuffer) {\n    const scene = this.scene;\n    const camera = this.camera;\n    const renderTargetA = this.renderTargetA;\n    const renderTargetB = this.renderTargetB;\n    let material = this.convolutionMaterial;\n    let uniforms = material.uniforms;\n    uniforms.depthBuffer.value = inputBuffer.depthTexture;\n    const kernel = material.kernel;\n    let lastRT = inputBuffer;\n    let destRT;\n    let i, l;\n    // Apply the multi-pass blur.\n    for (i = 0, l = kernel.length - 1; i < l; ++i) {\n      // Alternate between targets.\n      destRT = (i & 1) === 0 ? renderTargetA : renderTargetB;\n      uniforms.kernel.value = kernel[i];\n      uniforms.inputBuffer.value = lastRT.texture;\n      renderer.setRenderTarget(destRT);\n      renderer.render(scene, camera);\n      lastRT = destRT;\n    }\n    uniforms.kernel.value = kernel[i];\n    uniforms.inputBuffer.value = lastRT.texture;\n    renderer.setRenderTarget(this.renderToScreen ? null : outputBuffer);\n    renderer.render(scene, camera);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/BlurPass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/ConvolutionMaterial.js":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/ConvolutionMaterial.js ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvolutionMaterial: () => (/* binding */ ConvolutionMaterial)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _helpers_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/constants.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/helpers/constants.js\");\n\n\n\nclass ConvolutionMaterial extends three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial {\n  constructor(texelSize = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(null),\n        depthBuffer: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(null),\n        resolution: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()),\n        texelSize: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()),\n        halfTexelSize: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()),\n        kernel: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(0.0),\n        scale: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(1.0),\n        cameraNear: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(0.0),\n        cameraFar: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(1.0),\n        minDepthThreshold: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(0.0),\n        maxDepthThreshold: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(1.0),\n        depthScale: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(0.0),\n        depthToBlurRatioBias: new three__WEBPACK_IMPORTED_MODULE_0__.Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${_helpers_constants_js__WEBPACK_IMPORTED_MODULE_1__.version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: three__WEBPACK_IMPORTED_MODULE_0__.NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/ConvolutionMaterial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/DiscardMaterial.js":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/DiscardMaterial.js ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiscardMaterial: () => (/* binding */ DiscardMaterial)\n/* harmony export */ });\n/* harmony import */ var _core_shaderMaterial_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/shaderMaterial.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/shaderMaterial.js\");\n\n\nconst DiscardMaterial = /* @__PURE__ */(0,_core_shaderMaterial_js__WEBPACK_IMPORTED_MODULE_0__.shaderMaterial)({}, 'void main() { }', 'void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }');\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXRocmVlK2RyZWlAOS4xMjIuMF9AcmVhY3QtdGhyZWUrZmliZXJAOC4xOC4wX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zX2M1NzZ2Z21lZTRoeG1yM2FqbXI0ZGdjenRpL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZHJlaS9tYXRlcmlhbHMvRGlzY2FyZE1hdGVyaWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEOztBQUUzRCx1Q0FBdUMsdUVBQWMsR0FBRyxrQkFBa0Isa0JBQWtCLHlDQUF5QyxXQUFXOztBQUVySCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmVhY3QtdGhyZWUrZHJlaUA5LjEyMi4wX0ByZWFjdC10aHJlZStmaWJlckA4LjE4LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjNfYzU3NnZnbWVlNGh4bXIzYWptcjRkZ2N6dGkvbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9kcmVpL21hdGVyaWFscy9EaXNjYXJkTWF0ZXJpYWwuanM/NTZiZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzaGFkZXJNYXRlcmlhbCB9IGZyb20gJy4uL2NvcmUvc2hhZGVyTWF0ZXJpYWwuanMnO1xuXG5jb25zdCBEaXNjYXJkTWF0ZXJpYWwgPSAvKiBAX19QVVJFX18gKi9zaGFkZXJNYXRlcmlhbCh7fSwgJ3ZvaWQgbWFpbigpIHsgfScsICd2b2lkIG1haW4oKSB7IGdsX0ZyYWdDb2xvciA9IHZlYzQoMC4wLCAwLjAsIDAuMCwgMC4wKTsgZGlzY2FyZDsgIH0nKTtcblxuZXhwb3J0IHsgRGlzY2FyZE1hdGVyaWFsIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/DiscardMaterial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/MeshReflectorMaterial.js":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/MeshReflectorMaterial.js ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeshReflectorMaterial: () => (/* binding */ MeshReflectorMaterial)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\nclass MeshReflectorMaterial extends three__WEBPACK_IMPORTED_MODULE_0__.MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this._tDepth = {\n      value: null\n    };\n    this._distortionMap = {\n      value: null\n    };\n    this._tDiffuse = {\n      value: null\n    };\n    this._tDiffuseBlur = {\n      value: null\n    };\n    this._textureMatrix = {\n      value: null\n    };\n    this._hasBlur = {\n      value: false\n    };\n    this._mirror = {\n      value: 0.0\n    };\n    this._mixBlur = {\n      value: 0.0\n    };\n    this._blurStrength = {\n      value: 0.5\n    };\n    this._minDepthThreshold = {\n      value: 0.9\n    };\n    this._maxDepthThreshold = {\n      value: 1\n    };\n    this._depthScale = {\n      value: 0\n    };\n    this._depthToBlurRatioBias = {\n      value: 0.25\n    };\n    this._distortion = {\n      value: 1\n    };\n    this._mixContrast = {\n      value: 1.0\n    };\n    this.setValues(parameters);\n  }\n  onBeforeCompile(shader) {\n    var _shader$defines;\n    if (!((_shader$defines = shader.defines) != null && _shader$defines.USE_UV)) {\n      shader.defines.USE_UV = '';\n    }\n    shader.uniforms.hasBlur = this._hasBlur;\n    shader.uniforms.tDiffuse = this._tDiffuse;\n    shader.uniforms.tDepth = this._tDepth;\n    shader.uniforms.distortionMap = this._distortionMap;\n    shader.uniforms.tDiffuseBlur = this._tDiffuseBlur;\n    shader.uniforms.textureMatrix = this._textureMatrix;\n    shader.uniforms.mirror = this._mirror;\n    shader.uniforms.mixBlur = this._mixBlur;\n    shader.uniforms.mixStrength = this._blurStrength;\n    shader.uniforms.minDepthThreshold = this._minDepthThreshold;\n    shader.uniforms.maxDepthThreshold = this._maxDepthThreshold;\n    shader.uniforms.depthScale = this._depthScale;\n    shader.uniforms.depthToBlurRatioBias = this._depthToBlurRatioBias;\n    shader.uniforms.distortion = this._distortion;\n    shader.uniforms.mixContrast = this._mixContrast;\n    shader.vertexShader = `\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${shader.vertexShader}`;\n    shader.vertexShader = shader.vertexShader.replace('#include <project_vertex>', `#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );`);\n    shader.fragmentShader = `\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${shader.fragmentShader}`;\n    shader.fragmentShader = shader.fragmentShader.replace('#include <emissivemap_fragment>', `#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      `);\n  }\n  get tDiffuse() {\n    return this._tDiffuse.value;\n  }\n  set tDiffuse(v) {\n    this._tDiffuse.value = v;\n  }\n  get tDepth() {\n    return this._tDepth.value;\n  }\n  set tDepth(v) {\n    this._tDepth.value = v;\n  }\n  get distortionMap() {\n    return this._distortionMap.value;\n  }\n  set distortionMap(v) {\n    this._distortionMap.value = v;\n  }\n  get tDiffuseBlur() {\n    return this._tDiffuseBlur.value;\n  }\n  set tDiffuseBlur(v) {\n    this._tDiffuseBlur.value = v;\n  }\n  get textureMatrix() {\n    return this._textureMatrix.value;\n  }\n  set textureMatrix(v) {\n    this._textureMatrix.value = v;\n  }\n  get hasBlur() {\n    return this._hasBlur.value;\n  }\n  set hasBlur(v) {\n    this._hasBlur.value = v;\n  }\n  get mirror() {\n    return this._mirror.value;\n  }\n  set mirror(v) {\n    this._mirror.value = v;\n  }\n  get mixBlur() {\n    return this._mixBlur.value;\n  }\n  set mixBlur(v) {\n    this._mixBlur.value = v;\n  }\n  get mixStrength() {\n    return this._blurStrength.value;\n  }\n  set mixStrength(v) {\n    this._blurStrength.value = v;\n  }\n  get minDepthThreshold() {\n    return this._minDepthThreshold.value;\n  }\n  set minDepthThreshold(v) {\n    this._minDepthThreshold.value = v;\n  }\n  get maxDepthThreshold() {\n    return this._maxDepthThreshold.value;\n  }\n  set maxDepthThreshold(v) {\n    this._maxDepthThreshold.value = v;\n  }\n  get depthScale() {\n    return this._depthScale.value;\n  }\n  set depthScale(v) {\n    this._depthScale.value = v;\n  }\n  get depthToBlurRatioBias() {\n    return this._depthToBlurRatioBias.value;\n  }\n  set depthToBlurRatioBias(v) {\n    this._depthToBlurRatioBias.value = v;\n  }\n  get distortion() {\n    return this._distortion.value;\n  }\n  set distortion(v) {\n    this._distortion.value = v;\n  }\n  get mixContrast() {\n    return this._mixContrast.value;\n  }\n  set mixContrast(v) {\n    this._mixContrast.value = v;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/materials/MeshReflectorMaterial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Html: () => (/* binding */ Html)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.26.9/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom/client */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-dom/client.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n\n\n\n\n\n\nconst v1 = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_3__.Vector3();\nconst v2 = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_3__.Vector3();\nconst v3 = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_3__.Vector3();\nconst v4 = /* @__PURE__ */new three__WEBPACK_IMPORTED_MODULE_3__.Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof three__WEBPACK_IMPORTED_MODULE_3__.OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof three__WEBPACK_IMPORTED_MODULE_3__.PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof three__WEBPACK_IMPORTED_MODULE_3__.PerspectiveCamera || camera instanceof three__WEBPACK_IMPORTED_MODULE_3__.OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.D)();\n  const [el] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => document.createElement(as));\n  const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n  const group = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const oldZoom = react__WEBPACK_IMPORTED_MODULE_1__.useRef(0);\n  const oldPosition = react__WEBPACK_IMPORTED_MODULE_1__.useRef([0, 0]);\n  const transformOuterRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const transformInnerRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const isMeshSizeSet = react__WEBPACK_IMPORTED_MODULE_1__.useRef(false);\n  const isRayCastOcclusion = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = react_dom_client__WEBPACK_IMPORTED_MODULE_2__.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = react__WEBPACK_IMPORTED_MODULE_1__.useRef(true);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F)(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof three__WEBPACK_IMPORTED_MODULE_3__.Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"planeGeometry\", null), material || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"shaderMaterial\", {\n    side: three__WEBPACK_IMPORTED_MODULE_3__.DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loader: () => (/* binding */ Loader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_Progress_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/Progress.js */ \"(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Progress.js\");\n\n\n\nconst defaultDataInterpolation = p => `Loading ${p.toFixed(2)}%`;\nfunction Loader({\n  containerStyles,\n  innerStyles,\n  barStyles,\n  dataStyles,\n  dataInterpolation = defaultDataInterpolation,\n  initialState = active => active\n}) {\n  const {\n    active,\n    progress\n  } = (0,_core_Progress_js__WEBPACK_IMPORTED_MODULE_1__.useProgress)();\n  const progressRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const rafRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const progressSpanRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const [shown, setShown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialState(active));\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let t;\n    if (active !== shown) t = setTimeout(() => setShown(active), 300);\n    return () => clearTimeout(t);\n  }, [shown, active]);\n  const updateProgress = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (!progressSpanRef.current) return;\n    progressRef.current += (progress - progressRef.current) / 2;\n    if (progressRef.current > 0.95 * progress || progress === 100) progressRef.current = progress;\n    progressSpanRef.current.innerText = dataInterpolation(progressRef.current);\n    if (progressRef.current < progress) rafRef.current = requestAnimationFrame(updateProgress);\n  }, [dataInterpolation, progress]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    updateProgress();\n    return () => cancelAnimationFrame(rafRef.current);\n  }, [updateProgress]);\n  return shown ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: {\n      ...styles.container,\n      opacity: active ? 1 : 0,\n      ...containerStyles\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: {\n      ...styles.inner,\n      ...innerStyles\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: {\n      ...styles.bar,\n      transform: `scaleX(${progress / 100})`,\n      ...barStyles\n    }\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n    ref: progressSpanRef,\n    style: {\n      ...styles.data,\n      ...dataStyles\n    }\n  })))) : null;\n}\nconst styles = {\n  container: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    background: '#171717',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'opacity 300ms ease',\n    zIndex: 1000\n  },\n  inner: {\n    width: 100,\n    height: 3,\n    background: '#272727',\n    textAlign: 'center'\n  },\n  bar: {\n    height: 3,\n    width: '100%',\n    background: 'white',\n    transition: 'transform 200ms',\n    transformOrigin: 'left center'\n  },\n  data: {\n    display: 'inline-block',\n    position: 'relative',\n    fontVariantNumeric: 'tabular-nums',\n    marginTop: '0.8em',\n    color: '#f0f0f0',\n    fontSize: '0.6em',\n    fontFamily: `-apple-system, BlinkMacSystemFont, \"Inter\", \"Segoe UI\", \"Helvetica Neue\", Helvetica, Arial, Roboto, Ubuntu, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"`,\n    whiteSpace: 'nowrap'\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\n");

/***/ })

};
;