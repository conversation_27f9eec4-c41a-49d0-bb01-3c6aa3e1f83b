"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+signature-v4@3.299.0";
exports.ids = ["vendor-chunks/@aws-sdk+signature-v4@3.299.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4: () => (/* binding */ SignatureV4)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-middleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-middleware@3.296.0/node_modules/@aws-sdk/util-middleware/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./credentialDerivation */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\");\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./getPayloadHash */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _headerUtil__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./headerUtil */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./prepareRequest */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _utilDate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utilDate */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nclass SignatureV4 {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        this.service = service;\n        this.sha256 = sha256;\n        this.uriEscapePath = uriEscapePath;\n        this.applyChecksum = typeof applyChecksum === \"boolean\" ? applyChecksum : true;\n        this.regionProvider = (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(region);\n        this.credentialProvider = (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(credentials);\n    }\n    async presign(originalRequest, options = {}) {\n        const { signingDate = new Date(), expiresIn = 3600, unsignableHeaders, unhoistableHeaders, signableHeaders, signingRegion, signingService, } = options;\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { longDate, shortDate } = formatDate(signingDate);\n        if (expiresIn > _constants__WEBPACK_IMPORTED_MODULE_3__.MAX_PRESIGNED_TTL) {\n            return Promise.reject(\"Signature version 4 presigned URLs\" + \" must have an expiration date less than one week in\" + \" the future\");\n        }\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        const request = (0,_moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_9__.moveHeadersToQuery)((0,_prepareRequest__WEBPACK_IMPORTED_MODULE_10__.prepareRequest)(originalRequest), { unhoistableHeaders });\n        if (credentials.sessionToken) {\n            request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        }\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_QUERY_PARAM] = _constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.CREDENTIAL_QUERY_PARAM] = `${credentials.accessKeyId}/${scope}`;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.AMZ_DATE_QUERY_PARAM] = longDate;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.EXPIRES_QUERY_PARAM] = expiresIn.toString(10);\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.SIGNED_HEADERS_QUERY_PARAM] = getCanonicalHeaderList(canonicalHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.SIGNATURE_QUERY_PARAM] = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)(originalRequest, this.sha256)));\n        return request;\n    }\n    async sign(toSign, options) {\n        if (typeof toSign === \"string\") {\n            return this.signString(toSign, options);\n        }\n        else if (toSign.headers && toSign.payload) {\n            return this.signEvent(toSign, options);\n        }\n        else {\n            return this.signRequest(toSign, options);\n        }\n    }\n    async signEvent({ headers, payload }, { signingDate = new Date(), priorSignature, signingRegion, signingService }) {\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate, longDate } = formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        const hashedPayload = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)({ headers: {}, body: payload }, this.sha256);\n        const hash = new this.sha256();\n        hash.update(headers);\n        const hashedHeaders = (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n        const stringToSign = [\n            _constants__WEBPACK_IMPORTED_MODULE_3__.EVENT_ALGORITHM_IDENTIFIER,\n            longDate,\n            scope,\n            priorSignature,\n            hashedHeaders,\n            hashedPayload,\n        ].join(\"\\n\");\n        return this.signString(stringToSign, { signingDate, signingRegion: region, signingService });\n    }\n    async signString(stringToSign, { signingDate = new Date(), signingRegion, signingService } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate } = formatDate(signingDate);\n        const hash = new this.sha256(await this.getSigningKey(credentials, region, shortDate, signingService));\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(stringToSign));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    async signRequest(requestToSign, { signingDate = new Date(), signableHeaders, unsignableHeaders, signingRegion, signingService, } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const request = (0,_prepareRequest__WEBPACK_IMPORTED_MODULE_10__.prepareRequest)(requestToSign);\n        const { longDate, shortDate } = formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.AMZ_DATE_HEADER] = longDate;\n        if (credentials.sessionToken) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.TOKEN_HEADER] = credentials.sessionToken;\n        }\n        const payloadHash = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)(request, this.sha256);\n        if (!(0,_headerUtil__WEBPACK_IMPORTED_MODULE_8__.hasHeader)(_constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER, request.headers) && this.applyChecksum) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER] = payloadHash;\n        }\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        const signature = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, payloadHash));\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.AUTH_HEADER] =\n            `${_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER} ` +\n                `Credential=${credentials.accessKeyId}/${scope}, ` +\n                `SignedHeaders=${getCanonicalHeaderList(canonicalHeaders)}, ` +\n                `Signature=${signature}`;\n        return request;\n    }\n    createCanonicalRequest(request, canonicalHeaders, payloadHash) {\n        const sortedHeaders = Object.keys(canonicalHeaders).sort();\n        return `${request.method}\n${this.getCanonicalPath(request)}\n${(0,_getCanonicalQuery__WEBPACK_IMPORTED_MODULE_6__.getCanonicalQuery)(request)}\n${sortedHeaders.map((name) => `${name}:${canonicalHeaders[name]}`).join(\"\\n\")}\n\n${sortedHeaders.join(\";\")}\n${payloadHash}`;\n    }\n    async createStringToSign(longDate, credentialScope, canonicalRequest) {\n        const hash = new this.sha256();\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(canonicalRequest));\n        const hashedRequest = await hash.digest();\n        return `${_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER}\n${longDate}\n${credentialScope}\n${(0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(hashedRequest)}`;\n    }\n    getCanonicalPath({ path }) {\n        if (this.uriEscapePath) {\n            const normalizedPathSegments = [];\n            for (const pathSegment of path.split(\"/\")) {\n                if (pathSegment?.length === 0)\n                    continue;\n                if (pathSegment === \".\")\n                    continue;\n                if (pathSegment === \"..\") {\n                    normalizedPathSegments.pop();\n                }\n                else {\n                    normalizedPathSegments.push(pathSegment);\n                }\n            }\n            const normalizedPath = `${path?.startsWith(\"/\") ? \"/\" : \"\"}${normalizedPathSegments.join(\"/\")}${normalizedPathSegments.length > 0 && path?.endsWith(\"/\") ? \"/\" : \"\"}`;\n            const doubleEncoded = encodeURIComponent(normalizedPath);\n            return doubleEncoded.replace(/%2F/g, \"/\");\n        }\n        return path;\n    }\n    async getSignature(longDate, credentialScope, keyPromise, canonicalRequest) {\n        const stringToSign = await this.createStringToSign(longDate, credentialScope, canonicalRequest);\n        const hash = new this.sha256(await keyPromise);\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(stringToSign));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    getSigningKey(credentials, region, shortDate, service) {\n        return (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.getSigningKey)(this.sha256, credentials, shortDate, region, service || this.service);\n    }\n    validateResolvedCredentials(credentials) {\n        if (typeof credentials !== \"object\" ||\n            typeof credentials.accessKeyId !== \"string\" ||\n            typeof credentials.secretAccessKey !== \"string\") {\n            throw new Error(\"Resolved credential object is not valid\");\n        }\n    }\n}\nconst formatDate = (now) => {\n    const longDate = (0,_utilDate__WEBPACK_IMPORTED_MODULE_11__.iso8601)(now).replace(/[\\-:]/g, \"\");\n    return {\n        longDate,\n        shortDate: longDate.slice(0, 8),\n    };\n};\nconst getCanonicalHeaderList = (headers) => Object.keys(headers).sort().join(\";\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4: () => (/* binding */ SignatureV4)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-middleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-middleware@3.296.0/node_modules/@aws-sdk/util-middleware/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./credentialDerivation */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\");\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./getPayloadHash */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _headerUtil__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./headerUtil */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./prepareRequest */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _utilDate__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utilDate */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nclass SignatureV4 {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        this.service = service;\n        this.sha256 = sha256;\n        this.uriEscapePath = uriEscapePath;\n        this.applyChecksum = typeof applyChecksum === \"boolean\" ? applyChecksum : true;\n        this.regionProvider = (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(region);\n        this.credentialProvider = (0,_aws_sdk_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(credentials);\n    }\n    async presign(originalRequest, options = {}) {\n        const { signingDate = new Date(), expiresIn = 3600, unsignableHeaders, unhoistableHeaders, signableHeaders, signingRegion, signingService, } = options;\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { longDate, shortDate } = formatDate(signingDate);\n        if (expiresIn > _constants__WEBPACK_IMPORTED_MODULE_3__.MAX_PRESIGNED_TTL) {\n            return Promise.reject(\"Signature version 4 presigned URLs\" + \" must have an expiration date less than one week in\" + \" the future\");\n        }\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        const request = (0,_moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_9__.moveHeadersToQuery)((0,_prepareRequest__WEBPACK_IMPORTED_MODULE_10__.prepareRequest)(originalRequest), { unhoistableHeaders });\n        if (credentials.sessionToken) {\n            request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        }\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_QUERY_PARAM] = _constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.CREDENTIAL_QUERY_PARAM] = `${credentials.accessKeyId}/${scope}`;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.AMZ_DATE_QUERY_PARAM] = longDate;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.EXPIRES_QUERY_PARAM] = expiresIn.toString(10);\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.SIGNED_HEADERS_QUERY_PARAM] = getCanonicalHeaderList(canonicalHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_3__.SIGNATURE_QUERY_PARAM] = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)(originalRequest, this.sha256)));\n        return request;\n    }\n    async sign(toSign, options) {\n        if (typeof toSign === \"string\") {\n            return this.signString(toSign, options);\n        }\n        else if (toSign.headers && toSign.payload) {\n            return this.signEvent(toSign, options);\n        }\n        else {\n            return this.signRequest(toSign, options);\n        }\n    }\n    async signEvent({ headers, payload }, { signingDate = new Date(), priorSignature, signingRegion, signingService }) {\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate, longDate } = formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        const hashedPayload = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)({ headers: {}, body: payload }, this.sha256);\n        const hash = new this.sha256();\n        hash.update(headers);\n        const hashedHeaders = (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n        const stringToSign = [\n            _constants__WEBPACK_IMPORTED_MODULE_3__.EVENT_ALGORITHM_IDENTIFIER,\n            longDate,\n            scope,\n            priorSignature,\n            hashedHeaders,\n            hashedPayload,\n        ].join(\"\\n\");\n        return this.signString(stringToSign, { signingDate, signingRegion: region, signingService });\n    }\n    async signString(stringToSign, { signingDate = new Date(), signingRegion, signingService } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate } = formatDate(signingDate);\n        const hash = new this.sha256(await this.getSigningKey(credentials, region, shortDate, signingService));\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(stringToSign));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    async signRequest(requestToSign, { signingDate = new Date(), signableHeaders, unsignableHeaders, signingRegion, signingService, } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const request = (0,_prepareRequest__WEBPACK_IMPORTED_MODULE_10__.prepareRequest)(requestToSign);\n        const { longDate, shortDate } = formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.createScope)(shortDate, region, signingService ?? this.service);\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.AMZ_DATE_HEADER] = longDate;\n        if (credentials.sessionToken) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.TOKEN_HEADER] = credentials.sessionToken;\n        }\n        const payloadHash = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_7__.getPayloadHash)(request, this.sha256);\n        if (!(0,_headerUtil__WEBPACK_IMPORTED_MODULE_8__.hasHeader)(_constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER, request.headers) && this.applyChecksum) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER] = payloadHash;\n        }\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_5__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        const signature = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, payloadHash));\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_3__.AUTH_HEADER] =\n            `${_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER} ` +\n                `Credential=${credentials.accessKeyId}/${scope}, ` +\n                `SignedHeaders=${getCanonicalHeaderList(canonicalHeaders)}, ` +\n                `Signature=${signature}`;\n        return request;\n    }\n    createCanonicalRequest(request, canonicalHeaders, payloadHash) {\n        const sortedHeaders = Object.keys(canonicalHeaders).sort();\n        return `${request.method}\n${this.getCanonicalPath(request)}\n${(0,_getCanonicalQuery__WEBPACK_IMPORTED_MODULE_6__.getCanonicalQuery)(request)}\n${sortedHeaders.map((name) => `${name}:${canonicalHeaders[name]}`).join(\"\\n\")}\n\n${sortedHeaders.join(\";\")}\n${payloadHash}`;\n    }\n    async createStringToSign(longDate, credentialScope, canonicalRequest) {\n        const hash = new this.sha256();\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(canonicalRequest));\n        const hashedRequest = await hash.digest();\n        return `${_constants__WEBPACK_IMPORTED_MODULE_3__.ALGORITHM_IDENTIFIER}\n${longDate}\n${credentialScope}\n${(0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(hashedRequest)}`;\n    }\n    getCanonicalPath({ path }) {\n        if (this.uriEscapePath) {\n            const normalizedPathSegments = [];\n            for (const pathSegment of path.split(\"/\")) {\n                if (pathSegment?.length === 0)\n                    continue;\n                if (pathSegment === \".\")\n                    continue;\n                if (pathSegment === \"..\") {\n                    normalizedPathSegments.pop();\n                }\n                else {\n                    normalizedPathSegments.push(pathSegment);\n                }\n            }\n            const normalizedPath = `${path?.startsWith(\"/\") ? \"/\" : \"\"}${normalizedPathSegments.join(\"/\")}${normalizedPathSegments.length > 0 && path?.endsWith(\"/\") ? \"/\" : \"\"}`;\n            const doubleEncoded = encodeURIComponent(normalizedPath);\n            return doubleEncoded.replace(/%2F/g, \"/\");\n        }\n        return path;\n    }\n    async getSignature(longDate, credentialScope, keyPromise, canonicalRequest) {\n        const stringToSign = await this.createStringToSign(longDate, credentialScope, canonicalRequest);\n        const hash = new this.sha256(await keyPromise);\n        hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(stringToSign));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    getSigningKey(credentials, region, shortDate, service) {\n        return (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_4__.getSigningKey)(this.sha256, credentials, shortDate, region, service || this.service);\n    }\n    validateResolvedCredentials(credentials) {\n        if (typeof credentials !== \"object\" ||\n            typeof credentials.accessKeyId !== \"string\" ||\n            typeof credentials.secretAccessKey !== \"string\") {\n            throw new Error(\"Resolved credential object is not valid\");\n        }\n    }\n}\nconst formatDate = (now) => {\n    const longDate = (0,_utilDate__WEBPACK_IMPORTED_MODULE_11__.iso8601)(now).replace(/[\\-:]/g, \"\");\n    return {\n        longDate,\n        shortDate: longDate.slice(0, 8),\n    };\n};\nconst getCanonicalHeaderList = (headers) => Object.keys(headers).sort().join(\";\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cloneQuery: () => (/* binding */ cloneQuery),\n/* harmony export */   cloneRequest: () => (/* binding */ cloneRequest)\n/* harmony export */ });\nconst cloneRequest = ({ headers, query, ...rest }) => ({\n    ...rest,\n    headers: { ...headers },\n    query: query ? cloneQuery(query) : undefined,\n});\nconst cloneQuery = (query) => Object.keys(query).reduce((carry, paramName) => {\n    const param = query[paramName];\n    return {\n        ...carry,\n        [paramName]: Array.isArray(param) ? [...param] : param,\n    };\n}, {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvY2xvbmVSZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sd0JBQXdCLHlCQUF5QjtBQUN4RDtBQUNBLGVBQWUsWUFBWTtBQUMzQjtBQUNBLENBQUM7QUFDTTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2Nsb25lUmVxdWVzdC5qcz9jMGQ4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjbG9uZVJlcXVlc3QgPSAoeyBoZWFkZXJzLCBxdWVyeSwgLi4ucmVzdCB9KSA9PiAoe1xuICAgIC4uLnJlc3QsXG4gICAgaGVhZGVyczogeyAuLi5oZWFkZXJzIH0sXG4gICAgcXVlcnk6IHF1ZXJ5ID8gY2xvbmVRdWVyeShxdWVyeSkgOiB1bmRlZmluZWQsXG59KTtcbmV4cG9ydCBjb25zdCBjbG9uZVF1ZXJ5ID0gKHF1ZXJ5KSA9PiBPYmplY3Qua2V5cyhxdWVyeSkucmVkdWNlKChjYXJyeSwgcGFyYW1OYW1lKSA9PiB7XG4gICAgY29uc3QgcGFyYW0gPSBxdWVyeVtwYXJhbU5hbWVdO1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmNhcnJ5LFxuICAgICAgICBbcGFyYW1OYW1lXTogQXJyYXkuaXNBcnJheShwYXJhbSkgPyBbLi4ucGFyYW1dIDogcGFyYW0sXG4gICAgfTtcbn0sIHt9KTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cloneQuery: () => (/* binding */ cloneQuery),\n/* harmony export */   cloneRequest: () => (/* binding */ cloneRequest)\n/* harmony export */ });\nconst cloneRequest = ({ headers, query, ...rest }) => ({\n    ...rest,\n    headers: { ...headers },\n    query: query ? cloneQuery(query) : undefined,\n});\nconst cloneQuery = (query) => Object.keys(query).reduce((carry, paramName) => {\n    const param = query[paramName];\n    return {\n        ...carry,\n        [paramName]: Array.isArray(param) ? [...param] : param,\n    };\n}, {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2Nsb25lUmVxdWVzdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLHdCQUF3Qix5QkFBeUI7QUFDeEQ7QUFDQSxlQUFlLFlBQVk7QUFDM0I7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy9jbG9uZVJlcXVlc3QuanM/ZGYyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY2xvbmVSZXF1ZXN0ID0gKHsgaGVhZGVycywgcXVlcnksIC4uLnJlc3QgfSkgPT4gKHtcbiAgICAuLi5yZXN0LFxuICAgIGhlYWRlcnM6IHsgLi4uaGVhZGVycyB9LFxuICAgIHF1ZXJ5OiBxdWVyeSA/IGNsb25lUXVlcnkocXVlcnkpIDogdW5kZWZpbmVkLFxufSk7XG5leHBvcnQgY29uc3QgY2xvbmVRdWVyeSA9IChxdWVyeSkgPT4gT2JqZWN0LmtleXMocXVlcnkpLnJlZHVjZSgoY2FycnksIHBhcmFtTmFtZSkgPT4ge1xuICAgIGNvbnN0IHBhcmFtID0gcXVlcnlbcGFyYW1OYW1lXTtcbiAgICByZXR1cm4ge1xuICAgICAgICAuLi5jYXJyeSxcbiAgICAgICAgW3BhcmFtTmFtZV06IEFycmF5LmlzQXJyYXkocGFyYW0pID8gWy4uLnBhcmFtXSA6IHBhcmFtLFxuICAgIH07XG59LCB7fSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_IDENTIFIER_V4A: () => (/* binding */ ALGORITHM_IDENTIFIER_V4A),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   ALWAYS_UNSIGNABLE_HEADERS: () => (/* binding */ ALWAYS_UNSIGNABLE_HEADERS),\n/* harmony export */   AMZ_DATE_HEADER: () => (/* binding */ AMZ_DATE_HEADER),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   AUTH_HEADER: () => (/* binding */ AUTH_HEADER),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   DATE_HEADER: () => (/* binding */ DATE_HEADER),\n/* harmony export */   EVENT_ALGORITHM_IDENTIFIER: () => (/* binding */ EVENT_ALGORITHM_IDENTIFIER),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   GENERATED_HEADERS: () => (/* binding */ GENERATED_HEADERS),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   KEY_TYPE_IDENTIFIER: () => (/* binding */ KEY_TYPE_IDENTIFIER),\n/* harmony export */   MAX_CACHE_SIZE: () => (/* binding */ MAX_CACHE_SIZE),\n/* harmony export */   MAX_PRESIGNED_TTL: () => (/* binding */ MAX_PRESIGNED_TTL),\n/* harmony export */   PROXY_HEADER_PATTERN: () => (/* binding */ PROXY_HEADER_PATTERN),\n/* harmony export */   REGION_SET_PARAM: () => (/* binding */ REGION_SET_PARAM),\n/* harmony export */   SEC_HEADER_PATTERN: () => (/* binding */ SEC_HEADER_PATTERN),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNATURE_HEADER: () => (/* binding */ SIGNATURE_HEADER),\n/* harmony export */   SIGNATURE_QUERY_PARAM: () => (/* binding */ SIGNATURE_QUERY_PARAM),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   TOKEN_HEADER: () => (/* binding */ TOKEN_HEADER),\n/* harmony export */   TOKEN_QUERY_PARAM: () => (/* binding */ TOKEN_QUERY_PARAM),\n/* harmony export */   UNSIGNABLE_PATTERNS: () => (/* binding */ UNSIGNABLE_PATTERNS),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst SIGNATURE_QUERY_PARAM = \"X-Amz-Signature\";\nconst TOKEN_QUERY_PARAM = \"X-Amz-Security-Token\";\nconst REGION_SET_PARAM = \"X-Amz-Region-Set\";\nconst AUTH_HEADER = \"authorization\";\nconst AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nconst DATE_HEADER = \"date\";\nconst GENERATED_HEADERS = [AUTH_HEADER, AMZ_DATE_HEADER, DATE_HEADER];\nconst SIGNATURE_HEADER = SIGNATURE_QUERY_PARAM.toLowerCase();\nconst SHA256_HEADER = \"x-amz-content-sha256\";\nconst TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\nconst HOST_HEADER = \"host\";\nconst ALWAYS_UNSIGNABLE_HEADERS = {\n    authorization: true,\n    \"cache-control\": true,\n    connection: true,\n    expect: true,\n    from: true,\n    \"keep-alive\": true,\n    \"max-forwards\": true,\n    pragma: true,\n    referer: true,\n    te: true,\n    trailer: true,\n    \"transfer-encoding\": true,\n    upgrade: true,\n    \"user-agent\": true,\n    \"x-amzn-trace-id\": true,\n};\nconst PROXY_HEADER_PATTERN = /^proxy-/;\nconst SEC_HEADER_PATTERN = /^sec-/;\nconst UNSIGNABLE_PATTERNS = [/^proxy-/i, /^sec-/i];\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\nconst ALGORITHM_IDENTIFIER_V4A = \"AWS4-ECDSA-P256-SHA256\";\nconst EVENT_ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256-PAYLOAD\";\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst MAX_CACHE_SIZE = 50;\nconst KEY_TYPE_IDENTIFIER = \"aws4_request\";\nconst MAX_PRESIGNED_TTL = 60 * 60 * 24 * 7;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_IDENTIFIER_V4A: () => (/* binding */ ALGORITHM_IDENTIFIER_V4A),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   ALWAYS_UNSIGNABLE_HEADERS: () => (/* binding */ ALWAYS_UNSIGNABLE_HEADERS),\n/* harmony export */   AMZ_DATE_HEADER: () => (/* binding */ AMZ_DATE_HEADER),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   AUTH_HEADER: () => (/* binding */ AUTH_HEADER),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   DATE_HEADER: () => (/* binding */ DATE_HEADER),\n/* harmony export */   EVENT_ALGORITHM_IDENTIFIER: () => (/* binding */ EVENT_ALGORITHM_IDENTIFIER),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   GENERATED_HEADERS: () => (/* binding */ GENERATED_HEADERS),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   KEY_TYPE_IDENTIFIER: () => (/* binding */ KEY_TYPE_IDENTIFIER),\n/* harmony export */   MAX_CACHE_SIZE: () => (/* binding */ MAX_CACHE_SIZE),\n/* harmony export */   MAX_PRESIGNED_TTL: () => (/* binding */ MAX_PRESIGNED_TTL),\n/* harmony export */   PROXY_HEADER_PATTERN: () => (/* binding */ PROXY_HEADER_PATTERN),\n/* harmony export */   REGION_SET_PARAM: () => (/* binding */ REGION_SET_PARAM),\n/* harmony export */   SEC_HEADER_PATTERN: () => (/* binding */ SEC_HEADER_PATTERN),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNATURE_HEADER: () => (/* binding */ SIGNATURE_HEADER),\n/* harmony export */   SIGNATURE_QUERY_PARAM: () => (/* binding */ SIGNATURE_QUERY_PARAM),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   TOKEN_HEADER: () => (/* binding */ TOKEN_HEADER),\n/* harmony export */   TOKEN_QUERY_PARAM: () => (/* binding */ TOKEN_QUERY_PARAM),\n/* harmony export */   UNSIGNABLE_PATTERNS: () => (/* binding */ UNSIGNABLE_PATTERNS),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst SIGNATURE_QUERY_PARAM = \"X-Amz-Signature\";\nconst TOKEN_QUERY_PARAM = \"X-Amz-Security-Token\";\nconst REGION_SET_PARAM = \"X-Amz-Region-Set\";\nconst AUTH_HEADER = \"authorization\";\nconst AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nconst DATE_HEADER = \"date\";\nconst GENERATED_HEADERS = [AUTH_HEADER, AMZ_DATE_HEADER, DATE_HEADER];\nconst SIGNATURE_HEADER = SIGNATURE_QUERY_PARAM.toLowerCase();\nconst SHA256_HEADER = \"x-amz-content-sha256\";\nconst TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\nconst HOST_HEADER = \"host\";\nconst ALWAYS_UNSIGNABLE_HEADERS = {\n    authorization: true,\n    \"cache-control\": true,\n    connection: true,\n    expect: true,\n    from: true,\n    \"keep-alive\": true,\n    \"max-forwards\": true,\n    pragma: true,\n    referer: true,\n    te: true,\n    trailer: true,\n    \"transfer-encoding\": true,\n    upgrade: true,\n    \"user-agent\": true,\n    \"x-amzn-trace-id\": true,\n};\nconst PROXY_HEADER_PATTERN = /^proxy-/;\nconst SEC_HEADER_PATTERN = /^sec-/;\nconst UNSIGNABLE_PATTERNS = [/^proxy-/i, /^sec-/i];\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\nconst ALGORITHM_IDENTIFIER_V4A = \"AWS4-ECDSA-P256-SHA256\";\nconst EVENT_ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256-PAYLOAD\";\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst MAX_CACHE_SIZE = 50;\nconst KEY_TYPE_IDENTIFIER = \"aws4_request\";\nconst MAX_PRESIGNED_TTL = 60 * 60 * 24 * 7;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCredentialCache: () => (/* binding */ clearCredentialCache),\n/* harmony export */   createScope: () => (/* binding */ createScope),\n/* harmony export */   getSigningKey: () => (/* binding */ getSigningKey)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\n\nconst signingKeyCache = {};\nconst cacheQueue = [];\nconst createScope = (shortDate, region, service) => `${shortDate}/${region}/${service}/${_constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER}`;\nconst getSigningKey = async (sha256Constructor, credentials, shortDate, region, service) => {\n    const credsHash = await hmac(sha256Constructor, credentials.secretAccessKey, credentials.accessKeyId);\n    const cacheKey = `${shortDate}:${region}:${service}:${(0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(credsHash)}:${credentials.sessionToken}`;\n    if (cacheKey in signingKeyCache) {\n        return signingKeyCache[cacheKey];\n    }\n    cacheQueue.push(cacheKey);\n    while (cacheQueue.length > _constants__WEBPACK_IMPORTED_MODULE_2__.MAX_CACHE_SIZE) {\n        delete signingKeyCache[cacheQueue.shift()];\n    }\n    let key = `AWS4${credentials.secretAccessKey}`;\n    for (const signable of [shortDate, region, service, _constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER]) {\n        key = await hmac(sha256Constructor, key, signable);\n    }\n    return (signingKeyCache[cacheKey] = key);\n};\nconst clearCredentialCache = () => {\n    cacheQueue.length = 0;\n    Object.keys(signingKeyCache).forEach((cacheKey) => {\n        delete signingKeyCache[cacheKey];\n    });\n};\nconst hmac = (ctor, secret, data) => {\n    const hash = new ctor(secret);\n    hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(data));\n    return hash.digest();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCredentialCache: () => (/* binding */ clearCredentialCache),\n/* harmony export */   createScope: () => (/* binding */ createScope),\n/* harmony export */   getSigningKey: () => (/* binding */ getSigningKey)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\n\nconst signingKeyCache = {};\nconst cacheQueue = [];\nconst createScope = (shortDate, region, service) => `${shortDate}/${region}/${service}/${_constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER}`;\nconst getSigningKey = async (sha256Constructor, credentials, shortDate, region, service) => {\n    const credsHash = await hmac(sha256Constructor, credentials.secretAccessKey, credentials.accessKeyId);\n    const cacheKey = `${shortDate}:${region}:${service}:${(0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(credsHash)}:${credentials.sessionToken}`;\n    if (cacheKey in signingKeyCache) {\n        return signingKeyCache[cacheKey];\n    }\n    cacheQueue.push(cacheKey);\n    while (cacheQueue.length > _constants__WEBPACK_IMPORTED_MODULE_2__.MAX_CACHE_SIZE) {\n        delete signingKeyCache[cacheQueue.shift()];\n    }\n    let key = `AWS4${credentials.secretAccessKey}`;\n    for (const signable of [shortDate, region, service, _constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER]) {\n        key = await hmac(sha256Constructor, key, signable);\n    }\n    return (signingKeyCache[cacheKey] = key);\n};\nconst clearCredentialCache = () => {\n    cacheQueue.length = 0;\n    Object.keys(signingKeyCache).forEach((cacheKey) => {\n        delete signingKeyCache[cacheKey];\n    });\n};\nconst hmac = (ctor, secret, data) => {\n    const hash = new ctor(secret);\n    hash.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(data));\n    return hash.digest();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalHeaders: () => (/* binding */ getCanonicalHeaders)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\nconst getCanonicalHeaders = ({ headers }, unsignableHeaders, signableHeaders) => {\n    const canonical = {};\n    for (const headerName of Object.keys(headers).sort()) {\n        if (headers[headerName] == undefined) {\n            continue;\n        }\n        const canonicalHeaderName = headerName.toLowerCase();\n        if (canonicalHeaderName in _constants__WEBPACK_IMPORTED_MODULE_0__.ALWAYS_UNSIGNABLE_HEADERS ||\n            unsignableHeaders?.has(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.PROXY_HEADER_PATTERN.test(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.SEC_HEADER_PATTERN.test(canonicalHeaderName)) {\n            if (!signableHeaders || (signableHeaders && !signableHeaders.has(canonicalHeaderName))) {\n                continue;\n            }\n        }\n        canonical[canonicalHeaderName] = headers[headerName].trim().replace(/\\s+/g, \" \");\n    }\n    return canonical;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalHeaders: () => (/* binding */ getCanonicalHeaders)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\nconst getCanonicalHeaders = ({ headers }, unsignableHeaders, signableHeaders) => {\n    const canonical = {};\n    for (const headerName of Object.keys(headers).sort()) {\n        if (headers[headerName] == undefined) {\n            continue;\n        }\n        const canonicalHeaderName = headerName.toLowerCase();\n        if (canonicalHeaderName in _constants__WEBPACK_IMPORTED_MODULE_0__.ALWAYS_UNSIGNABLE_HEADERS ||\n            unsignableHeaders?.has(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.PROXY_HEADER_PATTERN.test(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.SEC_HEADER_PATTERN.test(canonicalHeaderName)) {\n            if (!signableHeaders || (signableHeaders && !signableHeaders.has(canonicalHeaderName))) {\n                continue;\n            }\n        }\n        canonical[canonicalHeaderName] = headers[headerName].trim().replace(/\\s+/g, \" \");\n    }\n    return canonical;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2dldENhbm9uaWNhbEhlYWRlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0c7QUFDM0YsK0JBQStCLFNBQVM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGlFQUF5QjtBQUM1RDtBQUNBLFlBQVksNERBQW9CO0FBQ2hDLFlBQVksMERBQWtCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2dldENhbm9uaWNhbEhlYWRlcnMuanM/NWRmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBTFdBWVNfVU5TSUdOQUJMRV9IRUFERVJTLCBQUk9YWV9IRUFERVJfUEFUVEVSTiwgU0VDX0hFQURFUl9QQVRURVJOIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgY29uc3QgZ2V0Q2Fub25pY2FsSGVhZGVycyA9ICh7IGhlYWRlcnMgfSwgdW5zaWduYWJsZUhlYWRlcnMsIHNpZ25hYmxlSGVhZGVycykgPT4ge1xuICAgIGNvbnN0IGNhbm9uaWNhbCA9IHt9O1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKS5zb3J0KCkpIHtcbiAgICAgICAgaWYgKGhlYWRlcnNbaGVhZGVyTmFtZV0gPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjYW5vbmljYWxIZWFkZXJOYW1lID0gaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBpZiAoY2Fub25pY2FsSGVhZGVyTmFtZSBpbiBBTFdBWVNfVU5TSUdOQUJMRV9IRUFERVJTIHx8XG4gICAgICAgICAgICB1bnNpZ25hYmxlSGVhZGVycz8uaGFzKGNhbm9uaWNhbEhlYWRlck5hbWUpIHx8XG4gICAgICAgICAgICBQUk9YWV9IRUFERVJfUEFUVEVSTi50ZXN0KGNhbm9uaWNhbEhlYWRlck5hbWUpIHx8XG4gICAgICAgICAgICBTRUNfSEVBREVSX1BBVFRFUk4udGVzdChjYW5vbmljYWxIZWFkZXJOYW1lKSkge1xuICAgICAgICAgICAgaWYgKCFzaWduYWJsZUhlYWRlcnMgfHwgKHNpZ25hYmxlSGVhZGVycyAmJiAhc2lnbmFibGVIZWFkZXJzLmhhcyhjYW5vbmljYWxIZWFkZXJOYW1lKSkpIHtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjYW5vbmljYWxbY2Fub25pY2FsSGVhZGVyTmFtZV0gPSBoZWFkZXJzW2hlYWRlck5hbWVdLnRyaW0oKS5yZXBsYWNlKC9cXHMrL2csIFwiIFwiKTtcbiAgICB9XG4gICAgcmV0dXJuIGNhbm9uaWNhbDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalQuery: () => (/* binding */ getCanonicalQuery)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-uri-escape */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-uri-escape@3.295.0/node_modules/@aws-sdk/util-uri-escape/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\nconst getCanonicalQuery = ({ query = {} }) => {\n    const keys = [];\n    const serialized = {};\n    for (const key of Object.keys(query).sort()) {\n        if (key.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNATURE_HEADER) {\n            continue;\n        }\n        keys.push(key);\n        const value = query[key];\n        if (typeof value === \"string\") {\n            serialized[key] = `${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(key)}=${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`;\n        }\n        else if (Array.isArray(value)) {\n            serialized[key] = value\n                .slice(0)\n                .sort()\n                .reduce((encoded, value) => encoded.concat([`${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(key)}=${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`]), [])\n                .join(\"&\");\n        }\n    }\n    return keys\n        .map((key) => serialized[key])\n        .filter((serialized) => serialized)\n        .join(\"&\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalQuery: () => (/* binding */ getCanonicalQuery)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-uri-escape */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-uri-escape@3.295.0/node_modules/@aws-sdk/util-uri-escape/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\nconst getCanonicalQuery = ({ query = {} }) => {\n    const keys = [];\n    const serialized = {};\n    for (const key of Object.keys(query).sort()) {\n        if (key.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNATURE_HEADER) {\n            continue;\n        }\n        keys.push(key);\n        const value = query[key];\n        if (typeof value === \"string\") {\n            serialized[key] = `${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(key)}=${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`;\n        }\n        else if (Array.isArray(value)) {\n            serialized[key] = value\n                .slice(0)\n                .sort()\n                .reduce((encoded, value) => encoded.concat([`${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(key)}=${(0,_aws_sdk_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`]), [])\n                .join(\"&\");\n        }\n    }\n    return keys\n        .map((key) => serialized[key])\n        .filter((serialized) => serialized)\n        .join(\"&\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPayloadHash: () => (/* binding */ getPayloadHash)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/is-array-buffer */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\n\n\nconst getPayloadHash = async ({ headers, body }, hashConstructor) => {\n    for (const headerName of Object.keys(headers)) {\n        if (headerName.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER) {\n            return headers[headerName];\n        }\n    }\n    if (body == undefined) {\n        return \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\";\n    }\n    else if (typeof body === \"string\" || ArrayBuffer.isView(body) || (0,_aws_sdk_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__.isArrayBuffer)(body)) {\n        const hashCtor = new hashConstructor();\n        hashCtor.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(body));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__.toHex)(await hashCtor.digest());\n    }\n    return _constants__WEBPACK_IMPORTED_MODULE_3__.UNSIGNED_PAYLOAD;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvZ2V0UGF5bG9hZEhhc2guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUQ7QUFDTjtBQUNEO0FBQ1k7QUFDdkQsZ0NBQWdDLGVBQWU7QUFDdEQ7QUFDQSx5Q0FBeUMscURBQWE7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUVBQXFFLHVFQUFhO0FBQ2xGO0FBQ0Esd0JBQXdCLGdFQUFZO0FBQ3BDLGVBQWUsaUVBQUs7QUFDcEI7QUFDQSxXQUFXLHdEQUFnQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvZ2V0UGF5bG9hZEhhc2guanM/MzZhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0FycmF5QnVmZmVyIH0gZnJvbSBcIkBhd3Mtc2RrL2lzLWFycmF5LWJ1ZmZlclwiO1xuaW1wb3J0IHsgdG9IZXggfSBmcm9tIFwiQGF3cy1zZGsvdXRpbC1oZXgtZW5jb2RpbmdcIjtcbmltcG9ydCB7IHRvVWludDhBcnJheSB9IGZyb20gXCJAYXdzLXNkay91dGlsLXV0ZjhcIjtcbmltcG9ydCB7IFNIQTI1Nl9IRUFERVIsIFVOU0lHTkVEX1BBWUxPQUQgfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCBjb25zdCBnZXRQYXlsb2FkSGFzaCA9IGFzeW5jICh7IGhlYWRlcnMsIGJvZHkgfSwgaGFzaENvbnN0cnVjdG9yKSA9PiB7XG4gICAgZm9yIChjb25zdCBoZWFkZXJOYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChoZWFkZXJOYW1lLnRvTG93ZXJDYXNlKCkgPT09IFNIQTI1Nl9IRUFERVIpIHtcbiAgICAgICAgICAgIHJldHVybiBoZWFkZXJzW2hlYWRlck5hbWVdO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChib2R5ID09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gXCJlM2IwYzQ0Mjk4ZmMxYzE0OWFmYmY0Yzg5OTZmYjkyNDI3YWU0MWU0NjQ5YjkzNGNhNDk1OTkxYjc4NTJiODU1XCI7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBib2R5ID09PSBcInN0cmluZ1wiIHx8IEFycmF5QnVmZmVyLmlzVmlldyhib2R5KSB8fCBpc0FycmF5QnVmZmVyKGJvZHkpKSB7XG4gICAgICAgIGNvbnN0IGhhc2hDdG9yID0gbmV3IGhhc2hDb25zdHJ1Y3RvcigpO1xuICAgICAgICBoYXNoQ3Rvci51cGRhdGUodG9VaW50OEFycmF5KGJvZHkpKTtcbiAgICAgICAgcmV0dXJuIHRvSGV4KGF3YWl0IGhhc2hDdG9yLmRpZ2VzdCgpKTtcbiAgICB9XG4gICAgcmV0dXJuIFVOU0lHTkVEX1BBWUxPQUQ7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPayloadHash: () => (/* binding */ getPayloadHash)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/is-array-buffer */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+is-array-buffer@3.295.0/node_modules/@aws-sdk/is-array-buffer/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-hex-encoding@3.295.0/node_modules/@aws-sdk/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-utf8@3.295.0/node_modules/@aws-sdk/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\n\n\nconst getPayloadHash = async ({ headers, body }, hashConstructor) => {\n    for (const headerName of Object.keys(headers)) {\n        if (headerName.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER) {\n            return headers[headerName];\n        }\n    }\n    if (body == undefined) {\n        return \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\";\n    }\n    else if (typeof body === \"string\" || ArrayBuffer.isView(body) || (0,_aws_sdk_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__.isArrayBuffer)(body)) {\n        const hashCtor = new hashConstructor();\n        hashCtor.update((0,_aws_sdk_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(body));\n        return (0,_aws_sdk_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__.toHex)(await hashCtor.digest());\n    }\n    return _constants__WEBPACK_IMPORTED_MODULE_3__.UNSIGNED_PAYLOAD;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2dldFBheWxvYWRIYXNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlEO0FBQ047QUFDRDtBQUNZO0FBQ3ZELGdDQUFnQyxlQUFlO0FBQ3REO0FBQ0EseUNBQXlDLHFEQUFhO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFFQUFxRSx1RUFBYTtBQUNsRjtBQUNBLHdCQUF3QixnRUFBWTtBQUNwQyxlQUFlLGlFQUFLO0FBQ3BCO0FBQ0EsV0FBVyx3REFBZ0I7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2dldFBheWxvYWRIYXNoLmpzPzQ1NzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBcnJheUJ1ZmZlciB9IGZyb20gXCJAYXdzLXNkay9pcy1hcnJheS1idWZmZXJcIjtcbmltcG9ydCB7IHRvSGV4IH0gZnJvbSBcIkBhd3Mtc2RrL3V0aWwtaGV4LWVuY29kaW5nXCI7XG5pbXBvcnQgeyB0b1VpbnQ4QXJyYXkgfSBmcm9tIFwiQGF3cy1zZGsvdXRpbC11dGY4XCI7XG5pbXBvcnQgeyBTSEEyNTZfSEVBREVSLCBVTlNJR05FRF9QQVlMT0FEIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgY29uc3QgZ2V0UGF5bG9hZEhhc2ggPSBhc3luYyAoeyBoZWFkZXJzLCBib2R5IH0sIGhhc2hDb25zdHJ1Y3RvcikgPT4ge1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKSkge1xuICAgICAgICBpZiAoaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpID09PSBTSEEyNTZfSEVBREVSKSB7XG4gICAgICAgICAgICByZXR1cm4gaGVhZGVyc1toZWFkZXJOYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoYm9keSA9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIFwiZTNiMGM0NDI5OGZjMWMxNDlhZmJmNGM4OTk2ZmI5MjQyN2FlNDFlNDY0OWI5MzRjYTQ5NTk5MWI3ODUyYjg1NVwiO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgYm9keSA9PT0gXCJzdHJpbmdcIiB8fCBBcnJheUJ1ZmZlci5pc1ZpZXcoYm9keSkgfHwgaXNBcnJheUJ1ZmZlcihib2R5KSkge1xuICAgICAgICBjb25zdCBoYXNoQ3RvciA9IG5ldyBoYXNoQ29uc3RydWN0b3IoKTtcbiAgICAgICAgaGFzaEN0b3IudXBkYXRlKHRvVWludDhBcnJheShib2R5KSk7XG4gICAgICAgIHJldHVybiB0b0hleChhd2FpdCBoYXNoQ3Rvci5kaWdlc3QoKSk7XG4gICAgfVxuICAgIHJldHVybiBVTlNJR05FRF9QQVlMT0FEO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteHeader: () => (/* binding */ deleteHeader),\n/* harmony export */   getHeaderValue: () => (/* binding */ getHeaderValue),\n/* harmony export */   hasHeader: () => (/* binding */ hasHeader)\n/* harmony export */ });\nconst hasHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return true;\n        }\n    }\n    return false;\n};\nconst getHeaderValue = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return headers[headerName];\n        }\n    }\n    return undefined;\n};\nconst deleteHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            delete headers[headerName];\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaGVhZGVyVXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy9oZWFkZXJVdGlsLmpzPzBhNjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGhhc0hlYWRlciA9IChzb3VnaHRIZWFkZXIsIGhlYWRlcnMpID0+IHtcbiAgICBzb3VnaHRIZWFkZXIgPSBzb3VnaHRIZWFkZXIudG9Mb3dlckNhc2UoKTtcbiAgICBmb3IgKGNvbnN0IGhlYWRlck5hbWUgb2YgT2JqZWN0LmtleXMoaGVhZGVycykpIHtcbiAgICAgICAgaWYgKHNvdWdodEhlYWRlciA9PT0gaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59O1xuZXhwb3J0IGNvbnN0IGdldEhlYWRlclZhbHVlID0gKHNvdWdodEhlYWRlciwgaGVhZGVycykgPT4ge1xuICAgIHNvdWdodEhlYWRlciA9IHNvdWdodEhlYWRlci50b0xvd2VyQ2FzZSgpO1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKSkge1xuICAgICAgICBpZiAoc291Z2h0SGVhZGVyID09PSBoZWFkZXJOYW1lLnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgICAgIHJldHVybiBoZWFkZXJzW2hlYWRlck5hbWVdO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB1bmRlZmluZWQ7XG59O1xuZXhwb3J0IGNvbnN0IGRlbGV0ZUhlYWRlciA9IChzb3VnaHRIZWFkZXIsIGhlYWRlcnMpID0+IHtcbiAgICBzb3VnaHRIZWFkZXIgPSBzb3VnaHRIZWFkZXIudG9Mb3dlckNhc2UoKTtcbiAgICBmb3IgKGNvbnN0IGhlYWRlck5hbWUgb2YgT2JqZWN0LmtleXMoaGVhZGVycykpIHtcbiAgICAgICAgaWYgKHNvdWdodEhlYWRlciA9PT0gaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICAgICAgICBkZWxldGUgaGVhZGVyc1toZWFkZXJOYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteHeader: () => (/* binding */ deleteHeader),\n/* harmony export */   getHeaderValue: () => (/* binding */ getHeaderValue),\n/* harmony export */   hasHeader: () => (/* binding */ hasHeader)\n/* harmony export */ });\nconst hasHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return true;\n        }\n    }\n    return false;\n};\nconst getHeaderValue = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return headers[headerName];\n        }\n    }\n    return undefined;\n};\nconst deleteHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            delete headers[headerName];\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2hlYWRlclV0aWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaGVhZGVyVXRpbC5qcz9jYTkwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBoYXNIZWFkZXIgPSAoc291Z2h0SGVhZGVyLCBoZWFkZXJzKSA9PiB7XG4gICAgc291Z2h0SGVhZGVyID0gc291Z2h0SGVhZGVyLnRvTG93ZXJDYXNlKCk7XG4gICAgZm9yIChjb25zdCBoZWFkZXJOYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChzb3VnaHRIZWFkZXIgPT09IGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufTtcbmV4cG9ydCBjb25zdCBnZXRIZWFkZXJWYWx1ZSA9IChzb3VnaHRIZWFkZXIsIGhlYWRlcnMpID0+IHtcbiAgICBzb3VnaHRIZWFkZXIgPSBzb3VnaHRIZWFkZXIudG9Mb3dlckNhc2UoKTtcbiAgICBmb3IgKGNvbnN0IGhlYWRlck5hbWUgb2YgT2JqZWN0LmtleXMoaGVhZGVycykpIHtcbiAgICAgICAgaWYgKHNvdWdodEhlYWRlciA9PT0gaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICAgICAgICByZXR1cm4gaGVhZGVyc1toZWFkZXJOYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufTtcbmV4cG9ydCBjb25zdCBkZWxldGVIZWFkZXIgPSAoc291Z2h0SGVhZGVyLCBoZWFkZXJzKSA9PiB7XG4gICAgc291Z2h0SGVhZGVyID0gc291Z2h0SGVhZGVyLnRvTG93ZXJDYXNlKCk7XG4gICAgZm9yIChjb25zdCBoZWFkZXJOYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChzb3VnaHRIZWFkZXIgPT09IGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSkge1xuICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnNbaGVhZGVyTmFtZV07XG4gICAgICAgIH1cbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/headerUtil.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalHeaders: () => (/* reexport safe */ _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_1__.getCanonicalHeaders),\n/* harmony export */   getCanonicalQuery: () => (/* reexport safe */ _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_2__.getCanonicalQuery),\n/* harmony export */   getPayloadHash: () => (/* reexport safe */ _getPayloadHash__WEBPACK_IMPORTED_MODULE_3__.getPayloadHash),\n/* harmony export */   moveHeadersToQuery: () => (/* reexport safe */ _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_4__.moveHeadersToQuery),\n/* harmony export */   prepareRequest: () => (/* reexport safe */ _prepareRequest__WEBPACK_IMPORTED_MODULE_5__.prepareRequest)\n/* harmony export */ });\n/* harmony import */ var _SignatureV4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4 */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _SignatureV4__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"getCanonicalHeaders\",\"getCanonicalQuery\",\"getPayloadHash\",\"moveHeadersToQuery\",\"prepareRequest\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _SignatureV4__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getPayloadHash */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prepareRequest */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./credentialDerivation */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__) if([\"default\",\"getCanonicalHeaders\",\"getCanonicalQuery\",\"getPayloadHash\",\"moveHeadersToQuery\",\"prepareRequest\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBQzhCO0FBQ0o7QUFDTjtBQUNRO0FBQ1I7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaW5kZXguanM/NTFkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9TaWduYXR1cmVWNFwiO1xuZXhwb3J0IHsgZ2V0Q2Fub25pY2FsSGVhZGVycyB9IGZyb20gXCIuL2dldENhbm9uaWNhbEhlYWRlcnNcIjtcbmV4cG9ydCB7IGdldENhbm9uaWNhbFF1ZXJ5IH0gZnJvbSBcIi4vZ2V0Q2Fub25pY2FsUXVlcnlcIjtcbmV4cG9ydCB7IGdldFBheWxvYWRIYXNoIH0gZnJvbSBcIi4vZ2V0UGF5bG9hZEhhc2hcIjtcbmV4cG9ydCB7IG1vdmVIZWFkZXJzVG9RdWVyeSB9IGZyb20gXCIuL21vdmVIZWFkZXJzVG9RdWVyeVwiO1xuZXhwb3J0IHsgcHJlcGFyZVJlcXVlc3QgfSBmcm9tIFwiLi9wcmVwYXJlUmVxdWVzdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vY3JlZGVudGlhbERlcml2YXRpb25cIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4: () => (/* reexport safe */ _SignatureV4__WEBPACK_IMPORTED_MODULE_0__.SignatureV4),\n/* harmony export */   clearCredentialCache: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__.clearCredentialCache),\n/* harmony export */   createScope: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__.createScope),\n/* harmony export */   getCanonicalHeaders: () => (/* reexport safe */ _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_1__.getCanonicalHeaders),\n/* harmony export */   getCanonicalQuery: () => (/* reexport safe */ _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_2__.getCanonicalQuery),\n/* harmony export */   getPayloadHash: () => (/* reexport safe */ _getPayloadHash__WEBPACK_IMPORTED_MODULE_3__.getPayloadHash),\n/* harmony export */   getSigningKey: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__.getSigningKey),\n/* harmony export */   moveHeadersToQuery: () => (/* reexport safe */ _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_4__.moveHeadersToQuery),\n/* harmony export */   prepareRequest: () => (/* reexport safe */ _prepareRequest__WEBPACK_IMPORTED_MODULE_5__.prepareRequest)\n/* harmony export */ });\n/* harmony import */ var _SignatureV4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/SignatureV4.js\");\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getPayloadHash */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prepareRequest */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./credentialDerivation */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/credentialDerivation.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDOEI7QUFDSjtBQUNOO0FBQ1E7QUFDUjtBQUNYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy9pbmRleC5qcz9kZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL1NpZ25hdHVyZVY0XCI7XG5leHBvcnQgeyBnZXRDYW5vbmljYWxIZWFkZXJzIH0gZnJvbSBcIi4vZ2V0Q2Fub25pY2FsSGVhZGVyc1wiO1xuZXhwb3J0IHsgZ2V0Q2Fub25pY2FsUXVlcnkgfSBmcm9tIFwiLi9nZXRDYW5vbmljYWxRdWVyeVwiO1xuZXhwb3J0IHsgZ2V0UGF5bG9hZEhhc2ggfSBmcm9tIFwiLi9nZXRQYXlsb2FkSGFzaFwiO1xuZXhwb3J0IHsgbW92ZUhlYWRlcnNUb1F1ZXJ5IH0gZnJvbSBcIi4vbW92ZUhlYWRlcnNUb1F1ZXJ5XCI7XG5leHBvcnQgeyBwcmVwYXJlUmVxdWVzdCB9IGZyb20gXCIuL3ByZXBhcmVSZXF1ZXN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jcmVkZW50aWFsRGVyaXZhdGlvblwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveHeadersToQuery: () => (/* binding */ moveHeadersToQuery)\n/* harmony export */ });\n/* harmony import */ var _cloneRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cloneRequest */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\");\n\nconst moveHeadersToQuery = (request, options = {}) => {\n    const { headers, query = {} } = typeof request.clone === \"function\" ? request.clone() : (0,_cloneRequest__WEBPACK_IMPORTED_MODULE_0__.cloneRequest)(request);\n    for (const name of Object.keys(headers)) {\n        const lname = name.toLowerCase();\n        if (lname.slice(0, 6) === \"x-amz-\" && !options.unhoistableHeaders?.has(lname)) {\n            query[name] = headers[name];\n            delete headers[name];\n        }\n    }\n    return {\n        ...request,\n        headers,\n        query,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvbW92ZUhlYWRlcnNUb1F1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDO0FBQ3ZDLGlEQUFpRDtBQUN4RCxZQUFZLHNCQUFzQiwwREFBMEQsMkRBQVk7QUFDeEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL21vdmVIZWFkZXJzVG9RdWVyeS5qcz85OTcxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsb25lUmVxdWVzdCB9IGZyb20gXCIuL2Nsb25lUmVxdWVzdFwiO1xuZXhwb3J0IGNvbnN0IG1vdmVIZWFkZXJzVG9RdWVyeSA9IChyZXF1ZXN0LCBvcHRpb25zID0ge30pID0+IHtcbiAgICBjb25zdCB7IGhlYWRlcnMsIHF1ZXJ5ID0ge30gfSA9IHR5cGVvZiByZXF1ZXN0LmNsb25lID09PSBcImZ1bmN0aW9uXCIgPyByZXF1ZXN0LmNsb25lKCkgOiBjbG9uZVJlcXVlc3QocmVxdWVzdCk7XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGNvbnN0IGxuYW1lID0gbmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBpZiAobG5hbWUuc2xpY2UoMCwgNikgPT09IFwieC1hbXotXCIgJiYgIW9wdGlvbnMudW5ob2lzdGFibGVIZWFkZXJzPy5oYXMobG5hbWUpKSB7XG4gICAgICAgICAgICBxdWVyeVtuYW1lXSA9IGhlYWRlcnNbbmFtZV07XG4gICAgICAgICAgICBkZWxldGUgaGVhZGVyc1tuYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICAuLi5yZXF1ZXN0LFxuICAgICAgICBoZWFkZXJzLFxuICAgICAgICBxdWVyeSxcbiAgICB9O1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveHeadersToQuery: () => (/* binding */ moveHeadersToQuery)\n/* harmony export */ });\n/* harmony import */ var _cloneRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cloneRequest */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\");\n\nconst moveHeadersToQuery = (request, options = {}) => {\n    const { headers, query = {} } = typeof request.clone === \"function\" ? request.clone() : (0,_cloneRequest__WEBPACK_IMPORTED_MODULE_0__.cloneRequest)(request);\n    for (const name of Object.keys(headers)) {\n        const lname = name.toLowerCase();\n        if (lname.slice(0, 6) === \"x-amz-\" && !options.unhoistableHeaders?.has(lname)) {\n            query[name] = headers[name];\n            delete headers[name];\n        }\n    }\n    return {\n        ...request,\n        headers,\n        query,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL21vdmVIZWFkZXJzVG9RdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUN2QyxpREFBaUQ7QUFDeEQsWUFBWSxzQkFBc0IsMERBQTBELDJEQUFZO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy9tb3ZlSGVhZGVyc1RvUXVlcnkuanM/OTdlYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbG9uZVJlcXVlc3QgfSBmcm9tIFwiLi9jbG9uZVJlcXVlc3RcIjtcbmV4cG9ydCBjb25zdCBtb3ZlSGVhZGVyc1RvUXVlcnkgPSAocmVxdWVzdCwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgY29uc3QgeyBoZWFkZXJzLCBxdWVyeSA9IHt9IH0gPSB0eXBlb2YgcmVxdWVzdC5jbG9uZSA9PT0gXCJmdW5jdGlvblwiID8gcmVxdWVzdC5jbG9uZSgpIDogY2xvbmVSZXF1ZXN0KHJlcXVlc3QpO1xuICAgIGZvciAoY29uc3QgbmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKSkge1xuICAgICAgICBjb25zdCBsbmFtZSA9IG5hbWUudG9Mb3dlckNhc2UoKTtcbiAgICAgICAgaWYgKGxuYW1lLnNsaWNlKDAsIDYpID09PSBcIngtYW16LVwiICYmICFvcHRpb25zLnVuaG9pc3RhYmxlSGVhZGVycz8uaGFzKGxuYW1lKSkge1xuICAgICAgICAgICAgcXVlcnlbbmFtZV0gPSBoZWFkZXJzW25hbWVdO1xuICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnNbbmFtZV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgLi4ucmVxdWVzdCxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgICAgcXVlcnksXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/moveHeadersToQuery.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareRequest: () => (/* binding */ prepareRequest)\n/* harmony export */ });\n/* harmony import */ var _cloneRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cloneRequest */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\nconst prepareRequest = (request) => {\n    request = typeof request.clone === \"function\" ? request.clone() : (0,_cloneRequest__WEBPACK_IMPORTED_MODULE_0__.cloneRequest)(request);\n    for (const headerName of Object.keys(request.headers)) {\n        if (_constants__WEBPACK_IMPORTED_MODULE_1__.GENERATED_HEADERS.indexOf(headerName.toLowerCase()) > -1) {\n            delete request.headers[headerName];\n        }\n    }\n    return request;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvcHJlcGFyZVJlcXVlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBQ0U7QUFDekM7QUFDUCxzRUFBc0UsMkRBQVk7QUFDbEY7QUFDQSxZQUFZLHlEQUFpQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy9wcmVwYXJlUmVxdWVzdC5qcz9mZWM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsb25lUmVxdWVzdCB9IGZyb20gXCIuL2Nsb25lUmVxdWVzdFwiO1xuaW1wb3J0IHsgR0VORVJBVEVEX0hFQURFUlMgfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCBjb25zdCBwcmVwYXJlUmVxdWVzdCA9IChyZXF1ZXN0KSA9PiB7XG4gICAgcmVxdWVzdCA9IHR5cGVvZiByZXF1ZXN0LmNsb25lID09PSBcImZ1bmN0aW9uXCIgPyByZXF1ZXN0LmNsb25lKCkgOiBjbG9uZVJlcXVlc3QocmVxdWVzdCk7XG4gICAgZm9yIChjb25zdCBoZWFkZXJOYW1lIG9mIE9iamVjdC5rZXlzKHJlcXVlc3QuaGVhZGVycykpIHtcbiAgICAgICAgaWYgKEdFTkVSQVRFRF9IRUFERVJTLmluZGV4T2YoaGVhZGVyTmFtZS50b0xvd2VyQ2FzZSgpKSA+IC0xKSB7XG4gICAgICAgICAgICBkZWxldGUgcmVxdWVzdC5oZWFkZXJzW2hlYWRlck5hbWVdO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXF1ZXN0O1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareRequest: () => (/* binding */ prepareRequest)\n/* harmony export */ });\n/* harmony import */ var _cloneRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cloneRequest */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/cloneRequest.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/constants.js\");\n\n\nconst prepareRequest = (request) => {\n    request = typeof request.clone === \"function\" ? request.clone() : (0,_cloneRequest__WEBPACK_IMPORTED_MODULE_0__.cloneRequest)(request);\n    for (const headerName of Object.keys(request.headers)) {\n        if (_constants__WEBPACK_IMPORTED_MODULE_1__.GENERATED_HEADERS.indexOf(headerName.toLowerCase()) > -1) {\n            delete request.headers[headerName];\n        }\n    }\n    return request;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL3ByZXBhcmVSZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUNFO0FBQ3pDO0FBQ1Asc0VBQXNFLDJEQUFZO0FBQ2xGO0FBQ0EsWUFBWSx5REFBaUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvcHJlcGFyZVJlcXVlc3QuanM/MmQ3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbG9uZVJlcXVlc3QgfSBmcm9tIFwiLi9jbG9uZVJlcXVlc3RcIjtcbmltcG9ydCB7IEdFTkVSQVRFRF9IRUFERVJTIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgY29uc3QgcHJlcGFyZVJlcXVlc3QgPSAocmVxdWVzdCkgPT4ge1xuICAgIHJlcXVlc3QgPSB0eXBlb2YgcmVxdWVzdC5jbG9uZSA9PT0gXCJmdW5jdGlvblwiID8gcmVxdWVzdC5jbG9uZSgpIDogY2xvbmVSZXF1ZXN0KHJlcXVlc3QpO1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhyZXF1ZXN0LmhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChHRU5FUkFURURfSEVBREVSUy5pbmRleE9mKGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSkgPiAtMSkge1xuICAgICAgICAgICAgZGVsZXRlIHJlcXVlc3QuaGVhZGVyc1toZWFkZXJOYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVxdWVzdDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/prepareRequest.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iso8601: () => (/* binding */ iso8601),\n/* harmony export */   toDate: () => (/* binding */ toDate)\n/* harmony export */ });\nconst iso8601 = (time) => toDate(time)\n    .toISOString()\n    .replace(/\\.\\d{3}Z$/, \"Z\");\nconst toDate = (time) => {\n    if (typeof time === \"number\") {\n        return new Date(time * 1000);\n    }\n    if (typeof time === \"string\") {\n        if (Number(time)) {\n            return new Date(Number(time) * 1000);\n        }\n        return new Date(time);\n    }\n    return time;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytzaWduYXR1cmUtdjRAMy4yOTkuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvdXRpbERhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0EsbUJBQW1CLEVBQUU7QUFDZDtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL3V0aWxEYXRlLmpzP2U4ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGlzbzg2MDEgPSAodGltZSkgPT4gdG9EYXRlKHRpbWUpXG4gICAgLnRvSVNPU3RyaW5nKClcbiAgICAucmVwbGFjZSgvXFwuXFxkezN9WiQvLCBcIlpcIik7XG5leHBvcnQgY29uc3QgdG9EYXRlID0gKHRpbWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHRpbWUgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBEYXRlKHRpbWUgKiAxMDAwKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiB0aW1lID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGlmIChOdW1iZXIodGltZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShOdW1iZXIodGltZSkgKiAxMDAwKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IERhdGUodGltZSk7XG4gICAgfVxuICAgIHJldHVybiB0aW1lO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iso8601: () => (/* binding */ iso8601),\n/* harmony export */   toDate: () => (/* binding */ toDate)\n/* harmony export */ });\nconst iso8601 = (time) => toDate(time)\n    .toISOString()\n    .replace(/\\.\\d{3}Z$/, \"Z\");\nconst toDate = (time) => {\n    if (typeof time === \"number\") {\n        return new Date(time * 1000);\n    }\n    if (typeof time === \"string\") {\n        if (Number(time)) {\n            return new Date(Number(time) * 1000);\n        }\n        return new Date(time);\n    }\n    return time;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrc2lnbmF0dXJlLXY0QDMuMjk5LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC9kaXN0LWVzL3V0aWxEYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBLG1CQUFtQixFQUFFO0FBQ2Q7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3NpZ25hdHVyZS12NEAzLjI5OS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zaWduYXR1cmUtdjQvZGlzdC1lcy91dGlsRGF0ZS5qcz83NmU1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc284NjAxID0gKHRpbWUpID0+IHRvRGF0ZSh0aW1lKVxuICAgIC50b0lTT1N0cmluZygpXG4gICAgLnJlcGxhY2UoL1xcLlxcZHszfVokLywgXCJaXCIpO1xuZXhwb3J0IGNvbnN0IHRvRGF0ZSA9ICh0aW1lKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB0aW1lID09PSBcIm51bWJlclwiKSB7XG4gICAgICAgIHJldHVybiBuZXcgRGF0ZSh0aW1lICogMTAwMCk7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdGltZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBpZiAoTnVtYmVyKHRpbWUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IERhdGUoTnVtYmVyKHRpbWUpICogMTAwMCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ldyBEYXRlKHRpbWUpO1xuICAgIH1cbiAgICByZXR1cm4gdGltZTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+signature-v4@3.299.0/node_modules/@aws-sdk/signature-v4/dist-es/utilDate.js\n");

/***/ })

};
;