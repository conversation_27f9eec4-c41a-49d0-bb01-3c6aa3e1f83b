import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ize2,
} from "lucide-react";

export function InitialOverlayPanel() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has seen the overlay before
    const hasSeenOverlay = localStorage.getItem("agape_overlay_seen");
    if (!hasSeenOverlay) {
      setIsVisible(true);
    }
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // Mark that the user has seen the overlay
    localStorage.setItem("agape_overlay_seen", "true");
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[2000] bg-black/95 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-[#363643] to-[#1a1a2e] rounded-2xl p-6 sm:p-8 max-w-2xl w-full relative border border-[#FDE9CE]/20 shadow-2xl">
        <button
          onClick={handleClose}
          className="absolute top-3 right-3 sm:top-4 sm:right-4 text-[#FDE9CE] hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full"
        >
          <X size={20} className="sm:w-6 sm:h-6" />
        </button>

        <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
          <Sparkles className="text-[#FDE9CE] w-6 h-6 sm:w-8 sm:h-8" />
          <h2 className="text-xl sm:text-2xl md:text-3xl tracking-wide text-[#FDE9CE]">
            Welcome to <span className="font-serif italic">CSS</span> Jewelry
            Editor
          </h2>
        </div>

        <div className="space-y-4 sm:space-y-6 text-[#FDE9CE]">
          <p className="text-xs md:text-sm sm:text-base md:text-lg leading-relaxed font-light tracking-wide">
            Experience our interactive 3D jewelry editor. Explore our exquisite
            collection of jewelry designs in stunning detail and customize them
            to your heart&apos;s desire.
          </p>

          <div className="space-y-3 sm:space-y-4">
            <h3 className="text-base sm:text-lg md:text-xl font-light tracking-wide flex items-center gap-2">
              <MousePointer className="w-4 h-4 sm:w-5 sm:h-5" />
              How to interact:
            </h3>
            <ul className="space-y-2 sm:space-y-3">
              <li className="flex items-center gap-2 sm:gap-3">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#FDE9CE]/10 flex items-center justify-center">
                  <RotateCw className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <span className="text-xs md:text-sm sm:text-base font-light">
                  Click and drag to rotate the view
                </span>
              </li>
              <li className="flex items-center gap-2 sm:gap-3">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#FDE9CE]/10 flex items-center justify-center">
                  <ZoomIn className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <span className="text-xs md:text-sm sm:text-base font-light">
                  Scroll to zoom in and out
                </span>
              </li>
              <li className="flex items-center gap-2 sm:gap-3">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#FDE9CE]/10 flex items-center justify-center">
                  <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <span className="text-xs md:text-sm sm:text-base font-light">
                  Use the toolbar on the left to customize materials and
                  lighting
                </span>
              </li>
              <li className="flex items-center gap-2 sm:gap-3">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#FDE9CE]/10 flex items-center justify-center">
                  <MousePointer className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <span className="text-xs md:text-sm sm:text-base font-light">
                  Click on different parts of the jewelry to select them
                </span>
              </li>
              <li className="flex items-center gap-2 sm:gap-3">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#FDE9CE]/10 flex items-center justify-center">
                  <Maximize2 className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <span className="text-xs md:text-sm sm:text-base font-light">
                  Click the fullscreen icon in the header to view only the model
                </span>
              </li>
            </ul>
          </div>

          <div className="pt-3 sm:pt-4 border-t border-[#FDE9CE]/20 flex justify-end">
            <p className="text-[10px] md:text-xs sm:text-xs  text-[#FDE9CE]/80 font-light">
              crafted with ❤️ by Reunite Limited
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
