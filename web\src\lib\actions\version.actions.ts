"use server";
import { revalidatePath } from "next/cache";
import Membership from "../db/models/membership.models";
import Version from "../db/models/version.models";
import { connectToDatabase } from "../mongoose";
import { parseStringify } from "../utils";

interface CreateVersionParams {
  userId: string;
  workspaceId: string;
  name: string;
  config: Record<string, any>;
  description: string;
}

export async function createVersion(params: CreateVersionParams) {
  try {
    await connectToDatabase();
    const { userId, workspaceId, name, config, description } = params;

    // Check if user has access to workspace
    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    }).populate("user", "name image");
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    if (membership.role !== "admin" && membership.role !== "editor") {
      throw new Error("Not authorized to create a version");
    }

    // Find the latest version number for this workspace
    const lastVersion = await Version.findOne({ workspaceId }).sort({
      versionNumber: -1,
    });
    const versionNumber = lastVersion ? lastVersion.versionNumber + 1 : 1;

    const newVersion = new Version({
      userId,
      workspaceId,
      name,
      config,
      versionNumber,
      description,
    });
    await newVersion.save();

    const transformedVersion = {
      _id: newVersion._id,
      id: newVersion._id,
      timestamp: newVersion.createdAt,
      versionNumber: newVersion.versionNumber,
      author: {
        name: membership.user.name,
        image: membership.user.image,
      },
      description: newVersion.description,
      config: newVersion.config,
      name: newVersion.name,
    };

    revalidatePath(`/workspace/${workspaceId}`);

    return parseStringify(transformedVersion);
  } catch (error) {
    console.error("Error creating version:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface GetVersionsParams {
  userId: string;
  workspaceId: string;
}

export async function getVersions(params: GetVersionsParams) {
  try {
    await connectToDatabase();
    const { userId, workspaceId } = params;

    // Check if user has access to workspace
    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    const data = await Version.find({ workspaceId })
      .populate("userId", "name image")
      .sort({ createdAt: -1 });

    //map the versions to the Version interface
    const versions = data.map((version) => ({
      _id: version._id,
      id: version._id,
      timestamp: version.createdAt,
      versionNumber: version.versionNumber,
      author: {
        name: version.userId.name,
        image: version.userId.image,
      },
      description: version.description,
      config: version.config,
      name: version.name,
    }));

    return parseStringify(versions);
  } catch (error) {
    console.error("Error getting versions:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface GetVersionParams {
  userId: string;
  versionId: string;
  workspaceId: string;
}

export async function getVersion(params: GetVersionParams) {
  try {
    await connectToDatabase();
    const { userId, versionId, workspaceId } = params;

    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }
    const version = await Version.findById(versionId).populate(
      "userId",
      "name image"
    );
    if (!version) {
      throw new Error("Version not found");
    }

    if (version.workspaceId !== workspaceId) {
      throw new Error("Version not found in this workspace");
    }
    const transformedVersion = {
      _id: version._id,
      id: version._id,
      timestamp: version.createdAt,
      versionNumber: version.versionNumber,
      author: {
        name: version.userId.name,
        image: version.userId.image,
      },
      description: version.description,
      config: version.config,
      name: version.name,
    };
    return parseStringify(transformedVersion);
  } catch (error) {
    console.error("Error getting version:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface GetCurrentVersionParams {
  userId: string;
  workspaceId: string;
}
export async function getCurrentVersion(params: GetCurrentVersionParams) {
  try {
    await connectToDatabase();
    const { userId, workspaceId } = params;

    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    const currentVersion = await Version.findOne({
      workspaceId,
    })
      .sort({ createdAt: -1 })
      .populate("userId", "name image");
    if (!currentVersion) {
      return null;
    }

    const transformedVersion = {
      _id: currentVersion._id,
      id: currentVersion._id,
      timestamp: currentVersion.createdAt,
      versionNumber: currentVersion.versionNumber,
      author: {
        name: currentVersion.userId.name,
        image: currentVersion.userId.image,
      },
      description: currentVersion.description,
      config: currentVersion.config,
      name: currentVersion.name,
    };

    return parseStringify(transformedVersion);
  } catch (error) {
    console.error("Error getting current version:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface UpdateVersionParams {
  userId: string;
  versionId: string;
  workspaceId: string;
  name?: string;
  config?: Record<string, any>;
  description?: string;
}

export async function updateVersion(params: UpdateVersionParams) {
  try {
    await connectToDatabase();
    const { userId, versionId, workspaceId, name, config, description } =
      params;

    // Check if user has access to workspace
    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    if (membership.role !== "admin" && membership.role !== "editor") {
      throw new Error("Not authorized to update this version");
    }

    const version = await Version.findById(versionId);
    if (!version) {
      throw new Error("Version not found");
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (config !== undefined) updateData.config = config;
    if (description !== undefined) updateData.description = description;
    const updatedVersion = await Version.findByIdAndUpdate(
      versionId,
      updateData,
      { new: true }
    ).populate("userId", "name image");

    const transformedVersion = {
      _id: updatedVersion._id,
      id: updatedVersion._id,
      timestamp: updatedVersion.createdAt,
      versionNumber: updatedVersion.versionNumber,
      author: {
        name: updatedVersion.userId.name,
        image: updatedVersion.userId.image,
      },
      description: updatedVersion.description,
      config: updatedVersion.config,
      name: updatedVersion.name,
    };

    revalidatePath(`/workspace/${workspaceId}`);

    return parseStringify(transformedVersion);
  } catch (error) {
    console.error("Error updating version:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface DeleteVersionParams {
  userId: string;
  versionId: string;
  workspaceId: string;
}

export async function deleteVersion(params: DeleteVersionParams) {
  try {
    await connectToDatabase();
    const { userId, versionId, workspaceId } = params;

    // Check if user has access to workspace
    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    if (membership.role !== "admin" && membership.role !== "editor") {
      throw new Error("Not authorized to delete this version");
    }

    const version = await Version.findById(versionId);
    if (!version) {
      throw new Error("Version not found");
    }

    await Version.findByIdAndDelete(versionId);

    revalidatePath(`/workspace/${workspaceId}`);

    return parseStringify({ success: true, versionId });
  } catch (error) {
    console.error("Error deleting version:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}

interface DeleteVersionsParams {
  userId: string;
  workspaceId: string;
  versionIds: string[];
}

export async function deleteVersions(params: DeleteVersionsParams) {
  try {
    await connectToDatabase();
    const { userId, workspaceId, versionIds } = params;

    // Check if user has access to workspace
    const membership = await Membership.findOne({
      workspace: workspaceId,
      user: userId,
    });
    if (!membership) {
      throw new Error("Not a member of this workspace");
    }

    // For batch delete, only admin can delete others' versions
    if (membership.role !== "admin") {
      // Check if all versions belong to the user
      const versions = await Version.find({
        _id: { $in: versionIds },
        workspaceId,
      });

      const unauthorizedVersions = versions.filter(
        (version) => version.userId.toString() !== userId
      );

      if (unauthorizedVersions.length > 0) {
        throw new Error("Not authorized to delete some versions");
      }
    }

    const result = await Version.deleteMany({
      _id: { $in: versionIds },
      workspaceId,
    });

    revalidatePath(`/workspace/${workspaceId}`);

    return parseStringify({ success: true, deletedCount: result.deletedCount });
  } catch (error) {
    console.error("Error deleting versions:", error);
    throw new Error(
      `${error instanceof Error ? error.message : String(error)}`
    );
  }
}
