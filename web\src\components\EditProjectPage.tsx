import EditorExperience from "@/components/ArtistTool/EditorExperience";

interface EditProjectPageProps {
  project: any;
  user: any;
}

export default function EditProjectPage({
  project,
  user,
}: EditProjectPageProps) {
  console.log("renderingggg edit project page");

  return (
    <div className="relative h-screen w-screen">
      <EditorExperience
        modelUrl={project.modelPath}
        project={project}
        user={user}
      />
    </div>
  );
}
