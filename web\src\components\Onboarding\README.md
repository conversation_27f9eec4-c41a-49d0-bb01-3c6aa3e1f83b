# Onboarding System

This onboarding system uses react-joyride to provide guided tours for new users across different pages of the application.

## Features

- **Multi-page onboarding**: Different tours for HomePage, ProjectPage, and ModelViewer
- **Automatic triggering**: Onboarding starts automatically for first-time visitors
- **Manual triggering**: Users can restart onboarding using help buttons
- **Progress tracking**: Each onboarding flow is tracked separately in localStorage
- **Custom styling**: Styled to match the application's design system

## Components

### OnboardingProvider

The main provider component that manages the onboarding state and renders the Joyride component.

### OnboardingTrigger

A reusable button component that can trigger onboarding for any page.

### useOnboarding Hook

A custom hook that provides access to onboarding functions:

- `startOnboarding(page: string)`: Start onboarding for a specific page
- `stopOnboarding()`: Stop the current onboarding
- `isOnboardingActive`: Boolean indicating if onboarding is currently running
- `currentPage`: The current page being toured

## Usage

### 1. Wrap your app with OnboardingProvider

**Note**: The HomePage onboarding is triggered by the question mark icon in the RightSidebar component.

```tsx
// In your root layout
import { OnboardingProvider } from "@/components/Onboarding";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <OnboardingProvider>{children}</OnboardingProvider>
      </body>
    </html>
  );
}
```

### 2. Use the useOnboarding hook in your components

```tsx
import { useOnboarding } from "@/components/Onboarding";

export default function MyComponent() {
  const { startOnboarding } = useOnboarding();

  // Start onboarding automatically for new users
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem("onboarding-completed");
    if (!hasCompletedOnboarding) {
      startOnboarding("home");
    }
  }, [startOnboarding]);

  return <div>Your component</div>;
}
```

### 3. Add OnboardingTrigger buttons

```tsx
import { OnboardingTrigger } from "@/components/Onboarding";

export default function MyComponent() {
  return (
    <div>
      <OnboardingTrigger
        page="home"
        className="your-custom-classes"
        variant="default"
      >
        Help
      </OnboardingTrigger>
    </div>
  );
}
```

## Available Pages

- `'home'`: HomePage onboarding
- `'project'`: ProjectPage onboarding
- `'modelViewer'`: ModelViewer onboarding

## CSS Classes Required

The onboarding system targets specific CSS classes. Make sure these classes are present on your components:

### HomePage

- `.sidebar`: The main sidebar
- `.search-bar`: The search input
- `.filter-button`: The filter button
- `.new-project-button`: The "New" project creation button
- `.sort-button`: The sort/filter menu button
- `.batch-selection`: Batch selection controls
- `.project-card`: Individual project cards
- `.library-tab`: Library tab button

### ProjectPage

- `.logo`: The logo/back button
- `.comments-button`: Comments mode toggle
- `.model-viewer`: The 3D model viewer
- `.comments-panel`: Comments list panel

### ModelViewer

- `.canvas`: The Three.js canvas
- `.comment-markers`: Comment markers on the model
- `.comment-form`: Comment input form

## Customization

### Adding New Steps

To add new onboarding steps, modify the step arrays in `OnboardingProvider.tsx`:

```tsx
const homePageSteps: Step[] = [
  {
    target: ".your-new-element",
    content: "Your new step content",
    placement: "bottom",
  },
  // ... existing steps
];
```

### Styling

The onboarding tooltips are styled to match your application's design. You can customize the styles in the `styles` prop of the Joyride component in `OnboardingProvider.tsx`.

### Local Storage Keys

The system uses these localStorage keys to track completion:

- `'onboarding-completed'`: General onboarding completion
- `'project-onboarding-completed'`: Project page onboarding
- `'modelviewer-onboarding-completed'`: Model viewer onboarding
- `'first-visit'`: Tracks if user has visited before

## Testing

To test the onboarding system:

1. Clear localStorage to simulate a new user
2. Refresh the page to trigger automatic onboarding
3. Use the help buttons to manually trigger specific tours
4. Complete or skip tours to test the completion tracking

## Troubleshooting

- **Steps not showing**: Check that the target CSS classes exist on the page
- **Onboarding not starting**: Verify that OnboardingProvider is wrapping your app
- **Styling issues**: Check that the custom styles are being applied correctly
