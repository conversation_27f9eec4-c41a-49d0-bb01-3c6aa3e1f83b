"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/maath@0.6.0_@types+three@0.168.0_three@0.167.1";
exports.ids = ["vendor-chunks/maath@0.6.0_@types+three@0.168.0_three@0.167.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/buffer-d2a4726c.esm.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/buffer-d2a4726c.esm.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ addAxis),\n/* harmony export */   b: () => (/* binding */ buffer),\n/* harmony export */   c: () => (/* binding */ reduce),\n/* harmony export */   d: () => (/* binding */ center),\n/* harmony export */   e: () => (/* binding */ expand),\n/* harmony export */   f: () => (/* binding */ sort),\n/* harmony export */   l: () => (/* binding */ lerp),\n/* harmony export */   m: () => (/* binding */ map),\n/* harmony export */   r: () => (/* binding */ rotate),\n/* harmony export */   s: () => (/* binding */ swizzle),\n/* harmony export */   t: () => (/* binding */ translate)\n/* harmony export */ });\n/* harmony import */ var _objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./objectSpread2-284232a6.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js\");\n/* harmony import */ var _triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./triangle-b62b9067.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./misc-7d870b3c.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js\");\n/* harmony import */ var _vector2_d2bf51f1_esm_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./vector2-d2bf51f1.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector2-d2bf51f1.esm.js\");\n/* harmony import */ var _vector3_0a088b7f_esm_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./vector3-0a088b7f.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector3-0a088b7f.esm.js\");\n\n\n\n\n\n\n\nfunction swizzle(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var swizzle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"xyz\";\n  var o = {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n\n  for (var _i = 0; _i < buffer.length; _i += stride) {\n    o.x = buffer[_i];\n    o.y = buffer[_i + 1];\n    o.z = buffer[_i + 2];\n\n    var _swizzle$split = swizzle.split(\"\"),\n        _swizzle$split2 = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_1__._)(_swizzle$split, 3),\n        x = _swizzle$split2[0],\n        y = _swizzle$split2[1],\n        z = _swizzle$split2[2]; // TODO Fix this ugly type\n\n\n    buffer[_i] = o[x];\n    buffer[_i + 1] = o[y];\n\n    if (stride === 3) {\n      buffer[_i + 2] = o[z];\n    }\n  }\n\n  return buffer;\n}\n/**\n * @param buffer A stride 2 points buffer\n * @param valueGenerator A function that returns the value of the z axis at index i\n * @returns\n */\n\nfunction addAxis(buffer, size) {\n  var valueGenerator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return Math.random();\n  };\n  var newSize = size + 1;\n  var newBuffer = new Float32Array(buffer.length / size * newSize);\n\n  for (var _i2 = 0; _i2 < buffer.length; _i2 += size) {\n    var _j = _i2 / size * newSize;\n\n    newBuffer[_j] = buffer[_i2];\n    newBuffer[_j + 1] = buffer[_i2 + 1];\n\n    if (size === 2) {\n      newBuffer[_j + 2] = valueGenerator(_j);\n    }\n\n    if (size === 3) {\n      newBuffer[_j + 2] = buffer[_i2 + 2];\n      newBuffer[_j + 3] = valueGenerator(_j);\n    }\n  }\n\n  return newBuffer;\n}\n/**\n * Lerps bufferA and bufferB into final\n *\n * @param bufferA\n * @param bufferB\n * @param final\n * @param t\n */\n\nfunction lerp(bufferA, bufferB, _final, t) {\n  for (var _i3 = 0; _i3 < bufferA.length; _i3++) {\n    _final[_i3] = (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(bufferA[_i3], bufferB[_i3], t);\n  }\n} // TODO add stride\n// TODO Fix types & vectors\n\n/**\n *\n * Translate all points in the passed buffer by the passed translactionVector.\n *\n * @param buffer\n * @param translationVector\n * @returns\n */\n\nfunction translate(buffer, translationVector) {\n  var stride = translationVector.length;\n\n  for (var _i4 = 0; _i4 < buffer.length; _i4 += stride) {\n    buffer[_i4] += translationVector[0];\n    buffer[_i4 + 1] += translationVector[1];\n    buffer[_i4 + 2] += translationVector[2];\n  }\n\n  return buffer;\n} // TODO add stride\n// TODO remove quaternion & vector3 dependencies\n\nfunction rotate(buffer, rotation) {\n  var defaultRotation = {\n    center: [0, 0, 0],\n    q: new three__WEBPACK_IMPORTED_MODULE_5__.Quaternion().identity()\n  };\n  var v = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n\n  var _defaultRotation$rota = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultRotation), rotation),\n      q = _defaultRotation$rota.q,\n      center = _defaultRotation$rota.center;\n\n  for (var _i5 = 0; _i5 < buffer.length; _i5 += 3) {\n    v.set(buffer[_i5] - center[0], buffer[_i5 + 1] - center[1], buffer[_i5 + 2] - center[2]);\n    v.applyQuaternion(q);\n    buffer[_i5] = v.x + center[0];\n    buffer[_i5 + 1] = v.y + center[1];\n    buffer[_i5 + 2] = v.z + center[1];\n  }\n\n  return buffer;\n}\nfunction map(buffer, stride, callback) {\n  for (var _i6 = 0, _j2 = 0; _i6 < buffer.length; _i6 += stride, _j2++) {\n    if (stride === 3) {\n      var res = callback([buffer[_i6], buffer[_i6 + 1], buffer[_i6 + 2]], _j2);\n      buffer.set(res, _i6);\n    } else {\n      buffer.set(callback([buffer[_i6], buffer[_i6 + 1]], _j2), _i6);\n    }\n  }\n\n  return buffer;\n}\n/**\n * Reduces passed buffer\n */\n\nfunction reduce(b, stride, callback, acc) {\n  for (var _i7 = 0, _j3 = 0; _i7 < b.length; _i7 += stride, _j3++) {\n    if (stride === 2) {\n      acc = callback(acc, [b[_i7], b[_i7 + 1]], _j3);\n    } else {\n      acc = callback(acc, [b[_i7], b[_i7 + 1], b[_i7 + 2]], _j3);\n    }\n  }\n\n  return acc;\n}\nfunction expand(b, stride, opts) {\n  var defaultExpandOptions = {\n    center: [0, 0, 0]\n  };\n\n  var _defaultExpandOptions = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultExpandOptions), opts),\n      center = _defaultExpandOptions.center,\n      distance = _defaultExpandOptions.distance;\n\n  for (var _i8 = 0; _i8 < b.length; _i8 += stride) {\n    /**\n     * 1. translate to origin (subtract the scaling center)\n     * 2. scale by the correct amount (multiply by a constant)\n     * 2. translate from origin (add the scaling center)\n     */\n    b[_i8] = (b[_i8] - center[0]) * (1 + distance) + center[0];\n    b[_i8 + 1] = (b[_i8 + 1] - center[1]) * (1 + distance) + center[1];\n\n    if (stride === 3) {\n      b[_i8 + 2] = (b[_i8 + 2] - center[1]) * (1 + distance) + center[2];\n    }\n  }\n\n  return b;\n}\nfunction center(myBuffer, stride) {\n  return reduce(myBuffer, stride, function (acc, point) {\n    if (stride === 3) {\n      // some type hacking is necessary to avoid type errors going from [n, n] => [n, n, n]\n      // but it's not an actual problem, as this path would always get a v3\n      acc = (0,_vector3_0a088b7f_esm_js__WEBPACK_IMPORTED_MODULE_4__.a)(acc, point);\n    } else {\n      acc = (0,_vector2_d2bf51f1_esm_js__WEBPACK_IMPORTED_MODULE_3__.a)(acc, point);\n    }\n\n    return acc;\n  }, (0,_vector2_d2bf51f1_esm_js__WEBPACK_IMPORTED_MODULE_3__.z)());\n}\nfunction sort(myBuffer, stride, callback) {\n  // 1. make an array of the correct size\n  var indices = Int16Array.from({\n    length: myBuffer.length / stride\n  }, function (_, i) {\n    return i;\n  }); // 2. sort the indices array\n\n  indices.sort(function (a, b) {\n    var pa = myBuffer.slice(a * stride, a * stride + stride);\n    var pb = myBuffer.slice(b * stride, b * stride + stride);\n    return callback(pa, pb);\n  }); // 3. make a copy of the original array to fetch indices from\n\n  var prevBuffer = myBuffer.slice(0); // 4. mutate the passed array\n\n  for (var _i9 = 0; _i9 < indices.length; _i9++) {\n    var _j4 = indices[_i9];\n    myBuffer.set(prevBuffer.slice(_j4 * stride, _j4 * stride + stride), _i9 * 3);\n  }\n\n  return myBuffer;\n}\n\nvar buffer = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  swizzle: swizzle,\n  addAxis: addAxis,\n  lerp: lerp,\n  translate: translate,\n  rotate: rotate,\n  map: map,\n  reduce: reduce,\n  expand: expand,\n  center: center,\n  sort: sort\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/buffer-d2a4726c.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _classCallCheck)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWFhdGhAMC42LjBfQHR5cGVzK3RocmVlQDAuMTY4LjBfdGhyZWVAMC4xNjcuMS9ub2RlX21vZHVsZXMvbWFhdGgvZGlzdC9jbGFzc0NhbGxDaGVjay05MDk4YjAwNi5lc20uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL21hYXRoQDAuNi4wX0B0eXBlcyt0aHJlZUAwLjE2OC4wX3RocmVlQDAuMTY3LjEvbm9kZV9tb2R1bGVzL21hYXRoL2Rpc3QvY2xhc3NDYWxsQ2hlY2stOTA5OGIwMDYuZXNtLmpzPzNjYzQiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX2NsYXNzQ2FsbENoZWNrKGluc3RhbmNlLCBDb25zdHJ1Y3Rvcikge1xuICBpZiAoIShpbnN0YW5jZSBpbnN0YW5jZW9mIENvbnN0cnVjdG9yKSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb25cIik7XG4gIH1cbn1cblxuZXhwb3J0IHsgX2NsYXNzQ2FsbENoZWNrIGFzIF8gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/easing-3be59c6d.esm.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/easing-3be59c6d.esm.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ exp),\n/* harmony export */   b: () => (/* binding */ dampAngle),\n/* harmony export */   c: () => (/* binding */ damp2),\n/* harmony export */   d: () => (/* binding */ damp),\n/* harmony export */   e: () => (/* binding */ easing),\n/* harmony export */   f: () => (/* binding */ damp3),\n/* harmony export */   g: () => (/* binding */ damp4),\n/* harmony export */   h: () => (/* binding */ dampE),\n/* harmony export */   i: () => (/* binding */ dampC),\n/* harmony export */   j: () => (/* binding */ dampQ),\n/* harmony export */   k: () => (/* binding */ dampS),\n/* harmony export */   l: () => (/* binding */ dampM),\n/* harmony export */   r: () => (/* binding */ rsqw)\n/* harmony export */ });\n/* harmony import */ var _triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./triangle-b62b9067.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./misc-7d870b3c.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js\");\n\n\n\n\n/**\n * Rounded square wave easing\n */\n\nvar rsqw = function rsqw(t) {\n  var delta = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.01;\n  var a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var f = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1 / (2 * Math.PI);\n  return a / Math.atan(1 / delta) * Math.atan(Math.sin(2 * Math.PI * t * f) / delta);\n};\n/**\n * Exponential easing\n */\n\nvar exp = function exp(t) {\n  return 1 / (1 + t + 0.48 * t * t + 0.235 * t * t * t);\n};\n/**\n * Damp, based on Game Programming Gems 4 Chapter 1.10\n *   Return value indicates whether the animation is still running.\n */\n\nfunction damp(\n/** The object */\ncurrent,\n/** The key to animate */\nprop,\n/** To goal value */\ntarget) {\n  var smoothTime = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.25;\n  var delta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.01;\n  var maxSpeed = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : Infinity;\n  var easing = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : exp;\n  var eps = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 0.001;\n  var vel = \"velocity_\" + prop;\n  if (current.__damp === undefined) current.__damp = {};\n  if (current.__damp[vel] === undefined) current.__damp[vel] = 0;\n\n  if (Math.abs(current[prop] - target) <= eps) {\n    current[prop] = target;\n    return false;\n  }\n\n  smoothTime = Math.max(0.0001, smoothTime);\n  var omega = 2 / smoothTime;\n  var t = easing(omega * delta);\n  var change = current[prop] - target;\n  var originalTo = target; // Clamp maximum maxSpeed\n\n  var maxChange = maxSpeed * smoothTime;\n  change = Math.min(Math.max(change, -maxChange), maxChange);\n  target = current[prop] - change;\n  var temp = (current.__damp[vel] + omega * change) * delta;\n  current.__damp[vel] = (current.__damp[vel] - omega * temp) * t;\n  var output = target + (change + temp) * t; // Prevent overshooting\n\n  if (originalTo - current[prop] > 0.0 === output > originalTo) {\n    output = originalTo;\n    current.__damp[vel] = (output - originalTo) / delta;\n  }\n\n  current[prop] = output;\n  return true;\n}\n/**\n * DampAngle, based on Game Programming Gems 4 Chapter 1.10\n */\n\nfunction dampAngle(current, prop, target, smoothTime, delta, maxSpeed, easing, eps) {\n  return damp(current, prop, current[prop] + (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_1__.d)(current[prop], target), smoothTime, delta, maxSpeed, easing, eps);\n}\n/**\n * Vector2D Damp\n */\n\nvar v2d = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector2();\nvar a2, b2;\nfunction damp2(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v2d.setScalar(target);else if (Array.isArray(target)) v2d.set(target[0], target[1]);else v2d.copy(target);\n  a2 = damp(current, \"x\", v2d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b2 = damp(current, \"y\", v2d.y, smoothTime, delta, maxSpeed, easing, eps);\n  return a2 || b2;\n}\n/**\n * Vector3D Damp\n */\n\nvar v3d = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\nvar a3, b3, c3;\nfunction damp3(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v3d.setScalar(target);else if (Array.isArray(target)) v3d.set(target[0], target[1], target[2]);else v3d.copy(target);\n  a3 = damp(current, \"x\", v3d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b3 = damp(current, \"y\", v3d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c3 = damp(current, \"z\", v3d.z, smoothTime, delta, maxSpeed, easing, eps);\n  return a3 || b3 || c3;\n}\n/**\n * Vector4D Damp\n */\n\nvar v4d = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector4();\nvar a4, b4, c4, d4;\nfunction damp4(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v4d.setScalar(target);else if (Array.isArray(target)) v4d.set(target[0], target[1], target[2], target[3]);else v4d.copy(target);\n  a4 = damp(current, \"x\", v4d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b4 = damp(current, \"y\", v4d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c4 = damp(current, \"z\", v4d.z, smoothTime, delta, maxSpeed, easing, eps);\n  d4 = damp(current, \"w\", v4d.w, smoothTime, delta, maxSpeed, easing, eps);\n  return a4 || b4 || c4 || d4;\n}\n/**\n * Euler Damp\n */\n\nvar rot = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Euler();\nvar aE, bE, cE;\nfunction dampE(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) rot.set(target[0], target[1], target[2], target[3]);else rot.copy(target);\n  aE = dampAngle(current, \"x\", rot.x, smoothTime, delta, maxSpeed, easing, eps);\n  bE = dampAngle(current, \"y\", rot.y, smoothTime, delta, maxSpeed, easing, eps);\n  cE = dampAngle(current, \"z\", rot.z, smoothTime, delta, maxSpeed, easing, eps);\n  return aE || bE || cE;\n}\n/**\n * Color Damp\n */\n\nvar col = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Color();\nvar aC, bC, cC;\nfunction dampC(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (target instanceof three__WEBPACK_IMPORTED_MODULE_2__.Color) col.copy(target);else if (Array.isArray(target)) col.setRGB(target[0], target[1], target[2]);else col.set(target);\n  aC = damp(current, \"r\", col.r, smoothTime, delta, maxSpeed, easing, eps);\n  bC = damp(current, \"g\", col.g, smoothTime, delta, maxSpeed, easing, eps);\n  cC = damp(current, \"b\", col.b, smoothTime, delta, maxSpeed, easing, eps);\n  return aC || bC || cC;\n}\n/**\n * Quaternion Damp\n * https://gist.github.com/maxattack/4c7b4de00f5c1b95a33b\n * Copyright 2016 Max Kaufmann (<EMAIL>)\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nvar qt = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion();\nvar v4result = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector4();\nvar v4velocity = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector4();\nvar v4error = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector4();\nvar aQ, bQ, cQ, dQ;\nfunction dampQ(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (Array.isArray(target)) qt.set(target[0], target[1], target[2], target[3]);else qt.copy(target);\n  var multi = current.dot(qt) > 0 ? 1 : -1;\n  qt.x *= multi;\n  qt.y *= multi;\n  qt.z *= multi;\n  qt.w *= multi;\n  aQ = damp(current, \"x\", qt.x, smoothTime, delta, maxSpeed, easing, eps);\n  bQ = damp(current, \"y\", qt.y, smoothTime, delta, maxSpeed, easing, eps);\n  cQ = damp(current, \"z\", qt.z, smoothTime, delta, maxSpeed, easing, eps);\n  dQ = damp(current, \"w\", qt.w, smoothTime, delta, maxSpeed, easing, eps); // smooth damp (nlerp approx)\n\n  v4result.set(current.x, current.y, current.z, current.w).normalize();\n  v4velocity.set(cur.__damp.velocity_x, cur.__damp.velocity_y, cur.__damp.velocity_z, cur.__damp.velocity_w); // ensure deriv is tangent\n\n  v4error.copy(v4result).multiplyScalar(v4velocity.dot(v4result) / v4result.dot(v4result));\n  cur.__damp.velocity_x -= v4error.x;\n  cur.__damp.velocity_y -= v4error.y;\n  cur.__damp.velocity_z -= v4error.z;\n  cur.__damp.velocity_w -= v4error.w;\n  current.set(v4result.x, v4result.y, v4result.z, v4result.w);\n  return aQ || bQ || cQ || dQ;\n}\n/**\n * Spherical Damp\n */\n\nvar spherical = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Spherical();\nvar aS, bS, cS;\nfunction dampS(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) spherical.set(target[0], target[1], target[2]);else spherical.copy(target);\n  aS = damp(current, \"radius\", spherical.radius, smoothTime, delta, maxSpeed, easing, eps);\n  bS = dampAngle(current, \"phi\", spherical.phi, smoothTime, delta, maxSpeed, easing, eps);\n  cS = dampAngle(current, \"theta\", spherical.theta, smoothTime, delta, maxSpeed, easing, eps);\n  return aS || bS || cS;\n}\n/**\n * Matrix4 Damp\n */\n\nvar mat = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4();\nvar mPos = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\nvar mRot = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion();\nvar mSca = /*@__PURE__*/new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\nvar aM, bM, cM;\nfunction dampM(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n\n  if (cur.__damp === undefined) {\n    cur.__damp = {\n      position: new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(),\n      rotation: new three__WEBPACK_IMPORTED_MODULE_2__.Quaternion(),\n      scale: new three__WEBPACK_IMPORTED_MODULE_2__.Vector3()\n    };\n    current.decompose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  }\n\n  if (Array.isArray(target)) mat.set.apply(mat, (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(target));else mat.copy(target);\n  mat.decompose(mPos, mRot, mSca);\n  aM = damp3(cur.__damp.position, mPos, smoothTime, delta, maxSpeed, easing, eps);\n  bM = dampQ(cur.__damp.rotation, mRot, smoothTime, delta, maxSpeed, easing, eps);\n  cM = damp3(cur.__damp.scale, mSca, smoothTime, delta, maxSpeed, easing, eps);\n  current.compose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  return aM || bM || cM;\n}\n\nvar easing = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  rsqw: rsqw,\n  exp: exp,\n  damp: damp,\n  dampAngle: dampAngle,\n  damp2: damp2,\n  damp3: damp3,\n  damp4: damp4,\n  dampE: dampE,\n  dampC: dampC,\n  dampQ: dampQ,\n  dampS: dampS,\n  dampM: dampM\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/easing-3be59c6d.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/geometry-982366ff.esm.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/geometry-982366ff.esm.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   R: () => (/* binding */ RoundedPlaneGeometry),\n/* harmony export */   a: () => (/* binding */ applySphereUV),\n/* harmony export */   b: () => (/* binding */ applyBoxUV),\n/* harmony export */   g: () => (/* binding */ geometry)\n/* harmony export */ });\n/* harmony import */ var _classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classCallCheck-9098b006.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js\");\n/* harmony import */ var _isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isNativeReflectConstruct-5594d075.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) (0,_isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_1__._)(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = (0,_isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n\n  var _super = _createSuper(RoundedPlaneGeometry);\n\n  function RoundedPlaneGeometry() {\n    var _this;\n\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n\n    (0,_classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(this, RoundedPlaneGeometry);\n\n    _this = _super.call(this); // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n\n    _this.setIndex(new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(new Uint32Array(indices), 1));\n\n    _this.setAttribute(\"position\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(new Float32Array(positions), 3));\n\n    _this.setAttribute(\"uv\", new three__WEBPACK_IMPORTED_MODULE_2__.BufferAttribute(new Float32Array(uvs), 2));\n\n    return _this;\n  }\n\n  return RoundedPlaneGeometry;\n}(three__WEBPACK_IMPORTED_MODULE_2__.BufferGeometry); // Author: https://stackoverflow.com/users/268905/knee-cola\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applySphereUV(bufferGeometry) {\n  var uvs = [];\n  var vertices = [];\n\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    vertices.push(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(x, y, z));\n  }\n\n  var polarVertices = vertices.map(cartesian2polar);\n\n  for (var _i = 0; _i < polarVertices.length / 3; _i++) {\n    var tri = new three__WEBPACK_IMPORTED_MODULE_2__.Triangle(vertices[_i * 3 + 0], vertices[_i * 3 + 1], vertices[_i * 3 + 2]);\n    var normal = tri.getNormal(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3());\n\n    for (var f = 0; f < 3; f++) {\n      var vertex = polarVertices[_i * 3 + f];\n\n      if (vertex.theta === 0 && (vertex.phi === 0 || vertex.phi === Math.PI)) {\n        var alignedVertice = vertex.phi === 0 ? _i * 3 + 1 : _i * 3 + 0;\n        vertex = {\n          r: vertex.r,\n          phi: vertex.phi,\n          theta: polarVertices[alignedVertice].theta\n        };\n      }\n\n      if (vertex.theta === Math.PI && cartesian2polar(normal).theta < Math.PI / 2) {\n        vertex.theta = -Math.PI;\n      }\n\n      var canvasPoint = polar2canvas(vertex);\n      uvs.push(1 - canvasPoint.x, 1 - canvasPoint.y);\n    }\n  }\n\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new three__WEBPACK_IMPORTED_MODULE_2__.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction cartesian2polar(position) {\n  var r = Math.sqrt(position.x * position.x + position.z * position.z + position.y * position.y);\n  return {\n    r: r,\n    phi: Math.acos(position.y / r),\n    theta: Math.atan2(position.z, position.x)\n  };\n}\n\nfunction polar2canvas(polarPoint) {\n  return {\n    y: polarPoint.phi / Math.PI,\n    x: (polarPoint.theta + Math.PI) / (2 * Math.PI)\n  };\n} // Author: Alex Khoroshylov (https://stackoverflow.com/users/8742287/alex-khoroshylov)\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\n\nfunction applyBoxUV(bufferGeometry) {\n  bufferGeometry.computeBoundingBox();\n  var bboxSize = bufferGeometry.boundingBox.getSize(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3());\n  var boxSize = Math.min(bboxSize.x, bboxSize.y, bboxSize.z);\n  var boxGeometry = new three__WEBPACK_IMPORTED_MODULE_2__.BoxGeometry(boxSize, boxSize, boxSize);\n  var cube = new three__WEBPACK_IMPORTED_MODULE_2__.Mesh(boxGeometry);\n  cube.rotation.set(0, 0, 0);\n  cube.updateWorldMatrix(true, false);\n  var transformMatrix = cube.matrix.clone().invert();\n  var uvBbox = new three__WEBPACK_IMPORTED_MODULE_2__.Box3(new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(-boxSize / 2, -boxSize / 2, -boxSize / 2), new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(boxSize / 2, boxSize / 2, boxSize / 2));\n\n  _applyBoxUV(bufferGeometry, transformMatrix, uvBbox, boxSize);\n\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction _applyBoxUV(geom, transformMatrix, bbox, bbox_max_size) {\n  var coords = [];\n  coords.length = 2 * geom.attributes.position.array.length / 3; //maps 3 verts of 1 face on the better side of the cube\n  //side of the cube can be XY, XZ or YZ\n\n  var makeUVs = function makeUVs(v0, v1, v2) {\n    //pre-rotate the model so that cube sides match world axis\n    v0.applyMatrix4(transformMatrix);\n    v1.applyMatrix4(transformMatrix);\n    v2.applyMatrix4(transformMatrix); //get normal of the face, to know into which cube side it maps better\n\n    var n = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3();\n    n.crossVectors(v1.clone().sub(v0), v1.clone().sub(v2)).normalize();\n    n.x = Math.abs(n.x);\n    n.y = Math.abs(n.y);\n    n.z = Math.abs(n.z);\n    var uv0 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector2();\n    var uv1 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector2();\n    var uv2 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector2(); // xz mapping\n\n    if (n.y > n.x && n.y > n.z) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (bbox.max.z - v0.z) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (bbox.max.z - v1.z) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (bbox.max.z - v2.z) / bbox_max_size;\n    } else if (n.x > n.y && n.x > n.z) {\n      uv0.x = (v0.z - bbox.min.z) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.z - bbox.min.z) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.z - bbox.min.z) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    } else if (n.z > n.y && n.z > n.x) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    }\n\n    return {\n      uv0: uv0,\n      uv1: uv1,\n      uv2: uv2\n    };\n  };\n\n  if (geom.index) {\n    // is it indexed buffer geometry?\n    for (var vi = 0; vi < geom.index.array.length; vi += 3) {\n      var idx0 = geom.index.array[vi];\n      var idx1 = geom.index.array[vi + 1];\n      var idx2 = geom.index.array[vi + 2];\n      var vx0 = geom.attributes.position.array[3 * idx0];\n      var vy0 = geom.attributes.position.array[3 * idx0 + 1];\n      var vz0 = geom.attributes.position.array[3 * idx0 + 2];\n      var vx1 = geom.attributes.position.array[3 * idx1];\n      var vy1 = geom.attributes.position.array[3 * idx1 + 1];\n      var vz1 = geom.attributes.position.array[3 * idx1 + 2];\n      var vx2 = geom.attributes.position.array[3 * idx2];\n      var vy2 = geom.attributes.position.array[3 * idx2 + 1];\n      var vz2 = geom.attributes.position.array[3 * idx2 + 2];\n      var v0 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(vx0, vy0, vz0);\n      var v1 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(vx1, vy1, vz1);\n      var v2 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(vx2, vy2, vz2);\n      var uvs = makeUVs(v0, v1, v2);\n      coords[2 * idx0] = uvs.uv0.x;\n      coords[2 * idx0 + 1] = uvs.uv0.y;\n      coords[2 * idx1] = uvs.uv1.x;\n      coords[2 * idx1 + 1] = uvs.uv1.y;\n      coords[2 * idx2] = uvs.uv2.x;\n      coords[2 * idx2 + 1] = uvs.uv2.y;\n    }\n  } else {\n    for (var _vi = 0; _vi < geom.attributes.position.array.length; _vi += 9) {\n      var _vx = geom.attributes.position.array[_vi];\n      var _vy = geom.attributes.position.array[_vi + 1];\n      var _vz = geom.attributes.position.array[_vi + 2];\n      var _vx2 = geom.attributes.position.array[_vi + 3];\n      var _vy2 = geom.attributes.position.array[_vi + 4];\n      var _vz2 = geom.attributes.position.array[_vi + 5];\n      var _vx3 = geom.attributes.position.array[_vi + 6];\n      var _vy3 = geom.attributes.position.array[_vi + 7];\n      var _vz3 = geom.attributes.position.array[_vi + 8];\n\n      var _v = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(_vx, _vy, _vz);\n\n      var _v2 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(_vx2, _vy2, _vz2);\n\n      var _v3 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(_vx3, _vy3, _vz3);\n\n      var _uvs = makeUVs(_v, _v2, _v3);\n\n      var _idx = _vi / 3;\n\n      var _idx2 = _idx + 1;\n\n      var _idx3 = _idx + 2;\n\n      coords[2 * _idx] = _uvs.uv0.x;\n      coords[2 * _idx + 1] = _uvs.uv0.y;\n      coords[2 * _idx2] = _uvs.uv1.x;\n      coords[2 * _idx2 + 1] = _uvs.uv1.y;\n      coords[2 * _idx3] = _uvs.uv2.x;\n      coords[2 * _idx3 + 1] = _uvs.uv2.y;\n    }\n  }\n\n  if (geom.attributes.uv) delete geom.attributes.uv;\n  geom.setAttribute(\"uv\", new three__WEBPACK_IMPORTED_MODULE_2__.Float32BufferAttribute(coords, 2));\n}\n\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry,\n  applySphereUV: applySphereUV,\n  applyBoxUV: applyBoxUV\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/geometry-982366ff.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/index-43782085.esm.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/index-43782085.esm.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   G: () => (/* binding */ Generator),\n/* harmony export */   a: () => (/* binding */ inSphere),\n/* harmony export */   b: () => (/* binding */ inCircle),\n/* harmony export */   c: () => (/* binding */ onCircle),\n/* harmony export */   d: () => (/* binding */ inRect),\n/* harmony export */   e: () => (/* binding */ onRect),\n/* harmony export */   f: () => (/* binding */ inBox),\n/* harmony export */   g: () => (/* binding */ onBox),\n/* harmony export */   i: () => (/* binding */ index),\n/* harmony export */   n: () => (/* binding */ noise),\n/* harmony export */   o: () => (/* binding */ onSphere)\n/* harmony export */ });\n/* harmony import */ var _objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./objectSpread2-284232a6.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js\");\n/* harmony import */ var _classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classCallCheck-9098b006.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js\");\n/* harmony import */ var _misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./misc-7d870b3c.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js\");\n\n\n\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by Stefan Gustavson (<EMAIL>).\n * Optimisations by Peter Eastman (<EMAIL>).\n * Better rank ordering method by Stefan Gustavson in 2012.\n * Converted to Javascript by Joseph Gentle.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * Stefan Gustavson. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  (0,_classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_1__._)(this, Grad);\n\n  (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.f)(x); // Interpolate the four results\n\n  return (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)((0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n00, n10, u), (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n01, n11, u), (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.f)(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.f)(x);\n  var v = (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.f)(y);\n  var w = (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.f)(z); // Interpolate\n\n  return (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)((0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)((0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n000, n100, u), (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n001, n101, u), w), (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)((0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n010, n110, u), (0,_misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_2__.l)(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2; // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  (0,_classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_1__._)(this, Generator);\n\n  (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(this, \"seed\", 0);\n\n  (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = (0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)((0,_objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_0__._)({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/index-43782085.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _setPrototypeOf),\n/* harmony export */   a: () => (/* binding */ _isNativeReflectConstruct)\n/* harmony export */ });\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWFhdGhAMC42LjBfQHR5cGVzK3RocmVlQDAuMTY4LjBfdGhyZWVAMC4xNjcuMS9ub2RlX21vZHVsZXMvbWFhdGgvZGlzdC9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QtNTU5NGQwNzUuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZ0ZBQWdGO0FBQ2hGO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFZ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vbWFhdGhAMC42LjBfQHR5cGVzK3RocmVlQDAuMTY4LjBfdGhyZWVAMC4xNjcuMS9ub2RlX21vZHVsZXMvbWFhdGgvZGlzdC9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QtNTU5NGQwNzUuZXNtLmpzPzE5MTciXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX3NldFByb3RvdHlwZU9mKG8sIHApIHtcbiAgX3NldFByb3RvdHlwZU9mID0gT2JqZWN0LnNldFByb3RvdHlwZU9mIHx8IGZ1bmN0aW9uIF9zZXRQcm90b3R5cGVPZihvLCBwKSB7XG4gICAgby5fX3Byb3RvX18gPSBwO1xuICAgIHJldHVybiBvO1xuICB9O1xuXG4gIHJldHVybiBfc2V0UHJvdG90eXBlT2YobywgcCk7XG59XG5cbmZ1bmN0aW9uIF9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QoKSB7XG4gIGlmICh0eXBlb2YgUmVmbGVjdCA9PT0gXCJ1bmRlZmluZWRcIiB8fCAhUmVmbGVjdC5jb25zdHJ1Y3QpIHJldHVybiBmYWxzZTtcbiAgaWYgKFJlZmxlY3QuY29uc3RydWN0LnNoYW0pIHJldHVybiBmYWxzZTtcbiAgaWYgKHR5cGVvZiBQcm94eSA9PT0gXCJmdW5jdGlvblwiKSByZXR1cm4gdHJ1ZTtcblxuICB0cnkge1xuICAgIEJvb2xlYW4ucHJvdG90eXBlLnZhbHVlT2YuY2FsbChSZWZsZWN0LmNvbnN0cnVjdChCb29sZWFuLCBbXSwgZnVuY3Rpb24gKCkge30pKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG5leHBvcnQgeyBfc2V0UHJvdG90eXBlT2YgYXMgXywgX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCBhcyBhIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/maath.esm.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/maath.esm.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buffer: () => (/* reexport safe */ _buffer_d2a4726c_esm_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   easing: () => (/* reexport safe */ _easing_3be59c6d_esm_js__WEBPACK_IMPORTED_MODULE_2__.e),\n/* harmony export */   geometry: () => (/* reexport safe */ _geometry_982366ff_esm_js__WEBPACK_IMPORTED_MODULE_3__.g),\n/* harmony export */   matrix: () => (/* reexport safe */ _matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_4__.m),\n/* harmony export */   misc: () => (/* reexport safe */ _misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_5__.m),\n/* harmony export */   random: () => (/* reexport safe */ _index_43782085_esm_js__WEBPACK_IMPORTED_MODULE_1__.i),\n/* harmony export */   three: () => (/* reexport safe */ _three_eb2ad8c0_esm_js__WEBPACK_IMPORTED_MODULE_6__.t),\n/* harmony export */   triangle: () => (/* reexport safe */ _triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_7__.t),\n/* harmony export */   vector2: () => (/* reexport safe */ _vector2_d2bf51f1_esm_js__WEBPACK_IMPORTED_MODULE_8__.v),\n/* harmony export */   vector3: () => (/* reexport safe */ _vector3_0a088b7f_esm_js__WEBPACK_IMPORTED_MODULE_9__.v)\n/* harmony export */ });\n/* harmony import */ var _buffer_d2a4726c_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer-d2a4726c.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/buffer-d2a4726c.esm.js\");\n/* harmony import */ var _index_43782085_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index-43782085.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/index-43782085.esm.js\");\n/* harmony import */ var _easing_3be59c6d_esm_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./easing-3be59c6d.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/easing-3be59c6d.esm.js\");\n/* harmony import */ var _geometry_982366ff_esm_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./geometry-982366ff.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/geometry-982366ff.esm.js\");\n/* harmony import */ var _matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./matrix-baa530bf.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js\");\n/* harmony import */ var _misc_7d870b3c_esm_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./misc-7d870b3c.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js\");\n/* harmony import */ var _three_eb2ad8c0_esm_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./three-eb2ad8c0.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/three-eb2ad8c0.esm.js\");\n/* harmony import */ var _triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./triangle-b62b9067.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js\");\n/* harmony import */ var _vector2_d2bf51f1_esm_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./vector2-d2bf51f1.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector2-d2bf51f1.esm.js\");\n/* harmony import */ var _vector3_0a088b7f_esm_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./vector3-0a088b7f.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector3-0a088b7f.esm.js\");\n/* harmony import */ var _objectSpread2_284232a6_esm_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./objectSpread2-284232a6.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js\");\n/* harmony import */ var _classCallCheck_9098b006_esm_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./classCallCheck-9098b006.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/classCallCheck-9098b006.esm.js\");\n/* harmony import */ var _isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./isNativeReflectConstruct-5594d075.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWFhdGhAMC42LjBfQHR5cGVzK3RocmVlQDAuMTY4LjBfdGhyZWVAMC4xNjcuMS9ub2RlX21vZHVsZXMvbWFhdGgvZGlzdC9tYWF0aC5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDRDtBQUNDO0FBQ0k7QUFDSjtBQUNKO0FBQ0U7QUFDTTtBQUNGO0FBQ0E7QUFDaEI7QUFDMUI7QUFDMkI7QUFDVSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9tYWF0aEAwLjYuMF9AdHlwZXMrdGhyZWVAMC4xNjguMF90aHJlZUAwLjE2Ny4xL25vZGVfbW9kdWxlcy9tYWF0aC9kaXN0L21hYXRoLmVzbS5qcz81NzA4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGIgYXMgYnVmZmVyIH0gZnJvbSAnLi9idWZmZXItZDJhNDcyNmMuZXNtLmpzJztcbmV4cG9ydCB7IGkgYXMgcmFuZG9tIH0gZnJvbSAnLi9pbmRleC00Mzc4MjA4NS5lc20uanMnO1xuZXhwb3J0IHsgZSBhcyBlYXNpbmcgfSBmcm9tICcuL2Vhc2luZy0zYmU1OWM2ZC5lc20uanMnO1xuZXhwb3J0IHsgZyBhcyBnZW9tZXRyeSB9IGZyb20gJy4vZ2VvbWV0cnktOTgyMzY2ZmYuZXNtLmpzJztcbmV4cG9ydCB7IG0gYXMgbWF0cml4IH0gZnJvbSAnLi9tYXRyaXgtYmFhNTMwYmYuZXNtLmpzJztcbmV4cG9ydCB7IG0gYXMgbWlzYyB9IGZyb20gJy4vbWlzYy03ZDg3MGIzYy5lc20uanMnO1xuZXhwb3J0IHsgdCBhcyB0aHJlZSB9IGZyb20gJy4vdGhyZWUtZWIyYWQ4YzAuZXNtLmpzJztcbmV4cG9ydCB7IHQgYXMgdHJpYW5nbGUgfSBmcm9tICcuL3RyaWFuZ2xlLWI2MmI5MDY3LmVzbS5qcyc7XG5leHBvcnQgeyB2IGFzIHZlY3RvcjIgfSBmcm9tICcuL3ZlY3RvcjItZDJiZjUxZjEuZXNtLmpzJztcbmV4cG9ydCB7IHYgYXMgdmVjdG9yMyB9IGZyb20gJy4vdmVjdG9yMy0wYTA4OGI3Zi5lc20uanMnO1xuaW1wb3J0ICcuL29iamVjdFNwcmVhZDItMjg0MjMyYTYuZXNtLmpzJztcbmltcG9ydCAndGhyZWUnO1xuaW1wb3J0ICcuL2NsYXNzQ2FsbENoZWNrLTkwOThiMDA2LmVzbS5qcyc7XG5pbXBvcnQgJy4vaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0LTU1OTRkMDc1LmVzbS5qcyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/maath.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ matrixSum3),\n/* harmony export */   b: () => (/* binding */ determinant2),\n/* harmony export */   c: () => (/* binding */ determinant4),\n/* harmony export */   d: () => (/* binding */ determinant3),\n/* harmony export */   g: () => (/* binding */ getMinor),\n/* harmony export */   m: () => (/* binding */ matrix)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3],\n      e = terms[4],\n      f = terms[5],\n      g = terms[6],\n      h = terms[7],\n      i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n\n  terms[0];\n      terms[1];\n      terms[2];\n      terms[3];\n      terms[4];\n      terms[5];\n      terms[6];\n      terms[7];\n      terms[8];\n      terms[9];\n      terms[10];\n      terms[11];\n      terms[12];\n      terms[13];\n      terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n\n  return new three__WEBPACK_IMPORTED_MODULE_0__.Matrix3().fromArray(sum);\n}\n\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ degToRad),\n/* harmony export */   b: () => (/* binding */ fibonacciOnSphere),\n/* harmony export */   c: () => (/* binding */ clamp),\n/* harmony export */   d: () => (/* binding */ deltaAngle),\n/* harmony export */   e: () => (/* binding */ lexicographic),\n/* harmony export */   f: () => (/* binding */ fade),\n/* harmony export */   g: () => (/* binding */ convexHull),\n/* harmony export */   h: () => (/* binding */ remap),\n/* harmony export */   i: () => (/* binding */ inverseLerp),\n/* harmony export */   j: () => (/* binding */ rotateVectorOnVector),\n/* harmony export */   k: () => (/* binding */ pointToCoordinate),\n/* harmony export */   l: () => (/* binding */ lerp),\n/* harmony export */   m: () => (/* binding */ misc),\n/* harmony export */   n: () => (/* binding */ normalize),\n/* harmony export */   o: () => (/* binding */ coordinateToPoint),\n/* harmony export */   p: () => (/* binding */ pointOnCubeToPointOnSphere),\n/* harmony export */   q: () => (/* binding */ planeSegmentIntersection),\n/* harmony export */   r: () => (/* binding */ radToDeg),\n/* harmony export */   s: () => (/* binding */ pointToPlaneDistance),\n/* harmony export */   t: () => (/* binding */ getIndexFrom3D),\n/* harmony export */   u: () => (/* binding */ get3DFromIndex),\n/* harmony export */   v: () => (/* binding */ vectorEquals),\n/* harmony export */   w: () => (/* binding */ getIndexFrom2D),\n/* harmony export */   x: () => (/* binding */ get2DFromIndex)\n/* harmony export */ });\n/* harmony import */ var _triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./triangle-b62b9067.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./matrix-baa530bf.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js\");\n\n\n\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n      radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n\n    return a.y - b.y;\n  }\n\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)((0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)((0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(_ref2, 2),\n      low1 = _ref4[0],\n      high1 = _ref4[1];\n\n  var _ref5 = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(_ref3, 2),\n      low2 = _ref5[0],\n      high2 = _ref5[1];\n\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new three__WEBPACK_IMPORTED_MODULE_2__.Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n\n  var _final = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)((0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)(i, vx), vxsquared);\n\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(segment, 2),\n      a = _segment[0],\n      b = _segment[1];\n\n  var matrix = rotateVectorOnVector(plane.normal, new three__WEBPACK_IMPORTED_MODULE_2__.Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new three__WEBPACK_IMPORTED_MODULE_2__.Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(coords, 3),\n      ix = _coords[0],\n      iy = _coords[1],\n      iz = _coords[2];\n\n  var _sides = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(sides, 2),\n      rx = _sides[0],\n      ry = _sides[1];\n\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = (0,_triangle_b62b9067_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(size, 2),\n      rx = _size[0],\n      ry = _size[1];\n\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\n\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/misc-7d870b3c.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _objectSpread2),\n/* harmony export */   a: () => (/* binding */ _defineProperty)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/objectSpread2-284232a6.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/three-eb2ad8c0.esm.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/three-eb2ad8c0.esm.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   b: () => (/* binding */ bufferToVectors),\n/* harmony export */   t: () => (/* binding */ three),\n/* harmony export */   v: () => (/* binding */ vectorsToBuffer)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n/**\n * Helpers for converting buffers to and from Three.js objects\n */\n\n/**\n * Convents passed buffer of passed stride to an array of vectors with the correct length.\n *\n * @param buffer\n * @param stride\n * @returns\n */\nfunction bufferToVectors(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var p = [];\n\n  for (var i = 0, j = 0; i < buffer.length; i += stride, j++) {\n    if (stride === 3) {\n      p[j] = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(buffer[i], buffer[i + 1], buffer[i + 2]);\n    } else {\n      p[j] = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(buffer[i], buffer[i + 1]);\n    }\n  }\n\n  return p;\n}\n/**\n * Transforms a passed Vector2 or Vector3 array to a points buffer\n *\n * @param vectorArray\n * @returns\n */\n\nfunction vectorsToBuffer(vectorArray) {\n  var l = vectorArray.length;\n  var stride = vectorArray[0].hasOwnProperty(\"z\") ? 3 : 2;\n  var buffer = new Float32Array(l * stride);\n\n  for (var i = 0; i < l; i++) {\n    var j = i * stride;\n    buffer[j] = vectorArray[i].x;\n    buffer[j + 1] = vectorArray[i].y;\n\n    if (stride === 3) {\n      buffer[j + 2] = vectorArray[i].z;\n    }\n  }\n\n  return buffer;\n}\n\nvar three = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  bufferToVectors: bufferToVectors,\n  vectorsToBuffer: vectorsToBuffer\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/three-eb2ad8c0.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _slicedToArray),\n/* harmony export */   a: () => (/* binding */ _toConsumableArray),\n/* harmony export */   b: () => (/* binding */ triangleDeterminant),\n/* harmony export */   c: () => (/* binding */ arePointsCollinear),\n/* harmony export */   d: () => (/* binding */ doThreePointsMakeARight),\n/* harmony export */   e: () => (/* binding */ isTriangleClockwise),\n/* harmony export */   f: () => (/* binding */ isPointInCircumcircle),\n/* harmony export */   g: () => (/* binding */ getCircumcircle),\n/* harmony export */   i: () => (/* binding */ isPointInTriangle),\n/* harmony export */   t: () => (/* binding */ triangle)\n/* harmony export */ });\n/* harmony import */ var _isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isNativeReflectConstruct-5594d075.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./matrix-baa530bf.esm.js */ \"(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/matrix-baa530bf.esm.js\");\n\n\n\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _construct(Parent, args, Class) {\n  if ((0,_isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) (0,_isNativeReflectConstruct_5594d075_esm_js__WEBPACK_IMPORTED_MODULE_0__._)(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n      ax = _triangle$[0],\n      ay = _triangle$[1];\n\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$2[0],\n      by = _triangle$2[1];\n\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$3[0],\n      cy = _triangle$3[1];\n\n  var _point = _slicedToArray(point, 2),\n      px = _point[0],\n      py = _point[1]; // TODO Sub with static calc\n\n\n  var matrix = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n      x1 = _triangle$4[0],\n      y1 = _triangle$4[1];\n\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n      x2 = _triangle$5[0],\n      y2 = _triangle$5[1];\n\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n      x3 = _triangle$6[0],\n      y3 = _triangle$6[1]; // prettier-ignore\n\n\n  return (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.d)(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n      ax = _triangle$7[0],\n      ay = _triangle$7[1]; // @ts-ignore\n\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$8[0],\n      by = _triangle$8[1]; // @ts-ignore\n\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$9[0],\n      cy = _triangle$9[1];\n\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new three__WEBPACK_IMPORTED_MODULE_2__.Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.g)(m, 1, 1);\n  var m13 = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.g)(m, 1, 3);\n  var m12 = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.g)(m, 1, 2);\n  var m14 = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.g)(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n      _ref2 = _slicedToArray(_ref, 2),\n      ax = _ref2[0],\n      ay = _ref2[1];\n\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n      _ref4 = _slicedToArray(_ref3, 2),\n      bx = _ref4[0],\n      by = _ref4[1];\n\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n      _ref6 = _slicedToArray(_ref5, 2),\n      cx = _ref6[0],\n      cy = _ref6[1];\n\n  var _point2 = _slicedToArray(point, 2),\n      px = _point2[0],\n      py = _point2[1];\n\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = (0,_matrix_baa530bf_esm_js__WEBPACK_IMPORTED_MODULE_1__.d)(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector2();\nvar mv2 = new three__WEBPACK_IMPORTED_MODULE_2__.Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n    if (Array.isArray(p)) {\n      return _construct(three__WEBPACK_IMPORTED_MODULE_2__.Vector2, _toConsumableArray(p));\n    }\n\n    return p;\n  }),\n      _points$map2 = _slicedToArray(_points$map, 3),\n      p1 = _points$map2[0],\n      p2 = _points$map2[1],\n      p3 = _points$map2[2];\n\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\n\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/triangle-b62b9067.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector2-d2bf51f1.esm.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector2-d2bf51f1.esm.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ add),\n/* harmony export */   b: () => (/* binding */ addValue),\n/* harmony export */   c: () => (/* binding */ subValue),\n/* harmony export */   d: () => (/* binding */ scale),\n/* harmony export */   e: () => (/* binding */ dot),\n/* harmony export */   f: () => (/* binding */ length),\n/* harmony export */   g: () => (/* binding */ distance),\n/* harmony export */   l: () => (/* binding */ lengthSqr),\n/* harmony export */   o: () => (/* binding */ one),\n/* harmony export */   s: () => (/* binding */ sub),\n/* harmony export */   v: () => (/* binding */ vector2),\n/* harmony export */   z: () => (/* binding */ zero)\n/* harmony export */ });\n/**\n *\n */\nfunction zero() {\n  return [0, 0];\n}\nfunction one() {\n  return [1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]));\n}\n\nvar vector2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector2-d2bf51f1.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector3-0a088b7f.esm.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector3-0a088b7f.esm.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ add),\n/* harmony export */   b: () => (/* binding */ addValue),\n/* harmony export */   c: () => (/* binding */ subValue),\n/* harmony export */   d: () => (/* binding */ scale),\n/* harmony export */   e: () => (/* binding */ dot),\n/* harmony export */   f: () => (/* binding */ cross),\n/* harmony export */   g: () => (/* binding */ length),\n/* harmony export */   h: () => (/* binding */ distance),\n/* harmony export */   l: () => (/* binding */ lengthSqr),\n/* harmony export */   o: () => (/* binding */ one),\n/* harmony export */   s: () => (/* binding */ sub),\n/* harmony export */   v: () => (/* binding */ vector3),\n/* harmony export */   z: () => (/* binding */ zero)\n/* harmony export */ });\n/**\n *\n */\nfunction zero() {\n  return [0, 0, 0];\n}\nfunction one() {\n  return [1, 1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n, a[2] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n, a[2] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n, a[2] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cross(a, b) {\n  var x = a[1] * b[2] - a[2] * b[1];\n  var y = a[2] * b[0] - a[0] * b[2];\n  var z = a[0] * b[1] - a[1] * b[0];\n  return [x, y, z];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1] + a[2] * a[2];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]) + (a[2] - b[2]) * (a[2] - b[2]));\n}\n\nvar vector3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  cross: cross,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/maath@0.6.0_@types+three@0.168.0_three@0.167.1/node_modules/maath/dist/vector3-0a088b7f.esm.js\n");

/***/ })

};
;