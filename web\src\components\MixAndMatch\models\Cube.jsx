import React from "react";
import { useGLTF } from "@react-three/drei";

export function Cube(props) {
  const { nodes, materials } = useGLTF("/models/mix-and-match/cube.glb");
  return (
    <group {...props} dispose={null}>
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Cube.geometry}
        material={materials.Material_0}
      />
    </group>
  );
}

useGLTF.preload("/models/mix-and-match/cube.glb");
