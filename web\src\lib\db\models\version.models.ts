import { Schema, model, models, Document } from "mongoose";

export interface IVersion extends Document {
  userId: string;
  workspaceId: string; // or ringId, depending on your naming
  name: string;
  config: Record<string, any>; // the sceneConfig JSON
  versionNumber: number;
  createdAt: Date;
  updatedAt: Date;
}

const VersionSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    workspaceId: {
      type: Schema.Types.ObjectId,
      ref: "Workspace",
      required: true,
      index: true,
    },
    name: {
      type: String,
      required: true,
    },
    config: {
      type: Schema.Types.Mixed, // allows any JSON
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    versionNumber: {
      type: Number,
      required: true,
      index: true,
    },
  },
  {
    timestamps: true, // adds createdAt and updatedAt
  }
);

VersionSchema.index({ userId: 1, workspaceId: 1 });

const Version = models.Version || model("Version", VersionSchema);
export default Version;
