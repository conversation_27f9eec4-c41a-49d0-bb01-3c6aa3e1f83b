"use client";
import { useState } from "react";
import { Canvas } from "@react-three/fiber";
import ConfiguratorUI from "@/components/MixAndMatch/ConfiguratorUI.jsx";
import { Bloom, EffectComposer, Vignette } from "@react-three/postprocessing";
import Experience from "@/components/MixAndMatch/Experience.jsx";

// Material options for the configurator
const MATERIAL_OPTIONS = [
  {
    id: "grey",
    name: "Untextured",
    color: "#d8d8d8",
  },
  {
    id: "gold",
    name: "Gold",
    color: "#E5b377",
  },
  {
    id: "rose-gold",
    name: "Rose Gold",
    color: "#e8b4a0",
  },
  {
    id: "silver",
    name: "Silver",
    color: "#c2c2c3",
  },
];

function MixAndMatchPage() {
  const [selectedParts, setSelectedParts] = useState({
    crown: "crown-A",
    ring: "ring-A",
    paw: "paw-A",
    material: "grey",
  });

  const [autoRotate, setAutoRotate] = useState(true);
  const [enablePostProcessing, setEnablePostProcessing] = useState(false);
  const [activeSection, setActiveSection] = useState("material");

  const handlePartSelect = (partType: string, partId: string) => {
    setSelectedParts((prev) => ({
      ...prev,
      [partType]: partId,
    }));
  };

  const resetConfiguration = () => {
    setSelectedParts({
      crown: "crown-A",
      ring: "ring-A",
      paw: "paw-A",
      material: "grey",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      <ConfiguratorUI
        selectedParts={selectedParts}
        handlePartSelect={handlePartSelect}
        resetConfiguration={resetConfiguration}
        autoRotate={autoRotate}
        setAutoRotate={setAutoRotate}
        materialOptions={MATERIAL_OPTIONS}
        enablePostProcessing={enablePostProcessing}
        setEnablePostProcessing={setEnablePostProcessing}
        activeSection={activeSection}
        setActiveSection={setActiveSection}
      />

      {/* Main 3D Canvas */}
      <div className="relative z-10 h-screen w-full">
        <div
          className="h-full w-full relative"
          style={{
            background: "linear-gradient(to bottom right, #363643, #5C5C6E)",
          }}
        >
          <Canvas
            camera={{ position: [0, 0, 4], fov: 45 }}
            shadows
            dpr={[1, 2]}
          >
            <Experience
              selectedParts={selectedParts}
              autoRotate={autoRotate}
              materialOptions={MATERIAL_OPTIONS}
            />

            {enablePostProcessing && (
              <EffectComposer>
                <Bloom mipmapBlur luminanceThreshold={0.8} intensity={0.25} />
                <Vignette eskil={false} offset={0.1} darkness={0.5} />
              </EffectComposer>
            )}
          </Canvas>
        </div>
      </div>
    </div>
  );
}

export default MixAndMatchPage;
