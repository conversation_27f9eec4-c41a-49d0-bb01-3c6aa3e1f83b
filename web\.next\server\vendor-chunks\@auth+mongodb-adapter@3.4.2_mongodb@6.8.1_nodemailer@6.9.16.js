"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16";
exports.ids = ["vendor-chunks/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16/node_modules/@auth/mongodb-adapter/index.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16/node_modules/@auth/mongodb-adapter/index.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MongoDBAdapter: () => (/* binding */ MongoDBAdapter),\n/* harmony export */   _id: () => (/* binding */ _id),\n/* harmony export */   defaultCollections: () => (/* binding */ defaultCollections),\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\nvar __addDisposableResource = (undefined && undefined.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (undefined && undefined.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  <p>Official <a href=\"https://www.mongodb.com\">MongoDB</a> adapter for Auth.js / NextAuth.js.</p>\n *  <a href=\"https://www.mongodb.com\">\n *   <img style={{display: \"block\"}} src=\"https://authjs.dev/img/adapters/mongodb.svg\" width=\"30\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @auth/mongodb-adapter mongodb\n * ```\n *\n * @module @auth/mongodb-adapter\n */\n\n/**\n * This adapter uses https://www.typescriptlang.org/docs/handbook/release-notes/typescript-5-2.html#using-declarations-and-explicit-resource-management.\n * This feature is very new and requires runtime polyfills for `Symbol.asyncDispose` in order to work properly in all environments.\n * It is also required to set in the `tsconfig.json` file the compilation target to `es2022` or below and configure the `lib` option to include `esnext` or `esnext.disposable`.\n *\n * You can find more information about this feature and the polyfills in the link above.\n */\n// @ts-expect-error read only property is not assignable\nSymbol.asyncDispose ?? (Symbol.asyncDispose = Symbol(\"Symbol.asyncDispose\"));\nconst defaultCollections = {\n    Users: \"users\",\n    Accounts: \"accounts\",\n    Sessions: \"sessions\",\n    VerificationTokens: \"verification_tokens\",\n};\nconst format = {\n    /** Takes a MongoDB object and returns a plain old JavaScript object */\n    from(object) {\n        const newObject = {};\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"_id\") {\n                newObject.id = value.toHexString();\n            }\n            else if (key === \"userId\") {\n                newObject[key] = value.toHexString();\n            }\n            else {\n                newObject[key] = value;\n            }\n        }\n        return newObject;\n    },\n    /** Takes a plain old JavaScript object and turns it into a MongoDB object */\n    to(object) {\n        const newObject = {\n            _id: _id(object.id),\n        };\n        for (const key in object) {\n            const value = object[key];\n            if (key === \"userId\")\n                newObject[key] = _id(value);\n            else if (key === \"id\")\n                continue;\n            else\n                newObject[key] = value;\n        }\n        return newObject;\n    },\n};\n/** @internal */\nfunction _id(hex) {\n    if (hex?.length !== 24)\n        return new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId();\n    return new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(hex);\n}\nfunction MongoDBAdapter(\n/**\n * The MongoDB client.\n *\n * The MongoDB team recommends providing a non-connected `MongoClient` instance to avoid unhandled promise rejections if the client fails to connect.\n *\n * Alternatively, you can also pass:\n * - A promise that resolves to a connected `MongoClient` (not recommended).\n * - A function, to handle more complex and custom connection strategies.\n *\n * Using a function that returns `MongoClient | Promise<MongoClient>`, combined with `options.onClose`, can be useful when you want a more advanced and customized connection strategy to address challenges related to persistence, container reuse, and connection closure.\n */\nclient, options = {}) {\n    const { collections } = options;\n    const { from, to } = format;\n    const getDb = async () => {\n        const _client = await (typeof client === \"function\"\n            ? client()\n            : client);\n        const _db = _client.db(options.databaseName);\n        const c = { ...defaultCollections, ...collections };\n        return {\n            U: _db.collection(c.Users),\n            A: _db.collection(c.Accounts),\n            S: _db.collection(c.Sessions),\n            V: _db.collection(c?.VerificationTokens),\n            [Symbol.asyncDispose]: async () => {\n                await options.onClose?.(_client);\n            },\n        };\n    };\n    return {\n        async createUser(data) {\n            const env_1 = { stack: [], error: void 0, hasError: false };\n            try {\n                const user = to(data);\n                const db = __addDisposableResource(env_1, await getDb(), true);\n                await db.U.insertOne(user);\n                return from(user);\n            }\n            catch (e_1) {\n                env_1.error = e_1;\n                env_1.hasError = true;\n            }\n            finally {\n                const result_1 = __disposeResources(env_1);\n                if (result_1)\n                    await result_1;\n            }\n        },\n        async getUser(id) {\n            const env_2 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_2, await getDb(), true);\n                const user = await db.U.findOne({ _id: _id(id) });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_2) {\n                env_2.error = e_2;\n                env_2.hasError = true;\n            }\n            finally {\n                const result_2 = __disposeResources(env_2);\n                if (result_2)\n                    await result_2;\n            }\n        },\n        async getUserByEmail(email) {\n            const env_3 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_3, await getDb(), true);\n                const user = await db.U.findOne({ email });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_3) {\n                env_3.error = e_3;\n                env_3.hasError = true;\n            }\n            finally {\n                const result_3 = __disposeResources(env_3);\n                if (result_3)\n                    await result_3;\n            }\n        },\n        async getUserByAccount(provider_providerAccountId) {\n            const env_4 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_4, await getDb(), true);\n                const account = await db.A.findOne(provider_providerAccountId);\n                if (!account)\n                    return null;\n                const user = await db.U.findOne({ _id: new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(account.userId) });\n                if (!user)\n                    return null;\n                return from(user);\n            }\n            catch (e_4) {\n                env_4.error = e_4;\n                env_4.hasError = true;\n            }\n            finally {\n                const result_4 = __disposeResources(env_4);\n                if (result_4)\n                    await result_4;\n            }\n        },\n        async updateUser(data) {\n            const env_5 = { stack: [], error: void 0, hasError: false };\n            try {\n                const { _id, ...user } = to(data);\n                const db = __addDisposableResource(env_5, await getDb(), true);\n                const result = await db.U.findOneAndUpdate({ _id }, { $set: user }, { returnDocument: \"after\" });\n                return from(result);\n            }\n            catch (e_5) {\n                env_5.error = e_5;\n                env_5.hasError = true;\n            }\n            finally {\n                const result_5 = __disposeResources(env_5);\n                if (result_5)\n                    await result_5;\n            }\n        },\n        async deleteUser(id) {\n            const env_6 = { stack: [], error: void 0, hasError: false };\n            try {\n                const userId = _id(id);\n                const db = __addDisposableResource(env_6, await getDb(), true);\n                await Promise.all([\n                    db.A.deleteMany({ userId: userId }),\n                    db.S.deleteMany({ userId: userId }),\n                    db.U.deleteOne({ _id: userId }),\n                ]);\n            }\n            catch (e_6) {\n                env_6.error = e_6;\n                env_6.hasError = true;\n            }\n            finally {\n                const result_6 = __disposeResources(env_6);\n                if (result_6)\n                    await result_6;\n            }\n        },\n        linkAccount: async (data) => {\n            const env_7 = { stack: [], error: void 0, hasError: false };\n            try {\n                const account = to(data);\n                const db = __addDisposableResource(env_7, await getDb(), true);\n                await db.A.insertOne(account);\n                return account;\n            }\n            catch (e_7) {\n                env_7.error = e_7;\n                env_7.hasError = true;\n            }\n            finally {\n                const result_7 = __disposeResources(env_7);\n                if (result_7)\n                    await result_7;\n            }\n        },\n        async unlinkAccount(provider_providerAccountId) {\n            const env_8 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_8, await getDb(), true);\n                const account = await db.A.findOneAndDelete(provider_providerAccountId);\n                return from(account);\n            }\n            catch (e_8) {\n                env_8.error = e_8;\n                env_8.hasError = true;\n            }\n            finally {\n                const result_8 = __disposeResources(env_8);\n                if (result_8)\n                    await result_8;\n            }\n        },\n        async getSessionAndUser(sessionToken) {\n            const env_9 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_9, await getDb(), true);\n                const session = await db.S.findOne({ sessionToken });\n                if (!session)\n                    return null;\n                const user = await db.U.findOne({ _id: new mongodb__WEBPACK_IMPORTED_MODULE_0__.ObjectId(session.userId) });\n                if (!user)\n                    return null;\n                return {\n                    user: from(user),\n                    session: from(session),\n                };\n            }\n            catch (e_9) {\n                env_9.error = e_9;\n                env_9.hasError = true;\n            }\n            finally {\n                const result_9 = __disposeResources(env_9);\n                if (result_9)\n                    await result_9;\n            }\n        },\n        async createSession(data) {\n            const env_10 = { stack: [], error: void 0, hasError: false };\n            try {\n                const session = to(data);\n                const db = __addDisposableResource(env_10, await getDb(), true);\n                await db.S.insertOne(session);\n                return from(session);\n            }\n            catch (e_10) {\n                env_10.error = e_10;\n                env_10.hasError = true;\n            }\n            finally {\n                const result_10 = __disposeResources(env_10);\n                if (result_10)\n                    await result_10;\n            }\n        },\n        async updateSession(data) {\n            const env_11 = { stack: [], error: void 0, hasError: false };\n            try {\n                const { _id, ...session } = to(data);\n                const db = __addDisposableResource(env_11, await getDb(), true);\n                const updatedSession = await db.S.findOneAndUpdate({ sessionToken: session.sessionToken }, { $set: session }, { returnDocument: \"after\" });\n                return from(updatedSession);\n            }\n            catch (e_11) {\n                env_11.error = e_11;\n                env_11.hasError = true;\n            }\n            finally {\n                const result_11 = __disposeResources(env_11);\n                if (result_11)\n                    await result_11;\n            }\n        },\n        async deleteSession(sessionToken) {\n            const env_12 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_12, await getDb(), true);\n                const session = await db.S.findOneAndDelete({\n                    sessionToken,\n                });\n                return from(session);\n            }\n            catch (e_12) {\n                env_12.error = e_12;\n                env_12.hasError = true;\n            }\n            finally {\n                const result_12 = __disposeResources(env_12);\n                if (result_12)\n                    await result_12;\n            }\n        },\n        async createVerificationToken(data) {\n            const env_13 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_13, await getDb(), true);\n                await db.V.insertOne(to(data));\n                return data;\n            }\n            catch (e_13) {\n                env_13.error = e_13;\n                env_13.hasError = true;\n            }\n            finally {\n                const result_13 = __disposeResources(env_13);\n                if (result_13)\n                    await result_13;\n            }\n        },\n        async useVerificationToken(identifier_token) {\n            const env_14 = { stack: [], error: void 0, hasError: false };\n            try {\n                const db = __addDisposableResource(env_14, await getDb(), true);\n                const verificationToken = await db.V.findOneAndDelete(identifier_token);\n                if (!verificationToken)\n                    return null;\n                const { _id, ...rest } = verificationToken;\n                return rest;\n            }\n            catch (e_14) {\n                env_14.error = e_14;\n                env_14.hasError = true;\n            }\n            finally {\n                const result_14 = __disposeResources(env_14);\n                if (result_14)\n                    await result_14;\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF1dGgrbW9uZ29kYi1hZGFwdGVyQDMuNC4yX21vbmdvZGJANi44LjFfbm9kZW1haWxlckA2LjkuMTYvbm9kZV9tb2R1bGVzL0BhdXRoL21vbmdvZGItYWRhcHRlci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLCtCQUErQixTQUFJLElBQUksU0FBSTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOENBQThDO0FBQ3ZFO0FBQ0E7QUFDQSx5QkFBeUIsYUFBYTtBQUN0QztBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsU0FBSSxJQUFJLFNBQUk7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyRkFBMkYsU0FBUyxnQkFBZ0I7QUFDcEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxnQkFBZ0IscUZBQXFGO0FBQ3JHO0FBQ0E7QUFDQSxrQkFBa0IsbUJBQW1CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNPO0FBQ1A7QUFDQSxtQkFBbUIsNkNBQVE7QUFDM0IsZUFBZSw2Q0FBUTtBQUN2QjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQixZQUFZLGNBQWM7QUFDMUIsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQSxrREFBa0QsY0FBYztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0Esa0RBQWtELE9BQU87QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxTQUFTLDZDQUFRLGtCQUFrQjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBLHdCQUF3QixlQUFlO0FBQ3ZDO0FBQ0EsNkRBQTZELEtBQUssSUFBSSxZQUFZLElBQUkseUJBQXlCO0FBQy9HO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsZ0JBQWdCO0FBQ3RELHNDQUFzQyxnQkFBZ0I7QUFDdEQscUNBQXFDLGFBQWE7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0EscURBQXFELGNBQWM7QUFDbkU7QUFDQTtBQUNBLGtEQUFrRCxTQUFTLDZDQUFRLGtCQUFrQjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQSx3QkFBd0Isa0JBQWtCO0FBQzFDO0FBQ0EscUVBQXFFLG9DQUFvQyxJQUFJLGVBQWUsSUFBSSx5QkFBeUI7QUFDeko7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixlQUFlO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXV0aCttb25nb2RiLWFkYXB0ZXJAMy40LjJfbW9uZ29kYkA2LjguMV9ub2RlbWFpbGVyQDYuOS4xNi9ub2RlX21vZHVsZXMvQGF1dGgvbW9uZ29kYi1hZGFwdGVyL2luZGV4LmpzP2NjYWMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fYWRkRGlzcG9zYWJsZVJlc291cmNlID0gKHRoaXMgJiYgdGhpcy5fX2FkZERpc3Bvc2FibGVSZXNvdXJjZSkgfHwgZnVuY3Rpb24gKGVudiwgdmFsdWUsIGFzeW5jKSB7XG4gICAgaWYgKHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSB2b2lkIDApIHtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgdmFsdWUgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIk9iamVjdCBleHBlY3RlZC5cIik7XG4gICAgICAgIHZhciBkaXNwb3NlO1xuICAgICAgICBpZiAoYXN5bmMpIHtcbiAgICAgICAgICAgIGlmICghU3ltYm9sLmFzeW5jRGlzcG9zZSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlN5bWJvbC5hc3luY0Rpc3Bvc2UgaXMgbm90IGRlZmluZWQuXCIpO1xuICAgICAgICAgICAgZGlzcG9zZSA9IHZhbHVlW1N5bWJvbC5hc3luY0Rpc3Bvc2VdO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkaXNwb3NlID09PSB2b2lkIDApIHtcbiAgICAgICAgICAgIGlmICghU3ltYm9sLmRpc3Bvc2UpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuZGlzcG9zZSBpcyBub3QgZGVmaW5lZC5cIik7XG4gICAgICAgICAgICBkaXNwb3NlID0gdmFsdWVbU3ltYm9sLmRpc3Bvc2VdO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGlzcG9zZSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiT2JqZWN0IG5vdCBkaXNwb3NhYmxlLlwiKTtcbiAgICAgICAgZW52LnN0YWNrLnB1c2goeyB2YWx1ZTogdmFsdWUsIGRpc3Bvc2U6IGRpc3Bvc2UsIGFzeW5jOiBhc3luYyB9KTtcbiAgICB9XG4gICAgZWxzZSBpZiAoYXN5bmMpIHtcbiAgICAgICAgZW52LnN0YWNrLnB1c2goeyBhc3luYzogdHJ1ZSB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcbnZhciBfX2Rpc3Bvc2VSZXNvdXJjZXMgPSAodGhpcyAmJiB0aGlzLl9fZGlzcG9zZVJlc291cmNlcykgfHwgKGZ1bmN0aW9uIChTdXBwcmVzc2VkRXJyb3IpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKGVudikge1xuICAgICAgICBmdW5jdGlvbiBmYWlsKGUpIHtcbiAgICAgICAgICAgIGVudi5lcnJvciA9IGVudi5oYXNFcnJvciA/IG5ldyBTdXBwcmVzc2VkRXJyb3IoZSwgZW52LmVycm9yLCBcIkFuIGVycm9yIHdhcyBzdXBwcmVzc2VkIGR1cmluZyBkaXNwb3NhbC5cIikgOiBlO1xuICAgICAgICAgICAgZW52Lmhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBmdW5jdGlvbiBuZXh0KCkge1xuICAgICAgICAgICAgd2hpbGUgKGVudi5zdGFjay5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICB2YXIgcmVjID0gZW52LnN0YWNrLnBvcCgpO1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciByZXN1bHQgPSByZWMuZGlzcG9zZSAmJiByZWMuZGlzcG9zZS5jYWxsKHJlYy52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZWMuYXN5bmMpIHJldHVybiBQcm9taXNlLnJlc29sdmUocmVzdWx0KS50aGVuKG5leHQsIGZ1bmN0aW9uKGUpIHsgZmFpbChlKTsgcmV0dXJuIG5leHQoKTsgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIGZhaWwoZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGVudi5oYXNFcnJvcikgdGhyb3cgZW52LmVycm9yO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXh0KCk7XG4gICAgfTtcbn0pKHR5cGVvZiBTdXBwcmVzc2VkRXJyb3IgPT09IFwiZnVuY3Rpb25cIiA/IFN1cHByZXNzZWRFcnJvciA6IGZ1bmN0aW9uIChlcnJvciwgc3VwcHJlc3NlZCwgbWVzc2FnZSkge1xuICAgIHZhciBlID0gbmV3IEVycm9yKG1lc3NhZ2UpO1xuICAgIHJldHVybiBlLm5hbWUgPSBcIlN1cHByZXNzZWRFcnJvclwiLCBlLmVycm9yID0gZXJyb3IsIGUuc3VwcHJlc3NlZCA9IHN1cHByZXNzZWQsIGU7XG59KTtcbi8qKlxuICogPGRpdiBzdHlsZT17e2Rpc3BsYXk6IFwiZmxleFwiLCBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsIHBhZGRpbmc6IDE2fX0+XG4gKiAgPHA+T2ZmaWNpYWwgPGEgaHJlZj1cImh0dHBzOi8vd3d3Lm1vbmdvZGIuY29tXCI+TW9uZ29EQjwvYT4gYWRhcHRlciBmb3IgQXV0aC5qcyAvIE5leHRBdXRoLmpzLjwvcD5cbiAqICA8YSBocmVmPVwiaHR0cHM6Ly93d3cubW9uZ29kYi5jb21cIj5cbiAqICAgPGltZyBzdHlsZT17e2Rpc3BsYXk6IFwiYmxvY2tcIn19IHNyYz1cImh0dHBzOi8vYXV0aGpzLmRldi9pbWcvYWRhcHRlcnMvbW9uZ29kYi5zdmdcIiB3aWR0aD1cIjMwXCIgLz5cbiAqICA8L2E+XG4gKiA8L2Rpdj5cbiAqXG4gKiAjIyBJbnN0YWxsYXRpb25cbiAqXG4gKiBgYGBiYXNoIG5wbTJ5YXJuXG4gKiBucG0gaW5zdGFsbCBAYXV0aC9tb25nb2RiLWFkYXB0ZXIgbW9uZ29kYlxuICogYGBgXG4gKlxuICogQG1vZHVsZSBAYXV0aC9tb25nb2RiLWFkYXB0ZXJcbiAqL1xuaW1wb3J0IHsgT2JqZWN0SWQgfSBmcm9tIFwibW9uZ29kYlwiO1xuLyoqXG4gKiBUaGlzIGFkYXB0ZXIgdXNlcyBodHRwczovL3d3dy50eXBlc2NyaXB0bGFuZy5vcmcvZG9jcy9oYW5kYm9vay9yZWxlYXNlLW5vdGVzL3R5cGVzY3JpcHQtNS0yLmh0bWwjdXNpbmctZGVjbGFyYXRpb25zLWFuZC1leHBsaWNpdC1yZXNvdXJjZS1tYW5hZ2VtZW50LlxuICogVGhpcyBmZWF0dXJlIGlzIHZlcnkgbmV3IGFuZCByZXF1aXJlcyBydW50aW1lIHBvbHlmaWxscyBmb3IgYFN5bWJvbC5hc3luY0Rpc3Bvc2VgIGluIG9yZGVyIHRvIHdvcmsgcHJvcGVybHkgaW4gYWxsIGVudmlyb25tZW50cy5cbiAqIEl0IGlzIGFsc28gcmVxdWlyZWQgdG8gc2V0IGluIHRoZSBgdHNjb25maWcuanNvbmAgZmlsZSB0aGUgY29tcGlsYXRpb24gdGFyZ2V0IHRvIGBlczIwMjJgIG9yIGJlbG93IGFuZCBjb25maWd1cmUgdGhlIGBsaWJgIG9wdGlvbiB0byBpbmNsdWRlIGBlc25leHRgIG9yIGBlc25leHQuZGlzcG9zYWJsZWAuXG4gKlxuICogWW91IGNhbiBmaW5kIG1vcmUgaW5mb3JtYXRpb24gYWJvdXQgdGhpcyBmZWF0dXJlIGFuZCB0aGUgcG9seWZpbGxzIGluIHRoZSBsaW5rIGFib3ZlLlxuICovXG4vLyBAdHMtZXhwZWN0LWVycm9yIHJlYWQgb25seSBwcm9wZXJ0eSBpcyBub3QgYXNzaWduYWJsZVxuU3ltYm9sLmFzeW5jRGlzcG9zZSA/PyAoU3ltYm9sLmFzeW5jRGlzcG9zZSA9IFN5bWJvbChcIlN5bWJvbC5hc3luY0Rpc3Bvc2VcIikpO1xuZXhwb3J0IGNvbnN0IGRlZmF1bHRDb2xsZWN0aW9ucyA9IHtcbiAgICBVc2VyczogXCJ1c2Vyc1wiLFxuICAgIEFjY291bnRzOiBcImFjY291bnRzXCIsXG4gICAgU2Vzc2lvbnM6IFwic2Vzc2lvbnNcIixcbiAgICBWZXJpZmljYXRpb25Ub2tlbnM6IFwidmVyaWZpY2F0aW9uX3Rva2Vuc1wiLFxufTtcbmV4cG9ydCBjb25zdCBmb3JtYXQgPSB7XG4gICAgLyoqIFRha2VzIGEgTW9uZ29EQiBvYmplY3QgYW5kIHJldHVybnMgYSBwbGFpbiBvbGQgSmF2YVNjcmlwdCBvYmplY3QgKi9cbiAgICBmcm9tKG9iamVjdCkge1xuICAgICAgICBjb25zdCBuZXdPYmplY3QgPSB7fTtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gb2JqZWN0KSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG9iamVjdFtrZXldO1xuICAgICAgICAgICAgaWYgKGtleSA9PT0gXCJfaWRcIikge1xuICAgICAgICAgICAgICAgIG5ld09iamVjdC5pZCA9IHZhbHVlLnRvSGV4U3RyaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChrZXkgPT09IFwidXNlcklkXCIpIHtcbiAgICAgICAgICAgICAgICBuZXdPYmplY3Rba2V5XSA9IHZhbHVlLnRvSGV4U3RyaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBuZXdPYmplY3Rba2V5XSA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXdPYmplY3Q7XG4gICAgfSxcbiAgICAvKiogVGFrZXMgYSBwbGFpbiBvbGQgSmF2YVNjcmlwdCBvYmplY3QgYW5kIHR1cm5zIGl0IGludG8gYSBNb25nb0RCIG9iamVjdCAqL1xuICAgIHRvKG9iamVjdCkge1xuICAgICAgICBjb25zdCBuZXdPYmplY3QgPSB7XG4gICAgICAgICAgICBfaWQ6IF9pZChvYmplY3QuaWQpLFxuICAgICAgICB9O1xuICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBvYmplY3QpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gb2JqZWN0W2tleV07XG4gICAgICAgICAgICBpZiAoa2V5ID09PSBcInVzZXJJZFwiKVxuICAgICAgICAgICAgICAgIG5ld09iamVjdFtrZXldID0gX2lkKHZhbHVlKTtcbiAgICAgICAgICAgIGVsc2UgaWYgKGtleSA9PT0gXCJpZFwiKVxuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIG5ld09iamVjdFtrZXldID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ld09iamVjdDtcbiAgICB9LFxufTtcbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBfaWQoaGV4KSB7XG4gICAgaWYgKGhleD8ubGVuZ3RoICE9PSAyNClcbiAgICAgICAgcmV0dXJuIG5ldyBPYmplY3RJZCgpO1xuICAgIHJldHVybiBuZXcgT2JqZWN0SWQoaGV4KTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBNb25nb0RCQWRhcHRlcihcbi8qKlxuICogVGhlIE1vbmdvREIgY2xpZW50LlxuICpcbiAqIFRoZSBNb25nb0RCIHRlYW0gcmVjb21tZW5kcyBwcm92aWRpbmcgYSBub24tY29ubmVjdGVkIGBNb25nb0NsaWVudGAgaW5zdGFuY2UgdG8gYXZvaWQgdW5oYW5kbGVkIHByb21pc2UgcmVqZWN0aW9ucyBpZiB0aGUgY2xpZW50IGZhaWxzIHRvIGNvbm5lY3QuXG4gKlxuICogQWx0ZXJuYXRpdmVseSwgeW91IGNhbiBhbHNvIHBhc3M6XG4gKiAtIEEgcHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIGEgY29ubmVjdGVkIGBNb25nb0NsaWVudGAgKG5vdCByZWNvbW1lbmRlZCkuXG4gKiAtIEEgZnVuY3Rpb24sIHRvIGhhbmRsZSBtb3JlIGNvbXBsZXggYW5kIGN1c3RvbSBjb25uZWN0aW9uIHN0cmF0ZWdpZXMuXG4gKlxuICogVXNpbmcgYSBmdW5jdGlvbiB0aGF0IHJldHVybnMgYE1vbmdvQ2xpZW50IHwgUHJvbWlzZTxNb25nb0NsaWVudD5gLCBjb21iaW5lZCB3aXRoIGBvcHRpb25zLm9uQ2xvc2VgLCBjYW4gYmUgdXNlZnVsIHdoZW4geW91IHdhbnQgYSBtb3JlIGFkdmFuY2VkIGFuZCBjdXN0b21pemVkIGNvbm5lY3Rpb24gc3RyYXRlZ3kgdG8gYWRkcmVzcyBjaGFsbGVuZ2VzIHJlbGF0ZWQgdG8gcGVyc2lzdGVuY2UsIGNvbnRhaW5lciByZXVzZSwgYW5kIGNvbm5lY3Rpb24gY2xvc3VyZS5cbiAqL1xuY2xpZW50LCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IGNvbGxlY3Rpb25zIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IHsgZnJvbSwgdG8gfSA9IGZvcm1hdDtcbiAgICBjb25zdCBnZXREYiA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc3QgX2NsaWVudCA9IGF3YWl0ICh0eXBlb2YgY2xpZW50ID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgICAgID8gY2xpZW50KClcbiAgICAgICAgICAgIDogY2xpZW50KTtcbiAgICAgICAgY29uc3QgX2RiID0gX2NsaWVudC5kYihvcHRpb25zLmRhdGFiYXNlTmFtZSk7XG4gICAgICAgIGNvbnN0IGMgPSB7IC4uLmRlZmF1bHRDb2xsZWN0aW9ucywgLi4uY29sbGVjdGlvbnMgfTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIFU6IF9kYi5jb2xsZWN0aW9uKGMuVXNlcnMpLFxuICAgICAgICAgICAgQTogX2RiLmNvbGxlY3Rpb24oYy5BY2NvdW50cyksXG4gICAgICAgICAgICBTOiBfZGIuY29sbGVjdGlvbihjLlNlc3Npb25zKSxcbiAgICAgICAgICAgIFY6IF9kYi5jb2xsZWN0aW9uKGM/LlZlcmlmaWNhdGlvblRva2VucyksXG4gICAgICAgICAgICBbU3ltYm9sLmFzeW5jRGlzcG9zZV06IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICBhd2FpdCBvcHRpb25zLm9uQ2xvc2U/LihfY2xpZW50KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgfTtcbiAgICByZXR1cm4ge1xuICAgICAgICBhc3luYyBjcmVhdGVVc2VyKGRhdGEpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudl8xID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCB1c2VyID0gdG8oZGF0YSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfMSwgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgYXdhaXQgZGIuVS5pbnNlcnRPbmUodXNlcik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZyb20odXNlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV8xKSB7XG4gICAgICAgICAgICAgICAgZW52XzEuZXJyb3IgPSBlXzE7XG4gICAgICAgICAgICAgICAgZW52XzEuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzEgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzEpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfMSlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzE7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGdldFVzZXIoaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudl8yID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYiA9IF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudl8yLCBhd2FpdCBnZXREYigpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgZGIuVS5maW5kT25lKHsgX2lkOiBfaWQoaWQpIH0pO1xuICAgICAgICAgICAgICAgIGlmICghdXNlcilcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZyb20odXNlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV8yKSB7XG4gICAgICAgICAgICAgICAgZW52XzIuZXJyb3IgPSBlXzI7XG4gICAgICAgICAgICAgICAgZW52XzIuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzIgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzIpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfMilcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzI7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGdldFVzZXJCeUVtYWlsKGVtYWlsKSB7XG4gICAgICAgICAgICBjb25zdCBlbnZfMyA9IHsgc3RhY2s6IFtdLCBlcnJvcjogdm9pZCAwLCBoYXNFcnJvcjogZmFsc2UgfTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfMywgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IGRiLlUuZmluZE9uZSh7IGVtYWlsIH0pO1xuICAgICAgICAgICAgICAgIGlmICghdXNlcilcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZyb20odXNlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV8zKSB7XG4gICAgICAgICAgICAgICAgZW52XzMuZXJyb3IgPSBlXzM7XG4gICAgICAgICAgICAgICAgZW52XzMuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzMgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzMpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfMylcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzM7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGdldFVzZXJCeUFjY291bnQocHJvdmlkZXJfcHJvdmlkZXJBY2NvdW50SWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudl80ID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYiA9IF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudl80LCBhd2FpdCBnZXREYigpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50ID0gYXdhaXQgZGIuQS5maW5kT25lKHByb3ZpZGVyX3Byb3ZpZGVyQWNjb3VudElkKTtcbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnQpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBkYi5VLmZpbmRPbmUoeyBfaWQ6IG5ldyBPYmplY3RJZChhY2NvdW50LnVzZXJJZCkgfSk7XG4gICAgICAgICAgICAgICAgaWYgKCF1c2VyKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICByZXR1cm4gZnJvbSh1c2VyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlXzQpIHtcbiAgICAgICAgICAgICAgICBlbnZfNC5lcnJvciA9IGVfNDtcbiAgICAgICAgICAgICAgICBlbnZfNC5oYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHRfNCA9IF9fZGlzcG9zZVJlc291cmNlcyhlbnZfNCk7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdF80KVxuICAgICAgICAgICAgICAgICAgICBhd2FpdCByZXN1bHRfNDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgdXBkYXRlVXNlcihkYXRhKSB7XG4gICAgICAgICAgICBjb25zdCBlbnZfNSA9IHsgc3RhY2s6IFtdLCBlcnJvcjogdm9pZCAwLCBoYXNFcnJvcjogZmFsc2UgfTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBfaWQsIC4uLnVzZXIgfSA9IHRvKGRhdGEpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGRiID0gX19hZGREaXNwb3NhYmxlUmVzb3VyY2UoZW52XzUsIGF3YWl0IGdldERiKCksIHRydWUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGRiLlUuZmluZE9uZUFuZFVwZGF0ZSh7IF9pZCB9LCB7ICRzZXQ6IHVzZXIgfSwgeyByZXR1cm5Eb2N1bWVudDogXCJhZnRlclwiIH0pO1xuICAgICAgICAgICAgICAgIHJldHVybiBmcm9tKHJlc3VsdCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV81KSB7XG4gICAgICAgICAgICAgICAgZW52XzUuZXJyb3IgPSBlXzU7XG4gICAgICAgICAgICAgICAgZW52XzUuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzUgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzUpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfNSlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGRlbGV0ZVVzZXIoaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudl82ID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCB1c2VySWQgPSBfaWQoaWQpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGRiID0gX19hZGREaXNwb3NhYmxlUmVzb3VyY2UoZW52XzYsIGF3YWl0IGdldERiKCksIHRydWUpO1xuICAgICAgICAgICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgICAgICAgICAgICAgZGIuQS5kZWxldGVNYW55KHsgdXNlcklkOiB1c2VySWQgfSksXG4gICAgICAgICAgICAgICAgICAgIGRiLlMuZGVsZXRlTWFueSh7IHVzZXJJZDogdXNlcklkIH0pLFxuICAgICAgICAgICAgICAgICAgICBkYi5VLmRlbGV0ZU9uZSh7IF9pZDogdXNlcklkIH0pLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVfNikge1xuICAgICAgICAgICAgICAgIGVudl82LmVycm9yID0gZV82O1xuICAgICAgICAgICAgICAgIGVudl82Lmhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdF82ID0gX19kaXNwb3NlUmVzb3VyY2VzKGVudl82KTtcbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0XzYpXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IHJlc3VsdF82O1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBsaW5rQWNjb3VudDogYXN5bmMgKGRhdGEpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGVudl83ID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50ID0gdG8oZGF0YSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfNywgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgYXdhaXQgZGIuQS5pbnNlcnRPbmUoYWNjb3VudCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFjY291bnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV83KSB7XG4gICAgICAgICAgICAgICAgZW52XzcuZXJyb3IgPSBlXzc7XG4gICAgICAgICAgICAgICAgZW52XzcuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzcgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzcpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfNylcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0Xzc7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIHVubGlua0FjY291bnQocHJvdmlkZXJfcHJvdmlkZXJBY2NvdW50SWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudl84ID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYiA9IF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudl84LCBhd2FpdCBnZXREYigpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50ID0gYXdhaXQgZGIuQS5maW5kT25lQW5kRGVsZXRlKHByb3ZpZGVyX3Byb3ZpZGVyQWNjb3VudElkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZnJvbShhY2NvdW50KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlXzgpIHtcbiAgICAgICAgICAgICAgICBlbnZfOC5lcnJvciA9IGVfODtcbiAgICAgICAgICAgICAgICBlbnZfOC5oYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHRfOCA9IF9fZGlzcG9zZVJlc291cmNlcyhlbnZfOCk7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdF84KVxuICAgICAgICAgICAgICAgICAgICBhd2FpdCByZXN1bHRfODtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0U2Vzc2lvbkFuZFVzZXIoc2Vzc2lvblRva2VuKSB7XG4gICAgICAgICAgICBjb25zdCBlbnZfOSA9IHsgc3RhY2s6IFtdLCBlcnJvcjogdm9pZCAwLCBoYXNFcnJvcjogZmFsc2UgfTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfOSwgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGRiLlMuZmluZE9uZSh7IHNlc3Npb25Ub2tlbiB9KTtcbiAgICAgICAgICAgICAgICBpZiAoIXNlc3Npb24pXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBkYi5VLmZpbmRPbmUoeyBfaWQ6IG5ldyBPYmplY3RJZChzZXNzaW9uLnVzZXJJZCkgfSk7XG4gICAgICAgICAgICAgICAgaWYgKCF1c2VyKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICB1c2VyOiBmcm9tKHVzZXIpLFxuICAgICAgICAgICAgICAgICAgICBzZXNzaW9uOiBmcm9tKHNlc3Npb24pLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV85KSB7XG4gICAgICAgICAgICAgICAgZW52XzkuZXJyb3IgPSBlXzk7XG4gICAgICAgICAgICAgICAgZW52XzkuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzkgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzkpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfOSlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0Xzk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGNyZWF0ZVNlc3Npb24oZGF0YSkge1xuICAgICAgICAgICAgY29uc3QgZW52XzEwID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBzZXNzaW9uID0gdG8oZGF0YSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfMTAsIGF3YWl0IGdldERiKCksIHRydWUpO1xuICAgICAgICAgICAgICAgIGF3YWl0IGRiLlMuaW5zZXJ0T25lKHNlc3Npb24pO1xuICAgICAgICAgICAgICAgIHJldHVybiBmcm9tKHNlc3Npb24pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVfMTApIHtcbiAgICAgICAgICAgICAgICBlbnZfMTAuZXJyb3IgPSBlXzEwO1xuICAgICAgICAgICAgICAgIGVudl8xMC5oYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHRfMTAgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzEwKTtcbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0XzEwKVxuICAgICAgICAgICAgICAgICAgICBhd2FpdCByZXN1bHRfMTA7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIHVwZGF0ZVNlc3Npb24oZGF0YSkge1xuICAgICAgICAgICAgY29uc3QgZW52XzExID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IF9pZCwgLi4uc2Vzc2lvbiB9ID0gdG8oZGF0YSk7XG4gICAgICAgICAgICAgICAgY29uc3QgZGIgPSBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnZfMTEsIGF3YWl0IGdldERiKCksIHRydWUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRTZXNzaW9uID0gYXdhaXQgZGIuUy5maW5kT25lQW5kVXBkYXRlKHsgc2Vzc2lvblRva2VuOiBzZXNzaW9uLnNlc3Npb25Ub2tlbiB9LCB7ICRzZXQ6IHNlc3Npb24gfSwgeyByZXR1cm5Eb2N1bWVudDogXCJhZnRlclwiIH0pO1xuICAgICAgICAgICAgICAgIHJldHVybiBmcm9tKHVwZGF0ZWRTZXNzaW9uKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlXzExKSB7XG4gICAgICAgICAgICAgICAgZW52XzExLmVycm9yID0gZV8xMTtcbiAgICAgICAgICAgICAgICBlbnZfMTEuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzExID0gX19kaXNwb3NlUmVzb3VyY2VzKGVudl8xMSk7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdF8xMSlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzExO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBkZWxldGVTZXNzaW9uKHNlc3Npb25Ub2tlbikge1xuICAgICAgICAgICAgY29uc3QgZW52XzEyID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYiA9IF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudl8xMiwgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGRiLlMuZmluZE9uZUFuZERlbGV0ZSh7XG4gICAgICAgICAgICAgICAgICAgIHNlc3Npb25Ub2tlbixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZnJvbShzZXNzaW9uKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlXzEyKSB7XG4gICAgICAgICAgICAgICAgZW52XzEyLmVycm9yID0gZV8xMjtcbiAgICAgICAgICAgICAgICBlbnZfMTIuaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0XzEyID0gX19kaXNwb3NlUmVzb3VyY2VzKGVudl8xMik7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdF8xMilcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0XzEyO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBjcmVhdGVWZXJpZmljYXRpb25Ub2tlbihkYXRhKSB7XG4gICAgICAgICAgICBjb25zdCBlbnZfMTMgPSB7IHN0YWNrOiBbXSwgZXJyb3I6IHZvaWQgMCwgaGFzRXJyb3I6IGZhbHNlIH07XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRiID0gX19hZGREaXNwb3NhYmxlUmVzb3VyY2UoZW52XzEzLCBhd2FpdCBnZXREYigpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBhd2FpdCBkYi5WLmluc2VydE9uZSh0byhkYXRhKSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZV8xMykge1xuICAgICAgICAgICAgICAgIGVudl8xMy5lcnJvciA9IGVfMTM7XG4gICAgICAgICAgICAgICAgZW52XzEzLmhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdF8xMyA9IF9fZGlzcG9zZVJlc291cmNlcyhlbnZfMTMpO1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHRfMTMpXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IHJlc3VsdF8xMztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgdXNlVmVyaWZpY2F0aW9uVG9rZW4oaWRlbnRpZmllcl90b2tlbikge1xuICAgICAgICAgICAgY29uc3QgZW52XzE0ID0geyBzdGFjazogW10sIGVycm9yOiB2b2lkIDAsIGhhc0Vycm9yOiBmYWxzZSB9O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYiA9IF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudl8xNCwgYXdhaXQgZ2V0RGIoKSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgdmVyaWZpY2F0aW9uVG9rZW4gPSBhd2FpdCBkYi5WLmZpbmRPbmVBbmREZWxldGUoaWRlbnRpZmllcl90b2tlbik7XG4gICAgICAgICAgICAgICAgaWYgKCF2ZXJpZmljYXRpb25Ub2tlbilcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBfaWQsIC4uLnJlc3QgfSA9IHZlcmlmaWNhdGlvblRva2VuO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVfMTQpIHtcbiAgICAgICAgICAgICAgICBlbnZfMTQuZXJyb3IgPSBlXzE0O1xuICAgICAgICAgICAgICAgIGVudl8xNC5oYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZXN1bHRfMTQgPSBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52XzE0KTtcbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0XzE0KVxuICAgICAgICAgICAgICAgICAgICBhd2FpdCByZXN1bHRfMTQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16/node_modules/@auth/mongodb-adapter/index.js\n");

/***/ })

};
;