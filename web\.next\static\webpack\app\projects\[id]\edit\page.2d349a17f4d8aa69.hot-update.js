"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const { workingConfig, dbSyncedConfig, setWorkingConfig, setDbSyncedConfig, resetWorkingConfig } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Update the config state for unsaved changes tracking\n            setDbSyncedConfig(config);\n            setWorkingConfig(config);\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    // Get configuration for unsaved changes comparison (only specific fields)\n    const getComparisonConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return {\n            materials: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                })),\n            postProcessing: {\n                enabled: postProcessingEnabled,\n                settings: postProcessingSettings\n            },\n            lights: lights.map((light)=>{\n                var _lightRefs_current_light_id;\n                return {\n                    ...light,\n                    position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                };\n            }),\n            environment: {\n                preset: envPreset,\n                intensity: envIntensity,\n                blur: envBlur,\n                rotation: envRotation,\n                showEnvironment,\n                bgColor,\n                customHdri\n            }\n        };\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri\n    ]);\n    // Check if there are unsaved changes\n    const hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) return false;\n        const currentConfig = getComparisonConfig();\n        const savedConfig = {\n            materials: dbSyncedConfig.materials || [],\n            postProcessing: dbSyncedConfig.postProcessing || {\n                enabled: false,\n                settings: {}\n            },\n            lights: dbSyncedConfig.lights || [],\n            environment: dbSyncedConfig.environment || {}\n        };\n        return JSON.stringify(currentConfig) !== JSON.stringify(savedConfig);\n    }, [\n        dbSyncedConfig,\n        getComparisonConfig\n    ]);\n    // Revert to latest saved version\n    const revertToSaved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!dbSyncedConfig) {\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"No saved version to revert to\");\n            return;\n        }\n        const confirmRevert = window.confirm(\"Are you sure you want to revert all changes to the last saved version? This action cannot be undone.\");\n        if (confirmRevert) {\n            handleLoadScene(dbSyncedConfig);\n            sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Reverted to last saved version\");\n        }\n    }, [\n        dbSyncedConfig,\n        handleLoadScene\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                    // Update the synced config to reflect the new saved state\n                    setDbSyncedConfig(sceneConfig);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    // Update working config when relevant state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentConfig = getComparisonConfig();\n        setWorkingConfig(currentConfig);\n    }, [\n        sceneObjects,\n        postProcessingEnabled,\n        postProcessingSettings,\n        lights,\n        envPreset,\n        envIntensity,\n        envBlur,\n        envRotation,\n        showEnvironment,\n        bgColor,\n        customHdri,\n        setWorkingConfig,\n        getComparisonConfig\n    ]);\n    // Add beforeunload event listener to warn about unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (event)=>{\n            if (hasUnsavedChanges()) {\n                const message = \"You have unsaved changes. Are you sure you want to leave?\";\n                event.preventDefault();\n                event.returnValue = message; // For Chrome\n                return message; // For other browsers\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        hasUnsavedChanges\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                    // Set this as the baseline for unsaved changes tracking\n                    setDbSyncedConfig(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1913,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction,\n                hasUnsavedChanges: hasUnsavedChanges(),\n                onRevertToSaved: revertToSaved\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1914,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1929,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1937,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1932,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1961,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1973,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1990,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1999,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2020,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2078,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1956,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2151,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2150,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2189,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2213,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2225,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2251,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2260,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2254,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2274,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2275,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2269,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2293,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2311,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2310,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2317,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2319,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2122,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2327,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"O55hl4aQscugb8zplCWW3kVAmIc=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState,\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useConfigState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});