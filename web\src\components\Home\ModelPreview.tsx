"use client";
import { Canvas } from "@react-three/fiber";
import { Environment, OrbitControls, Stage, Html } from "@react-three/drei";
import { Gltf } from "@react-three/drei";
import { Suspense } from "react";

interface ModelPreviewProps {
  modelPath?: string;
}

// Simple loading component for project cards
const CardLoader = () => {
  return (
    <Html center>
      <div className="w-6 h-6 border-2 border-[#D2BBA0]/30 rounded-full animate-spin border-t-[#D2BBA0]"></div>
    </Html>
  );
};

export default function ModelPreview({ modelPath }: ModelPreviewProps) {
  if (!modelPath) {
    return (
      <div className="h-full w-full bg-black/60 flex items-center justify-center">
        <div className="text-[#FDE9CE] text-sm opacity-60">No Model</div>
      </div>
    );
  }

  return (
    <div className="h-full w-full relative bg-black/60">
      <Canvas
        camera={{ position: [0, 0, 3], fov: 50 }}
        style={{
          width: "100%",
          height: "100%",
          background: "transparent",
        }}
      >
        <Suspense fallback={<CardLoader />}>
          <Stage intensity={0.3} adjustCamera={1.5}>
            <Gltf src={modelPath} />
          </Stage>
          <OrbitControls
            makeDefault
            enableZoom={false}
            enablePan={false}
            enableRotate={false}
            autoRotate
            autoRotateSpeed={2}
          />
          <Environment preset="apartment" />
        </Suspense>
      </Canvas>
    </div>
  );
}
