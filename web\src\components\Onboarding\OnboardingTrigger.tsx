"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { HelpCircle } from "lucide-react";
import { useOnboarding } from "./OnboardingProvider";

interface OnboardingTriggerProps {
  page: string;
  className?: string;
  variant?: "default" | "ghost" | "outline";
  size?: "sm" | "lg";
  children?: React.ReactNode;
}

export const OnboardingTrigger: React.FC<OnboardingTriggerProps> = ({
  page,
  className = "",
  variant = "ghost",
  size = "sm",
  children,
}) => {
  const { startOnboarding } = useOnboarding();

  const handleStartOnboarding = () => {
    startOnboarding(page);
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`onboarding-trigger gold_icon_gradient p-[1px] !h-10 ${className}`}
      onClick={handleStartOnboarding}
    >
      {children || <HelpCircle className="w-4 h-4" />}
    </Button>
  );
};
