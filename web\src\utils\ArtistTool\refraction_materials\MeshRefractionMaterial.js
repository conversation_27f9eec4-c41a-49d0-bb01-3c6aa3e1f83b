import * as THREE from "three";
import { shaderMaterial } from "@react-three/drei";
import {
  
  MeshBVHUniformStruct,
} from "three-mesh-bvh";
import { version } from "@react-three/drei/helpers/constants";

// Author: N8Programs
const MeshRefractionMaterial = /* @__PURE__ */ shaderMaterial(
  {
    envMap: null,
    bounces: 3,
    ior: 2.4,
    correctMips: true,
    aberrationStrength: 0.01,
    colorEnvMapRotY: Math.PI * 0.25,
    fresnel: 0,
    fresnelstrength: 0,
    diamondOriginRadius: 0.0,
    normalEnvMap: null,
    bvh: /* @__PURE__ */ new MeshBVHUniformStruct(),
    color: /* @__PURE__ */ new THREE.Color("white"),
    fresnelcolor: /* @__PURE__ */ new THREE.Color("white"),
    diamondOriginCenter: /* @__PURE__ */ new THREE.Vector3(1.0, 1.0, 1.0),
    opacity: 1,
    resolution: /* @__PURE__ */ new THREE.Vector2(),
    viewMatrixInverse: /* @__PURE__ */ new THREE.Matrix4(),
    projectionMatrixInverse: /* @__PURE__ */ new THREE.Matrix4(),
  },
  /*glsl*/ `
  uniform mat4 viewMatrixInverse;

  varying vec3 vWorldPosition;
  varying vec3 vNormal;
  varying mat4 vModelMatrixInverse;
  varying vec2 v_uv;

  #include <color_pars_vertex>

  void main() {
    #include <color_vertex>
   v_uv = uv;
    vec4 transformedNormal = vec4(normal, 0.0);
    vec4 transformedPosition = vec4(position, 1.0);
    #ifdef USE_INSTANCING
      transformedNormal = instanceMatrix * transformedNormal;
      transformedPosition = instanceMatrix * transformedPosition;
    #endif

    #ifdef USE_INSTANCING
      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);
    #else
      vModelMatrixInverse = inverse(modelMatrix);
    #endif
    vWorldPosition = (modelMatrix * transformedPosition).xyz;
    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);
    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;
  }`,
  /*glsl*/ `
  #define ENVMAP_TYPE_CUBE_UV
  precision highp isampler2D;
  precision highp usampler2D;
  varying vec3 vWorldPosition;
  varying vec3 vNormal;
  varying vec2 v_uv;
  varying mat4 vModelMatrixInverse;
  uniform samplerCube normalEnvMap;

  #include <color_pars_fragment>

  #ifdef ENVMAP_TYPE_CUBE_M
    uniform samplerCube envMap;
  #else
    uniform sampler2D envMap;
  #endif

  uniform float bounces;
  uniform float ior;
  uniform bool correctMips;
  uniform vec2 resolution;
  uniform float fresnel;
  uniform float fresnelstrength;
  uniform mat4 modelMatrix;
  uniform mat4 projectionMatrixInverse;
  uniform mat4 viewMatrixInverse;
  uniform float aberrationStrength;
  uniform float diamondOriginRadius;
  uniform vec3 diamondOriginCenter;
  uniform vec3 color;
  uniform vec3 fresnelcolor;
  uniform float opacity;
  uniform float colorEnvMapRotY;

  mat3 rotateZ(float theta) {
    float c = cos(theta);
    float s = sin(theta);
    return mat3(
        vec3(c, -s, 0),
        vec3(s, c, 0),
        vec3(0, 0, 1)
    );
}

  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {
    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );
  }

  vec3 getCameraDirectionHighDetail () {
    vec3 directionCamPerfect;
    directionCamPerfect = (projectionMatrixInverse * vec4(v_uv * 2.0 - 1.0, 0.0, 1.0)).xyz;
    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;
    directionCamPerfect = normalize(directionCamPerfect);

    return directionCamPerfect;
}

  vec4 getPackedData(vec3 n){
    vec3 directionCamPerfect = getCameraDirectionHighDetail();

    vec4 data = textureGrad(normalEnvMap, n, dFdx(directionCamPerfect), dFdy(directionCamPerfect));

    data.rgb = data.rgb * 2.0 - 1.0;
    data.rgb = normalize(-data.rgb);
    data.rgb *= -1.0;
    data.a = data.a * diamondOriginRadius;
    //gl_FragColor = vec4(data.rgb, 1.0);
    return data;
}

  vec3 hitSphere(vec3 origin, vec3 direction, vec3 center, float radius) {
    vec3 oc = origin - center;

    float a = dot(direction, direction);
    float b = 2.0 * dot(oc, direction);
    float c = dot(oc, oc) - (radius * radius);
    float discriminant = (b * b) - ( a * c);

    if (discriminant > 0.0) {
        float sqrtDiscriminant = sqrt(discriminant);
        float t = (-b + sqrtDiscriminant) / a;
        return origin + direction * t;
    }

    return vec3(0.0);
}

vec3 hitPlane(vec3 linePoint, vec3 lineDir, vec3 planePoint, vec3 planeDir) {
    vec3 normalizedLineDir = normalize(lineDir);
    vec3 normalizedPlaneDir = normalize(planeDir);

    float dotProduct = dot(normalizedPlaneDir, planePoint - linePoint);
    float dotProductLine = dot(normalizedPlaneDir, normalizedLineDir);
    float howFar = dotProduct / dotProductLine;
    vec3 position = normalizedLineDir * howFar;
    vec3 intersectionPoint = linePoint + position;
    return intersectionPoint;
}

vec3 hitRay(vec3 rayOrigin, vec3 rayDirection) {
    vec3 sphereHitPoint = hitSphere(rayOrigin, rayDirection, diamondOriginCenter, diamondOriginRadius);
    vec3 sphereCenterOffset = diamondOriginCenter;
    
    vec3 dir1 = normalize(sphereHitPoint - sphereCenterOffset);
    vec4 faceNormal1 = getPackedData(dir1);
    float dist1 = faceNormal1.a;
    vec3 point1 = sphereCenterOffset + dir1 * dist1;
    vec3 normal1 = faceNormal1.rgb;
    vec3 hit1 = hitPlane(rayOrigin, rayDirection, point1, normal1);
    return hit1;
}


  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {
    vec3 rayOrigin = ro;
    vec3 rayDirection = rd;
    rayDirection = refract(rayDirection, normal, 1.0 / ior);
    rayOrigin = vWorldPosition + rayDirection * 0.1;
    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;
    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);
    for(float i = 0.0; i < bounces; i++) {
        vec3 hitPos = hitRay(rayOrigin, rayDirection);
        vec3 pureDirectionToRaycast = normalize(hitPos - diamondOriginCenter);
        vec4 faceNormal = getPackedData(pureDirectionToRaycast);
        vec3 diamondWallCastPoint = diamondOriginCenter + pureDirectionToRaycast * faceNormal.a;
        rayOrigin = diamondWallCastPoint;
        vec3 tempDir = refract(rayDirection, faceNormal.rgb, ior);
        if (length(tempDir) != 0.0) {
            rayDirection = tempDir;
            continue;
        }
            rayDirection = reflect(rayDirection, faceNormal.rgb);
        }
        rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);
        return rayDirection;
    }

  #include <common>
  #include <cube_uv_reflection_fragment>

  #ifdef ENVMAP_TYPE_CUBEM
    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {
      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));
    }
  #else
    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {
      vec2 uvv = equirectUv( rayDirection );
      vec2 smoothUv = equirectUv( directionCamPerfect );
      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));
    }
  #endif

  void main() {
    vec3 directionCamPerfect = getCameraDirectionHighDetail();
    vec3 normal = vNormal;
    vec3 rayOrigin = cameraPosition;
    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);

    vec4 diffuseColor = vec4(color, opacity);
    #include <color_fragment>

    #ifdef CHROMATIC_ABERRATIONS
      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);
      #ifdef FAST_CHROMA
        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));
        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));
      #else
        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);
        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);
      #endif
      rayDirectionR *= rotateZ(colorEnvMapRotY);
      rayDirectionG *= rotateZ(colorEnvMapRotY);
      rayDirectionB *= rotateZ(colorEnvMapRotY);
      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;
      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;
      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;
      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);
    #else
      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);
      rayDirection *= rotateZ(colorEnvMapRotY);
      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;
    #endif

    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);
     float fresnelterm = pow(1.0 -  dot( viewDirection, 2.0 * normal), fresnel);
    fresnelterm = clamp(fresnelterm * fresnelstrength, 0.0, 1.0);
    diffuseColor.rgb = diffuseColor.rgb + (fresnelterm * fresnelcolor);
    
    //float nFresnel = fresnelFunc(viewDirection,  normal) * fresnel;
    //gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);
    gl_FragColor = vec4(diffuseColor.rgb, 1.0);

    #include <tonemapping_fragment>
    #include <${version >= 154 ? "colorspace_fragment" : "encodings_fragment"}>
  }`
);

export { MeshRefractionMaterial };
