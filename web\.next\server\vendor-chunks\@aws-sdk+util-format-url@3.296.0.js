"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+util-format-url@3.296.0";
exports.ids = ["vendor-chunks/@aws-sdk+util-format-url@3.296.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatUrl: () => (/* binding */ formatUrl)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_querystring_builder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/querystring-builder */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+querystring-builder@3.296.0/node_modules/@aws-sdk/querystring-builder/dist-es/index.js\");\n\nfunction formatUrl(request) {\n    const { port, query } = request;\n    let { protocol, path, hostname } = request;\n    if (protocol && protocol.slice(-1) !== \":\") {\n        protocol += \":\";\n    }\n    if (port) {\n        hostname += `:${port}`;\n    }\n    if (path && path.charAt(0) !== \"/\") {\n        path = `/${path}`;\n    }\n    let queryString = query ? (0,_aws_sdk_querystring_builder__WEBPACK_IMPORTED_MODULE_0__.buildQueryString)(query) : \"\";\n    if (queryString && queryString[0] !== \"?\") {\n        queryString = `?${queryString}`;\n    }\n    return `${protocol}//${hostname}${path}${queryString}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWZvcm1hdC11cmxAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1mb3JtYXQtdXJsL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0U7QUFDekQ7QUFDUCxZQUFZLGNBQWM7QUFDMUIsVUFBVSwyQkFBMkI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsS0FBSztBQUM3QjtBQUNBO0FBQ0EsbUJBQW1CLEtBQUs7QUFDeEI7QUFDQSw4QkFBOEIsOEVBQWdCO0FBQzlDO0FBQ0EsMEJBQTBCLFlBQVk7QUFDdEM7QUFDQSxjQUFjLFNBQVMsSUFBSSxTQUFTLEVBQUUsS0FBSyxFQUFFLFlBQVk7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1mb3JtYXQtdXJsQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3V0aWwtZm9ybWF0LXVybC9kaXN0LWVzL2luZGV4LmpzP2VhZjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYnVpbGRRdWVyeVN0cmluZyB9IGZyb20gXCJAYXdzLXNkay9xdWVyeXN0cmluZy1idWlsZGVyXCI7XG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0VXJsKHJlcXVlc3QpIHtcbiAgICBjb25zdCB7IHBvcnQsIHF1ZXJ5IH0gPSByZXF1ZXN0O1xuICAgIGxldCB7IHByb3RvY29sLCBwYXRoLCBob3N0bmFtZSB9ID0gcmVxdWVzdDtcbiAgICBpZiAocHJvdG9jb2wgJiYgcHJvdG9jb2wuc2xpY2UoLTEpICE9PSBcIjpcIikge1xuICAgICAgICBwcm90b2NvbCArPSBcIjpcIjtcbiAgICB9XG4gICAgaWYgKHBvcnQpIHtcbiAgICAgICAgaG9zdG5hbWUgKz0gYDoke3BvcnR9YDtcbiAgICB9XG4gICAgaWYgKHBhdGggJiYgcGF0aC5jaGFyQXQoMCkgIT09IFwiL1wiKSB7XG4gICAgICAgIHBhdGggPSBgLyR7cGF0aH1gO1xuICAgIH1cbiAgICBsZXQgcXVlcnlTdHJpbmcgPSBxdWVyeSA/IGJ1aWxkUXVlcnlTdHJpbmcocXVlcnkpIDogXCJcIjtcbiAgICBpZiAocXVlcnlTdHJpbmcgJiYgcXVlcnlTdHJpbmdbMF0gIT09IFwiP1wiKSB7XG4gICAgICAgIHF1ZXJ5U3RyaW5nID0gYD8ke3F1ZXJ5U3RyaW5nfWA7XG4gICAgfVxuICAgIHJldHVybiBgJHtwcm90b2NvbH0vLyR7aG9zdG5hbWV9JHtwYXRofSR7cXVlcnlTdHJpbmd9YDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatUrl: () => (/* binding */ formatUrl)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_querystring_builder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/querystring-builder */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+querystring-builder@3.296.0/node_modules/@aws-sdk/querystring-builder/dist-es/index.js\");\n\nfunction formatUrl(request) {\n    const { port, query } = request;\n    let { protocol, path, hostname } = request;\n    if (protocol && protocol.slice(-1) !== \":\") {\n        protocol += \":\";\n    }\n    if (port) {\n        hostname += `:${port}`;\n    }\n    if (path && path.charAt(0) !== \"/\") {\n        path = `/${path}`;\n    }\n    let queryString = query ? (0,_aws_sdk_querystring_builder__WEBPACK_IMPORTED_MODULE_0__.buildQueryString)(query) : \"\";\n    if (queryString && queryString[0] !== \"?\") {\n        queryString = `?${queryString}`;\n    }\n    return `${protocol}//${hostname}${path}${queryString}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1mb3JtYXQtdXJsQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3V0aWwtZm9ybWF0LXVybC9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBQ3pEO0FBQ1AsWUFBWSxjQUFjO0FBQzFCLFVBQVUsMkJBQTJCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLEtBQUs7QUFDN0I7QUFDQTtBQUNBLG1CQUFtQixLQUFLO0FBQ3hCO0FBQ0EsOEJBQThCLDhFQUFnQjtBQUM5QztBQUNBLDBCQUEwQixZQUFZO0FBQ3RDO0FBQ0EsY0FBYyxTQUFTLElBQUksU0FBUyxFQUFFLEtBQUssRUFBRSxZQUFZO0FBQ3pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3V0aWwtZm9ybWF0LXVybEAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWZvcm1hdC11cmwvZGlzdC1lcy9pbmRleC5qcz82MzFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkUXVlcnlTdHJpbmcgfSBmcm9tIFwiQGF3cy1zZGsvcXVlcnlzdHJpbmctYnVpbGRlclwiO1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFVybChyZXF1ZXN0KSB7XG4gICAgY29uc3QgeyBwb3J0LCBxdWVyeSB9ID0gcmVxdWVzdDtcbiAgICBsZXQgeyBwcm90b2NvbCwgcGF0aCwgaG9zdG5hbWUgfSA9IHJlcXVlc3Q7XG4gICAgaWYgKHByb3RvY29sICYmIHByb3RvY29sLnNsaWNlKC0xKSAhPT0gXCI6XCIpIHtcbiAgICAgICAgcHJvdG9jb2wgKz0gXCI6XCI7XG4gICAgfVxuICAgIGlmIChwb3J0KSB7XG4gICAgICAgIGhvc3RuYW1lICs9IGA6JHtwb3J0fWA7XG4gICAgfVxuICAgIGlmIChwYXRoICYmIHBhdGguY2hhckF0KDApICE9PSBcIi9cIikge1xuICAgICAgICBwYXRoID0gYC8ke3BhdGh9YDtcbiAgICB9XG4gICAgbGV0IHF1ZXJ5U3RyaW5nID0gcXVlcnkgPyBidWlsZFF1ZXJ5U3RyaW5nKHF1ZXJ5KSA6IFwiXCI7XG4gICAgaWYgKHF1ZXJ5U3RyaW5nICYmIHF1ZXJ5U3RyaW5nWzBdICE9PSBcIj9cIikge1xuICAgICAgICBxdWVyeVN0cmluZyA9IGA/JHtxdWVyeVN0cmluZ31gO1xuICAgIH1cbiAgICByZXR1cm4gYCR7cHJvdG9jb2x9Ly8ke2hvc3RuYW1lfSR7cGF0aH0ke3F1ZXJ5U3RyaW5nfWA7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-format-url@3.296.0/node_modules/@aws-sdk/util-format-url/dist-es/index.js\n");

/***/ })

};
;