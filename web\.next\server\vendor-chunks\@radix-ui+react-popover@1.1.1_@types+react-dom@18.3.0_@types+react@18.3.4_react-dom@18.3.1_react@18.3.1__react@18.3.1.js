"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popover@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-popover@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popover/dist/index.mjs":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popover@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popover/dist/index.mjs ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Popover: () => (/* binding */ Popover),\n/* harmony export */   PopoverAnchor: () => (/* binding */ PopoverAnchor),\n/* harmony export */   PopoverArrow: () => (/* binding */ PopoverArrow),\n/* harmony export */   PopoverClose: () => (/* binding */ PopoverClose),\n/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),\n/* harmony export */   PopoverPortal: () => (/* binding */ PopoverPortal),\n/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createPopoverScope: () => (/* binding */ createPopoverScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.0/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom_yfklyfqno7isrggempim2x7psi/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3._sgbkes3iamq4rj6s55dcmugemq/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.5.7_@types+react@18.3.4_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,Close,Content,Popover,PopoverAnchor,PopoverArrow,PopoverClose,PopoverContent,PopoverPortal,PopoverTrigger,Portal,Root,Trigger,createPopoverScope auto */ // packages/react/popover/src/Popover.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPOVER_NAME, [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props)=>{\n    const { __scopePopover, children, open: openProp, defaultOpen, onOpenChange, modal = false } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [hasCustomAnchor, setHasCustomAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverProvider, {\n            scope: __scopePopover,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n            triggerRef,\n            open,\n            onOpenChange: setOpen,\n            onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n                setOpen\n            ]),\n            hasCustomAnchor,\n            onCustomAnchorAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(true), []),\n            onCustomAnchorRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(false), []),\n            modal,\n            children\n        })\n    });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onCustomAnchorAdd();\n        return ()=>onCustomAnchorRemove();\n    }, [\n        onCustomAnchorAdd,\n        onCustomAnchorRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: trigger\n    });\n});\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar PopoverPortal = (props)=>{\n    const { __scopePopover, forceMount, children, container } = props;\n    const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopePopover,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nPopoverContent.displayName = CONTENT_NAME;\nvar PopoverContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, contentRef);\n    const isRightClickOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.Slot,\n        allowPinchZoom: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n            ...props,\n            ref: composedRefs,\n            trapFocus: context.open,\n            disableOutsidePointerEvents: true,\n            onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n                event.preventDefault();\n                if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const originalEvent = event.detail.originalEvent;\n                const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n                const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n                isRightClickOutsideRef.current = isRightClick;\n            }, {\n                checkForDefaultPrevented: false\n            }),\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nvar PopoverContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar PopoverContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__.FocusScope, {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: ()=>context.onOpenChange(false),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...contentProps.style,\n                    // re-namespace exposed content custom properties\n                    ...{\n                        \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                        \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                        \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                        \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                        \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                    }\n                }\n            })\n        })\n    });\n});\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popover/dist/index.mjs\n");

/***/ })

};
;