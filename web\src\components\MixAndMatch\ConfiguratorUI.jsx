import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>rk<PERSON>,
  RotateCcw,
  Diamond,
  <PERSON>ap,
  Palette,
  <PERSON>,
  Flower,
  Gem,
  Sparkles as SparklesIcon,
  Save,
} from "lucide-react";
import { ringParts } from "./libs/constants";
import Link from "next/link";
import CurrentConfig from "./CurrentConfig";
import { toast } from "sonner";
import Image from "next/image";

const PART_TYPES = [
  { key: "material", label: "Material" },
  { key: "paw", label: "Paw" },
  { key: "crown", label: "Crown" },
  { key: "ring", label: "Ring" },
];

const ConfiguratorUI = ({
  selectedParts,
  handlePartSelect,
  resetConfiguration,
  autoRotate,
  setAutoRotate,
  materialOptions,
  enablePostProcessing,
  setEnablePostProcessing,
  activeSection,
  setActiveSection,
}) => {
  const handlePartSelectLocal = (partType, partId) => {
    handlePartSelect(partType, partId);
  };

  return (
    <>
      <div className="fixed top-6 left-6 z-30 flex items-center gap-4">
        <Link href={"/"} className="size-12">
          <div className="size-12">
            <Image
              src="/images/CSS Logo.png"
              alt="Logo"
              width={1024}
              height={780}
              className="w-full h-full object-contain"
            />
          </div>
        </Link>
        <div className="flex items-center gap-3">
          <h1 className="text-sm font-medium text-[#FDE9CE] font-larken">
            Mix and Match
          </h1>
        </div>
      </div>

      {/* Left: Part type selector */}
      <div className="fixed top-1/2 left-8 -translate-y-1/2 flex flex-col gap-4 z-20">
        {PART_TYPES.map((part) => (
          <button
            key={part.key}
            onClick={() => setActiveSection(part.key)}
            className={`flex w-[120px] text-center items-center justify-center gap-2 px-6 py-3 rounded-xl text-xs font-medium transition-all duration-300 border-2 shadow-sm backdrop-blur-xl focus:outline-none uppercase font-inter ${
              activeSection === part.key
                ? "bg-gradient-to-br from-amber-300/40 to-yellow-400/40 border-amber-400  scale-105"
                : "bg-white/20 border-white/20 text-slate-800 hover:bg-white/30 hover:scale-105"
            }`}
          >
            {part.label}
          </button>
        ))}
      </div>

      {/* Right: Part options for selected type */}
      <div className="fixed top-1/2 right-8 -translate-y-1/2 flex flex-col gap-4 z-20">
        {activeSection === "material"
          ? materialOptions.map((mat) => (
              <button
                key={mat.id}
                onClick={() => handlePartSelectLocal("material", mat.id)}
                className={`flex flex-col items-center justify-center size-20 rounded-2xl border-2 transition-all duration-300 shadow-md bg-white/10 hover:bg-amber-100/20 hover:scale-105 ${
                  selectedParts.material === mat.id
                    ? "border-amber-400 scale-110"
                    : "border-white/20"
                }`}
                title={mat.name}
              >
                <span
                  className="mb-3 block size-8 rounded-full border-2 border-white shadow-inner"
                  style={{ background: mat.color }}
                ></span>
                {/* <span className="text-xs text-slate-800 capitalize font-inter">
                  {mat.name}
                </span> */}
              </button>
            ))
          : ringParts[activeSection] &&
            ringParts[activeSection].map((part) => (
              <button
                key={part.id}
                onClick={() => handlePartSelectLocal(activeSection, part.id)}
                className={`flex flex-col items-center justify-center size-20 rounded-2xl border-2 transition-all duration-300 shadow-md text-3xl font-bold text-black bg-white/10 hover:bg-amber-100/20 hover:scale-105 ${
                  selectedParts[activeSection] === part.id
                    ? "border-amber-400 bg-gradient-to-br from-amber-200/40 to-yellow-300/40 text-amber-900 scale-110"
                    : "border-white/20"
                }`}
              >
                <span className="mb-2 text-2xl">{part.preview}</span>
                <span className="text-xs text-slate-800 capitalize font-inter">
                  {part.name}
                </span>
              </button>
            ))}
      </div>

      {/* Top right: Control buttons with frosted glass look */}
      <div className="fixed top-8 right-8 z-20 flex items-center gap-3">
        <CurrentConfig
          selectedParts={selectedParts}
          materialOptions={materialOptions}
        />

        <button
          onClick={() => setAutoRotate(!autoRotate)}
          className={`flex items-center justify-center size-8 rounded-xl border-2 transition-all duration-300 backdrop-blur-xl shadow-lg hover:scale-105 ${
            autoRotate
              ? "bg-gradient-to-br from-blue-500/30 to-cyan-500/30 border-blue-400/50 text-blue-200"
              : "bg-white/10 border-white/20 text-black hover:bg-white/20"
          }`}
          title="Auto Rotate"
        >
          <Zap
            size={20}
            className={autoRotate ? "text-blue-300" : "text-gray-300"}
          />
        </button>

        <button
          onClick={() => setEnablePostProcessing(!enablePostProcessing)}
          className={`flex items-center justify-center size-8 rounded-xl border-2 transition-all duration-300 backdrop-blur-xl shadow-lg hover:scale-105 ${
            enablePostProcessing
              ? "bg-gradient-to-br from-purple-500/30 to-pink-500/30 border-purple-400/50 text-purple-200"
              : "bg-white/10 border-white/20 text-black hover:bg-white/20"
          }`}
          title="Post Processing"
        >
          <SparklesIcon
            size={20}
            className={
              enablePostProcessing ? "text-purple-300" : "text-gray-300"
            }
          />
        </button>

        <button
          onClick={resetConfiguration}
          className="flex items-center justify-center size-8 rounded-xl border-2 transition-all duration-300 backdrop-blur-xl shadow-lg hover:scale-105 bg-white/10 border-white/20 text-black hover:bg-red-500/20 hover:border-red-400/50 hover:text-red-200"
          title="Reset Design"
        >
          <RotateCcw size={20} className="text-gray-300" />
        </button>

        <button
          onClick={() => {
            console.log("Save to project library:", selectedParts);
            toast.success("Design saved to project library");
          }}
          className="flex items-center justify-center size-8 rounded-xl border-2 transition-all duration-300 backdrop-blur-xl shadow-lg hover:scale-105 bg-white/10 border-white/20 text-black hover:bg-green-500/20 hover:border-green-400/50 hover:text-green-200"
          title="Save to Project Library"
        >
          <Save size={20} className="text-gray-300" />
        </button>
      </div>
    </>
  );
};

export default ConfiguratorUI;
