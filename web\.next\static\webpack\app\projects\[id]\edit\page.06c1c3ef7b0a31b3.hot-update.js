"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Header.jsx":
/*!**********************************************!*\
  !*** ./src/components/ArtistTool/Header.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SaveSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/SaveSceneModal.jsx\");\n/* harmony import */ var _LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LoadSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/LoadSceneModal.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _VersionHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../VersionHistory */ \"(app-pages-browser)/./src/components/VersionHistory.tsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onReset, onScreenshot, onSaveScene, onLoadScene, isFullscreen, onToggleFullscreen, user, project, onUndo, onRedo, hasUnsavedChanges, onRevertToSaved } = param;\n    _s();\n    const [showSaveModal, setShowSaveModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showLoadModal, setShowLoadModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showVersionHistory, setShowVersionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                id: \"header\",\n                className: \"fixed w-full z-50 px-4 py-2 transition-all duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full px-6 py-1 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-auto h-10 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: \"/projects/\".concat(project._id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"size-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/CSS Logo.png\",\n                                        alt: \"Logo\",\n                                        width: 1024,\n                                        height: 780,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                        icon: isFullscreen ? _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        tooltip: isFullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\",\n                                        onClick: onToggleFullscreen\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-white/20 mx-1 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\")\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                tooltip: \"Undo (Ctrl+Z)\",\n                                                onClick: onUndo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                tooltip: \"Redo (Ctrl+Y)\",\n                                                onClick: onRedo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                        icon: RotateLeft,\n                                                        tooltip: \"Revert to Last Saved Version\",\n                                                        onClick: onRevertToSaved\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 px-2 py-1 rounded-full bg-orange-500/20 border border-orange-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-3 h-3 text-orange-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-orange-400 font-medium\",\n                                                            children: \"Unsaved Changes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                tooltip: \"Reset Scene\",\n                                                onClick: onReset\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                tooltip: \"Screenshot\",\n                                                onClick: onScreenshot\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                tooltip: \"Version History\",\n                                                onClick: ()=>setShowVersionHistory(true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowLoadModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Load Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowSaveModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Save Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__.SaveSceneModal, {\n                isOpen: showSaveModal,\n                onClose: ()=>setShowSaveModal(false),\n                onSave: (sceneName, description, saveType)=>{\n                    onSaveScene(sceneName, description, saveType);\n                    setShowSaveModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__.LoadSceneModal, {\n                isOpen: showLoadModal,\n                onClose: ()=>setShowLoadModal(false),\n                onLoad: (config)=>{\n                    onLoadScene(config);\n                    setShowLoadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VersionHistory__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showVersionHistory,\n                onClose: ()=>setShowVersionHistory(false),\n                userId: user.id,\n                workspaceId: project._id,\n                onApplyVersion: (config)=>{\n                    onLoadScene(config);\n                    setShowVersionHistory(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"BpQ93XwKg8UzXTTc2Gl4xwXptgA=\");\n_c = Header;\nfunction HeaderButton(param) {\n    let { icon: Icon, tooltip, primary = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClick,\n                    className: \"p-1.5 rounded-full \".concat(primary ? \"bg-primary text-white hover:bg-primary-600\" : \"text-white hover:bg-white/20\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: tooltip\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeaderButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"HeaderButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Header.jsx\n"));

/***/ })

});