import { Suspense, useRef, useEffect } from "react";
import { Center, Float } from "@react-three/drei";
import UploadedModel from "./UploadedModel";
import { BackPlate } from "./BackPlate";

export function ModelStage({
  selectedModel,
  wireframe,
  handleSceneUpdate,
  handleObjectClick,
  uploadedModel,
  groundType,
  engravingText,
  activeAnimation,
  decalPosition,
  decalScale,
  decalRotation,
  decalColor,
  font,
  isPlacingDecal,
  onDecalPlacement,
  showDecalDebug,
  sceneObjects,
  useDiamondShader,
  usePremiumWhiteGold,
  usePremiumRoseGold,
}) {
  const modelRef = useRef();

  useEffect(() => {
    if (modelRef.current) {
      handleSceneUpdate(modelRef.current);
    }
  }, [selectedModel, handleSceneUpdate]);

  const renderModel = () => {
    if (uploadedModel) {
      return (
        <UploadedModel
          ref={modelRef}
          url={uploadedModel}
          wireframe={wireframe}
          onLoad={handleSceneUpdate}
          onClick={handleObjectClick}
          engravingText={engravingText}
          decalPosition={decalPosition}
          decalScale={decalScale}
          decalRotation={decalRotation}
          decalColor={decalColor}
          font={font}
          isPlacingDecal={isPlacingDecal}
          onDecalPlacement={onDecalPlacement}
          showDecalDebug={showDecalDebug}
          sceneObjects={sceneObjects}
          onSceneUpdate={handleSceneUpdate}
          useDiamondShader={useDiamondShader}
          usePremiumWhiteGold={usePremiumWhiteGold}
          usePremiumRoseGold={usePremiumRoseGold}
        />
      );
    }

    return null;
  };

  return (
    <Suspense fallback={null}>
      {groundType === "backplate" && (
        <>
          <BackPlate scale={0.75} position={[0, 0.7, 0]} />
          <BackPlate
            scale={0.75}
            position={[0, 0.7, 0]}
            rotation={[0, Math.PI, 0]}
          />
        </>
      )}

      <Center key={`model-center-${selectedModel}`} top position={[0, 0, 0]}>
        {activeAnimation === "float" ? (
          <Float
            speed={1.5}
            floatIntensity={1.5}
            rotationIntensity={1}
            floatingRange={[0.1, 0.25]}
          >
            {renderModel()}
          </Float>
        ) : (
          renderModel()
        )}
      </Center>
    </Suspense>
  );
}
