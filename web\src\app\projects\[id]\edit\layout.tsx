import { Metadata } from "next";
import { getWorkspace } from "@/lib/actions/workspace.actions";
import { getCurrentUser } from "@/lib/session";

interface LayoutProps {
  children: React.ReactNode;
  params: { id: string };
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return {
        title: "Edit Project | CSS",
        description: "Edit your project settings and configurations.",
      };
    }

    const project = await getWorkspace({
      userId: user.id,
      workspaceId: params.id,
    });

    if (!project) {
      return {
        title: "Project Not Found | CSS",
        description: "The requested project could not be found.",
      };
    }

    return {
      title: `Edit ${project.name} | CSS`,
      description: `Edit and configure ${project.name} project settings in CSS Editor.`,
    };
  } catch (error) {
    return {
      title: "Edit Project | CSS",
      description: "Edit your project settings and configurations.",
    };
  }
}

export default function EditProjectLayout({ children }: LayoutProps) {
  return <>{children}</>;
}
