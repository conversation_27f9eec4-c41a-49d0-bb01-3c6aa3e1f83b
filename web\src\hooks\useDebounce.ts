import { useCallback, useRef } from "react";

/**
 * Custom hook for debouncing function calls
 * @param {Function} callback - The function to debounce
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - The debounced function
 */
export function useDebounce(callback, delay) {
  const timeoutRef = useRef(null);

  return useCallback(
    (...args) => {
      // Clear the previous timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  );
}

/**
 * Custom hook specifically for debouncing addToHistory calls
 * @param {Function} addToHistory - The addToHistory function
 * @param {number} delay - The delay in milliseconds (default: 300ms for sliders)
 * @returns {Object} - Object containing immediate and debounced versions
 */
export function useHistoryDebounce(addToHistory, delay = 300) {
  const debouncedAddToHistory = useDebounce(addToHistory, delay);

  return {
    // For immediate actions like button clicks, checkboxes
    addToHistoryImmediate: useCallback(() => {
      // Small delay to ensure state updates are processed
      setTimeout(addToHistory, 0);
    }, [addToHistory]),

    // For continuous actions like sliders, text inputs
    addToHistoryDebounced: debouncedAddToHistory,
  };
}
