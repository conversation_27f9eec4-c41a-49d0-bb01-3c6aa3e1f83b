"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1";
exports.ids = ["vendor-chunks/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useInstanceHandle),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useStore),\n/* harmony export */   D: () => (/* binding */ useThree),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useFrame),\n/* harmony export */   G: () => (/* binding */ useGraph),\n/* harmony export */   H: () => (/* binding */ useLoader),\n/* harmony export */   a: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   b: () => (/* binding */ createRoot),\n/* harmony export */   c: () => (/* binding */ createPointerEvents),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createEvents),\n/* harmony export */   g: () => (/* binding */ context),\n/* harmony export */   h: () => (/* binding */ createPortal),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ reconciler),\n/* harmony export */   k: () => (/* binding */ applyProps),\n/* harmony export */   l: () => (/* binding */ dispose),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ addEffect),\n/* harmony export */   p: () => (/* binding */ addAfterEffect),\n/* harmony export */   q: () => (/* binding */ addTail),\n/* harmony export */   r: () => (/* binding */ render),\n/* harmony export */   s: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useMutableCallback),\n/* harmony export */   v: () => (/* binding */ flushSync),\n/* harmony export */   w: () => (/* binding */ getRootState),\n/* harmony export */   x: () => (/* binding */ act),\n/* harmony export */   y: () => (/* binding */ buildGraph),\n/* harmony export */   z: () => (/* binding */ roots)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/.pnpm/react-reconciler@0.27.0_react@18.3.1/node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@3.7.2_react@18.3.1/node_modules/zustand/esm/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/.pnpm/suspend-react@0.1.3_react@18.3.1/node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/.pnpm/react-reconciler@0.27.0_react@18.3.1/node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/.pnpm/scheduler@0.21.0/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance.isBufferGeometry) instance.__r3f.attach = 'geometry';else if (instance.isMaterial) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        parentInstance.dispatchEvent({\n          type: 'childadded',\n          child\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-expect-error\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          (0,scheduler__WEBPACK_IMPORTED_MODULE_4__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_4__.unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, make it no-op\n  const handleTextInstance = () => {};\n  const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_3___default()({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-expect-error\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  const localState = instance.__r3f;\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f;\n    // Bail out on primitive object\n    if ((_instance$__r3f = instance.__r3f) != null && _instance$__r3f.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && \"development\" !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f2;\n  // Filter equals, events and reserved props\n  const localState = instance.__r3f;\n  const root = localState == null ? void 0 : localState.root;\n  const rootState = root == null ? void 0 : root.getState == null ? void 0 : root.getState();\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState == null ? void 0 : localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-expect-error\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent && localState) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        var _targetProp;\n        const isColor = (_targetProp = targetProp) == null ? void 0 : _targetProp.isColor;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && rootState && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      var _currentInstance$key;\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if ((_currentInstance$key = currentInstance[key]) != null && _currentInstance$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === three__WEBPACK_IMPORTED_MODULE_5__.RGBAFormat && currentInstance[key].type === three__WEBPACK_IMPORTED_MODULE_5__.UnsignedByteType && rootState) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState && localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f3, _instance$__r3f3$root;\n  const state = (_instance$__r3f3 = instance.__r3f) == null ? void 0 : (_instance$__r3f3$root = _instance$__r3f3.root) == null ? void 0 : _instance$__r3f3$root.getState == null ? void 0 : _instance$__r3f3$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n    default:\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = (0,zustand__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((set, get) => {\n    const position = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    const tempTarget = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new three__WEBPACK_IMPORTED_MODULE_5__.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new three__WEBPACK_IMPORTED_MODULE_5__.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let useFrameInProgress = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n    useFrameInProgress = false;\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n      // legacy support for people using frames parameters\n      // Increase frames, do not go higher than 60\n      state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n      if (useFrameInProgress) {\n        //called from within a useFrame, it means the user wants an additional frame\n        state.internal.frames = 2;\n      } else {\n        //the user need a new frame, no need to increment further than 1\n        state.internal.frames = 1;\n      }\n    }\n\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    invalidate,\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.suspend)(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.preload)(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.clear)([Proto, ...keys]);\n};\n\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new three__WEBPACK_IMPORTED_MODULE_5__.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_5__.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof three__WEBPACK_IMPORTED_MODULE_5__.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_5__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_5__.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n              camera.manual = true;\n              camera.updateProjectionMatrix();\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n        } else {\n          scene = new three__WEBPACK_IMPORTED_MODULE_5__.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: three__WEBPACK_IMPORTED_MODULE_5__.BasicShadowMap,\n            percentage: three__WEBPACK_IMPORTED_MODULE_5__.PCFShadowMap,\n            soft: three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap,\n            variance: three__WEBPACK_IMPORTED_MODULE_5__.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n      if (!configured) {\n        // Set color space and tonemapping preferences, once\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? three__WEBPACK_IMPORTED_MODULE_5__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_5__.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Portal, {\n    children: children,\n    container: container,\n    state: state\n  }, container.uuid);\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_5__.Raycaster());\n  const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_5__.Vector2());\n  const inject = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_5__.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = (0,zustand__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => {\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n    children: reconciler.createPortal( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\n\n/**\r\n * Force React to flush any updates inside the provided callback synchronously and immediately.\r\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\r\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\r\n * having to revert to a non-React solution.\r\n */\nfunction flushSync(fn) {\n  // `flushSync` implementation only takes one argument. I don't know what's up with the type declaration for it.\n  return reconciler.flushSync(fn, undefined);\n}\nreconciler.injectIntoDevTools({\n  bundleType:  false ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: react__WEBPACK_IMPORTED_MODULE_0__.version\n});\nconst act = react__WEBPACK_IMPORTED_MODULE_0__.unstable_act;\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   act: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   advance: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   context: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   createPointerEvents: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   events: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   extend: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   flushSync: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   render: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.G),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.H),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-776716bd.esm.js */ \"(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/.pnpm/react-use-measure@2.1.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/.pnpm/its-fine@1.2.5_react@18.3.1/node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/.pnpm/react-reconciler@0.27.0_react@18.3.1/node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/.pnpm/react-reconciler@0.27.0_react@18.3.1/node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/.pnpm/scheduler@0.21.0/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CanvasImpl = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n  const Bridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_7__.useContextBridge)();\n  const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(onPointerMissed);\n  const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n  const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n          set: setError,\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n              set: setBlock\n            }),\n            children: children != null ? children : null\n          })\n        })\n      }));\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_7__.FiberProvider, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n      ...props,\n      ref: ref\n    })\n  });\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ })

};
;