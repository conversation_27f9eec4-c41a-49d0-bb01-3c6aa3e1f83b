import React, { useRef } from "react";
import { useGLTF } from "@react-three/drei";

export function BackPlate(props) {
  const { nodes, materials } = useGLTF("/models/BackPlate.glb");
  return (
    <group {...props} dispose={null}>
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Vert032.geometry}
        material={materials["Material.009"]}
        position={[0, -0.973, 5.177]}
        scale={2.089}
      />
    </group>
  );
}

useGLTF.preload("/models/BackPlate.glb");
