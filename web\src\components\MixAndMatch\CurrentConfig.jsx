import { ringParts } from "./libs/constants";

export default function CurrentConfig({ selectedParts, materialOptions }) {
  const getMaterialColor = (materialId) => {
    const material = materialOptions.find((m) => m.id === materialId);
    return material?.color || "#d8d8d8";
  };

  const getPartName = (partType, partId) => {
    if (partType === "material") {
      const material = materialOptions.find((m) => m.id === partId);
      return material?.name || "Unknown";
    }

    const part = ringParts[partType]?.find((p) => p.id === partId);
    return part?.name || "Unknown";
  };

  return (
    <div className=" z-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-3 shadow-lg">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div
            className="w-2 h-2 rounded-full border border-white/30"
            style={{
              backgroundColor: getMaterialColor(selectedParts.material),
            }}
          />
          <span className="text-[10px] text-white/80 font-inter">
            {getPartName("material", selectedParts.material)}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[10px] text-white/80 font-inter">
            {getPartName("ring", selectedParts.ring)}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[10px] text-white/80 font-inter">
            {getPartName("crown", selectedParts.crown)}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[10px] text-white/80 font-inter">
            {getPartName("paw", selectedParts.paw)}
          </span>
        </div>
      </div>
    </div>
  );
}
