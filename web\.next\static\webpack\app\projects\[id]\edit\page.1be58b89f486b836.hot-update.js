"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Header.jsx":
/*!**********************************************!*\
  !*** ./src/components/ArtistTool/Header.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,RotateLeft,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SaveSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/SaveSceneModal.jsx\");\n/* harmony import */ var _LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LoadSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/LoadSceneModal.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _VersionHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../VersionHistory */ \"(app-pages-browser)/./src/components/VersionHistory.tsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onReset, onScreenshot, onSaveScene, onLoadScene, isFullscreen, onToggleFullscreen, user, project, onUndo, onRedo } = param;\n    _s();\n    const [showSaveModal, setShowSaveModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showLoadModal, setShowLoadModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showVersionHistory, setShowVersionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                id: \"header\",\n                className: \"fixed w-full z-50 px-4 py-2 transition-all duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full px-6 py-1 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-auto h-10 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: \"/projects/\".concat(project._id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"size-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/CSS Logo.png\",\n                                        alt: \"Logo\",\n                                        width: 1024,\n                                        height: 780,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                        icon: isFullscreen ? _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        tooltip: isFullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\",\n                                        onClick: onToggleFullscreen\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-white/20 mx-1 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\")\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                tooltip: \"Undo (Ctrl+Z)\",\n                                                onClick: onUndo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                tooltip: \"Redo (Ctrl+Y)\",\n                                                onClick: onRedo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                tooltip: \"Reset Scene\",\n                                                onClick: onReset\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                tooltip: \"Screenshot\",\n                                                onClick: onScreenshot\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                tooltip: \"Version History\",\n                                                onClick: ()=>setShowVersionHistory(true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowLoadModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 137,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Load Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowSaveModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_RotateLeft_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 152,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Save Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__.SaveSceneModal, {\n                isOpen: showSaveModal,\n                onClose: ()=>setShowSaveModal(false),\n                onSave: (sceneName, description, saveType)=>{\n                    onSaveScene(sceneName, description, saveType);\n                    setShowSaveModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__.LoadSceneModal, {\n                isOpen: showLoadModal,\n                onClose: ()=>setShowLoadModal(false),\n                onLoad: (config)=>{\n                    onLoadScene(config);\n                    setShowLoadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VersionHistory__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showVersionHistory,\n                onClose: ()=>setShowVersionHistory(false),\n                userId: user.id,\n                workspaceId: project._id,\n                onApplyVersion: (config)=>{\n                    onLoadScene(config);\n                    setShowVersionHistory(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"BpQ93XwKg8UzXTTc2Gl4xwXptgA=\");\n_c = Header;\nfunction HeaderButton(param) {\n    let { icon: Icon, tooltip, primary = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClick,\n                    className: \"p-1.5 rounded-full \".concat(primary ? \"bg-primary text-white hover:bg-primary-600\" : \"text-white hover:bg-white/20\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: tooltip\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeaderButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"HeaderButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Header.jsx\n"));

/***/ })

});