"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@gilbarbara+deep-equal@0.1.2";
exports.ids = ["vendor-chunks/@gilbarbara+deep-equal@0.1.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/helpers.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/helpers.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isRegex: () => (/* binding */ isRegex),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined)\n/* harmony export */ });\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType(type) {\n    return function (value) { return typeof value === type; };\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nvar isFunction = isOfType('function');\nvar isNull = function (value) {\n    return value === null;\n};\nvar isRegex = function (value) {\n    return Object.prototype.toString.call(value).slice(8, -1) === 'RegExp';\n};\nvar isObject = function (value) {\n    return !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value === 'object');\n};\nvar isUndefined = isOfType('undefined');\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGdpbGJhcmJhcmErZGVlcC1lcXVhbEAwLjEuMi9ub2RlX21vZHVsZXMvQGdpbGJhcmJhcmEvZGVlcC1lcXVhbC9lc20vaGVscGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZ2lsYmFyYmFyYStkZWVwLWVxdWFsQDAuMS4yL25vZGVfbW9kdWxlcy9AZ2lsYmFyYmFyYS9kZWVwLWVxdWFsL2VzbS9oZWxwZXJzLmpzPzM1ZmMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9iYW4tdHlwZXNcbmZ1bmN0aW9uIGlzT2ZUeXBlKHR5cGUpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHZhbHVlKSB7IHJldHVybiB0eXBlb2YgdmFsdWUgPT09IHR5cGU7IH07XG59XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L2Jhbi10eXBlc1xuZXhwb3J0IHZhciBpc0Z1bmN0aW9uID0gaXNPZlR5cGUoJ2Z1bmN0aW9uJyk7XG5leHBvcnQgdmFyIGlzTnVsbCA9IGZ1bmN0aW9uICh2YWx1ZSkge1xuICAgIHJldHVybiB2YWx1ZSA9PT0gbnVsbDtcbn07XG5leHBvcnQgdmFyIGlzUmVnZXggPSBmdW5jdGlvbiAodmFsdWUpIHtcbiAgICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHZhbHVlKS5zbGljZSg4LCAtMSkgPT09ICdSZWdFeHAnO1xufTtcbmV4cG9ydCB2YXIgaXNPYmplY3QgPSBmdW5jdGlvbiAodmFsdWUpIHtcbiAgICByZXR1cm4gIWlzVW5kZWZpbmVkKHZhbHVlKSAmJiAhaXNOdWxsKHZhbHVlKSAmJiAoaXNGdW5jdGlvbih2YWx1ZSkgfHwgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jyk7XG59O1xuZXhwb3J0IHZhciBpc1VuZGVmaW5lZCA9IGlzT2ZUeXBlKCd1bmRlZmluZWQnKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlbHBlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ equal)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/helpers.js\");\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nfunction equalArray(left, right) {\n    var length = left.length;\n    if (length !== right.length) {\n        return false;\n    }\n    for (var index = length; index-- !== 0;) {\n        if (!equal(left[index], right[index])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction equalArrayBuffer(left, right) {\n    if (left.byteLength !== right.byteLength) {\n        return false;\n    }\n    var view1 = new DataView(left.buffer);\n    var view2 = new DataView(right.buffer);\n    var index = left.byteLength;\n    while (index--) {\n        if (view1.getUint8(index) !== view2.getUint8(index)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction equalMap(left, right) {\n    var e_1, _a, e_2, _b;\n    if (left.size !== right.size) {\n        return false;\n    }\n    try {\n        for (var _c = __values(left.entries()), _d = _c.next(); !_d.done; _d = _c.next()) {\n            var index = _d.value;\n            if (!right.has(index[0])) {\n                return false;\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    try {\n        for (var _e = __values(left.entries()), _f = _e.next(); !_f.done; _f = _e.next()) {\n            var index = _f.value;\n            if (!equal(index[1], right.get(index[0]))) {\n                return false;\n            }\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return true;\n}\nfunction equalSet(left, right) {\n    var e_3, _a;\n    if (left.size !== right.size) {\n        return false;\n    }\n    try {\n        for (var _b = __values(left.entries()), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var index = _c.value;\n            if (!right.has(index[0])) {\n                return false;\n            }\n        }\n    }\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_3) throw e_3.error; }\n    }\n    return true;\n}\nfunction equal(left, right) {\n    if (left === right) {\n        return true;\n    }\n    if (left && (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isObject)(left) && right && (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isObject)(right)) {\n        if (left.constructor !== right.constructor) {\n            return false;\n        }\n        if (Array.isArray(left) && Array.isArray(right)) {\n            return equalArray(left, right);\n        }\n        if (left instanceof Map && right instanceof Map) {\n            return equalMap(left, right);\n        }\n        if (left instanceof Set && right instanceof Set) {\n            return equalSet(left, right);\n        }\n        if (ArrayBuffer.isView(left) && ArrayBuffer.isView(right)) {\n            return equalArrayBuffer(left, right);\n        }\n        if ((0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isRegex)(left) && (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isRegex)(right)) {\n            return left.source === right.source && left.flags === right.flags;\n        }\n        if (left.valueOf !== Object.prototype.valueOf) {\n            return left.valueOf() === right.valueOf();\n        }\n        if (left.toString !== Object.prototype.toString) {\n            return left.toString() === right.toString();\n        }\n        var leftKeys = Object.keys(left);\n        var rightKeys = Object.keys(right);\n        if (leftKeys.length !== rightKeys.length) {\n            return false;\n        }\n        for (var index = leftKeys.length; index-- !== 0;) {\n            if (!Object.prototype.hasOwnProperty.call(right, leftKeys[index])) {\n                return false;\n            }\n        }\n        for (var index = leftKeys.length; index-- !== 0;) {\n            var key = leftKeys[index];\n            if (key === '_owner' && left.$$typeof) {\n                // React-specific: avoid traversing React elements' _owner.\n                //  _owner contains circular references\n                // and is not needed when comparing the actual elements (and not their owners)\n                // eslint-disable-next-line no-continue\n                continue;\n            }\n            if (!equal(left[key], right[key])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (Number.isNaN(left) && Number.isNaN(right)) {\n        return true;\n    }\n    return left === right;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@gilbarbara+deep-equal@0.1.2/node_modules/@gilbarbara/deep-equal/esm/index.js\n");

/***/ })

};
;