"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-serde@3.296.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-serde@3.296.0"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddleware: () => (/* binding */ deserializerMiddleware)\n/* harmony export */ });\nconst deserializerMiddleware = (options, deserializer) => (next, context) => async (args) => {\n    const { response } = await next(args);\n    try {\n        const parsed = await deserializer(response, options);\n        return {\n            response,\n            output: parsed,\n        };\n    }\n    catch (error) {\n        Object.defineProperty(error, \"$response\", {\n            value: response,\n        });\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9kZXNlcmlhbGl6ZXJNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLFlBQVksV0FBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvZGVzZXJpYWxpemVyTWlkZGxld2FyZS5qcz8xZjIxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlID0gKG9wdGlvbnMsIGRlc2VyaWFsaXplcikgPT4gKG5leHQsIGNvbnRleHQpID0+IGFzeW5jIChhcmdzKSA9PiB7XG4gICAgY29uc3QgeyByZXNwb25zZSB9ID0gYXdhaXQgbmV4dChhcmdzKTtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBhd2FpdCBkZXNlcmlhbGl6ZXIocmVzcG9uc2UsIG9wdGlvbnMpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcmVzcG9uc2UsXG4gICAgICAgICAgICBvdXRwdXQ6IHBhcnNlZCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlcnJvciwgXCIkcmVzcG9uc2VcIiwge1xuICAgICAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddleware: () => (/* binding */ deserializerMiddleware)\n/* harmony export */ });\nconst deserializerMiddleware = (options, deserializer) => (next, context) => async (args) => {\n    const { response } = await next(args);\n    try {\n        const parsed = await deserializer(response, options);\n        return {\n            response,\n            output: parsed,\n        };\n    }\n    catch (error) {\n        Object.defineProperty(error, \"$response\", {\n            value: response,\n        });\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvZGVzZXJpYWxpemVyTWlkZGxld2FyZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtc2VyZGVAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZXJkZS9kaXN0LWVzL2Rlc2VyaWFsaXplck1pZGRsZXdhcmUuanM/NWEwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZGVzZXJpYWxpemVyTWlkZGxld2FyZSA9IChvcHRpb25zLCBkZXNlcmlhbGl6ZXIpID0+IChuZXh0LCBjb250ZXh0KSA9PiBhc3luYyAoYXJncykgPT4ge1xuICAgIGNvbnN0IHsgcmVzcG9uc2UgfSA9IGF3YWl0IG5leHQoYXJncyk7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcGFyc2VkID0gYXdhaXQgZGVzZXJpYWxpemVyKHJlc3BvbnNlLCBvcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlc3BvbnNlLFxuICAgICAgICAgICAgb3V0cHV0OiBwYXJzZWQsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXJyb3IsIFwiJHJlc3BvbnNlXCIsIHtcbiAgICAgICAgICAgIHZhbHVlOiByZXNwb25zZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _serdePlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serdePlugin */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _serdePlugin__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _serdePlugin__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./serializerMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXlDO0FBQ1g7QUFDUyIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9pbmRleC5qcz8zMTVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Rlc2VyaWFsaXplck1pZGRsZXdhcmVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlUGx1Z2luXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zZXJpYWxpemVyTWlkZGxld2FyZVwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddleware: () => (/* reexport safe */ _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__.deserializerMiddleware),\n/* harmony export */   deserializerMiddlewareOption: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.deserializerMiddlewareOption),\n/* harmony export */   getSerdePlugin: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin),\n/* harmony export */   serializerMiddleware: () => (/* reexport safe */ _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__.serializerMiddleware),\n/* harmony export */   serializerMiddlewareOption: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony import */ var _serdePlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serdePlugin */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js\");\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./serializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBeUM7QUFDWDtBQUNTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWdhcGUtbmV3LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtc2VyZGVAMy4yOTYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZXJkZS9kaXN0LWVzL2luZGV4LmpzPzdiOTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZGVzZXJpYWxpemVyTWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2VyZGVQbHVnaW5cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmlhbGl6ZXJNaWRkbGV3YXJlXCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddlewareOption: () => (/* binding */ deserializerMiddlewareOption),\n/* harmony export */   getSerdePlugin: () => (/* binding */ getSerdePlugin),\n/* harmony export */   serializerMiddlewareOption: () => (/* binding */ serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serializerMiddleware */ \"(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\");\n\n\nconst deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nconst serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nfunction getSerdePlugin(config, serializer, deserializer) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add((0,_deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__.deserializerMiddleware)(config, deserializer), deserializerMiddlewareOption);\n            commandStack.add((0,_serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__.serializerMiddleware)(config, serializer), serializerMiddlewareOption);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJkZVBsdWdpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRTtBQUNKO0FBQ3ZEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLDZCQUE2QiwrRUFBc0I7QUFDbkQsNkJBQTZCLDJFQUFvQjtBQUNqRCxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJkZVBsdWdpbi5qcz80NGMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlc2VyaWFsaXplck1pZGRsZXdhcmUgfSBmcm9tIFwiLi9kZXNlcmlhbGl6ZXJNaWRkbGV3YXJlXCI7XG5pbXBvcnQgeyBzZXJpYWxpemVyTWlkZGxld2FyZSB9IGZyb20gXCIuL3NlcmlhbGl6ZXJNaWRkbGV3YXJlXCI7XG5leHBvcnQgY29uc3QgZGVzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbiA9IHtcbiAgICBuYW1lOiBcImRlc2VyaWFsaXplck1pZGRsZXdhcmVcIixcbiAgICBzdGVwOiBcImRlc2VyaWFsaXplXCIsXG4gICAgdGFnczogW1wiREVTRVJJQUxJWkVSXCJdLFxuICAgIG92ZXJyaWRlOiB0cnVlLFxufTtcbmV4cG9ydCBjb25zdCBzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbiA9IHtcbiAgICBuYW1lOiBcInNlcmlhbGl6ZXJNaWRkbGV3YXJlXCIsXG4gICAgc3RlcDogXCJzZXJpYWxpemVcIixcbiAgICB0YWdzOiBbXCJTRVJJQUxJWkVSXCJdLFxuICAgIG92ZXJyaWRlOiB0cnVlLFxufTtcbmV4cG9ydCBmdW5jdGlvbiBnZXRTZXJkZVBsdWdpbihjb25maWcsIHNlcmlhbGl6ZXIsIGRlc2VyaWFsaXplcikge1xuICAgIHJldHVybiB7XG4gICAgICAgIGFwcGx5VG9TdGFjazogKGNvbW1hbmRTdGFjaykgPT4ge1xuICAgICAgICAgICAgY29tbWFuZFN0YWNrLmFkZChkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlKGNvbmZpZywgZGVzZXJpYWxpemVyKSwgZGVzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbik7XG4gICAgICAgICAgICBjb21tYW5kU3RhY2suYWRkKHNlcmlhbGl6ZXJNaWRkbGV3YXJlKGNvbmZpZywgc2VyaWFsaXplciksIHNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uKTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddlewareOption: () => (/* binding */ deserializerMiddlewareOption),\n/* harmony export */   getSerdePlugin: () => (/* binding */ getSerdePlugin),\n/* harmony export */   serializerMiddlewareOption: () => (/* binding */ serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\");\n\n\nconst deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nconst serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nfunction getSerdePlugin(config, serializer, deserializer) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add((0,_deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__.deserializerMiddleware)(config, deserializer), deserializerMiddlewareOption);\n            commandStack.add((0,_serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__.serializerMiddleware)(config, serializer), serializerMiddlewareOption);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvc2VyZGVQbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBa0U7QUFDSjtBQUN2RDtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSw2QkFBNkIsK0VBQXNCO0FBQ25ELDZCQUE2QiwyRUFBb0I7QUFDakQsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvc2VyZGVQbHVnaW4uanM/M2I5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlIH0gZnJvbSBcIi4vZGVzZXJpYWxpemVyTWlkZGxld2FyZVwiO1xuaW1wb3J0IHsgc2VyaWFsaXplck1pZGRsZXdhcmUgfSBmcm9tIFwiLi9zZXJpYWxpemVyTWlkZGxld2FyZVwiO1xuZXhwb3J0IGNvbnN0IGRlc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24gPSB7XG4gICAgbmFtZTogXCJkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlXCIsXG4gICAgc3RlcDogXCJkZXNlcmlhbGl6ZVwiLFxuICAgIHRhZ3M6IFtcIkRFU0VSSUFMSVpFUlwiXSxcbiAgICBvdmVycmlkZTogdHJ1ZSxcbn07XG5leHBvcnQgY29uc3Qgc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24gPSB7XG4gICAgbmFtZTogXCJzZXJpYWxpemVyTWlkZGxld2FyZVwiLFxuICAgIHN0ZXA6IFwic2VyaWFsaXplXCIsXG4gICAgdGFnczogW1wiU0VSSUFMSVpFUlwiXSxcbiAgICBvdmVycmlkZTogdHJ1ZSxcbn07XG5leHBvcnQgZnVuY3Rpb24gZ2V0U2VyZGVQbHVnaW4oY29uZmlnLCBzZXJpYWxpemVyLCBkZXNlcmlhbGl6ZXIpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBhcHBseVRvU3RhY2s6IChjb21tYW5kU3RhY2spID0+IHtcbiAgICAgICAgICAgIGNvbW1hbmRTdGFjay5hZGQoZGVzZXJpYWxpemVyTWlkZGxld2FyZShjb25maWcsIGRlc2VyaWFsaXplciksIGRlc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24pO1xuICAgICAgICAgICAgY29tbWFuZFN0YWNrLmFkZChzZXJpYWxpemVyTWlkZGxld2FyZShjb25maWcsIHNlcmlhbGl6ZXIpLCBzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbik7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serdePlugin.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serializerMiddleware: () => (/* binding */ serializerMiddleware)\n/* harmony export */ });\nconst serializerMiddleware = (options, serializer) => (next, context) => async (args) => {\n    const endpoint = context.endpointV2?.url && options.urlParser\n        ? async () => options.urlParser(context.endpointV2.url)\n        : options.endpoint;\n    if (!endpoint) {\n        throw new Error(\"No valid endpoint provider available.\");\n    }\n    const request = await serializer(args.input, { ...options, endpoint });\n    return next({\n        ...args,\n        request,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJpYWxpemVyTWlkZGxld2FyZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsc0JBQXNCO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnYXBlLW5ldy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXNlcmRlQDMuMjk2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJpYWxpemVyTWlkZGxld2FyZS5qcz84OGEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzZXJpYWxpemVyTWlkZGxld2FyZSA9IChvcHRpb25zLCBzZXJpYWxpemVyKSA9PiAobmV4dCwgY29udGV4dCkgPT4gYXN5bmMgKGFyZ3MpID0+IHtcbiAgICBjb25zdCBlbmRwb2ludCA9IGNvbnRleHQuZW5kcG9pbnRWMj8udXJsICYmIG9wdGlvbnMudXJsUGFyc2VyXG4gICAgICAgID8gYXN5bmMgKCkgPT4gb3B0aW9ucy51cmxQYXJzZXIoY29udGV4dC5lbmRwb2ludFYyLnVybClcbiAgICAgICAgOiBvcHRpb25zLmVuZHBvaW50O1xuICAgIGlmICghZW5kcG9pbnQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gdmFsaWQgZW5kcG9pbnQgcHJvdmlkZXIgYXZhaWxhYmxlLlwiKTtcbiAgICB9XG4gICAgY29uc3QgcmVxdWVzdCA9IGF3YWl0IHNlcmlhbGl6ZXIoYXJncy5pbnB1dCwgeyAuLi5vcHRpb25zLCBlbmRwb2ludCB9KTtcbiAgICByZXR1cm4gbmV4dCh7XG4gICAgICAgIC4uLmFyZ3MsXG4gICAgICAgIHJlcXVlc3QsXG4gICAgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serializerMiddleware: () => (/* binding */ serializerMiddleware)\n/* harmony export */ });\nconst serializerMiddleware = (options, serializer) => (next, context) => async (args) => {\n    const endpoint = context.endpointV2?.url && options.urlParser\n        ? async () => options.urlParser(context.endpointV2.url)\n        : options.endpoint;\n    if (!endpoint) {\n        throw new Error(\"No valid endpoint provider available.\");\n    }\n    const request = await serializer(args.input, { ...options, endpoint });\n    return next({\n        ...args,\n        request,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvc2VyaWFsaXplck1pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELHNCQUFzQjtBQUN6RTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1zZXJkZUAzLjI5Ni4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvc2VyaWFsaXplck1pZGRsZXdhcmUuanM/ZmNjZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2VyaWFsaXplck1pZGRsZXdhcmUgPSAob3B0aW9ucywgc2VyaWFsaXplcikgPT4gKG5leHQsIGNvbnRleHQpID0+IGFzeW5jIChhcmdzKSA9PiB7XG4gICAgY29uc3QgZW5kcG9pbnQgPSBjb250ZXh0LmVuZHBvaW50VjI/LnVybCAmJiBvcHRpb25zLnVybFBhcnNlclxuICAgICAgICA/IGFzeW5jICgpID0+IG9wdGlvbnMudXJsUGFyc2VyKGNvbnRleHQuZW5kcG9pbnRWMi51cmwpXG4gICAgICAgIDogb3B0aW9ucy5lbmRwb2ludDtcbiAgICBpZiAoIWVuZHBvaW50KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIk5vIHZhbGlkIGVuZHBvaW50IHByb3ZpZGVyIGF2YWlsYWJsZS5cIik7XG4gICAgfVxuICAgIGNvbnN0IHJlcXVlc3QgPSBhd2FpdCBzZXJpYWxpemVyKGFyZ3MuaW5wdXQsIHsgLi4ub3B0aW9ucywgZW5kcG9pbnQgfSk7XG4gICAgcmV0dXJuIG5leHQoe1xuICAgICAgICAuLi5hcmdzLFxuICAgICAgICByZXF1ZXN0LFxuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-serde@3.296.0/node_modules/@aws-sdk/middleware-serde/dist-es/serializerMiddleware.js\n");

/***/ })

};
;