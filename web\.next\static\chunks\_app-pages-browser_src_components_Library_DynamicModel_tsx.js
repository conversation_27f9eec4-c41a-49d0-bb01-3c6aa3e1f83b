"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_Library_DynamicModel_tsx"],{

/***/ "(app-pages-browser)/./src/components/Library/DynamicModel.tsx":
/*!*************************************************!*\
  !*** ./src/components/Library/DynamicModel.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst DynamicModel = (param)=>{\n    let { modelUrl, onClick, onRightClick } = param;\n    _s();\n    const gltfRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isScaled, setIsScaled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(()=>{\n        if (gltfRef.current && !isScaled) {\n            const box = new three__WEBPACK_IMPORTED_MODULE_3__.Box3().setFromObject(gltfRef.current);\n            const size = box.getSize(new three__WEBPACK_IMPORTED_MODULE_3__.Vector3());\n            const maxDimension = Math.max(size.x, size.y, size.z);\n            // Target size of 2 units\n            const targetSize = 2;\n            const scale = targetSize / maxDimension;\n            if (scale !== 1) {\n                gltfRef.current.scale.setScalar(scale);\n            }\n            setIsScaled(true);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Gltf, {\n        ref: gltfRef,\n        src: modelUrl,\n        onClick: onClick,\n        onContextMenu: onRightClick\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\Library\\\\DynamicModel.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DynamicModel, \"nvCuAEw0BgaF53AR0gK5fiKRo+A=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F\n    ];\n});\n_c = DynamicModel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DynamicModel);\nvar _c;\n$RefreshReg$(_c, \"DynamicModel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Library/DynamicModel.tsx\n"));

/***/ })

}]);