"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.0/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom_yfklyfqno7isrggempim2x7psi/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3._sgbkes3iamq4rj6s55dcmugemq/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previous@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@1_q53tfll65grekjiuivj3ykwz34/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.5.7_@types+react@18.3.4_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // packages/react/select/src/Select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? Boolean(trigger.closest(\"form\")) : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(BubbleSelect, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = ()=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false) {\n                    handleOpen();\n                    context.triggerPointerDownPosRef.current = {\n                        x: Math.round(event.pageX),\n                        y: Math.round(event.pageY)\n                    };\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    rightEdge - contentWidth\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    leftEdge - contentWidth\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        overflow: \"auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, handleSelect),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nvar BubbleSelect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", {\n            ...selectProps,\n            ref: composedRefs,\n            defaultValue: value\n        })\n    });\n});\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1.1_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;