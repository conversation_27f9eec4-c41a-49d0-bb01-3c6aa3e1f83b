import { Metadata } from "next";
import { getWorkspace } from "@/lib/actions/workspace.actions";
import { getCurrentUser } from "@/lib/session";

interface LayoutProps {
  children: React.ReactNode;
  params: { id: string };
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return {
        title: "Project | Agape",
        description: "View your project details and settings.",
      };
    }

    const project = await getWorkspace({
      userId: user.id,
      workspaceId: params.id,
    });

    if (!project) {
      return {
        title: "Project Not Found | CSS",
        description: "The requested project could not be found.",
      };
    }

    return {
      title: `${project.name} | CSS`,
      description:
        project.description ||
        `View and edit ${project.name} project in CSS Editor.`,
    };
  } catch (error) {
    return {
      title: "Project | CSS",
      description: "View your project details and settings.",
    };
  }
}

export default function ProjectLayout({ children }: LayoutProps) {
  return <>{children}</>;
}
