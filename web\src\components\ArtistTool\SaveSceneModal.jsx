import { useState } from "react";
import { Download, Save, CloudUpload } from "lucide-react";
import { Slide, toast } from "react-toastify";

export function SaveSceneModal({ isOpen, onClose, onSave }) {
  const [sceneName, setSceneName] = useState("My Scene");
  const [description, setDescription] = useState("");
  const [saveType, setSaveType] = useState("local"); // 'local' or 'export'

  if (!isOpen) return null;

  const handleSave = () => {
    if (!sceneName.trim()) {
      toast.error("Please enter a scene name", {
        position: "bottom-right",
        theme: "colored",
      });
      return;
    }

    if (!description.trim()) {
      toast.error("Please enter a description", {
        position: "bottom-right",
        theme: "colored",
      });
      return;
    }

    if (onSave) {
      onSave(sceneName.trim(), description.trim(), saveType);
      if (saveType === "local") {
        toast.success("Scene saved successfully!", {
          position: "bottom-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
          transition: Slide,
        });
      } else if (saveType === "export") {
        toast.success("Scene exported successfully!", {
          position: "bottom-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
          transition: Slide,
        });
      }
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[2000]">
      <div className="bg-gradient-to-tl from-[#32343D] to-[#14192D] p-6 rounded-lg shadow-lg w-[400px]">
        <h3 className="text-lg font-semibold text-[#FDE9CE] mb-4">
          Save Scene
        </h3>

        <div className="mb-4">
          <label className="block text-xs md:text-sm text-[#FDE9CE]/80 mb-2">
            Scene Name
          </label>
          <input
            type="text"
            value={sceneName}
            onChange={(e) => setSceneName(e.target.value)}
            className="w-full bg-[#1A1D2E] border border-[#FDE9CE]/20 rounded px-3 py-2 text-[#FDE9CE] focus:outline-none focus:border-[#FDE9CE]/40"
            required
          />
        </div>

        <div className="mb-4">
          <label className="block text-xs md:text-sm text-[#FDE9CE]/80 mb-2">
            Description
          </label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="w-full bg-[#1A1D2E] border border-[#FDE9CE]/20 rounded px-3 py-2 text-[#FDE9CE] focus:outline-none focus:border-[#FDE9CE]/40 min-h-[60px]"
            placeholder="Describe this version..."
            required
          />
        </div>

        <div className="mb-4">
          <label className="block text-xs md:text-sm text-[#FDE9CE]/80 mb-2">
            Save Type
          </label>
          <div className="flex gap-2">
            <button
              onClick={() => setSaveType("local")}
              className={`flex-1 py-2 rounded ${
                saveType === "local"
                  ? "bg-white/20 border border-[#FDE9CE]/40"
                  : "bg-[#1A1D2E] border border-[#FDE9CE]/20"
              }`}
            >
              <div className="flex items-center justify-center gap-2">
                <Save size={16} className="text-[#FDE9CE]" />
                <span className="text-xs md:text-sm text-[#FDE9CE]">
                  Save to Browser
                </span>
              </div>
              <span className="text-[10px] md:text-xs text-[#FDE9CE]/60 block mt-1">
                Save and restore on next visit
              </span>
            </button>
            <button
              onClick={() => setSaveType("db")}
              className={`flex-1 py-2 rounded ${
                saveType === "db"
                  ? "bg-white/20 border border-[#FDE9CE]/40"
                  : "bg-[#1A1D2E] border border-[#FDE9CE]/20"
              }`}
            >
              <div className="flex items-center justify-center gap-2">
                <CloudUpload size={16} className="text-[#FDE9CE]" />
                <span className="text-xs md:text-sm text-[#FDE9CE]">
                  Save to Cloud
                </span>
              </div>
              <span className="text-[10px] md:text-xs text-[#FDE9CE]/60 block mt-1">
                Save to your account (DB)
              </span>
            </button>
            <button
              onClick={() => setSaveType("export")}
              className={`flex-1 py-2 rounded ${
                saveType === "export"
                  ? "bg-white/20 border border-[#FDE9CE]/40"
                  : "bg-[#1A1D2E] border border-[#FDE9CE]/20"
              }`}
            >
              <div className="flex items-center justify-center gap-2">
                <Download size={16} className="text-[#FDE9CE]" />
                <span className="text-xs md:text-sm text-[#FDE9CE]">
                  Export Config
                </span>
              </div>
              <span className="text-[10px] md:text-xs text-[#FDE9CE]/60 block mt-1">
                Download scene settings
              </span>
            </button>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-xs md:text-sm text-[#FDE9CE] hover:bg-white/10 rounded transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-xs md:text-sm bg-white/10 text-[#FDE9CE] rounded hover:bg-white/20 transition-colors flex items-center gap-2"
          >
            {saveType === "local" || saveType === "db" ? (
              <>
                <Save size={16} />
                Save
              </>
            ) : (
              <>
                <Download size={16} />
                Export
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
