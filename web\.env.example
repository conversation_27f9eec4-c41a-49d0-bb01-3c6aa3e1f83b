AUTH_SECRET=SIFSRK
NEXTAUTH_SECRET=SIFSRK

NEXT_PUBLIC_SITE_URL=https://css-cms.vercel.app

NODE_ENV=development

WEB_SOCKET_URL=Replace with backend url


MONGODB_URI="**************************************************************************************************************************"



AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=flsRKlMO8WAOptGYgsBjFEEF2Ml9eSQEHJr/o6ma
AWS_BUCKET=cmsmodels
NEXT_PUBLIC_AWS_BUCKET=cmsmodels
AWS_REGION=ap-south-1

S3_DERIVED_WORKSPACE_URL=https://cmsmodels.s3.amazonaws.com/workspace-models


NEXT_PUBLIC_MEASUREMENT_ID=SAMPLE


EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=MARY will provide
EMAIL_SERVER_PASSWORD=""
EMAIL_FROM=<EMAIL>

PORT=8000
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PWD=agape
REDIS_USER=default