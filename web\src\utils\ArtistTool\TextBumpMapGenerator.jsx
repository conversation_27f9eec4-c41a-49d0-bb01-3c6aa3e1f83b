import React, { useMemo } from "react";
import { Canvas, use<PERSON>rame, useThree } from "@react-three/fiber";
import { Text, PerspectiveCamera, RenderTexture } from "@react-three/drei";
import * as THREE from "three";

// Generate normal map from height map
function generateNormalMapFromHeight(heightCanvas, strength = 1.0) {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = heightCanvas.width;
  canvas.height = heightCanvas.height;

  const heightCtx = heightCanvas.getContext("2d");
  const heightData = heightCtx.getImageData(0, 0, canvas.width, canvas.height);
  const normalData = ctx.createImageData(canvas.width, canvas.height);

  for (let y = 0; y < canvas.height; y++) {
    for (let x = 0; x < canvas.width; x++) {
      const i = (y * canvas.width + x) * 4;

      // Sample neighboring pixels for gradient calculation
      const getHeight = (px, py) => {
        const clampedX = Math.max(0, Math.min(canvas.width - 1, px));
        const clampedY = Math.max(0, Math.min(canvas.height - 1, py));
        const idx = (clampedY * canvas.width + clampedX) * 4;
        return heightData.data[idx] / 255.0; // Normalize to 0-1
      };

      // Calculate gradients
      const heightL = getHeight(x - 1, y);
      const heightR = getHeight(x + 1, y);
      const heightU = getHeight(x, y - 1);
      const heightD = getHeight(x, y + 1);

      // Calculate normal vector
      const dx = (heightR - heightL) * strength;
      const dy = (heightD - heightU) * strength;
      const dz = 1.0;

      // Normalize
      const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
      const nx = dx / length;
      const ny = dy / length;
      const nz = dz / length;

      // Convert to 0-255 range and store
      normalData.data[i] = (nx + 1) * 0.5 * 255; // R
      normalData.data[i + 1] = (ny + 1) * 0.5 * 255; // G
      normalData.data[i + 2] = (nz + 1) * 0.5 * 255; // B
      normalData.data[i + 3] = 255; // A
    }
  }

  ctx.putImageData(normalData, 0, 0);
  return canvas;
}

export function TextBumpMapGenerator({
  text,
  font,
  fontSize = 5,
  aspect = 3,
  bumpStrength = 1.0,
  children,
}) {
  const textureSize = useMemo(() => {
    if (!text) return { width: 512, height: 512 / aspect };

    // Calculate dynamic width based on text length
    const baseWidth = 512;
    const minWidth = 256;
    const maxWidth = 2048;

    // Estimate text width (rough approximation)
    const charWidth = 0.6; // Average character width ratio
    const estimatedTextWidth = text.length * charWidth;

    // Calculate required width with padding
    const requiredWidth = Math.max(
      minWidth,
      Math.min(maxWidth, baseWidth * Math.max(1, estimatedTextWidth / 10))
    );

    // Ensure width is power of 2 for better texture performance
    const powerOfTwo = Math.pow(2, Math.ceil(Math.log2(requiredWidth)));

    return {
      width: powerOfTwo,
      height: Math.max(128, powerOfTwo / aspect),
    };
  }, [text, aspect]);

  const { bumpMap, normalMap } = useMemo(() => {
    if (!text || !font) return { bumpMap: null, normalMap: null };

    // Create a temporary canvas to render text as height map
    const canvas = document.createElement("canvas");
    canvas.width = textureSize.width;
    canvas.height = textureSize.height;
    const ctx = canvas.getContext("2d");

    // Clear canvas
    ctx.fillStyle = "black";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Calculate optimal font size based on canvas size and text length
    let fontSizePixels = fontSize * (canvas.height / 10);

    // Set initial font to measure text
    ctx.font = `${fontSizePixels}px Arial`;
    let textMetrics = ctx.measureText(text);
    let textWidth = textMetrics.width;

    // Adjust font size to fit within canvas width with padding
    const padding = canvas.width * 0.1; // 10% padding on each side
    const availableWidth = canvas.width - padding * 2;

    if (textWidth > availableWidth) {
      // Scale down font size to fit
      fontSizePixels = (fontSizePixels * availableWidth) / textWidth;
      ctx.font = `${fontSizePixels}px Arial`;
      textMetrics = ctx.measureText(text);
    } else if (textWidth < availableWidth * 0.6) {
      // Scale up font size if text is too small (up to a reasonable limit)
      const scaleFactor = Math.min(2.0, (availableWidth * 0.8) / textWidth);
      fontSizePixels = fontSizePixels * scaleFactor;
      ctx.font = `${fontSizePixels}px Arial`;
      textMetrics = ctx.measureText(text);
    }

    // Set up text rendering with optimized font size
    ctx.fillStyle = "white";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    // Add text shadow/glow for better depth effect
    ctx.shadowColor = "rgba(255, 255, 255, 0.5)";
    ctx.shadowBlur = Math.max(2, fontSizePixels * 0.05);

    // Render text at center
    ctx.fillText(text, canvas.width / 2, canvas.height / 2);

    // Reset shadow for cleaner edges
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;

    // Create bump map texture
    const bumpTexture = new THREE.CanvasTexture(canvas);
    bumpTexture.wrapS = THREE.RepeatWrapping;
    bumpTexture.wrapT = THREE.RepeatWrapping;
    bumpTexture.needsUpdate = true;

    // Generate normal map from the height map
    const normalCanvas = generateNormalMapFromHeight(canvas, bumpStrength);
    const normalTexture = new THREE.CanvasTexture(normalCanvas);
    normalTexture.wrapS = THREE.RepeatWrapping;
    normalTexture.wrapT = THREE.RepeatWrapping;
    normalTexture.needsUpdate = true;

    return {
      bumpMap: bumpTexture,
      normalMap: normalTexture,
    };
  }, [text, font, fontSize, aspect, bumpStrength, textureSize]);

  return children({ bumpMap, normalMap });
}

// Enhanced material component with bump mapping
export function EngravingMaterial({
  text,
  font,
  fontSize = 5,
  color = "#2c2c2c",
  decalColor = "#000000",
  aspect = 3,
  ...materialProps
}) {
  // Calculate dynamic aspect ratio based on text length
  const dynamicAspect = useMemo(() => {
    if (!text) return aspect;

    // Adjust aspect ratio based on text length
    const baseAspect = aspect;
    const textLength = text.length;

    // For longer text, use wider aspect ratio
    if (textLength > 10) {
      return Math.min(baseAspect * 1.5, 6);
    } else if (textLength > 6) {
      return Math.min(baseAspect * 1.2, 4);
    }

    return baseAspect;
  }, [text, aspect]);

  // Optimal realistic values for carved engravings
  const bumpStrength = 0.8; // Strong height variation for deep carving
  const normalScale = 0.6; // Good lighting response
  const depthScale = 0.08; // Visible depth without being too pronounced

  return (
    <TextBumpMapGenerator
      text={text}
      font={font}
      fontSize={fontSize}
      aspect={dynamicAspect}
      bumpStrength={bumpStrength}
    >
      {({ bumpMap, normalMap }) => (
        <meshStandardMaterial
          roughness={0.8}
          metalness={0.3}
          transparent
          opacity={0.9}
          color={color}
          polygonOffset
          polygonOffsetFactor={-15}
          polygonOffsetUnits={-4}
          depthWrite={false}
          bumpMap={bumpMap}
          bumpScale={depthScale}
          normalMap={normalMap}
          normalScale={[normalScale, normalScale]}
          {...materialProps}
        >
          <RenderTexture attach="map" anisotropy={16}>
            <PerspectiveCamera
              makeDefault
              manual
              aspect={dynamicAspect}
              position={[0, 0, 10]}
              near={0.1}
              far={1000}
              fov={50}
            />
            <Text
              font={font}
              fontSize={fontSize}
              letterSpacing={-0.05}
              color={decalColor}
            >
              {text}
            </Text>
          </RenderTexture>
        </meshStandardMaterial>
      )}
    </TextBumpMapGenerator>
  );
}
