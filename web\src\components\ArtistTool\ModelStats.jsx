import { Html } from "@react-three/drei";
import { useEffect, useState } from "react";

export function ModelStats({ stats }) {
  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        left: "20px",
        background: "rgba(0,0,0,0.7)",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontFamily: "monospace",
        zIndex: 1000,
      }}
    >
      <div>Vertices: {stats.vertices.toLocaleString()}</div>
      <div>Triangles: {stats.triangles.toLocaleString()}</div>
      <div>Materials: {stats.materials}</div>
      <div>Animations: {stats.animations}</div>
    </div>
  );
}
