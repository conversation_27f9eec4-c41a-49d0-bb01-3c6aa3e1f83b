"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-lite@0.8.2";
exports.ids = ["vendor-chunks/is-lite@0.8.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getObjectType: () => (/* binding */ getObjectType)\n/* harmony export */ });\nvar DOM_PROPERTIES_TO_CHECK = [\n    'innerHTML',\n    'ownerDocument',\n    'style',\n    'attributes',\n    'nodeValue',\n];\nvar objectTypes = [\n    'Array',\n    'ArrayBuffer',\n    'AsyncFunction',\n    'AsyncGenerator',\n    'AsyncGeneratorFunction',\n    'Date',\n    'Error',\n    'Function',\n    'Generator',\n    'GeneratorFunction',\n    'HTMLElement',\n    'Map',\n    'Object',\n    'Promise',\n    'RegExp',\n    'Set',\n    'WeakMap',\n    'WeakSet',\n];\nvar primitiveTypes = [\n    'bigint',\n    'boolean',\n    'null',\n    'number',\n    'string',\n    'symbol',\n    'undefined',\n];\nfunction getObjectType(value) {\n    var objectTypeName = Object.prototype.toString.call(value).slice(8, -1);\n    if (/HTML\\w+Element/.test(objectTypeName)) {\n        return 'HTMLElement';\n    }\n    if (isObjectType(objectTypeName)) {\n        return objectTypeName;\n    }\n    return undefined;\n}\nfunction isObjectOfType(type) {\n    return function (value) { return getObjectType(value) === type; };\n}\nfunction isObjectType(name) {\n    return objectTypes.includes(name);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType(type) {\n    return function (value) { return typeof value === type; };\n}\nfunction isPrimitiveType(name) {\n    return primitiveTypes.includes(name);\n}\nfunction is(value) {\n    if (value === null) {\n        return 'null';\n    }\n    switch (typeof value) {\n        case 'bigint':\n            return 'bigint';\n        case 'boolean':\n            return 'boolean';\n        case 'number':\n            return 'number';\n        case 'string':\n            return 'string';\n        case 'symbol':\n            return 'symbol';\n        case 'undefined':\n            return 'undefined';\n        default:\n    }\n    if (is.array(value)) {\n        return 'Array';\n    }\n    if (is.plainFunction(value)) {\n        return 'Function';\n    }\n    var tagType = getObjectType(value);\n    /* istanbul ignore else */\n    if (tagType) {\n        return tagType;\n    }\n    /* istanbul ignore next */\n    return 'Object';\n}\nis.array = Array.isArray;\nis.arrayOf = function (target, predicate) {\n    if (!is.array(target) && !is.function(predicate)) {\n        return false;\n    }\n    return target.every(function (d) { return predicate(d); });\n};\nis.asyncGeneratorFunction = function (value) {\n    return getObjectType(value) === 'AsyncGeneratorFunction';\n};\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.asyncFunction = isObjectOfType('AsyncFunction');\nis.bigint = isOfType('bigint');\nis.boolean = function (value) {\n    return value === true || value === false;\n};\nis.date = isObjectOfType('Date');\nis.defined = function (value) { return !is.undefined(value); };\nis.domElement = function (value) {\n    return (is.object(value) &&\n        !is.plainObject(value) &&\n        value.nodeType === 1 &&\n        is.string(value.nodeName) &&\n        DOM_PROPERTIES_TO_CHECK.every(function (property) { return property in value; }));\n};\nis.empty = function (value) {\n    return ((is.string(value) && value.length === 0) ||\n        (is.array(value) && value.length === 0) ||\n        (is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0) ||\n        (is.set(value) && value.size === 0) ||\n        (is.map(value) && value.size === 0));\n};\nis.error = isObjectOfType('Error');\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.function = isOfType('function');\nis.generator = function (value) {\n    return (is.iterable(value) &&\n        is.function(value.next) &&\n        is.function(value.throw));\n};\nis.generatorFunction = isObjectOfType('GeneratorFunction');\nis.instanceOf = function (instance, class_) {\n    if (!instance || !class_) {\n        return false;\n    }\n    return Object.getPrototypeOf(instance) === class_.prototype;\n};\nis.iterable = function (value) {\n    return (!is.nullOrUndefined(value) && is.function(value[Symbol.iterator]));\n};\nis.map = isObjectOfType('Map');\nis.nan = function (value) {\n    return Number.isNaN(value);\n};\nis.null = function (value) {\n    return value === null;\n};\nis.nullOrUndefined = function (value) {\n    return is.null(value) || is.undefined(value);\n};\nis.number = function (value) {\n    return isOfType('number')(value) && !is.nan(value);\n};\nis.numericString = function (value) {\n    return is.string(value) && value.length > 0 && !Number.isNaN(Number(value));\n};\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.object = function (value) {\n    return !is.nullOrUndefined(value) && (is.function(value) || typeof value === 'object');\n};\nis.oneOf = function (target, value) {\n    if (!is.array(target)) {\n        return false;\n    }\n    // eslint-disable-next-line unicorn/prefer-includes\n    return target.indexOf(value) > -1;\n};\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.plainFunction = isObjectOfType('Function');\nis.plainObject = function (value) {\n    if (getObjectType(value) !== 'Object') {\n        return false;\n    }\n    var prototype = Object.getPrototypeOf(value);\n    return prototype === null || prototype === Object.getPrototypeOf({});\n};\nis.primitive = function (value) {\n    return is.null(value) || isPrimitiveType(typeof value);\n};\nis.promise = isObjectOfType('Promise');\nis.propertyOf = function (target, key, predicate) {\n    if (!is.object(target) || !key) {\n        return false;\n    }\n    var value = target[key];\n    if (is.function(predicate)) {\n        return predicate(value);\n    }\n    return is.defined(value);\n};\nis.regexp = isObjectOfType('RegExp');\nis.set = isObjectOfType('Set');\nis.string = isOfType('string');\nis.symbol = isOfType('symbol');\nis.undefined = isOfType('undefined');\nis.weakMap = isObjectOfType('WeakMap');\nis.weakSet = isObjectOfType('WeakSet');\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (is);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/is-lite@0.8.2/node_modules/is-lite/esm/index.js\n");

/***/ })

};
;