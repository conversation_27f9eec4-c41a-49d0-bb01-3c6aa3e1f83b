"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.6.10/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1.1.0_@types+react@18.3.4_react@18.3.1/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/Popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXBvcHBlckAxLjIuMF9AdHlwZXMrcmVhY3QtZG9tQDE4LjMuMF9AdHlwZXMrcmVhY3RAMTguMy40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1wb3BwZXIvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUI7QUFXaEI7QUFDeUI7QUFDQTtBQUNHO0FBQ1Q7QUFDSztBQUNDO0FBQ1I7QUFrQ3BCO0FBNUJKLElBQU1tQixlQUFlO0lBQUM7SUFBTztJQUFTO0lBQVU7Q0FBTTtBQUN0RCxJQUFNQyxnQkFBZ0I7SUFBQztJQUFTO0lBQVU7Q0FBSztBQVMvQyxJQUFNQyxjQUFjO0FBR3BCLElBQU0sQ0FBQ0MscUJBQXFCQyxrQkFBaUIsR0FBSVYsMkVBQWtCQSxDQUFDUTtBQU1wRSxJQUFNLENBQUNHLGdCQUFnQkMsaUJBQWdCLEdBQUlILG9CQUF3Q0Q7QUFLbkYsSUFBTUssU0FBZ0MsQ0FBQ0M7SUFDckMsTUFBTSxFQUFFQyxhQUFBLEVBQWVDLFFBQUEsRUFBUyxHQUFJRjtJQUNwQyxNQUFNLENBQUNHLFFBQVFDLFVBQVMsR0FBVS9CLDJDQUFBLENBQTRCO0lBQzlELE9BQ0UsZ0JBQUFrQixzREFBQUEsQ0FBQ00sZ0JBQUE7UUFBZVMsT0FBT0w7UUFBZUU7UUFBZ0JJLGdCQUFnQkg7UUFDbkVGO0lBQUE7QUFHUDtBQUVBSCxPQUFPUyxXQUFBLEdBQWNkO0FBTXJCLElBQU1lLGNBQWM7QUFRcEIsSUFBTUMsNkJBQXFCckMsNkNBQUEsQ0FDekIsQ0FBQzJCLE9BQXVDWTtJQUN0QyxNQUFNLEVBQUVYLGFBQUEsRUFBZVksVUFBQSxFQUFZLEdBQUdDLGFBQVksR0FBSWQ7SUFDdEQsTUFBTWUsVUFBVWpCLGlCQUFpQlcsYUFBYVI7SUFDOUMsTUFBTWUsTUFBWTNDLHlDQUFBLENBQTRCO0lBQzlDLE1BQU02QyxlQUFlakMsNkVBQWVBLENBQUMyQixjQUFjSTtJQUU3QzNDLDRDQUFBLENBQVU7UUFJZDBDLFFBQVFSLGNBQUEsQ0FBZU0sWUFBWU8sV0FBV0osSUFBSUksT0FBTztJQUMzRDtJQUVBLE9BQU9QLGFBQWEsT0FBTyxnQkFBQXRCLHNEQUFBQSxDQUFDSixnRUFBU0EsQ0FBQ2tDLEdBQUEsRUFBVjtRQUFlLEdBQUdQLFdBQUE7UUFBYUUsS0FBS0U7SUFBQTtBQUNsRTtBQUdGUixhQUFhRixXQUFBLEdBQWNDO0FBTTNCLElBQU1hLGVBQWU7QUFVckIsSUFBTSxDQUFDQyx1QkFBdUJDLGtCQUFpQixHQUM3QzdCLG9CQUErQzJCO0FBb0JqRCxJQUFNRyw4QkFBc0JwRCw2Q0FBQSxDQUMxQixDQUFDMkIsT0FBd0NZO0lBQ3ZDLE1BQU0sRUFDSlgsYUFBQSxFQUNBeUIsT0FBTyxVQUNQQyxhQUFhLEdBQ2JDLFFBQVEsVUFDUkMsY0FBYyxHQUNkQyxlQUFlLEdBQ2ZDLGtCQUFrQixNQUNsQkMsb0JBQW9CLEVBQUMsRUFDckJDLGtCQUFrQkMsdUJBQXVCLEdBQ3pDQyxTQUFTLFdBQ1RDLG1CQUFtQixPQUNuQkMseUJBQXlCLGFBQ3pCQyxRQUFBLEVBQ0EsR0FBR0MsY0FDTCxHQUFJdkM7SUFFSixNQUFNZSxVQUFVakIsaUJBQWlCd0IsY0FBY3JCO0lBRS9DLE1BQU0sQ0FBQ3VDLFNBQVNDLFdBQVUsR0FBVXBFLDJDQUFBLENBQWdDO0lBQ3BFLE1BQU02QyxlQUFlakMsNkVBQWVBLENBQUMyQixjQUFjLENBQUM4QixPQUFTRCxXQUFXQztJQUV4RSxNQUFNLENBQUM5RCxPQUFPK0QsU0FBUSxHQUFVdEUsMkNBQUEsQ0FBaUM7SUFDakUsTUFBTXVFLFlBQVl0RCxpRUFBT0EsQ0FBQ1Y7SUFDMUIsTUFBTWlFLGFBQWFELFdBQVdFLFNBQVM7SUFDdkMsTUFBTUMsY0FBY0gsV0FBV0ksVUFBVTtJQUV6QyxNQUFNQyxtQkFBb0J2QixPQUFRRSxDQUFBQSxVQUFVLFdBQVcsTUFBTUEsUUFBUTtJQUVyRSxNQUFNSyxtQkFDSixPQUFPQyx5QkFBeUIsV0FDNUJBLHVCQUNBO1FBQUVnQixLQUFLO1FBQUdDLE9BQU87UUFBR0MsUUFBUTtRQUFHQyxNQUFNO1FBQUcsR0FBR25CLG9CQUFBO0lBQXFCO0lBRXRFLE1BQU1vQixXQUFXQyxNQUFNQyxPQUFBLENBQVF4QixxQkFBcUJBLG9CQUFvQjtRQUFDQTtLQUFpQjtJQUMxRixNQUFNeUIsd0JBQXdCSCxTQUFTSSxNQUFBLEdBQVM7SUFFaEQsTUFBTUMsd0JBQXdCO1FBQzVCQyxTQUFTM0I7UUFDVHFCLFVBQVVBLFNBQVNPLE1BQUEsQ0FBT0M7UUFBUztRQUVuQ0MsYUFBYU47SUFDZjtJQUVBLE1BQU0sRUFBRU8sSUFBQSxFQUFNQyxjQUFBLEVBQWdCQyxTQUFBLEVBQVdDLFlBQUEsRUFBY0MsY0FBQSxFQUFlLEdBQUk5RixtRUFBV0EsQ0FBQztRQUFBO1FBRXBGK0YsVUFBVTtRQUNWSCxXQUFXakI7UUFDWHFCLHNCQUFzQixJQUFJQztZQUN4QixNQUFNQyxVQUFVakcsa0VBQVVBLElBQUlnRyxNQUFNO2dCQUNsQ0UsZ0JBQWdCcEMsMkJBQTJCO1lBQzdDO1lBQ0EsT0FBT21DO1FBQ1Q7UUFDQUUsVUFBVTtZQUNSQyxXQUFXNUQsUUFBUVosTUFBQTtRQUNyQjtRQUNBeUUsWUFBWTtZQUNWcEcsOERBQU1BLENBQUM7Z0JBQUVxRyxVQUFVbEQsYUFBYW9CO2dCQUFhK0IsZUFBZWpEO1lBQVk7WUFDeEVFLG1CQUNFdEQsNkRBQUtBLENBQUM7Z0JBQ0pvRyxVQUFVO2dCQUNWRSxXQUFXO2dCQUNYQyxTQUFTN0MsV0FBVyxZQUFZekQsa0VBQVVBLEtBQUs7Z0JBQy9DLEdBQUdpRixxQkFBQTtZQUNMO1lBQ0Y1QixtQkFBbUJqRCw0REFBSUEsQ0FBQztnQkFBRSxHQUFHNkUscUJBQUE7WUFBc0I7WUFDbkQ1RSw0REFBSUEsQ0FBQztnQkFDSCxHQUFHNEUscUJBQUE7Z0JBQ0hzQixPQUFPLENBQUMsRUFBRVAsUUFBQSxFQUFVUSxLQUFBLEVBQU9DLGNBQUEsRUFBZ0JDLGVBQUEsRUFBZ0I7b0JBQ3pELE1BQU0sRUFBRXRDLE9BQU91QyxXQUFBLEVBQWFyQyxRQUFRc0MsWUFBQSxFQUFhLEdBQUlKLE1BQU1QLFNBQUE7b0JBQzNELE1BQU1ZLGVBQWViLFNBQVNjLFFBQUEsQ0FBU0MsS0FBQTtvQkFDdkNGLGFBQWFHLFdBQUEsQ0FBWSxrQ0FBa0MsR0FBR1AsZUFBYyxHQUFJO29CQUNoRkksYUFBYUcsV0FBQSxDQUFZLG1DQUFtQyxHQUFHTixnQkFBZSxHQUFJO29CQUNsRkcsYUFBYUcsV0FBQSxDQUFZLCtCQUErQixHQUFHTCxZQUFXLEdBQUk7b0JBQzFFRSxhQUFhRyxXQUFBLENBQVksZ0NBQWdDLEdBQUdKLGFBQVksR0FBSTtnQkFDOUU7WUFDRjtZQUNBMUcsU0FBU0MsNkRBQWVBLENBQUM7Z0JBQUU4RyxTQUFTL0c7Z0JBQU9nRixTQUFTOUI7WUFBYTtZQUNqRThELGdCQUFnQjtnQkFBRS9DO2dCQUFZRTtZQUFZO1lBQzFDWCxvQkFBb0J6RCw0REFBSUEsQ0FBQztnQkFBRTBGLFVBQVU7Z0JBQW1CLEdBQUdWLHFCQUFBO1lBQXNCO1NBQ25GO0lBQ0Y7SUFFQSxNQUFNLENBQUNrQyxZQUFZQyxZQUFXLEdBQUlDLDZCQUE2QjdCO0lBRS9ELE1BQU04QixlQUFlNUcsZ0ZBQWNBLENBQUNrRDtJQUNwQ2pELGtGQUFlQSxDQUFDO1FBQ2QsSUFBSThFLGNBQWM7WUFDaEI2QjtRQUNGO0lBQ0YsR0FBRztRQUFDN0I7UUFBYzZCO0tBQWE7SUFFL0IsTUFBTUMsU0FBUzdCLGVBQWV4RixLQUFBLEVBQU9zSDtJQUNyQyxNQUFNQyxTQUFTL0IsZUFBZXhGLEtBQUEsRUFBT3dIO0lBQ3JDLE1BQU1DLG9CQUFvQmpDLGVBQWV4RixLQUFBLEVBQU8wSCxpQkFBaUI7SUFFakUsTUFBTSxDQUFDQyxlQUFlQyxpQkFBZ0IsR0FBVW5JLDJDQUFBO0lBQ2hEZ0Isa0ZBQWVBLENBQUM7UUFDZCxJQUFJbUQsU0FBU2dFLGlCQUFpQkMsT0FBT0MsZ0JBQUEsQ0FBaUJsRSxTQUFTbUUsTUFBTTtJQUN2RSxHQUFHO1FBQUNuRTtLQUFRO0lBRVosT0FDRSxnQkFBQWpELHNEQUFBQSxDQUFDO1FBQ0N5QixLQUFLZ0QsS0FBSzRDLFdBQUE7UUFDVixxQ0FBa0M7UUFDbENuQixPQUFPO1lBQ0wsR0FBR3hCLGNBQUE7WUFDSDRDLFdBQVcxQyxlQUFlRixlQUFlNEMsU0FBQSxHQUFZO1lBQUE7WUFDckRDLFVBQVU7WUFDVkgsUUFBUUo7WUFDUixDQUFDLGtDQUF3QyxFQUFHO2dCQUMxQ25DLGVBQWV3QixlQUFBLEVBQWlCTTtnQkFDaEM5QixlQUFld0IsZUFBQSxFQUFpQlE7YUFDbEMsQ0FBRVcsSUFBQSxDQUFLO1lBQUc7WUFBQTtZQUFBO1lBS1YsR0FBSTNDLGVBQWV6RixJQUFBLEVBQU1xSSxtQkFBbUI7Z0JBQzFDQyxZQUFZO2dCQUNaQyxlQUFlO1lBQ2pCO1FBQ0Y7UUFJQUMsS0FBS25ILE1BQU1tSCxHQUFBO1FBRVhqSCxVQUFBLGdCQUFBWCxzREFBQUEsQ0FBQ2dDLHVCQUFBO1lBQ0NqQixPQUFPTDtZQUNQNEY7WUFDQXVCLGVBQWV6RTtZQUNmc0Q7WUFDQUU7WUFDQWtCLGlCQUFpQmhCO1lBRWpCbkcsVUFBQSxnQkFBQVgsc0RBQUFBLENBQUNKLGdFQUFTQSxDQUFDa0MsR0FBQSxFQUFWO2dCQUNDLGFBQVd3RTtnQkFDWCxjQUFZQztnQkFDWCxHQUFHdkQsWUFBQTtnQkFDSnZCLEtBQUtFO2dCQUNMdUUsT0FBTztvQkFDTCxHQUFHbEQsYUFBYWtELEtBQUE7b0JBQUE7b0JBQUE7b0JBR2hCNkIsV0FBVyxDQUFDbkQsZUFBZSxTQUFTO2dCQUN0QztZQUFBO1FBQ0Y7SUFDRjtBQUdOO0FBR0YxQyxjQUFjakIsV0FBQSxHQUFjYztBQU01QixJQUFNaUcsYUFBYTtBQUVuQixJQUFNQyxnQkFBb0M7SUFDeEN0RSxLQUFLO0lBQ0xDLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxNQUFNO0FBQ1I7QUFNQSxJQUFNb0UsNEJBQW9CcEosNkNBQUEsQ0FBaUQsU0FBU29KLGFBQ2xGekgsS0FBQSxFQUNBWSxZQUFBO0lBRUEsTUFBTSxFQUFFWCxhQUFBLEVBQWUsR0FBR3lILFlBQVcsR0FBSTFIO0lBQ3pDLE1BQU0ySCxpQkFBaUJuRyxrQkFBa0IrRixZQUFZdEg7SUFDckQsTUFBTTJILFdBQVdKLGFBQUEsQ0FBY0csZUFBZTlCLFVBQVU7SUFFeEQ7SUFBQTtJQUFBO0lBSUUsZ0JBQUF0RyxzREFBQUEsQ0FBQztRQUNDeUIsS0FBSzJHLGVBQWVQLGFBQUE7UUFDcEIzQixPQUFPO1lBQ0xvQyxVQUFVO1lBQ1Z4RSxNQUFNc0UsZUFBZTFCLE1BQUE7WUFDckIvQyxLQUFLeUUsZUFBZXhCLE1BQUE7WUFDcEIsQ0FBQ3lCLFNBQVEsRUFBRztZQUNaaEMsaUJBQWlCO2dCQUNmMUMsS0FBSztnQkFDTEMsT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsTUFBTTtZQUNSLEVBQUVzRSxlQUFlOUIsVUFBVTtZQUMzQmdCLFdBQVc7Z0JBQ1QzRCxLQUFLO2dCQUNMQyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxNQUFNO1lBQ1IsRUFBRXNFLGVBQWU5QixVQUFVO1lBQzNCb0IsWUFBWVUsZUFBZU4sZUFBQSxHQUFrQixXQUFXO1FBQzFEO1FBRUFuSCxVQUFBLGdCQUFBWCxzREFBQUEsQ0FBZ0JQLHdEQUFBLEVBQWY7WUFDRSxHQUFHMEksVUFBQTtZQUNKMUcsS0FBS0o7WUFDTDZFLE9BQU87Z0JBQ0wsR0FBR2lDLFdBQVdqQyxLQUFBO2dCQUFBO2dCQUVkc0MsU0FBUztZQUNYO1FBQUE7SUFDRjtBQUdOO0FBRUFOLFlBQVlqSCxXQUFBLEdBQWMrRztBQUkxQixTQUFTekQsVUFBYWtFLEtBQUE7SUFDcEIsT0FBT0EsVUFBVTtBQUNuQjtBQUVBLElBQU1wQyxrQkFBa0IsQ0FBQ3FDLFVBQXNFO1FBQzdGQyxNQUFNO1FBQ05EO1FBQ0FFLElBQUdDLElBQUE7WUFDRCxNQUFNLEVBQUVsRSxTQUFBLEVBQVdnQixLQUFBLEVBQU9kLGNBQUEsRUFBZSxHQUFJZ0U7WUFFN0MsTUFBTS9CLG9CQUFvQmpDLGVBQWV4RixLQUFBLEVBQU8wSCxpQkFBaUI7WUFDakUsTUFBTStCLGdCQUFnQmhDO1lBQ3RCLE1BQU14RCxhQUFhd0YsZ0JBQWdCLElBQUlKLFFBQVFwRixVQUFBO1lBQy9DLE1BQU1FLGNBQWNzRixnQkFBZ0IsSUFBSUosUUFBUWxGLFdBQUE7WUFFaEQsTUFBTSxDQUFDOEMsWUFBWUMsWUFBVyxHQUFJQyw2QkFBNkI3QjtZQUMvRCxNQUFNb0UsZUFBZTtnQkFBRUMsT0FBTztnQkFBTUMsUUFBUTtnQkFBT0MsS0FBSztZQUFPLEVBQUUzQyxZQUFXO1lBRTVFLE1BQU00QyxlQUFBLENBQWdCdEUsZUFBZXhGLEtBQUEsRUFBT3NILEtBQUssS0FBS3JELGFBQWE7WUFDbkUsTUFBTThGLGVBQUEsQ0FBZ0J2RSxlQUFleEYsS0FBQSxFQUFPd0gsS0FBSyxLQUFLckQsY0FBYztZQUVwRSxJQUFJbUQsSUFBSTtZQUNSLElBQUlFLElBQUk7WUFFUixJQUFJUCxlQUFlLFVBQVU7Z0JBQzNCSyxJQUFJbUMsZ0JBQWdCQyxlQUFlLEdBQUdJLGFBQVk7Z0JBQ2xEdEMsSUFBSSxHQUFHLENBQUNyRCxZQUFXO1lBQ3JCLFdBQVc4QyxlQUFlLE9BQU87Z0JBQy9CSyxJQUFJbUMsZ0JBQWdCQyxlQUFlLEdBQUdJLGFBQVk7Z0JBQ2xEdEMsSUFBSSxHQUFHbEIsTUFBTU0sUUFBQSxDQUFTeEMsTUFBQSxHQUFTRCxZQUFXO1lBQzVDLFdBQVc4QyxlQUFlLFNBQVM7Z0JBQ2pDSyxJQUFJLEdBQUcsQ0FBQ25ELFlBQVc7Z0JBQ25CcUQsSUFBSWlDLGdCQUFnQkMsZUFBZSxHQUFHSyxhQUFZO1lBQ3BELFdBQVc5QyxlQUFlLFFBQVE7Z0JBQ2hDSyxJQUFJLEdBQUdoQixNQUFNTSxRQUFBLENBQVMxQyxLQUFBLEdBQVFDLFlBQVc7Z0JBQ3pDcUQsSUFBSWlDLGdCQUFnQkMsZUFBZSxHQUFHSyxhQUFZO1lBQ3BEO1lBQ0EsT0FBTztnQkFBRVAsTUFBTTtvQkFBRWxDO29CQUFHRTtnQkFBRTtZQUFFO1FBQzFCO0lBQ0Y7QUFFQSxTQUFTTCw2QkFBNkI3QixTQUFBO0lBQ3BDLE1BQU0sQ0FBQ3hDLE1BQU1FLFFBQVEsUUFBUSxJQUFJc0MsVUFBVTBFLEtBQUEsQ0FBTTtJQUNqRCxPQUFPO1FBQUNsSDtRQUFjRTtLQUFjO0FBQ3RDO0FBRUEsSUFBTWtHLFFBQU8vSDtBQUNiLElBQU04SSxTQUFTbkk7QUFDZixJQUFNb0ksVUFBVXJIO0FBQ2hCLElBQU1zSCxRQUFRdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi4vc3JjL1BvcHBlci50c3g/MmZmYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICB1c2VGbG9hdGluZyxcbiAgYXV0b1VwZGF0ZSxcbiAgb2Zmc2V0LFxuICBzaGlmdCxcbiAgbGltaXRTaGlmdCxcbiAgaGlkZSxcbiAgYXJyb3cgYXMgZmxvYXRpbmdVSWFycm93LFxuICBmbGlwLFxuICBzaXplLFxufSBmcm9tICdAZmxvYXRpbmctdWkvcmVhY3QtZG9tJztcbmltcG9ydCAqIGFzIEFycm93UHJpbWl0aXZlIGZyb20gJ0ByYWRpeC11aS9yZWFjdC1hcnJvdyc7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHRTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZic7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QnO1xuaW1wb3J0IHsgdXNlU2l6ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2Utc2l6ZSc7XG5cbmltcG9ydCB0eXBlIHsgUGxhY2VtZW50LCBNaWRkbGV3YXJlIH0gZnJvbSAnQGZsb2F0aW5nLXVpL3JlYWN0LWRvbSc7XG5pbXBvcnQgdHlwZSB7IFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuaW1wb3J0IHR5cGUgeyBNZWFzdXJhYmxlIH0gZnJvbSAnQHJhZGl4LXVpL3JlY3QnO1xuXG5jb25zdCBTSURFX09QVElPTlMgPSBbJ3RvcCcsICdyaWdodCcsICdib3R0b20nLCAnbGVmdCddIGFzIGNvbnN0O1xuY29uc3QgQUxJR05fT1BUSU9OUyA9IFsnc3RhcnQnLCAnY2VudGVyJywgJ2VuZCddIGFzIGNvbnN0O1xuXG50eXBlIFNpZGUgPSAodHlwZW9mIFNJREVfT1BUSU9OUylbbnVtYmVyXTtcbnR5cGUgQWxpZ24gPSAodHlwZW9mIEFMSUdOX09QVElPTlMpW251bWJlcl07XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcHBlclxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBQT1BQRVJfTkFNRSA9ICdQb3BwZXInO1xuXG50eXBlIFNjb3BlZFByb3BzPFA+ID0gUCAmIHsgX19zY29wZVBvcHBlcj86IFNjb3BlIH07XG5jb25zdCBbY3JlYXRlUG9wcGVyQ29udGV4dCwgY3JlYXRlUG9wcGVyU2NvcGVdID0gY3JlYXRlQ29udGV4dFNjb3BlKFBPUFBFUl9OQU1FKTtcblxudHlwZSBQb3BwZXJDb250ZXh0VmFsdWUgPSB7XG4gIGFuY2hvcjogTWVhc3VyYWJsZSB8IG51bGw7XG4gIG9uQW5jaG9yQ2hhbmdlKGFuY2hvcjogTWVhc3VyYWJsZSB8IG51bGwpOiB2b2lkO1xufTtcbmNvbnN0IFtQb3BwZXJQcm92aWRlciwgdXNlUG9wcGVyQ29udGV4dF0gPSBjcmVhdGVQb3BwZXJDb250ZXh0PFBvcHBlckNvbnRleHRWYWx1ZT4oUE9QUEVSX05BTUUpO1xuXG5pbnRlcmZhY2UgUG9wcGVyUHJvcHMge1xuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cbmNvbnN0IFBvcHBlcjogUmVhY3QuRkM8UG9wcGVyUHJvcHM+ID0gKHByb3BzOiBTY29wZWRQcm9wczxQb3BwZXJQcm9wcz4pID0+IHtcbiAgY29uc3QgeyBfX3Njb3BlUG9wcGVyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIGNvbnN0IFthbmNob3IsIHNldEFuY2hvcl0gPSBSZWFjdC51c2VTdGF0ZTxNZWFzdXJhYmxlIHwgbnVsbD4obnVsbCk7XG4gIHJldHVybiAoXG4gICAgPFBvcHBlclByb3ZpZGVyIHNjb3BlPXtfX3Njb3BlUG9wcGVyfSBhbmNob3I9e2FuY2hvcn0gb25BbmNob3JDaGFuZ2U9e3NldEFuY2hvcn0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Qb3BwZXJQcm92aWRlcj5cbiAgKTtcbn07XG5cblBvcHBlci5kaXNwbGF5TmFtZSA9IFBPUFBFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBQb3BwZXJBbmNob3JcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQU5DSE9SX05BTUUgPSAnUG9wcGVyQW5jaG9yJztcblxudHlwZSBQb3BwZXJBbmNob3JFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG50eXBlIFByaW1pdGl2ZURpdlByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbmludGVyZmFjZSBQb3BwZXJBbmNob3JQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHtcbiAgdmlydHVhbFJlZj86IFJlYWN0LlJlZk9iamVjdDxNZWFzdXJhYmxlPjtcbn1cblxuY29uc3QgUG9wcGVyQW5jaG9yID0gUmVhY3QuZm9yd2FyZFJlZjxQb3BwZXJBbmNob3JFbGVtZW50LCBQb3BwZXJBbmNob3JQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8UG9wcGVyQW5jaG9yUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVQb3BwZXIsIHZpcnR1YWxSZWYsIC4uLmFuY2hvclByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlUG9wcGVyQ29udGV4dChBTkNIT1JfTkFNRSwgX19zY29wZVBvcHBlcik7XG4gICAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPFBvcHBlckFuY2hvckVsZW1lbnQ+KG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZik7XG5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgLy8gQ29uc3VtZXIgY2FuIGFuY2hvciB0aGUgcG9wcGVyIHRvIHNvbWV0aGluZyB0aGF0IGlzbid0XG4gICAgICAvLyBhIERPTSBub2RlIGUuZy4gcG9pbnRlciBwb3NpdGlvbiwgc28gd2Ugb3ZlcnJpZGUgdGhlXG4gICAgICAvLyBgYW5jaG9yUmVmYCB3aXRoIHRoZWlyIHZpcnR1YWwgcmVmIGluIHRoaXMgY2FzZS5cbiAgICAgIGNvbnRleHQub25BbmNob3JDaGFuZ2UodmlydHVhbFJlZj8uY3VycmVudCB8fCByZWYuY3VycmVudCk7XG4gICAgfSk7XG5cbiAgICByZXR1cm4gdmlydHVhbFJlZiA/IG51bGwgOiA8UHJpbWl0aXZlLmRpdiB7Li4uYW5jaG9yUHJvcHN9IHJlZj17Y29tcG9zZWRSZWZzfSAvPjtcbiAgfVxuKTtcblxuUG9wcGVyQW5jaG9yLmRpc3BsYXlOYW1lID0gQU5DSE9SX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcHBlckNvbnRlbnRcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQ09OVEVOVF9OQU1FID0gJ1BvcHBlckNvbnRlbnQnO1xuXG50eXBlIFBvcHBlckNvbnRlbnRDb250ZXh0VmFsdWUgPSB7XG4gIHBsYWNlZFNpZGU6IFNpZGU7XG4gIG9uQXJyb3dDaGFuZ2UoYXJyb3c6IEhUTUxTcGFuRWxlbWVudCB8IG51bGwpOiB2b2lkO1xuICBhcnJvd1g/OiBudW1iZXI7XG4gIGFycm93WT86IG51bWJlcjtcbiAgc2hvdWxkSGlkZUFycm93OiBib29sZWFuO1xufTtcblxuY29uc3QgW1BvcHBlckNvbnRlbnRQcm92aWRlciwgdXNlQ29udGVudENvbnRleHRdID1cbiAgY3JlYXRlUG9wcGVyQ29udGV4dDxQb3BwZXJDb250ZW50Q29udGV4dFZhbHVlPihDT05URU5UX05BTUUpO1xuXG50eXBlIEJvdW5kYXJ5ID0gRWxlbWVudCB8IG51bGw7XG5cbnR5cGUgUG9wcGVyQ29udGVudEVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbmludGVyZmFjZSBQb3BwZXJDb250ZW50UHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVEaXZQcm9wcyB7XG4gIHNpZGU/OiBTaWRlO1xuICBzaWRlT2Zmc2V0PzogbnVtYmVyO1xuICBhbGlnbj86IEFsaWduO1xuICBhbGlnbk9mZnNldD86IG51bWJlcjtcbiAgYXJyb3dQYWRkaW5nPzogbnVtYmVyO1xuICBhdm9pZENvbGxpc2lvbnM/OiBib29sZWFuO1xuICBjb2xsaXNpb25Cb3VuZGFyeT86IEJvdW5kYXJ5IHwgQm91bmRhcnlbXTtcbiAgY29sbGlzaW9uUGFkZGluZz86IG51bWJlciB8IFBhcnRpYWw8UmVjb3JkPFNpZGUsIG51bWJlcj4+O1xuICBzdGlja3k/OiAncGFydGlhbCcgfCAnYWx3YXlzJztcbiAgaGlkZVdoZW5EZXRhY2hlZD86IGJvb2xlYW47XG4gIHVwZGF0ZVBvc2l0aW9uU3RyYXRlZ3k/OiAnb3B0aW1pemVkJyB8ICdhbHdheXMnO1xuICBvblBsYWNlZD86ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IFBvcHBlckNvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcHBlckNvbnRlbnRFbGVtZW50LCBQb3BwZXJDb250ZW50UHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFBvcHBlckNvbnRlbnRQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVQb3BwZXIsXG4gICAgICBzaWRlID0gJ2JvdHRvbScsXG4gICAgICBzaWRlT2Zmc2V0ID0gMCxcbiAgICAgIGFsaWduID0gJ2NlbnRlcicsXG4gICAgICBhbGlnbk9mZnNldCA9IDAsXG4gICAgICBhcnJvd1BhZGRpbmcgPSAwLFxuICAgICAgYXZvaWRDb2xsaXNpb25zID0gdHJ1ZSxcbiAgICAgIGNvbGxpc2lvbkJvdW5kYXJ5ID0gW10sXG4gICAgICBjb2xsaXNpb25QYWRkaW5nOiBjb2xsaXNpb25QYWRkaW5nUHJvcCA9IDAsXG4gICAgICBzdGlja3kgPSAncGFydGlhbCcsXG4gICAgICBoaWRlV2hlbkRldGFjaGVkID0gZmFsc2UsXG4gICAgICB1cGRhdGVQb3NpdGlvblN0cmF0ZWd5ID0gJ29wdGltaXplZCcsXG4gICAgICBvblBsYWNlZCxcbiAgICAgIC4uLmNvbnRlbnRQcm9wc1xuICAgIH0gPSBwcm9wcztcblxuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VQb3BwZXJDb250ZXh0KENPTlRFTlRfTkFNRSwgX19zY29wZVBvcHBlcik7XG5cbiAgICBjb25zdCBbY29udGVudCwgc2V0Q29udGVudF0gPSBSZWFjdC51c2VTdGF0ZTxIVE1MRGl2RWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlKSA9PiBzZXRDb250ZW50KG5vZGUpKTtcblxuICAgIGNvbnN0IFthcnJvdywgc2V0QXJyb3ddID0gUmVhY3QudXNlU3RhdGU8SFRNTFNwYW5FbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgYXJyb3dTaXplID0gdXNlU2l6ZShhcnJvdyk7XG4gICAgY29uc3QgYXJyb3dXaWR0aCA9IGFycm93U2l6ZT8ud2lkdGggPz8gMDtcbiAgICBjb25zdCBhcnJvd0hlaWdodCA9IGFycm93U2l6ZT8uaGVpZ2h0ID8/IDA7XG5cbiAgICBjb25zdCBkZXNpcmVkUGxhY2VtZW50ID0gKHNpZGUgKyAoYWxpZ24gIT09ICdjZW50ZXInID8gJy0nICsgYWxpZ24gOiAnJykpIGFzIFBsYWNlbWVudDtcblxuICAgIGNvbnN0IGNvbGxpc2lvblBhZGRpbmcgPVxuICAgICAgdHlwZW9mIGNvbGxpc2lvblBhZGRpbmdQcm9wID09PSAnbnVtYmVyJ1xuICAgICAgICA/IGNvbGxpc2lvblBhZGRpbmdQcm9wXG4gICAgICAgIDogeyB0b3A6IDAsIHJpZ2h0OiAwLCBib3R0b206IDAsIGxlZnQ6IDAsIC4uLmNvbGxpc2lvblBhZGRpbmdQcm9wIH07XG5cbiAgICBjb25zdCBib3VuZGFyeSA9IEFycmF5LmlzQXJyYXkoY29sbGlzaW9uQm91bmRhcnkpID8gY29sbGlzaW9uQm91bmRhcnkgOiBbY29sbGlzaW9uQm91bmRhcnldO1xuICAgIGNvbnN0IGhhc0V4cGxpY2l0Qm91bmRhcmllcyA9IGJvdW5kYXJ5Lmxlbmd0aCA+IDA7XG5cbiAgICBjb25zdCBkZXRlY3RPdmVyZmxvd09wdGlvbnMgPSB7XG4gICAgICBwYWRkaW5nOiBjb2xsaXNpb25QYWRkaW5nLFxuICAgICAgYm91bmRhcnk6IGJvdW5kYXJ5LmZpbHRlcihpc05vdE51bGwpLFxuICAgICAgLy8gd2l0aCBgc3RyYXRlZ3k6ICdmaXhlZCdgLCB0aGlzIGlzIHRoZSBvbmx5IHdheSB0byBnZXQgaXQgdG8gcmVzcGVjdCBib3VuZGFyaWVzXG4gICAgICBhbHRCb3VuZGFyeTogaGFzRXhwbGljaXRCb3VuZGFyaWVzLFxuICAgIH07XG5cbiAgICBjb25zdCB7IHJlZnMsIGZsb2F0aW5nU3R5bGVzLCBwbGFjZW1lbnQsIGlzUG9zaXRpb25lZCwgbWlkZGxld2FyZURhdGEgfSA9IHVzZUZsb2F0aW5nKHtcbiAgICAgIC8vIGRlZmF1bHQgdG8gYGZpeGVkYCBzdHJhdGVneSBzbyB1c2VycyBkb24ndCBoYXZlIHRvIHBpY2sgYW5kIHdlIGFsc28gYXZvaWQgZm9jdXMgc2Nyb2xsIGlzc3Vlc1xuICAgICAgc3RyYXRlZ3k6ICdmaXhlZCcsXG4gICAgICBwbGFjZW1lbnQ6IGRlc2lyZWRQbGFjZW1lbnQsXG4gICAgICB3aGlsZUVsZW1lbnRzTW91bnRlZDogKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgY29uc3QgY2xlYW51cCA9IGF1dG9VcGRhdGUoLi4uYXJncywge1xuICAgICAgICAgIGFuaW1hdGlvbkZyYW1lOiB1cGRhdGVQb3NpdGlvblN0cmF0ZWd5ID09PSAnYWx3YXlzJyxcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBjbGVhbnVwO1xuICAgICAgfSxcbiAgICAgIGVsZW1lbnRzOiB7XG4gICAgICAgIHJlZmVyZW5jZTogY29udGV4dC5hbmNob3IsXG4gICAgICB9LFxuICAgICAgbWlkZGxld2FyZTogW1xuICAgICAgICBvZmZzZXQoeyBtYWluQXhpczogc2lkZU9mZnNldCArIGFycm93SGVpZ2h0LCBhbGlnbm1lbnRBeGlzOiBhbGlnbk9mZnNldCB9KSxcbiAgICAgICAgYXZvaWRDb2xsaXNpb25zICYmXG4gICAgICAgICAgc2hpZnQoe1xuICAgICAgICAgICAgbWFpbkF4aXM6IHRydWUsXG4gICAgICAgICAgICBjcm9zc0F4aXM6IGZhbHNlLFxuICAgICAgICAgICAgbGltaXRlcjogc3RpY2t5ID09PSAncGFydGlhbCcgPyBsaW1pdFNoaWZ0KCkgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMsXG4gICAgICAgICAgfSksXG4gICAgICAgIGF2b2lkQ29sbGlzaW9ucyAmJiBmbGlwKHsgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zIH0pLFxuICAgICAgICBzaXplKHtcbiAgICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMsXG4gICAgICAgICAgYXBwbHk6ICh7IGVsZW1lbnRzLCByZWN0cywgYXZhaWxhYmxlV2lkdGgsIGF2YWlsYWJsZUhlaWdodCB9KSA9PiB7XG4gICAgICAgICAgICBjb25zdCB7IHdpZHRoOiBhbmNob3JXaWR0aCwgaGVpZ2h0OiBhbmNob3JIZWlnaHQgfSA9IHJlY3RzLnJlZmVyZW5jZTtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRTdHlsZSA9IGVsZW1lbnRzLmZsb2F0aW5nLnN0eWxlO1xuICAgICAgICAgICAgY29udGVudFN0eWxlLnNldFByb3BlcnR5KCctLXJhZGl4LXBvcHBlci1hdmFpbGFibGUtd2lkdGgnLCBgJHthdmFpbGFibGVXaWR0aH1weGApO1xuICAgICAgICAgICAgY29udGVudFN0eWxlLnNldFByb3BlcnR5KCctLXJhZGl4LXBvcHBlci1hdmFpbGFibGUtaGVpZ2h0JywgYCR7YXZhaWxhYmxlSGVpZ2h0fXB4YCk7XG4gICAgICAgICAgICBjb250ZW50U3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtcG9wcGVyLWFuY2hvci13aWR0aCcsIGAke2FuY2hvcldpZHRofXB4YCk7XG4gICAgICAgICAgICBjb250ZW50U3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtcG9wcGVyLWFuY2hvci1oZWlnaHQnLCBgJHthbmNob3JIZWlnaHR9cHhgKTtcbiAgICAgICAgICB9LFxuICAgICAgICB9KSxcbiAgICAgICAgYXJyb3cgJiYgZmxvYXRpbmdVSWFycm93KHsgZWxlbWVudDogYXJyb3csIHBhZGRpbmc6IGFycm93UGFkZGluZyB9KSxcbiAgICAgICAgdHJhbnNmb3JtT3JpZ2luKHsgYXJyb3dXaWR0aCwgYXJyb3dIZWlnaHQgfSksXG4gICAgICAgIGhpZGVXaGVuRGV0YWNoZWQgJiYgaGlkZSh7IHN0cmF0ZWd5OiAncmVmZXJlbmNlSGlkZGVuJywgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zIH0pLFxuICAgICAgXSxcbiAgICB9KTtcblxuICAgIGNvbnN0IFtwbGFjZWRTaWRlLCBwbGFjZWRBbGlnbl0gPSBnZXRTaWRlQW5kQWxpZ25Gcm9tUGxhY2VtZW50KHBsYWNlbWVudCk7XG5cbiAgICBjb25zdCBoYW5kbGVQbGFjZWQgPSB1c2VDYWxsYmFja1JlZihvblBsYWNlZCk7XG4gICAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChpc1Bvc2l0aW9uZWQpIHtcbiAgICAgICAgaGFuZGxlUGxhY2VkPy4oKTtcbiAgICAgIH1cbiAgICB9LCBbaXNQb3NpdGlvbmVkLCBoYW5kbGVQbGFjZWRdKTtcblxuICAgIGNvbnN0IGFycm93WCA9IG1pZGRsZXdhcmVEYXRhLmFycm93Py54O1xuICAgIGNvbnN0IGFycm93WSA9IG1pZGRsZXdhcmVEYXRhLmFycm93Py55O1xuICAgIGNvbnN0IGNhbm5vdENlbnRlckFycm93ID0gbWlkZGxld2FyZURhdGEuYXJyb3c/LmNlbnRlck9mZnNldCAhPT0gMDtcblxuICAgIGNvbnN0IFtjb250ZW50WkluZGV4LCBzZXRDb250ZW50WkluZGV4XSA9IFJlYWN0LnVzZVN0YXRlPHN0cmluZz4oKTtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKGNvbnRlbnQpIHNldENvbnRlbnRaSW5kZXgod2luZG93LmdldENvbXB1dGVkU3R5bGUoY29udGVudCkuekluZGV4KTtcbiAgICB9LCBbY29udGVudF0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXZcbiAgICAgICAgcmVmPXtyZWZzLnNldEZsb2F0aW5nfVxuICAgICAgICBkYXRhLXJhZGl4LXBvcHBlci1jb250ZW50LXdyYXBwZXI9XCJcIlxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIC4uLmZsb2F0aW5nU3R5bGVzLFxuICAgICAgICAgIHRyYW5zZm9ybTogaXNQb3NpdGlvbmVkID8gZmxvYXRpbmdTdHlsZXMudHJhbnNmb3JtIDogJ3RyYW5zbGF0ZSgwLCAtMjAwJSknLCAvLyBrZWVwIG9mZiB0aGUgcGFnZSB3aGVuIG1lYXN1cmluZ1xuICAgICAgICAgIG1pbldpZHRoOiAnbWF4LWNvbnRlbnQnLFxuICAgICAgICAgIHpJbmRleDogY29udGVudFpJbmRleCxcbiAgICAgICAgICBbJy0tcmFkaXgtcG9wcGVyLXRyYW5zZm9ybS1vcmlnaW4nIGFzIGFueV06IFtcbiAgICAgICAgICAgIG1pZGRsZXdhcmVEYXRhLnRyYW5zZm9ybU9yaWdpbj8ueCxcbiAgICAgICAgICAgIG1pZGRsZXdhcmVEYXRhLnRyYW5zZm9ybU9yaWdpbj8ueSxcbiAgICAgICAgICBdLmpvaW4oJyAnKSxcblxuICAgICAgICAgIC8vIGhpZGUgdGhlIGNvbnRlbnQgaWYgdXNpbmcgdGhlIGhpZGUgbWlkZGxld2FyZSBhbmQgc2hvdWxkIGJlIGhpZGRlblxuICAgICAgICAgIC8vIHNldCB2aXNpYmlsaXR5IHRvIGhpZGRlbiBhbmQgZGlzYWJsZSBwb2ludGVyIGV2ZW50cyBzbyB0aGUgVUkgYmVoYXZlc1xuICAgICAgICAgIC8vIGFzIGlmIHRoZSBQb3BwZXJDb250ZW50IGlzbid0IHRoZXJlIGF0IGFsbFxuICAgICAgICAgIC4uLihtaWRkbGV3YXJlRGF0YS5oaWRlPy5yZWZlcmVuY2VIaWRkZW4gJiYge1xuICAgICAgICAgICAgdmlzaWJpbGl0eTogJ2hpZGRlbicsXG4gICAgICAgICAgICBwb2ludGVyRXZlbnRzOiAnbm9uZScsXG4gICAgICAgICAgfSksXG4gICAgICAgIH19XG4gICAgICAgIC8vIEZsb2F0aW5nIFVJIGludGVyYWxseSBjYWxjdWxhdGVzIGxvZ2ljYWwgYWxpZ25tZW50IGJhc2VkIHRoZSBgZGlyYCBhdHRyaWJ1dGUgb25cbiAgICAgICAgLy8gdGhlIHJlZmVyZW5jZS9mbG9hdGluZyBub2RlLCB3ZSBtdXN0IGFkZCB0aGlzIGF0dHJpYnV0ZSBoZXJlIHRvIGVuc3VyZVxuICAgICAgICAvLyB0aGlzIGlzIGNhbGN1bGF0ZWQgd2hlbiBwb3J0YWxsZWQgYXMgd2VsbCBhcyBpbmxpbmUuXG4gICAgICAgIGRpcj17cHJvcHMuZGlyfVxuICAgICAgPlxuICAgICAgICA8UG9wcGVyQ29udGVudFByb3ZpZGVyXG4gICAgICAgICAgc2NvcGU9e19fc2NvcGVQb3BwZXJ9XG4gICAgICAgICAgcGxhY2VkU2lkZT17cGxhY2VkU2lkZX1cbiAgICAgICAgICBvbkFycm93Q2hhbmdlPXtzZXRBcnJvd31cbiAgICAgICAgICBhcnJvd1g9e2Fycm93WH1cbiAgICAgICAgICBhcnJvd1k9e2Fycm93WX1cbiAgICAgICAgICBzaG91bGRIaWRlQXJyb3c9e2Nhbm5vdENlbnRlckFycm93fVxuICAgICAgICA+XG4gICAgICAgICAgPFByaW1pdGl2ZS5kaXZcbiAgICAgICAgICAgIGRhdGEtc2lkZT17cGxhY2VkU2lkZX1cbiAgICAgICAgICAgIGRhdGEtYWxpZ249e3BsYWNlZEFsaWdufVxuICAgICAgICAgICAgey4uLmNvbnRlbnRQcm9wc31cbiAgICAgICAgICAgIHJlZj17Y29tcG9zZWRSZWZzfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgLi4uY29udGVudFByb3BzLnN0eWxlLFxuICAgICAgICAgICAgICAvLyBpZiB0aGUgUG9wcGVyQ29udGVudCBoYXNuJ3QgYmVlbiBwbGFjZWQgeWV0IChub3QgYWxsIG1lYXN1cmVtZW50cyBkb25lKVxuICAgICAgICAgICAgICAvLyB3ZSBwcmV2ZW50IGFuaW1hdGlvbnMgc28gdGhhdCB1c2VycydzIGFuaW1hdGlvbiBkb24ndCBraWNrIGluIHRvbyBlYXJseSByZWZlcnJpbmcgd3Jvbmcgc2lkZXNcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiAhaXNQb3NpdGlvbmVkID8gJ25vbmUnIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L1BvcHBlckNvbnRlbnRQcm92aWRlcj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cbik7XG5cblBvcHBlckNvbnRlbnQuZGlzcGxheU5hbWUgPSBDT05URU5UX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcHBlckFycm93XG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEFSUk9XX05BTUUgPSAnUG9wcGVyQXJyb3cnO1xuXG5jb25zdCBPUFBPU0lURV9TSURFOiBSZWNvcmQ8U2lkZSwgU2lkZT4gPSB7XG4gIHRvcDogJ2JvdHRvbScsXG4gIHJpZ2h0OiAnbGVmdCcsXG4gIGJvdHRvbTogJ3RvcCcsXG4gIGxlZnQ6ICdyaWdodCcsXG59O1xuXG50eXBlIFBvcHBlckFycm93RWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIEFycm93UHJpbWl0aXZlLlJvb3Q+O1xudHlwZSBBcnJvd1Byb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBBcnJvd1ByaW1pdGl2ZS5Sb290PjtcbmludGVyZmFjZSBQb3BwZXJBcnJvd1Byb3BzIGV4dGVuZHMgQXJyb3dQcm9wcyB7fVxuXG5jb25zdCBQb3BwZXJBcnJvdyA9IFJlYWN0LmZvcndhcmRSZWY8UG9wcGVyQXJyb3dFbGVtZW50LCBQb3BwZXJBcnJvd1Byb3BzPihmdW5jdGlvbiBQb3BwZXJBcnJvdyhcbiAgcHJvcHM6IFNjb3BlZFByb3BzPFBvcHBlckFycm93UHJvcHM+LFxuICBmb3J3YXJkZWRSZWZcbikge1xuICBjb25zdCB7IF9fc2NvcGVQb3BwZXIsIC4uLmFycm93UHJvcHMgfSA9IHByb3BzO1xuICBjb25zdCBjb250ZW50Q29udGV4dCA9IHVzZUNvbnRlbnRDb250ZXh0KEFSUk9XX05BTUUsIF9fc2NvcGVQb3BwZXIpO1xuICBjb25zdCBiYXNlU2lkZSA9IE9QUE9TSVRFX1NJREVbY29udGVudENvbnRleHQucGxhY2VkU2lkZV07XG5cbiAgcmV0dXJuIChcbiAgICAvLyB3ZSBoYXZlIHRvIHVzZSBhbiBleHRyYSB3cmFwcGVyIGJlY2F1c2UgYFJlc2l6ZU9ic2VydmVyYCAodXNlZCBieSBgdXNlU2l6ZWApXG4gICAgLy8gZG9lc24ndCByZXBvcnQgc2l6ZSBhcyB3ZSdkIGV4cGVjdCBvbiBTVkcgZWxlbWVudHMuXG4gICAgLy8gaXQgcmVwb3J0cyB0aGVpciBib3VuZGluZyBib3ggd2hpY2ggaXMgZWZmZWN0aXZlbHkgdGhlIGxhcmdlc3QgcGF0aCBpbnNpZGUgdGhlIFNWRy5cbiAgICA8c3BhblxuICAgICAgcmVmPXtjb250ZW50Q29udGV4dC5vbkFycm93Q2hhbmdlfVxuICAgICAgc3R5bGU9e3tcbiAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgIGxlZnQ6IGNvbnRlbnRDb250ZXh0LmFycm93WCxcbiAgICAgICAgdG9wOiBjb250ZW50Q29udGV4dC5hcnJvd1ksXG4gICAgICAgIFtiYXNlU2lkZV06IDAsXG4gICAgICAgIHRyYW5zZm9ybU9yaWdpbjoge1xuICAgICAgICAgIHRvcDogJycsXG4gICAgICAgICAgcmlnaHQ6ICcwIDAnLFxuICAgICAgICAgIGJvdHRvbTogJ2NlbnRlciAwJyxcbiAgICAgICAgICBsZWZ0OiAnMTAwJSAwJyxcbiAgICAgICAgfVtjb250ZW50Q29udGV4dC5wbGFjZWRTaWRlXSxcbiAgICAgICAgdHJhbnNmb3JtOiB7XG4gICAgICAgICAgdG9wOiAndHJhbnNsYXRlWSgxMDAlKScsXG4gICAgICAgICAgcmlnaHQ6ICd0cmFuc2xhdGVZKDUwJSkgcm90YXRlKDkwZGVnKSB0cmFuc2xhdGVYKC01MCUpJyxcbiAgICAgICAgICBib3R0b206IGByb3RhdGUoMTgwZGVnKWAsXG4gICAgICAgICAgbGVmdDogJ3RyYW5zbGF0ZVkoNTAlKSByb3RhdGUoLTkwZGVnKSB0cmFuc2xhdGVYKDUwJSknLFxuICAgICAgICB9W2NvbnRlbnRDb250ZXh0LnBsYWNlZFNpZGVdLFxuICAgICAgICB2aXNpYmlsaXR5OiBjb250ZW50Q29udGV4dC5zaG91bGRIaWRlQXJyb3cgPyAnaGlkZGVuJyA6IHVuZGVmaW5lZCxcbiAgICAgIH19XG4gICAgPlxuICAgICAgPEFycm93UHJpbWl0aXZlLlJvb3RcbiAgICAgICAgey4uLmFycm93UHJvcHN9XG4gICAgICAgIHJlZj17Zm9yd2FyZGVkUmVmfVxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIC4uLmFycm93UHJvcHMuc3R5bGUsXG4gICAgICAgICAgLy8gZW5zdXJlcyB0aGUgZWxlbWVudCBjYW4gYmUgbWVhc3VyZWQgY29ycmVjdGx5IChtb3N0bHkgZm9yIGlmIFNWRylcbiAgICAgICAgICBkaXNwbGF5OiAnYmxvY2snLFxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICA8L3NwYW4+XG4gICk7XG59KTtcblxuUG9wcGVyQXJyb3cuZGlzcGxheU5hbWUgPSBBUlJPV19OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmZ1bmN0aW9uIGlzTm90TnVsbDxUPih2YWx1ZTogVCB8IG51bGwpOiB2YWx1ZSBpcyBUIHtcbiAgcmV0dXJuIHZhbHVlICE9PSBudWxsO1xufVxuXG5jb25zdCB0cmFuc2Zvcm1PcmlnaW4gPSAob3B0aW9uczogeyBhcnJvd1dpZHRoOiBudW1iZXI7IGFycm93SGVpZ2h0OiBudW1iZXIgfSk6IE1pZGRsZXdhcmUgPT4gKHtcbiAgbmFtZTogJ3RyYW5zZm9ybU9yaWdpbicsXG4gIG9wdGlvbnMsXG4gIGZuKGRhdGEpIHtcbiAgICBjb25zdCB7IHBsYWNlbWVudCwgcmVjdHMsIG1pZGRsZXdhcmVEYXRhIH0gPSBkYXRhO1xuXG4gICAgY29uc3QgY2Fubm90Q2VudGVyQXJyb3cgPSBtaWRkbGV3YXJlRGF0YS5hcnJvdz8uY2VudGVyT2Zmc2V0ICE9PSAwO1xuICAgIGNvbnN0IGlzQXJyb3dIaWRkZW4gPSBjYW5ub3RDZW50ZXJBcnJvdztcbiAgICBjb25zdCBhcnJvd1dpZHRoID0gaXNBcnJvd0hpZGRlbiA/IDAgOiBvcHRpb25zLmFycm93V2lkdGg7XG4gICAgY29uc3QgYXJyb3dIZWlnaHQgPSBpc0Fycm93SGlkZGVuID8gMCA6IG9wdGlvbnMuYXJyb3dIZWlnaHQ7XG5cbiAgICBjb25zdCBbcGxhY2VkU2lkZSwgcGxhY2VkQWxpZ25dID0gZ2V0U2lkZUFuZEFsaWduRnJvbVBsYWNlbWVudChwbGFjZW1lbnQpO1xuICAgIGNvbnN0IG5vQXJyb3dBbGlnbiA9IHsgc3RhcnQ6ICcwJScsIGNlbnRlcjogJzUwJScsIGVuZDogJzEwMCUnIH1bcGxhY2VkQWxpZ25dO1xuXG4gICAgY29uc3QgYXJyb3dYQ2VudGVyID0gKG1pZGRsZXdhcmVEYXRhLmFycm93Py54ID8/IDApICsgYXJyb3dXaWR0aCAvIDI7XG4gICAgY29uc3QgYXJyb3dZQ2VudGVyID0gKG1pZGRsZXdhcmVEYXRhLmFycm93Py55ID8/IDApICsgYXJyb3dIZWlnaHQgLyAyO1xuXG4gICAgbGV0IHggPSAnJztcbiAgICBsZXQgeSA9ICcnO1xuXG4gICAgaWYgKHBsYWNlZFNpZGUgPT09ICdib3R0b20nKSB7XG4gICAgICB4ID0gaXNBcnJvd0hpZGRlbiA/IG5vQXJyb3dBbGlnbiA6IGAke2Fycm93WENlbnRlcn1weGA7XG4gICAgICB5ID0gYCR7LWFycm93SGVpZ2h0fXB4YDtcbiAgICB9IGVsc2UgaWYgKHBsYWNlZFNpZGUgPT09ICd0b3AnKSB7XG4gICAgICB4ID0gaXNBcnJvd0hpZGRlbiA/IG5vQXJyb3dBbGlnbiA6IGAke2Fycm93WENlbnRlcn1weGA7XG4gICAgICB5ID0gYCR7cmVjdHMuZmxvYXRpbmcuaGVpZ2h0ICsgYXJyb3dIZWlnaHR9cHhgO1xuICAgIH0gZWxzZSBpZiAocGxhY2VkU2lkZSA9PT0gJ3JpZ2h0Jykge1xuICAgICAgeCA9IGAkey1hcnJvd0hlaWdodH1weGA7XG4gICAgICB5ID0gaXNBcnJvd0hpZGRlbiA/IG5vQXJyb3dBbGlnbiA6IGAke2Fycm93WUNlbnRlcn1weGA7XG4gICAgfSBlbHNlIGlmIChwbGFjZWRTaWRlID09PSAnbGVmdCcpIHtcbiAgICAgIHggPSBgJHtyZWN0cy5mbG9hdGluZy53aWR0aCArIGFycm93SGVpZ2h0fXB4YDtcbiAgICAgIHkgPSBpc0Fycm93SGlkZGVuID8gbm9BcnJvd0FsaWduIDogYCR7YXJyb3dZQ2VudGVyfXB4YDtcbiAgICB9XG4gICAgcmV0dXJuIHsgZGF0YTogeyB4LCB5IH0gfTtcbiAgfSxcbn0pO1xuXG5mdW5jdGlvbiBnZXRTaWRlQW5kQWxpZ25Gcm9tUGxhY2VtZW50KHBsYWNlbWVudDogUGxhY2VtZW50KSB7XG4gIGNvbnN0IFtzaWRlLCBhbGlnbiA9ICdjZW50ZXInXSA9IHBsYWNlbWVudC5zcGxpdCgnLScpO1xuICByZXR1cm4gW3NpZGUgYXMgU2lkZSwgYWxpZ24gYXMgQWxpZ25dIGFzIGNvbnN0O1xufVxuXG5jb25zdCBSb290ID0gUG9wcGVyO1xuY29uc3QgQW5jaG9yID0gUG9wcGVyQW5jaG9yO1xuY29uc3QgQ29udGVudCA9IFBvcHBlckNvbnRlbnQ7XG5jb25zdCBBcnJvdyA9IFBvcHBlckFycm93O1xuXG5leHBvcnQge1xuICBjcmVhdGVQb3BwZXJTY29wZSxcbiAgLy9cbiAgUG9wcGVyLFxuICBQb3BwZXJBbmNob3IsXG4gIFBvcHBlckNvbnRlbnQsXG4gIFBvcHBlckFycm93LFxuICAvL1xuICBSb290LFxuICBBbmNob3IsXG4gIENvbnRlbnQsXG4gIEFycm93LFxuICAvL1xuICBTSURFX09QVElPTlMsXG4gIEFMSUdOX09QVElPTlMsXG59O1xuZXhwb3J0IHR5cGUgeyBQb3BwZXJQcm9wcywgUG9wcGVyQW5jaG9yUHJvcHMsIFBvcHBlckNvbnRlbnRQcm9wcywgUG9wcGVyQXJyb3dQcm9wcyB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRmxvYXRpbmciLCJhdXRvVXBkYXRlIiwib2Zmc2V0Iiwic2hpZnQiLCJsaW1pdFNoaWZ0IiwiaGlkZSIsImFycm93IiwiZmxvYXRpbmdVSWFycm93IiwiZmxpcCIsInNpemUiLCJBcnJvd1ByaW1pdGl2ZSIsInVzZUNvbXBvc2VkUmVmcyIsImNyZWF0ZUNvbnRleHRTY29wZSIsIlByaW1pdGl2ZSIsInVzZUNhbGxiYWNrUmVmIiwidXNlTGF5b3V0RWZmZWN0IiwidXNlU2l6ZSIsImpzeCIsIlNJREVfT1BUSU9OUyIsIkFMSUdOX09QVElPTlMiLCJQT1BQRVJfTkFNRSIsImNyZWF0ZVBvcHBlckNvbnRleHQiLCJjcmVhdGVQb3BwZXJTY29wZSIsIlBvcHBlclByb3ZpZGVyIiwidXNlUG9wcGVyQ29udGV4dCIsIlBvcHBlciIsInByb3BzIiwiX19zY29wZVBvcHBlciIsImNoaWxkcmVuIiwiYW5jaG9yIiwic2V0QW5jaG9yIiwidXNlU3RhdGUiLCJzY29wZSIsIm9uQW5jaG9yQ2hhbmdlIiwiZGlzcGxheU5hbWUiLCJBTkNIT1JfTkFNRSIsIlBvcHBlckFuY2hvciIsImZvcndhcmRSZWYiLCJmb3J3YXJkZWRSZWYiLCJ2aXJ0dWFsUmVmIiwiYW5jaG9yUHJvcHMiLCJjb250ZXh0IiwicmVmIiwidXNlUmVmIiwiY29tcG9zZWRSZWZzIiwidXNlRWZmZWN0IiwiY3VycmVudCIsImRpdiIsIkNPTlRFTlRfTkFNRSIsIlBvcHBlckNvbnRlbnRQcm92aWRlciIsInVzZUNvbnRlbnRDb250ZXh0IiwiUG9wcGVyQ29udGVudCIsInNpZGUiLCJzaWRlT2Zmc2V0IiwiYWxpZ24iLCJhbGlnbk9mZnNldCIsImFycm93UGFkZGluZyIsImF2b2lkQ29sbGlzaW9ucyIsImNvbGxpc2lvbkJvdW5kYXJ5IiwiY29sbGlzaW9uUGFkZGluZyIsImNvbGxpc2lvblBhZGRpbmdQcm9wIiwic3RpY2t5IiwiaGlkZVdoZW5EZXRhY2hlZCIsInVwZGF0ZVBvc2l0aW9uU3RyYXRlZ3kiLCJvblBsYWNlZCIsImNvbnRlbnRQcm9wcyIsImNvbnRlbnQiLCJzZXRDb250ZW50Iiwibm9kZSIsInNldEFycm93IiwiYXJyb3dTaXplIiwiYXJyb3dXaWR0aCIsIndpZHRoIiwiYXJyb3dIZWlnaHQiLCJoZWlnaHQiLCJkZXNpcmVkUGxhY2VtZW50IiwidG9wIiwicmlnaHQiLCJib3R0b20iLCJsZWZ0IiwiYm91bmRhcnkiLCJBcnJheSIsImlzQXJyYXkiLCJoYXNFeHBsaWNpdEJvdW5kYXJpZXMiLCJsZW5ndGgiLCJkZXRlY3RPdmVyZmxvd09wdGlvbnMiLCJwYWRkaW5nIiwiZmlsdGVyIiwiaXNOb3ROdWxsIiwiYWx0Qm91bmRhcnkiLCJyZWZzIiwiZmxvYXRpbmdTdHlsZXMiLCJwbGFjZW1lbnQiLCJpc1Bvc2l0aW9uZWQiLCJtaWRkbGV3YXJlRGF0YSIsInN0cmF0ZWd5Iiwid2hpbGVFbGVtZW50c01vdW50ZWQiLCJhcmdzIiwiY2xlYW51cCIsImFuaW1hdGlvbkZyYW1lIiwiZWxlbWVudHMiLCJyZWZlcmVuY2UiLCJtaWRkbGV3YXJlIiwibWFpbkF4aXMiLCJhbGlnbm1lbnRBeGlzIiwiY3Jvc3NBeGlzIiwibGltaXRlciIsImFwcGx5IiwicmVjdHMiLCJhdmFpbGFibGVXaWR0aCIsImF2YWlsYWJsZUhlaWdodCIsImFuY2hvcldpZHRoIiwiYW5jaG9ySGVpZ2h0IiwiY29udGVudFN0eWxlIiwiZmxvYXRpbmciLCJzdHlsZSIsInNldFByb3BlcnR5IiwiZWxlbWVudCIsInRyYW5zZm9ybU9yaWdpbiIsInBsYWNlZFNpZGUiLCJwbGFjZWRBbGlnbiIsImdldFNpZGVBbmRBbGlnbkZyb21QbGFjZW1lbnQiLCJoYW5kbGVQbGFjZWQiLCJhcnJvd1giLCJ4IiwiYXJyb3dZIiwieSIsImNhbm5vdENlbnRlckFycm93IiwiY2VudGVyT2Zmc2V0IiwiY29udGVudFpJbmRleCIsInNldENvbnRlbnRaSW5kZXgiLCJ3aW5kb3ciLCJnZXRDb21wdXRlZFN0eWxlIiwiekluZGV4Iiwic2V0RmxvYXRpbmciLCJ0cmFuc2Zvcm0iLCJtaW5XaWR0aCIsImpvaW4iLCJyZWZlcmVuY2VIaWRkZW4iLCJ2aXNpYmlsaXR5IiwicG9pbnRlckV2ZW50cyIsImRpciIsIm9uQXJyb3dDaGFuZ2UiLCJzaG91bGRIaWRlQXJyb3ciLCJhbmltYXRpb24iLCJBUlJPV19OQU1FIiwiT1BQT1NJVEVfU0lERSIsIlBvcHBlckFycm93IiwiYXJyb3dQcm9wcyIsImNvbnRlbnRDb250ZXh0IiwiYmFzZVNpZGUiLCJwb3NpdGlvbiIsIlJvb3QiLCJkaXNwbGF5IiwidmFsdWUiLCJvcHRpb25zIiwibmFtZSIsImZuIiwiZGF0YSIsImlzQXJyb3dIaWRkZW4iLCJub0Fycm93QWxpZ24iLCJzdGFydCIsImNlbnRlciIsImVuZCIsImFycm93WENlbnRlciIsImFycm93WUNlbnRlciIsInNwbGl0IiwiQW5jaG9yIiwiQ29udGVudCIsIkFycm93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@18.3.0_@types+react@18.3.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;