"use client";
import React, { createContext, useContext, useState, useEffect } from "react";
import Joyride, { Step, CallBackProps, STATUS } from "react-joyride";

interface OnboardingContextType {
  startOnboarding: (page: string) => void;
  stopOnboarding: () => void;
  isOnboardingActive: boolean;
  currentPage: string | null;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
};

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({
  children,
}) => {
  const [isOnboardingActive, setIsOnboardingActive] = useState(false);
  const [currentPage, setCurrentPage] = useState<string | null>(null);
  const [steps, setSteps] = useState<Step[]>([]);

  // Check if user has completed onboarding
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem("onboarding-completed");
    if (hasCompletedOnboarding) {
      setIsOnboardingActive(false);
    }
  }, []);

  const homePageSteps: Step[] = [
    {
      target: ".sidebar",
      content:
        "Welcome to CSS CMS! This is your sidebar where you can organize your projects into folders.",
      placement: "right",
      disableBeacon: true,
    },
    {
      target: ".search-bar",
      content:
        "Use the search bar to quickly find your projects by name or tags.",
      placement: "bottom",
    },
    {
      target: ".filter-button",
      content:
        "Click here to filter your projects by categories like materials, cuts, and metals.",
      placement: "bottom",
    },
    {
      target: ".new-project-button",
      content:
        'Click the "New" button to create a new project. This will open a dialog where you can set the project name and category.',
      placement: "bottom",
    },
    {
      target: ".sort-button",
      content:
        "Use the sort button to organize your projects by name, category, time, or owner.",
      placement: "bottom",
    },
    {
      target: ".batch-selection",
      content:
        "Enable batch selection mode to select multiple projects for bulk operations like copying, sharing, or deleting.",
      placement: "bottom",
    },
    {
      target: ".project-card",
      content:
        "This is a project card. Click to view your 3D model, or use the preview button for a quick look.",
      placement: "top",
    },
    {
      target: ".library-tab",
      content:
        "Access your library of 3D models, materials, HDRIs, and images.",
      placement: "bottom",
    },
  ];

  const projectPageSteps: Step[] = [
    {
      target: ".logo",
      content: "Click the logo to return to your project dashboard.",
      placement: "bottom",
      disableBeacon: true,
    },
    {
      target: ".comments-button",
      content:
        "Click this button to enter comments mode and add feedback directly on the 3D model.",
      placement: "top",
      disableBeacon: false,
    },
    {
      target: ".model-viewer",
      content:
        "This is your 3D model viewer. Right-click anywhere to access additional tools.",
      placement: "center",
    },
    {
      target: ".comments-panel",
      content:
        "View and manage all comments for this project. Switch between unresolved and resolved comments.",
      placement: "left",
    },
  ];

  const modelViewerSteps: Step[] = [
    {
      target: ".canvas",
      content:
        "Click anywhere on the model to add comments when in comments mode.",
      placement: "center",
      disableBeacon: true,
    },
    {
      target: ".comment-markers",
      content:
        "These markers show existing comments. Click on them to view details and replies.",
      placement: "center",
    },
    {
      target: ".comment-form",
      content:
        "Add your comment and set the importance level (High, Medium, Low).",
      placement: "center",
    },
  ];

  const startOnboarding = (page: string) => {
    setCurrentPage(page);
    let pageSteps: Step[] = [];

    switch (page) {
      case "home":
        pageSteps = homePageSteps;
        break;
      case "project":
        pageSteps = projectPageSteps;
        break;
      case "modelViewer":
        pageSteps = modelViewerSteps;
        break;
      default:
        pageSteps = homePageSteps;
    }

    setSteps(pageSteps);
    setIsOnboardingActive(true);
  };

  const stopOnboarding = () => {
    setIsOnboardingActive(false);
    setCurrentPage(null);
  };

  const handleCallback = (data: CallBackProps) => {
    const { status, type } = data;

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      stopOnboarding();
      if (status === STATUS.FINISHED) {
        // Mark specific onboarding as completed based on current page
        switch (currentPage) {
          case "home":
            localStorage.setItem("onboarding-completed", "true");
            break;
          case "project":
            localStorage.setItem("project-onboarding-completed", "true");
            break;
          case "modelViewer":
            localStorage.setItem("modelviewer-onboarding-completed", "true");
            break;
          default:
            localStorage.setItem("onboarding-completed", "true");
        }
      }
    }
  };

  const contextValue: OnboardingContextType = {
    startOnboarding,
    stopOnboarding,
    isOnboardingActive,
    currentPage,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
      {isOnboardingActive && (
        <Joyride
          steps={steps}
          run={isOnboardingActive}
          continuous={true}
          showProgress={true}
          showSkipButton={true}
          callback={handleCallback}
          disableOverlayClose={true}
          disableScrolling={false}
          scrollToFirstStep={true}
          styles={{
            options: {
              primaryColor: "#F5C754",
              backgroundColor: "#18191E",
              textColor: "#FDE9CE",
              arrowColor: "#18191E",
              overlayColor: "rgba(24, 25, 30, 0.8)",
            },
            tooltip: {
              backgroundColor: "#47474B",
              borderRadius: "8px",
              padding: "16px",
              border: "1px solid #F5C754",
            },
            tooltipTitle: {
              color: "#F5C754",
              fontSize: "18px",
              fontWeight: "bold",
            },
            tooltipContent: {
              color: "#FDE9CE",
              fontSize: "14px",
            },
            buttonNext: {
              backgroundColor: "#F5C754",
              color: "#18191E",
              borderRadius: "6px",
              padding: "8px 16px",
              border: "none",
              fontWeight: "bold",
            },
            buttonBack: {
              backgroundColor: "transparent",
              color: "#FDE9CE",
              borderRadius: "6px",
              padding: "8px 16px",
              border: "1px solid #FDE9CE",
            },
            buttonSkip: {
              backgroundColor: "transparent",
              color: "#FDE9CE",
              borderRadius: "6px",
              padding: "8px 16px",
              border: "1px solid #FDE9CE",
            },
            buttonClose: {
              backgroundColor: "transparent",
              color: "#FDE9CE",
              borderRadius: "6px",
              padding: "8px 16px",
              border: "1px solid #FDE9CE",
            },
          }}
        />
      )}
    </OnboardingContext.Provider>
  );
};
