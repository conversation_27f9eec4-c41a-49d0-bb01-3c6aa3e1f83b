self.__REACT_LOADABLE_MANIFEST="{\"..\\\\node_modules\\\\.pnpm\\\\@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti\\\\node_modules\\\\@react-three\\\\drei\\\\core\\\\VideoTexture.js -> hls.js\":{\"id\":\"..\\\\node_modules\\\\.pnpm\\\\@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti\\\\node_modules\\\\@react-three\\\\drei\\\\core\\\\VideoTexture.js -> hls.js\",\"files\":[]},\"..\\\\node_modules\\\\.pnpm\\\\@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti\\\\node_modules\\\\@react-three\\\\drei\\\\web\\\\FaceLandmarker.js -> @mediapipe/tasks-vision\":{\"id\":\"..\\\\node_modules\\\\.pnpm\\\\@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti\\\\node_modules\\\\@react-three\\\\drei\\\\web\\\\FaceLandmarker.js -> @mediapipe/tasks-vision\",\"files\":[]},\"components\\\\Library\\\\ModelsCard.tsx -> ./DynamicModel\":{\"id\":\"components\\\\Library\\\\ModelsCard.tsx -> ./DynamicModel\",\"files\":[\"static/chunks/_app-pages-browser_src_components_Library_DynamicModel_tsx.js\"]}}"