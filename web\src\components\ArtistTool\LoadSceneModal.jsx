import { useState } from "react";
import { Upload } from "lucide-react";
import { Slide, toast } from "react-toastify";

export function LoadSceneModal({ isOpen, onClose, onLoad }) {
  const [file, setFile] = useState(null);

  if (!isOpen) return null;

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  const handleLoad = () => {
    if (!file) {
      toast.error("Please select a configuration file", {
        position: "bottom-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "colored",
        transition: Slide,
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target.result);
        if (onLoad) {
          onLoad(config);
          toast.success("Scene loaded successfully!", {
            position: "bottom-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "colored",
            transition: Slide,
          });
        }
        onClose();
      } catch (error) {
        toast.error("Invalid configuration file", {
          position: "bottom-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
          transition: Slide,
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[2000]">
      <div className="bg-gradient-to-tl from-[#32343D] to-[#14192D] p-6 rounded-lg shadow-lg w-[400px]">
        <h3 className="text-lg font-semibold text-[#FDE9CE] mb-4">
          Load Scene Configuration
        </h3>

        <div className="mb-4">
          <label className="block text-xs md:text-sm text-[#FDE9CE]/80 mb-2">
            Configuration File
          </label>
          <div className="flex items-center gap-2">
            <input
              type="file"
              accept=".json"
              onChange={handleFileChange}
              className="hidden"
              id="config-file"
            />
            <label
              htmlFor="config-file"
              className="flex-1 py-2 rounded bg-[#1A1D2E] border border-[#FDE9CE]/20 cursor-pointer hover:bg-white/10 transition-colors"
            >
              <div className="flex items-center justify-center gap-2">
                <Upload size={16} className="text-[#FDE9CE]" />
                <span className="text-xs md:text-sm text-[#FDE9CE]">
                  {file ? file.name : "Choose file"}
                </span>
              </div>
            </label>
          </div>
          <span className="text-[10px] md:text-xs text-[#FDE9CE]/60 block mt-1">
            Select a previously exported scene configuration
          </span>
        </div>

        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-xs md:text-sm text-[#FDE9CE] hover:bg-white/10 rounded transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleLoad}
            className="px-4 py-2 text-xs md:text-sm bg-white/10 text-[#FDE9CE] rounded hover:bg-white/20 transition-colors flex items-center gap-2"
          >
            <Upload size={16} />
            Load
          </button>
        </div>
      </div>
    </div>
  );
}
