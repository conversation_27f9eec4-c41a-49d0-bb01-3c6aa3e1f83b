import EditProjectPage from "@/components/EditProjectPage";
import { getWorkspace } from "@/lib/actions/workspace.actions";
import { getCurrentUser } from "@/lib/session";
import { redirect } from "next/navigation";

export default async function EditProjectPageRoute({
  params,
}: {
  params: { id: string };
}) {
  const user = await getCurrentUser();
  if (!user) {
    redirect("/login");
  }

  const { id } = params;
  const project = await getWorkspace({
    userId: user.id,
    workspaceId: id,
  });

  return <EditProjectPage project={project} user={user} />;
}
