import React, { useRef, Suspense } from "react";
import {
  OrbitControls,
  Environment,
  ContactShadows,
  useGLTF,
  Html,
  Loader,
} from "@react-three/drei";
import { ringParts } from "./libs/constants";
import { Cube } from "./models/Cube";
import * as THREE from "three";

const MaterialModel = ({
  modelPath,
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  scale = 1,
  materialId,
  materialOptions,
}) => {
  const { scene } = useGLTF(modelPath);

  const clonedScene = scene.clone();
  clonedScene.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      // Apply diamond material to meshes with "Diamond" in their name
      if (child.name && child.name.toLowerCase().includes("diamond")) {
        child.material = new THREE.MeshPhysicalMaterial({
          color: new THREE.Color("#FFFFFF"),
          metalness: 0,
          roughness: 0,
          transmission: 0.95,
          thickness: 0.5,
          ior: 2.4,
          attenuationDistance: 1,
          attenuationColor: new THREE.Color("#FFFFFF"),
          clearcoat: 1,
          clearcoatRoughness: 0,
          envMapIntensity: 2,
        });
        return;
      }

      // Apply selected material for all cases
      let material;

      switch (materialId) {
        case "grey":
          material = new THREE.MeshStandardMaterial({
            color: new THREE.Color("#d8d8d8"),
            metalness: 0.1,
            roughness: 0.75,
            envMapIntensity: 0,
          });
          break;
        case "gold":
          material = new THREE.MeshStandardMaterial({
            color: new THREE.Color("#E5b377"),
            metalness: 1,
            roughness: 0.01,
            envMapIntensity: 1.5,
          });
          break;
        case "rose-gold":
          material = new THREE.MeshStandardMaterial({
            color: new THREE.Color("#e8b4a0"),
            metalness: 1,
            roughness: 0.01,
            envMapIntensity: 1.3,
          });
          break;
        case "silver":
          material = new THREE.MeshStandardMaterial({
            color: new THREE.Color("#c2c2c3"),
            metalness: 1,
            roughness: 0.01,
            envMapIntensity: 1.0,
          });
          break;
        default:
          return;
      }

      child.material = material;
    }
  });

  return (
    <primitive
      object={clonedScene}
      position={position}
      rotation={rotation}
      scale={scale}
    />
  );
};

const Ring = ({ crownId, ringId, pawId, materialId, materialOptions }) => {
  const ringRef = useRef();

  // Find the model paths for selected parts
  const crownModel = ringParts.crown.find((p) => p.id === crownId)?.modelPath;
  const ringModel = ringParts.ring.find((p) => p.id === ringId)?.modelPath;
  const pawModel = ringParts.paw.find((p) => p.id === pawId)?.modelPath;

  // Function to get specific positioning for each paw model
  const getPawPosition = (pawId) => {
    switch (pawId) {
      case "paw-A":
        return [0, -1, 0];
      case "paw-B":
        return [2.5, -1, 0];
      case "paw-C":
        return [5, -1, 0];
      case "paw-D":
        return [7.5, -1, 0];
      default:
        return [0, -1, 0];
    }
  };

  return (
    <group ref={ringRef} position={[0, -0.75, 0]} scale={0.7}>
      {pawModel && (
        <MaterialModel
          modelPath={pawModel}
          position={getPawPosition(pawId)}
          scale={0.1}
          materialId={materialId}
          materialOptions={materialOptions}
        />
      )}

      {crownModel && (
        <MaterialModel
          modelPath={crownModel}
          position={[0, 0, 0]}
          scale={0.1}
          materialId={materialId}
          materialOptions={materialOptions}
        />
      )}

      {ringModel && (
        <MaterialModel
          modelPath={ringModel}
          position={[0, 0, 0]}
          scale={0.1}
          materialId={materialId}
          materialOptions={materialOptions}
        />
      )}
    </group>
  );
};

const Experience = ({ selectedParts, autoRotate, materialOptions }) => {
  return (
    <>
      <Suspense
        fallback={
          <Html>
            <Loader />
          </Html>
        }
      >
        <ambientLight intensity={0.3} />

        <Cube position={[0, -1.75, 0]} />
        <Ring
          crownId={selectedParts.crown}
          ringId={selectedParts.ring}
          pawId={selectedParts.paw}
          materialId={selectedParts.material}
          materialOptions={materialOptions}
        />

        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={10}
          target={[0, 0, 0]}
          autoRotate={autoRotate}
          autoRotateSpeed={1}
        />

        <ContactShadows
          position={[0, -2, 0]}
          opacity={0.75}
          scale={10}
          blur={2.5}
          far={4}
        />

        <Environment files="/hdris/hdri_15.hdr" />
      </Suspense>
    </>
  );
};

export default Experience;
