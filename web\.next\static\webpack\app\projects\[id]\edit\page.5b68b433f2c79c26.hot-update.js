"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Header.jsx":
/*!**********************************************!*\
  !*** ./src/components/ArtistTool/Header.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Camera,CloudUpload,History,Maximize2,Minimize2,Redo,RotateCcw,RotateCw,Share,Store,Undo,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SaveSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/SaveSceneModal.jsx\");\n/* harmony import */ var _LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LoadSceneModal */ \"(app-pages-browser)/./src/components/ArtistTool/LoadSceneModal.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _VersionHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../VersionHistory */ \"(app-pages-browser)/./src/components/VersionHistory.tsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { onReset, onScreenshot, onSaveScene, onLoadScene, isFullscreen, onToggleFullscreen, user, project, onUndo, onRedo, hasUnsavedChanges, onRevertToSaved } = param;\n    _s();\n    const [showSaveModal, setShowSaveModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showLoadModal, setShowLoadModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [showVersionHistory, setShowVersionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                id: \"header\",\n                className: \"fixed w-full z-50 px-4 py-2 transition-all duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-full px-6 py-1 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-auto h-10 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: \"/projects/\".concat(project._id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"size-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/CSS Logo.png\",\n                                        alt: \"Logo\",\n                                        width: 1024,\n                                        height: 780,\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                        icon: isFullscreen ? _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        tooltip: isFullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\",\n                                        onClick: onToggleFullscreen\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-white/20 mx-1 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\")\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 transition-all duration-300 \".concat(isFullscreen ? \"opacity-0\" : \"opacity-100\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                tooltip: \"Undo (Ctrl+Z)\",\n                                                onClick: onUndo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                tooltip: \"Redo (Ctrl+Y)\",\n                                                onClick: onRedo\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                        icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                        tooltip: \"Revert to Last Saved Version\",\n                                                        onClick: onRevertToSaved\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 px-2 py-1 rounded-full bg-orange-500/20 border border-orange-500/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-3 h-3 text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                tooltip: \"Reset Scene\",\n                                                onClick: onReset\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                tooltip: \"Screenshot\",\n                                                onClick: onScreenshot\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderButton, {\n                                                icon: _barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                tooltip: \"Version History\",\n                                                onClick: ()=>setShowVersionHistory(true)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-white/20 mx-1\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowLoadModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Load Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowSaveModal(true),\n                                                                    className: \"px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Camera_CloudUpload_History_Maximize2_Minimize2_Redo_RotateCcw_RotateCw_Share_Store_Undo_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-white/80 group-hover:text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Save Scene\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveSceneModal__WEBPACK_IMPORTED_MODULE_5__.SaveSceneModal, {\n                isOpen: showSaveModal,\n                onClose: ()=>setShowSaveModal(false),\n                onSave: (sceneName, description, saveType)=>{\n                    onSaveScene(sceneName, description, saveType);\n                    setShowSaveModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadSceneModal__WEBPACK_IMPORTED_MODULE_6__.LoadSceneModal, {\n                isOpen: showLoadModal,\n                onClose: ()=>setShowLoadModal(false),\n                onLoad: (config)=>{\n                    onLoadScene(config);\n                    setShowLoadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VersionHistory__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showVersionHistory,\n                onClose: ()=>setShowVersionHistory(false),\n                userId: user.id,\n                workspaceId: project._id,\n                onApplyVersion: (config)=>{\n                    onLoadScene(config);\n                    setShowVersionHistory(false);\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"BpQ93XwKg8UzXTTc2Gl4xwXptgA=\");\n_c = Header;\nfunction HeaderButton(param) {\n    let { icon: Icon, tooltip, primary = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClick,\n                    className: \"p-1.5 rounded-full \".concat(primary ? \"bg-primary text-white hover:bg-primary-600\" : \"text-white hover:bg-white/20\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: tooltip\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Header.jsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HeaderButton;\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"HeaderButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Header.jsx\n"));

/***/ })

});